export declare enum NetworkId {
    Mainnet = 1,
    Testnet = 0
}
export declare type NetworkMagic = number;
export declare enum NetworkMagics {
    Mainnet = 764824073,
    Preprod = 1,
    Preview = 2,
    Sanchonet = 4
}
export interface ChainId {
    networkId: NetworkId;
    networkMagic: NetworkMagic;
}
export declare const ChainIds: {
    Mainnet: {
        networkId: NetworkId;
        networkMagic: NetworkMagics;
    };
    Preprod: {
        networkId: NetworkId;
        networkMagic: NetworkMagics;
    };
    Preview: {
        networkId: NetworkId;
        networkMagic: NetworkMagics;
    };
    Sanchonet: {
        networkId: NetworkId;
        networkMagic: NetworkMagics;
    };
};
//# sourceMappingURL=ChainId.d.ts.map