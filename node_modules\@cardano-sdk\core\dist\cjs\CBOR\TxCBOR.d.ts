import { HexBlob, OpaqueString } from '@cardano-sdk/util';
import { Tx, TxBody } from '../Cardano';
import type { Cardano } from '..';
export declare type TxCBOR = OpaqueString<'TxCbor'>;
export declare const TxCBOR: {
    (tx: string): TxCBOR;
    serialize(tx: Cardano.Tx): TxCBOR;
    deserialize(tx: TxCBOR): Cardano.Tx;
};
export declare const deserializeTx: (txBody: HexBlob | Buffer | Uint8Array | string) => Tx<TxBody>;
//# sourceMappingURL=TxCBOR.d.ts.map