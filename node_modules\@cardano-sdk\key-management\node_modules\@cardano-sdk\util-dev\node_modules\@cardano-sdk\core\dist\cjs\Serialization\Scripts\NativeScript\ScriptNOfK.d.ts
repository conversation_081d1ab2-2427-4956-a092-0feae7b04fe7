import { HexBlob } from '@cardano-sdk/util';
import { NativeScript } from './NativeScript';
import type * as Cardano from '../../../Cardano';
export declare class ScriptNOfK {
    #private;
    constructor(nativeScripts: Array<NativeScript>, required: number);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): ScriptNOfK;
    toCore(): Cardano.RequireAtLeastScript;
    static fromCore(script: Cardano.RequireAtLeastScript): ScriptNOfK;
    required(): number;
    setRequired(required: number): void;
    nativeScripts(): Array<NativeScript>;
    setNativeScripts(nativeScripts: Array<NativeScript>): void;
}
//# sourceMappingURL=ScriptNOfK.d.ts.map