import { HexBlob } from '@cardano-sdk/util';
import type * as Cardano from '../../Cardano';
export declare class StakeRegistration {
    #private;
    constructor(credential: Cardano.Credential);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): StakeRegistration;
    toCore(): Cardano.StakeAddressCertificate;
    static fromCore(cert: Cardano.StakeAddressCertificate): StakeRegistration;
    stakeCredential(): Cardano.Credential;
    setStakeCredential(credential: Cardano.Credential): void;
}
//# sourceMappingURL=StakeRegistration.d.ts.map