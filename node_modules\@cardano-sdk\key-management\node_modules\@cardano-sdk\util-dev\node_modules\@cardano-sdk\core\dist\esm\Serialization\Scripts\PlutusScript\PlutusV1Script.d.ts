import * as Crypto from '@cardano-sdk/crypto';
import { HexBlob } from '@cardano-sdk/util';
import type * as Cardano from '../../../Cardano/index.js';
export declare class PlutusV1Script {
    #private;
    constructor(compiledByteCode: HexBlob);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): PlutusV1Script;
    toCore(): Cardano.PlutusScript;
    static fromCore(plutusScript: Cardano.PlutusScript): PlutusV1Script;
    hash(): Crypto.Hash28ByteBase16;
    rawBytes(): HexBlob;
    setRawBytes(bytes: HexBlob): void;
}
//# sourceMappingURL=PlutusV1Script.d.ts.map