import { NftMetadata as _NftMetadata } from './types';
export * from './types';
export * from './errors';
export declare type NftMetadata = _NftMetadata;
export declare const NftMetadata: {
    fromMetadatum: (asset: {
        policyId: import("../../Cardano").PolicyId;
        name: import("../../Cardano").AssetName;
    }, metadata: import("../../Cardano").TxMetadata | undefined, logger: import("ts-log").Logger, strict?: boolean) => _NftMetadata | null;
    fromPlutusData: (plutusData: import("../../Cardano").PlutusData | undefined, parentLogger: import("ts-log").Logger, strict?: boolean) => _NftMetadata | null;
};
//# sourceMappingURL=index.d.ts.map