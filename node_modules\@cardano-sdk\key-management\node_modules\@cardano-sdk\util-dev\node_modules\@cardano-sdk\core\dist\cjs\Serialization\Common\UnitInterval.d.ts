import { HexBlob } from '@cardano-sdk/util';
import type * as Cardano from '../../Cardano';
export declare class UnitInterval {
    #private;
    constructor(numerator: bigint, denominator: bigint);
    static fromFloat(number: number | undefined): UnitInterval | undefined;
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): UnitInterval;
    toCore(): Cardano.Fraction;
    static fromCore(fraction: Cardano.Fraction): UnitInterval;
    numerator(): bigint;
    setNumerator(numerator: bigint): void;
    denominator(): bigint;
    setDenominator(denominator: bigint): void;
    toFloat(): number;
}
//# sourceMappingURL=UnitInterval.d.ts.map