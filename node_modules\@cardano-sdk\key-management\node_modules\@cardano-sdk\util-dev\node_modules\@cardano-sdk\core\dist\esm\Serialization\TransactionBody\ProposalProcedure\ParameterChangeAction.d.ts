import { GovernanceActionId } from '../../Common/GovernanceActionId.js';
import { Hash28ByteBase16 } from '@cardano-sdk/crypto';
import { HexBlob } from '@cardano-sdk/util';
import { ProtocolParamUpdate } from '../../Update/index.js';
import type * as Cardano from '../../../Cardano/index.js';
export declare class ParameterChangeAction {
    #private;
    constructor(protocolParamUpdate: ProtocolParamUpdate, govActionId?: GovernanceActionId, policyHash?: Hash28ByteBase16);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): ParameterChangeAction;
    toCore(): Cardano.ParameterChangeAction;
    static fromCore(parameterChangeAction: Cardano.ParameterChangeAction): ParameterChangeAction;
    govActionId(): GovernanceActionId | undefined;
    protocolParamUpdate(): ProtocolParamUpdate;
    policyHash(): Hash28ByteBase16 | undefined;
}
//# sourceMappingURL=ParameterChangeAction.d.ts.map