import { AuxiliaryData } from './AuxiliaryData/index.js';
import { HexBlob, OpaqueString } from '@cardano-sdk/util';
import { TransactionBody } from './TransactionBody/index.js';
import { TransactionWitnessSet } from './TransactionWitnessSet/index.js';
import type * as Cardano from '../Cardano/index.js';
export declare type TxCBOR = OpaqueString<'TxCbor'>;
export declare type TxBodyCBOR = OpaqueString<'TxBodyCbor' & HexBlob['__opaqueString']>;
export declare class Transaction {
    #private;
    constructor(body: TransactionBody, witnessSet: TransactionWitnessSet, auxiliaryData?: AuxiliaryData);
    toCbor(): TxCBOR;
    static fromCbor(cbor: TxCBOR): Transaction;
    toCore(): Cardano.Tx;
    static fromCore(tx: Cardano.Tx): Transaction;
    body(): TransactionBody;
    setBody(body: TransactionBody): void;
    witnessSet(): TransactionWitnessSet;
    setWitnessSet(witnessSet: TransactionWitnessSet): void;
    isValid(): boolean;
    setIsValid(valid: boolean): void;
    auxiliaryData(): AuxiliaryData | undefined;
    setAuxiliaryData(auxiliaryData: AuxiliaryData | undefined): void;
    getId(): Cardano.TransactionId;
    clone(): Transaction;
}
export declare const TxCBOR: {
    (tx: string): TxCBOR;
    serialize(tx: Cardano.Tx): TxCBOR;
    deserialize(tx: TxCBOR): Cardano.Tx;
};
export declare const TxBodyCBOR: {
    (tx: string): TxBodyCBOR;
    fromTxCBOR(txCbor: TxCBOR): TxBodyCBOR;
};
export declare const deserializeTx: (txBody: HexBlob | Buffer | Uint8Array | string) => Cardano.Tx<Cardano.TxBody>;
//# sourceMappingURL=Transaction.d.ts.map