import { HexBlob } from '@cardano-sdk/util';
import { ProposedProtocolParameterUpdates } from './ProposedProtocolParameterUpdates.js';
import type * as Cardano from '../../Cardano/index.js';
export declare class Update {
    #private;
    constructor(updates: ProposedProtocolParameterUpdates, epoch: Cardano.EpochNo);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): Update;
    toCore(): Cardano.Update;
    static fromCore(update: Cardano.Update): Update;
    epoch(): Cardano.EpochNo;
    setEpoch(epoch: Cardano.EpochNo): void;
    proposedProtocolParameterUpdates(): ProposedProtocolParameterUpdates;
    setProposedProtocolParameterUpdates(updates: ProposedProtocolParameterUpdates): void;
}
//# sourceMappingURL=Update.d.ts.map