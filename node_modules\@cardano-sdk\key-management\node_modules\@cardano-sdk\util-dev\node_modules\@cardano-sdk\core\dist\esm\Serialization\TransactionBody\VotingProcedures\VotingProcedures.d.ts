import { GovernanceActionId } from '../../Common/GovernanceActionId.js';
import { HexBlob } from '@cardano-sdk/util';
import { Voter } from './Voter.js';
import { VotingProcedure } from './VotingProcedure.js';
import type * as Cardano from '../../../Cardano/index.js';
export declare class VotingProcedures {
    #private;
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): VotingProcedures;
    toCore(): Cardano.VotingProcedures;
    static fromCore(votingProcedures: Cardano.VotingProcedures): VotingProcedures;
    insert(voter: Voter, actionId: GovernanceActionId, votingProcedure: VotingProcedure): void;
    get(voter: Voter, governanceActionId: GovernanceActionId): VotingProcedure | undefined;
    getVoters(): Array<Voter>;
    getGovernanceActionIdsByVoter(voter: Voter): Array<GovernanceActionId>;
}
//# sourceMappingURL=VotingProcedures.d.ts.map