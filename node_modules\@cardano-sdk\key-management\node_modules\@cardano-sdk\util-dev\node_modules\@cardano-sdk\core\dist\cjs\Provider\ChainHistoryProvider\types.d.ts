import { Cardano, Paginated, PaginationArgs, Provider } from '../..';
import { Range } from '@cardano-sdk/util';
export declare type TransactionsByAddressesArgs = {
    addresses: Cardano.PaymentAddress[];
    pagination: PaginationArgs;
    blockRange?: Range<Cardano.BlockNo>;
};
export declare type TransactionsByIdsArgs = {
    ids: Cardano.TransactionId[];
};
export declare type BlocksByIdsArgs = {
    ids: Cardano.BlockId[];
};
export interface ChainHistoryProvider extends Provider {
    transactionsByAddresses: (args: TransactionsByAddressesArgs) => Promise<Paginated<Cardano.HydratedTx>>;
    transactionsByHashes: (args: TransactionsByIdsArgs) => Promise<Cardano.HydratedTx[]>;
    blocksByHashes: (args: BlocksByIdsArgs) => Promise<Cardano.ExtendedBlockInfo[]>;
}
//# sourceMappingURL=types.d.ts.map