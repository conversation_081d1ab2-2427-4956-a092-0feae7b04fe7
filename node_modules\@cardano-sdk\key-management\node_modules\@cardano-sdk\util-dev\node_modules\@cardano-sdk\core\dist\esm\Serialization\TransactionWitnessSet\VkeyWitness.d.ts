import * as Crypto from '@cardano-sdk/crypto';
import { HexBlob } from '@cardano-sdk/util';
export declare class VkeyWitness {
    #private;
    constructor(vkey: Crypto.Ed25519PublicKeyHex, signature: Crypto.Ed25519SignatureHex);
    toCbor(): HexBlob;
    static from<PERSON>bor(cbor: HexBlob): VkeyWitness;
    toCore(): [Crypto.Ed25519PublicKeyHex, Crypto.Ed25519SignatureHex];
    static fromCore(signatureEntry: [Crypto.Ed25519PublicKeyHex, Crypto.Ed25519SignatureHex]): VkeyWitness;
    vkey(): Crypto.Ed25519PublicKeyHex;
    setVkey(vkey: Crypto.Ed25519PublicKeyHex): void;
    signature(): Crypto.Ed25519SignatureHex;
    setSignature(signature: Crypto.Ed25519SignatureHex): void;
}
//# sourceMappingURL=VkeyWitness.d.ts.map