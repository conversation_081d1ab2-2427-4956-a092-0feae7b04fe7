'use client'

import { useState, useEffect } from 'react'
import { MapPin, Filter, Search, Mountain } from 'lucide-react'
import { TrailCard } from '@/components/trails/TrailCard'
import { Trail } from '@/types'

// Mock trail data - in production, this would come from an API
const mockTrails: Trail[] = [
  {
    id: '1',
    name: 'Ella Rock Trail',
    location: 'Ella, Sri Lanka',
    difficulty: 'Moderate',
    distance: '8.2 km',
    duration: '4-5 hours',
    description: 'A scenic hike offering panoramic views of the hill country with tea plantations and valleys.',
    price: 2500,
    available: true,
    features: ['Scenic Views', 'Tea Plantations', 'Photography Spots'],
    maxCapacity: 20,
    currentBookings: 12,
    coordinates: { lat: 6.8721, lng: 81.0462 }
  },
  {
    id: '2',
    name: 'Adam\'s Peak (Sri Pada)',
    location: 'Ratnapura, Sri Lanka',
    difficulty: 'Hard',
    distance: '11.5 km',
    duration: '6-8 hours',
    description: 'Sacred mountain pilgrimage with breathtaking sunrise views from the summit.',
    price: 3500,
    available: true,
    features: ['Sunrise Views', 'Religious Site', 'Night Hiking'],
    maxCapacity: 50,
    currentBookings: 35,
    coordinates: { lat: 6.8094, lng: 80.4992 }
  },
  {
    id: '3',
    name: 'Horton Plains National Park',
    location: 'Nuwara Eliya, Sri Lanka',
    difficulty: 'Easy',
    distance: '9.5 km',
    duration: '3-4 hours',
    description: 'UNESCO World Heritage site featuring World\'s End cliff and Baker\'s Falls.',
    price: 4000,
    available: false,
    features: ['World\'s End', 'Baker\'s Falls', 'Wildlife Viewing'],
    maxCapacity: 30,
    currentBookings: 30,
    coordinates: { lat: 6.8069, lng: 80.7906 }
  },
  {
    id: '4',
    name: 'Sigiriya Rock Fortress',
    location: 'Dambulla, Sri Lanka',
    difficulty: 'Moderate',
    distance: '3.2 km',
    duration: '2-3 hours',
    description: 'Ancient rock fortress with frescoes, gardens, and panoramic views.',
    price: 3000,
    available: true,
    features: ['Ancient Ruins', 'Frescoes', 'Historical Site'],
    maxCapacity: 40,
    currentBookings: 18,
    coordinates: { lat: 7.9570, lng: 80.7603 }
  },
  {
    id: '5',
    name: 'Knuckles Mountain Range',
    location: 'Matale, Sri Lanka',
    difficulty: 'Hard',
    distance: '15.8 km',
    duration: '8-10 hours',
    description: 'Challenging trek through cloud forests with diverse flora and fauna.',
    price: 5000,
    available: true,
    features: ['Cloud Forest', 'Biodiversity', 'Multi-day Option'],
    maxCapacity: 15,
    currentBookings: 8,
    coordinates: { lat: 7.4500, lng: 80.7500 }
  },
  {
    id: '6',
    name: 'Pidurangala Rock',
    location: 'Dambulla, Sri Lanka',
    difficulty: 'Easy',
    distance: '2.1 km',
    duration: '1-2 hours',
    description: 'Alternative viewpoint to Sigiriya with fewer crowds and great sunrise views.',
    price: 1500,
    available: true,
    features: ['Sunrise Views', 'Less Crowded', 'Quick Hike'],
    maxCapacity: 25,
    currentBookings: 10,
    coordinates: { lat: 7.9569, lng: 80.7511 }
  }
]

export default function TrailsPage() {
  const [trails, setTrails] = useState<Trail[]>(mockTrails)
  const [filteredTrails, setFilteredTrails] = useState<Trail[]>(mockTrails)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>('')
  const [selectedLocation, setSelectedLocation] = useState<string>('')
  const [showAvailableOnly, setShowAvailableOnly] = useState(false)
  const [sortBy, setSortBy] = useState<string>('name')

  // Get unique locations and difficulties for filters
  const locations = Array.from(new Set(trails.map(trail => trail.location.split(',')[1]?.trim() || trail.location)))
  const difficulties = Array.from(new Set(trails.map(trail => trail.difficulty)))

  useEffect(() => {
    filterAndSortTrails()
  }, [searchQuery, selectedDifficulty, selectedLocation, showAvailableOnly, sortBy])

  const filterAndSortTrails = () => {
    let filtered = trails.filter(trail => {
      const matchesSearch = trail.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           trail.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           trail.description.toLowerCase().includes(searchQuery.toLowerCase())
      
      const matchesDifficulty = !selectedDifficulty || trail.difficulty === selectedDifficulty
      const matchesLocation = !selectedLocation || trail.location.includes(selectedLocation)
      const matchesAvailability = !showAvailableOnly || trail.available
      
      return matchesSearch && matchesDifficulty && matchesLocation && matchesAvailability
    })

    // Sort trails
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'price':
          return a.price - b.price
        case 'difficulty':
          const difficultyOrder = { 'Easy': 1, 'Moderate': 2, 'Hard': 3 }
          return difficultyOrder[a.difficulty] - difficultyOrder[b.difficulty]
        case 'distance':
          return parseFloat(a.distance) - parseFloat(b.distance)
        default:
          return a.name.localeCompare(b.name)
      }
    })

    setFilteredTrails(filtered)
  }

  const clearFilters = () => {
    setSearchQuery('')
    setSelectedDifficulty('')
    setSelectedLocation('')
    setShowAvailableOnly(false)
    setSortBy('name')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <button 
                onClick={() => window.location.href = '/'}
                className="flex items-center space-x-2 hover:opacity-80 transition-opacity"
              >
                <Mountain className="h-8 w-8 text-green-600" />
                <span className="text-2xl font-bold text-gray-900">VinTrek</span>
              </button>
            </div>
            <nav className="hidden md:flex space-x-8">
              <a href="/" className="text-gray-700 hover:text-green-600 transition-colors">Home</a>
              <a href="/trails" className="text-green-600 font-medium">Trails</a>
              <a href="/dashboard" className="text-gray-700 hover:text-green-600 transition-colors">Dashboard</a>
              <a href="/rewards" className="text-gray-700 hover:text-green-600 transition-colors">Rewards</a>
            </nav>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Discover Sri Lankan Trails</h1>
          <p className="text-gray-600">Explore breathtaking hiking trails and earn blockchain rewards for your adventures.</p>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
            {/* Search */}
            <div className="lg:col-span-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search trails..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Difficulty Filter */}
            <div>
              <select
                value={selectedDifficulty}
                onChange={(e) => setSelectedDifficulty(e.target.value)}
                className="w-full py-2 px-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                <option value="">All Difficulties</option>
                {difficulties.map(difficulty => (
                  <option key={difficulty} value={difficulty}>{difficulty}</option>
                ))}
              </select>
            </div>

            {/* Location Filter */}
            <div>
              <select
                value={selectedLocation}
                onChange={(e) => setSelectedLocation(e.target.value)}
                className="w-full py-2 px-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                <option value="">All Locations</option>
                {locations.map(location => (
                  <option key={location} value={location}>{location}</option>
                ))}
              </select>
            </div>

            {/* Sort By */}
            <div>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="w-full py-2 px-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                <option value="name">Sort by Name</option>
                <option value="price">Sort by Price</option>
                <option value="difficulty">Sort by Difficulty</option>
                <option value="distance">Sort by Distance</option>
              </select>
            </div>

            {/* Available Only Toggle */}
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="available-only"
                checked={showAvailableOnly}
                onChange={(e) => setShowAvailableOnly(e.target.checked)}
                className="rounded border-gray-300 text-green-600 focus:ring-green-500"
              />
              <label htmlFor="available-only" className="text-sm text-gray-700">
                Available only
              </label>
            </div>
          </div>

          {/* Clear Filters */}
          <div className="mt-4 flex justify-between items-center">
            <p className="text-sm text-gray-600">
              Showing {filteredTrails.length} of {trails.length} trails
            </p>
            <button
              onClick={clearFilters}
              className="text-sm text-green-600 hover:text-green-700 transition-colors"
            >
              Clear all filters
            </button>
          </div>
        </div>

        {/* Trails Grid */}
        {filteredTrails.length === 0 ? (
          <div className="text-center py-12">
            <MapPin className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No trails found</h3>
            <p className="text-gray-600 mb-4">Try adjusting your search criteria or filters.</p>
            <button
              onClick={clearFilters}
              className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors"
            >
              Clear Filters
            </button>
          </div>
        ) : (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredTrails.map((trail) => (
              <TrailCard key={trail.id} trail={trail} />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
