"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/blockchain.ts":
/*!*******************************!*\
  !*** ./src/lib/blockchain.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BLOCKCHAIN_CONFIG: () => (/* binding */ BLOCKCHAIN_CONFIG),\n/* harmony export */   BlockchainService: () => (/* binding */ BlockchainService),\n/* harmony export */   TREK_TOKEN: () => (/* binding */ TREK_TOKEN),\n/* harmony export */   blockchainService: () => (/* binding */ blockchainService),\n/* harmony export */   formatAdaToLovelace: () => (/* binding */ formatAdaToLovelace),\n/* harmony export */   formatLovelaceToAda: () => (/* binding */ formatLovelaceToAda),\n/* harmony export */   generateAssetName: () => (/* binding */ generateAssetName)\n/* harmony export */ });\n// Dynamic imports for client-side only\nlet BrowserWallet = null;\nlet Transaction = null;\nlet AssetMetadata = null;\nif (true) {\n    __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_meshsdk_core_dist_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @meshsdk/core */ \"(app-pages-browser)/./node_modules/@meshsdk/core/dist/index.js\")).then((module)=>{\n        BrowserWallet = module.BrowserWallet;\n        Transaction = module.Transaction;\n        AssetMetadata = module.AssetMetadata;\n    });\n}\n// Blockchain configuration\nconst BLOCKCHAIN_CONFIG = {\n    network: \"testnet\" || 0,\n    blockfrostApiKey: \"testnetYourProjectIdHere\" || 0,\n    blockfrostUrl: \"https://cardano-testnet.blockfrost.io/api/v0\" || 0,\n    nftPolicyId: \"placeholder_nft_policy_id\" || 0,\n    tokenPolicyId: \"placeholder_token_policy_id\" || 0,\n    scriptAddress: \"addr_test1placeholder_script_address\" || 0\n};\n// TREK Token configuration\nconst TREK_TOKEN = {\n    symbol: 'TREK',\n    decimals: 6,\n    policyId: BLOCKCHAIN_CONFIG.tokenPolicyId,\n    assetName: '54524b'\n};\n// Blockchain service class\nclass BlockchainService {\n    setWallet(wallet) {\n        this.wallet = wallet;\n    }\n    // Get wallet balance in ADA\n    async getWalletBalance() {\n        if (!this.wallet || \"object\" === 'undefined') throw new Error('Wallet not connected');\n        try {\n            const balance = await this.wallet.getBalance();\n            return balance;\n        } catch (error) {\n            console.error('Error fetching wallet balance:', error);\n            throw error;\n        }\n    }\n    // Get wallet assets (NFTs and tokens)\n    async getWalletAssets() {\n        if (!this.wallet) throw new Error('Wallet not connected');\n        try {\n            const assets = await this.wallet.getAssets();\n            return assets;\n        } catch (error) {\n            console.error('Error fetching wallet assets:', error);\n            throw error;\n        }\n    }\n    // Get TREK token balance\n    async getTrekTokenBalance() {\n        try {\n            const assets = await this.getWalletAssets();\n            const trekAsset = assets.find((asset)=>asset.unit.startsWith(TREK_TOKEN.policyId));\n            if (trekAsset) {\n                return parseInt(trekAsset.quantity) / Math.pow(10, TREK_TOKEN.decimals);\n            }\n            return 0;\n        } catch (error) {\n            console.error('Error fetching TREK token balance:', error);\n            return 0;\n        }\n    }\n    // Get trail NFTs owned by wallet\n    async getTrailNFTs() {\n        try {\n            const assets = await this.getWalletAssets();\n            const nfts = assets.filter((asset)=>asset.unit.startsWith(BLOCKCHAIN_CONFIG.nftPolicyId) && asset.quantity === '1');\n            // Fetch metadata for each NFT\n            const nftsWithMetadata = await Promise.all(nfts.map(async (nft)=>{\n                try {\n                    const metadata = await this.fetchNFTMetadata(nft.unit);\n                    return {\n                        ...nft,\n                        metadata\n                    };\n                } catch (error) {\n                    console.error(\"Error fetching metadata for NFT \".concat(nft.unit, \":\"), error);\n                    return {\n                        ...nft,\n                        metadata: null\n                    };\n                }\n            }));\n            return nftsWithMetadata;\n        } catch (error) {\n            console.error('Error fetching trail NFTs:', error);\n            return [];\n        }\n    }\n    // Fetch NFT metadata from blockchain\n    async fetchNFTMetadata(assetId) {\n        try {\n            const response = await fetch(\"\".concat(BLOCKCHAIN_CONFIG.blockfrostUrl, \"/assets/\").concat(assetId), {\n                headers: {\n                    'project_id': BLOCKCHAIN_CONFIG.blockfrostApiKey\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const assetData = await response.json();\n            if (assetData.onchain_metadata) {\n                return assetData.onchain_metadata;\n            }\n            return null;\n        } catch (error) {\n            console.error('Error fetching NFT metadata:', error);\n            return null;\n        }\n    }\n    // Create booking transaction\n    async createBookingTransaction(trailId, amount, date) {\n        if (!this.wallet || \"object\" === 'undefined' || !Transaction) throw new Error('Wallet not connected or Transaction not available');\n        try {\n            const walletAddress = await this.wallet.getChangeAddress();\n            // Create booking metadata\n            const bookingMetadata = {\n                trail_id: trailId,\n                booking_date: date,\n                amount: amount,\n                timestamp: new Date().toISOString(),\n                hiker_address: walletAddress\n            };\n            // Build transaction (simplified - in production, this would interact with smart contracts)\n            const tx = new Transaction({\n                initiator: this.wallet\n            });\n            // Add metadata to transaction\n            tx.setMetadata(674, bookingMetadata);\n            // Send payment to script address (or trail operator)\n            tx.sendLovelace(BLOCKCHAIN_CONFIG.scriptAddress, (amount * 1000000).toString() // Convert ADA to Lovelace\n            );\n            const unsignedTx = await tx.build();\n            const signedTx = await this.wallet.signTx(unsignedTx);\n            const txHash = await this.wallet.submitTx(signedTx);\n            return txHash;\n        } catch (error) {\n            console.error('Error creating booking transaction:', error);\n            throw error;\n        }\n    }\n    // Mint trail completion NFT\n    async mintTrailNFT(trailData) {\n        if (!this.wallet || \"object\" === 'undefined' || !Transaction) throw new Error('Wallet not connected or Transaction not available');\n        try {\n            const walletAddress = await this.wallet.getChangeAddress();\n            const completionDate = new Date().toISOString();\n            // Create NFT metadata\n            const metadata = {\n                name: \"\".concat(trailData.trailName, \" Completion Certificate\"),\n                description: \"Proof of completion for \".concat(trailData.trailName, \" trail in \").concat(trailData.location),\n                image: \"ipfs://QmTrailNFTImage\".concat(Date.now()),\n                attributes: {\n                    trail_name: trailData.trailName,\n                    location: trailData.location,\n                    difficulty: trailData.difficulty,\n                    completion_date: completionDate,\n                    coordinates: trailData.coordinates,\n                    hiker_address: walletAddress\n                }\n            };\n            // Generate unique asset name\n            const assetName = \"VinTrekNFT\".concat(Date.now());\n            // Build minting transaction (simplified)\n            const tx = new Transaction({\n                initiator: this.wallet\n            });\n            // Add minting logic here (requires smart contract integration)\n            // This is a placeholder - actual implementation would use Mesh SDK's minting functions\n            const unsignedTx = await tx.build();\n            const signedTx = await this.wallet.signTx(unsignedTx);\n            const txHash = await this.wallet.submitTx(signedTx);\n            return txHash;\n        } catch (error) {\n            console.error('Error minting trail NFT:', error);\n            throw error;\n        }\n    }\n    // Mint TREK tokens as rewards\n    async mintTrekTokens(amount, reason) {\n        if (!this.wallet || \"object\" === 'undefined' || !Transaction) throw new Error('Wallet not connected or Transaction not available');\n        try {\n            const walletAddress = await this.wallet.getChangeAddress();\n            // Create reward metadata\n            const rewardMetadata = {\n                recipient: walletAddress,\n                amount: amount,\n                reason: reason,\n                timestamp: new Date().toISOString()\n            };\n            // Build token minting transaction (simplified)\n            const tx = new Transaction({\n                initiator: this.wallet\n            });\n            // Add metadata\n            tx.setMetadata(674, rewardMetadata);\n            // Add token minting logic here (requires smart contract integration)\n            const unsignedTx = await tx.build();\n            const signedTx = await this.wallet.signTx(unsignedTx);\n            const txHash = await this.wallet.submitTx(signedTx);\n            return txHash;\n        } catch (error) {\n            console.error('Error minting TREK tokens:', error);\n            throw error;\n        }\n    }\n    // Verify transaction on blockchain\n    async verifyTransaction(txHash) {\n        try {\n            const response = await fetch(\"\".concat(BLOCKCHAIN_CONFIG.blockfrostUrl, \"/txs/\").concat(txHash), {\n                headers: {\n                    'project_id': BLOCKCHAIN_CONFIG.blockfrostApiKey\n                }\n            });\n            if (response.ok) {\n                const txData = await response.json();\n                return txData.block !== null // Transaction is confirmed if it's in a block\n                ;\n            }\n            return false;\n        } catch (error) {\n            console.error('Error verifying transaction:', error);\n            return false;\n        }\n    }\n    constructor(wallet = null){\n        this.wallet = null;\n        this.wallet = wallet;\n    }\n}\n// Utility functions\nconst formatLovelaceToAda = (lovelace)=>{\n    return parseInt(lovelace) / 1000000;\n};\nconst formatAdaToLovelace = (ada)=>{\n    return (ada * 1000000).toString();\n};\nconst generateAssetName = (prefix)=>{\n    const timestamp = Date.now();\n    const random = Math.random().toString(36).substr(2, 9);\n    return \"\".concat(prefix).concat(timestamp).concat(random);\n};\n// Export singleton instance\nconst blockchainService = new BlockchainService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/blockchain.ts\n"));

/***/ })

});