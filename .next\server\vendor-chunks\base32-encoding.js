/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/base32-encoding";
exports.ids = ["vendor-chunks/base32-encoding"];
exports.modules = {

/***/ "(ssr)/./node_modules/base32-encoding/index.js":
/*!***********************************************!*\
  !*** ./node_modules/base32-encoding/index.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("var std = '23456789abcdefghijkmnpqrstuvwxyz'\n\nexports.stringify = function (buf, alphabet) {\n  if (alphabet == null) alphabet = std\n  return from(base32(buf), b => alphabet[b]).join('')\n}\n\nexports.parse = function (str, alphabet) {\n  if (alphabet == null) alphabet = std\n  return base256(str.split('').map(s => alphabet.indexOf(s)))\n}\n\nfunction from (buf, map) {\n  var a = new Array(buf.length)\n\n  for (var i = 0; i < a.length; i++) {\n    a[i] = map(buf[i])\n  }\n\n  return a\n}\n\nexports.encode = base32\nfunction base32 (buf, arr, offset) {\n  exports.encode.bytes = Math.ceil(buf.length * 8 / 5)\n  if (arr == null) arr = Buffer.alloc(exports.encode.bytes)\n  if (offset == null) offset = 0\n\n  // for every 5\n  for (var i = 0, j = offset; i + 5 <= buf.length; i += 5, j += 8) {\n    arr[j + 0] = ((buf[i + 0] & 0b11111000) >>> 3)\n    arr[j + 1] = ((buf[i + 0] & 0b00000111)  << 2) | ((buf[i + 1] & 0b11000000) >>> 6)\n    arr[j + 2] = ((buf[i + 1] & 0b00111110) >>> 1)\n    arr[j + 3] = ((buf[i + 1] & 0b00000001)  << 4) | ((buf[i + 2] & 0b11110000) >>> 4)\n    arr[j + 4] = ((buf[i + 2] & 0b00001111)  << 1) | ((buf[i + 3] & 0b10000000) >>> 7)\n    arr[j + 5] = ((buf[i + 3] & 0b01111100) >>> 2)\n    arr[j + 6] = ((buf[i + 3] & 0b00000011)  << 3) | ((buf[i + 4] & 0b11100000) >>> 5)\n    arr[j + 7] = ((buf[i + 4] & 0b00011111))\n  }\n\n  // This switch statement is meant to be read from bottom to top\n  switch (buf.length - i) {\n    // No need for 5 since we work in batches of 5 above\n    case 4:\n      arr[j + 4] |= ((buf[i + 3] & 0b10000000) >>> 7)\n      arr[j + 5] |= ((buf[i + 3] & 0b01111100) >>> 2)\n      arr[j + 6] |= ((buf[i + 3] & 0b00000011)  << 3)\n\n    case 3:\n      arr[j + 3] |= ((buf[i + 2] & 0b11110000) >>> 4)\n      arr[j + 4] |= ((buf[i + 2] & 0b00001111)  << 1)\n\n    case 2:\n      arr[j + 1] |= ((buf[i + 1] & 0b11000000) >>> 6)\n      arr[j + 2] |= ((buf[i + 1] & 0b00111110) >>> 1)\n      arr[j + 3] |= ((buf[i + 1] & 0b00000001)  << 4)\n\n    case 1:\n      arr[j + 0] |= ((buf[i + 0] & 0b11111000) >>> 3)\n      arr[j + 1] |= ((buf[i + 0] & 0b00000111)  << 2)\n  }\n\n  return arr\n}\n\nexports.decode = base256\nfunction base256 (buf, arr, offset) {\n  exports.decode.bytes = Math.floor(buf.length * 5 / 8)\n  if (arr == null) arr = Buffer.alloc(exports.decode.bytes)\n  if (offset == null) offset = 0\n\n  for (var i = 0, j = offset; i + 8 <= buf.length; i += 8, j += 5) {\n    arr[j + 0] = (buf[i + 0] << 3) & 255 | (buf[i + 1] >>> 2) & 255\n    arr[j + 1] = (buf[i + 1] << 6) & 255 | (buf[i + 2]  << 1) & 255 | (buf[i + 3] >>> 4) & 255\n    arr[j + 2] = (buf[i + 3] << 4) & 255 | (buf[i + 4] >>> 1) & 255\n    arr[j + 3] = (buf[i + 4] << 7) & 255 | (buf[i + 5]  << 2) & 255 | (buf[i + 6]  >> 3) & 255\n    arr[j + 4] = (buf[i + 6] << 5) & 255 | (buf[i + 7]      ) & 255\n  }\n\n  switch (buf.length - i) {\n    case 7:\n      arr[j + 3] |= (buf[i + 6]  >> 3) & 255\n      arr[j + 4] |= (buf[i + 6]  << 5) & 255\n\n    case 6:\n      arr[j + 3] |= (buf[i + 5]  << 2) & 255\n\n    case 5:\n      arr[j + 2] |= (buf[i + 4] >>> 1) & 255\n      arr[j + 3] |= (buf[i + 4]  << 7) & 255\n\n    case 4:\n      arr[j + 1] |= (buf[i + 3] >>> 4) & 255\n      arr[j + 2] |= (buf[i + 3]  << 4) & 255\n\n    case 3:\n      arr[j + 1] |= (buf[i + 2]  << 1) & 255\n\n    case 2:\n      arr[j + 0] |= (buf[i + 1] >>> 2) & 255\n      arr[j + 1] |= (buf[i + 1]  << 6) & 255\n\n    case 1:\n      arr[j + 0] |= (buf[i + 0]  << 3) & 255\n  }\n\n  return arr\n}\n\nexports.encodingLength = function (buf) {\n  return Math.ceil(buf.length * 8 / 5)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/base32-encoding/index.js\n");

/***/ })

};
;