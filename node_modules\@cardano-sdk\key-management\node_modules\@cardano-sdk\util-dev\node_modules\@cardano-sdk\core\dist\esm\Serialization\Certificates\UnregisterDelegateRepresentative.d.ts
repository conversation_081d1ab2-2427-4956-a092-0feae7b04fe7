import { HexBlob } from '@cardano-sdk/util';
import type * as Cardano from '../../Cardano/index.js';
export declare class UnregisterDelegateRepresentative {
    #private;
    constructor(drepCredential: Cardano.Credential, deposit: Cardano.Lovelace);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): UnregisterDelegateRepresentative;
    toCore(): Cardano.UnRegisterDelegateRepresentativeCertificate;
    static fromCore(cert: Cardano.UnRegisterDelegateRepresentativeCertificate): UnregisterDelegateRepresentative;
    credential(): Cardano.Credential;
    deposit(): Cardano.Lovelace;
}
//# sourceMappingURL=UnregisterDelegateRepresentative.d.ts.map