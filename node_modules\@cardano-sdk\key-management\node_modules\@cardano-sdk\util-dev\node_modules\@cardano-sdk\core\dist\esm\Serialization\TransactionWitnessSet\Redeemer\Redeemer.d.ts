import * as Crypto from '@cardano-sdk/crypto';
import { ExUnits } from '../../Common/index.js';
import { HexBlob } from '@cardano-sdk/util';
import { PlutusData } from '../../PlutusData/index.js';
import { RedeemerTag } from './RedeemerTag.js';
import type * as Cardano from '../../../Cardano/index.js';
export declare class Redeemer {
    #private;
    constructor(tag: RedeemerTag, index: bigint, data: PlutusData, exUnits: ExUnits);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): Redeemer;
    toCore(): Cardano.Redeemer;
    static fromCore(redeemer: Cardano.Redeemer): Redeemer;
    tag(): RedeemerTag;
    setTag(tag: RedeemerTag): void;
    index(): bigint;
    setIndex(index: bigint): void;
    data(): PlutusData;
    setData(data: PlutusData): void;
    exUnits(): ExUnits;
    setExUnits(exUnits: ExUnits): void;
    hash(): Crypto.Hash32ByteBase16;
}
//# sourceMappingURL=Redeemer.d.ts.map