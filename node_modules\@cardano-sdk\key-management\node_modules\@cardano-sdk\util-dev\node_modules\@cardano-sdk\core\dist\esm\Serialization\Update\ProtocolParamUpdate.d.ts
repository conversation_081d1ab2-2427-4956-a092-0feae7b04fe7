import { Costmdls } from './Costmdls/index.js';
import { DrepVotingThresholds } from './DrepVotingThresholds.js';
import { ExUnitPrices } from './ExUnitPrices.js';
import { ExUnits, ProtocolVersion, UnitInterval } from '../Common/index.js';
import { HexBlob } from '@cardano-sdk/util';
import { PoolVotingThresholds } from './PoolVotingThresholds.js';
import type * as Cardano from '../../Cardano/index.js';
export declare class ProtocolParamUpdate {
    #private;
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): ProtocolParamUpdate;
    toCore(): Cardano.ProtocolParametersUpdate;
    static fromCore<T extends Cardano.ProtocolParametersUpdateConway = Cardano.ProtocolParametersUpdate>(parametersUpdate: T): ProtocolParamUpdate;
    setMinFeeA(minFeeA: Cardano.Lovelace): void;
    minFeeA(): Cardano.Lovelace | undefined;
    setMinFeeB(minFeeB: Cardano.Lovelace): void;
    minFeeB(): Cardano.Lovelace | undefined;
    setMaxBlockBodySize(maxBlockBodySize: number): void;
    maxBlockBodySize(): number | undefined;
    setMaxTxSize(maxTxSize: number): void;
    maxTxSize(): number | undefined;
    setMaxBlockHeaderSize(maxBlockHeaderSize: number): void;
    maxBlockHeaderSize(): number | undefined;
    setKeyDeposit(keyDeposit: Cardano.Lovelace): void;
    keyDeposit(): Cardano.Lovelace | undefined;
    setPoolDeposit(poolDeposit: Cardano.Lovelace): void;
    poolDeposit(): Cardano.Lovelace | undefined;
    setMaxEpoch(maxEpoch: number): void;
    maxEpoch(): number | undefined;
    setNOpt(nOpt: number): void;
    nOpt(): number | undefined;
    setPoolPledgeInfluence(poolPledgeInfluence: UnitInterval): void;
    poolPledgeInfluence(): UnitInterval | undefined;
    setExpansionRate(expansionRate: UnitInterval): void;
    expansionRate(): UnitInterval | undefined;
    setTreasuryGrowthRate(treasuryGrowthRate: UnitInterval): void;
    treasuryGrowthRate(): UnitInterval | undefined;
    setD(d: UnitInterval): void;
    d(): UnitInterval | undefined;
    setExtraEntropy(extraEntropy: HexBlob): void;
    extraEntropy(): HexBlob | undefined;
    setProtocolVersion(protocolVersion: ProtocolVersion): void;
    protocolVersion(): ProtocolVersion | undefined;
    setMinPoolCost(minPoolCost: Cardano.Lovelace): void;
    minPoolCost(): Cardano.Lovelace | undefined;
    setAdaPerUtxoByte(adaPerUtxoByte: Cardano.Lovelace): void;
    adaPerUtxoByte(): Cardano.Lovelace | undefined;
    setCostModels(costModels: Costmdls): void;
    costModels(): Costmdls | undefined;
    setExecutionCosts(executionCosts: ExUnitPrices): void;
    executionCosts(): ExUnitPrices | undefined;
    setMaxTxExUnits(maxTxExUnits: ExUnits): void;
    maxTxExUnits(): ExUnits | undefined;
    setMaxBlockExUnits(maxBlockExUnits: ExUnits): void;
    maxBlockExUnits(): ExUnits | undefined;
    setMaxValueSize(maxValueSize: number): void;
    maxValueSize(): number | undefined;
    setCollateralPercentage(collateralPercentage: number): void;
    collateralPercentage(): number | undefined;
    setMaxCollateralInputs(maxCollateralInputs: number): void;
    maxCollateralInputs(): number | undefined;
    setPoolVotingThresholds(pooVotingThresholds: PoolVotingThresholds): void;
    poolVotingThresholds(): PoolVotingThresholds | undefined;
    setDrepVotingThresholds(drepVotingThresholds: DrepVotingThresholds): void;
    drepVotingThresholds(): DrepVotingThresholds | undefined;
    setMinCommitteeSize(minCommitteeSize: number): void;
    minCommitteeSize(): number | undefined;
    setCommitteeTermLimit(committeeTermLimit: number): void;
    committeeTermLimit(): number | undefined;
    setGovernanceActionValidityPeriod(governanceActionValidityPeriod: number): void;
    governanceActionValidityPeriod(): number | undefined;
    setGovernanceActionDeposit(governanceActionDeposit: number): void;
    governanceActionDeposit(): number | undefined;
    setDrepDeposit(drepDeposit: number): void;
    drepDeposit(): number | undefined;
    setDrepInactivityPeriod(drepInactivityPeriod: number): void;
    drepInactivityPeriod(): number | undefined;
    minFeeRefScriptCostPerByte(): UnitInterval | undefined;
}
//# sourceMappingURL=ProtocolParamUpdate.d.ts.map