/// <reference types="pouchdb-core" />
/// <reference types="node" />
import { Address, AddressProps, Credential } from './Address';
import { NetworkId } from '../ChainId';
export declare class EnterpriseAddress {
    #private;
    private constructor();
    static fromCredentials(networkId: NetworkId, payment: Credential): EnterpriseAddress;
    getPaymentCredential(): Credential;
    toAddress(): Address;
    static fromAddress(addr: Address): EnterpriseAddress | undefined;
    static packParts(props: AddressProps): Buffer;
    static unpackParts(type: number, data: Uint8Array): Address;
}
//# sourceMappingURL=EnterpriseAddress.d.ts.map