import { HexBlob } from '@cardano-sdk/util';
import type * as Cardano from '../../Cardano/index.js';
export declare class TransactionInput {
    #private;
    constructor(id: Cardano.TransactionId, index: bigint);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): TransactionInput;
    toCore(): Cardano.TxIn;
    static fromCore(coreTransactionInput: Cardano.TxIn): TransactionInput;
    transactionId(): Cardano.TransactionId;
    setTransactionId(id: Cardano.TransactionId): void;
    index(): bigint;
    setIndex(index: bigint): void;
}
//# sourceMappingURL=TransactionInput.d.ts.map