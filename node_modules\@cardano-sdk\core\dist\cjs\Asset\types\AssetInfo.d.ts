import { AssetFingerprint, AssetId, AssetName, PolicyId, TransactionId } from '../../Cardano';
import { NftMetadata } from '../NftMetadata';
import { TokenMetadata } from './TokenMetadata';
export interface AssetMintOrBurn {
    transactionId: TransactionId;
    quantity: bigint;
}
export interface AssetInfo {
    assetId: AssetId;
    policyId: PolicyId;
    name: AssetName;
    fingerprint: AssetFingerprint;
    quantity: bigint;
    supply: bigint;
    tokenMetadata?: TokenMetadata | null;
    nftMetadata?: NftMetadata | null;
}
//# sourceMappingURL=AssetInfo.d.ts.map