import * as Cardano from '../../../../Cardano';
import { HexBlob } from '@cardano-sdk/util';
export declare class SingleHostName {
    #private;
    constructor(dnsName: string, port?: number | undefined);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): SingleHostName;
    toCore(): Cardano.RelayByName;
    static fromCore(relay: Cardano.RelayByName): SingleHostName;
    port(): number | undefined;
    setPort(port: number | undefined): void;
    dnsName(): string;
    setDnsName(dnsName: string): void;
}
//# sourceMappingURL=SingleHostName.d.ts.map