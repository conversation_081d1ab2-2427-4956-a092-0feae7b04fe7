import { HexBlob } from '@cardano-sdk/util';
export declare enum PlutusListEncoding {
    FixedLength = 0,
    IndefiniteLength = 159
}
export declare enum PlutusMapEncoding {
    FixedLength = 0,
    IndefiniteLength = 191
}
export declare type PlutusList = {
    cbor?: HexBlob;
    encoding?: PlutusListEncoding;
    items: PlutusData[];
};
export declare type PlutusMap = {
    cbor?: HexBlob;
    encoding?: PlutusMapEncoding;
    data: Map<PlutusData, PlutusData>;
};
export declare type ConstrPlutusData = {
    cbor?: HexBlob;
    constructor: bigint;
    fields: PlutusList;
};
export declare type PlutusData = bigint | Uint8Array | PlutusList | PlutusMap | ConstrPlutusData;
//# sourceMappingURL=PlutusData.d.ts.map