import { HexBlob } from '@cardano-sdk/util';
import { MultiHostName } from './MultiHostName.js';
import { SingleHostAddr } from './SingleHostAddr.js';
import { SingleHostName } from './SingleHostName.js';
import type * as Cardano from '../../../../Cardano/index.js';
export declare enum RelayKind {
    SingleHostAddress = 0,
    SingleHostDnsName = 1,
    MultiHostDnsName = 2
}
export declare class Relay {
    #private;
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): Relay;
    toCore(): Cardano.Relay;
    static fromCore(coreRelay: Cardano.Relay): Relay;
    static newSingleHostAddr(singleHostaddr: SingleHostAddr): Relay;
    static newSingleHostName(singleHostName: SingleHostName): Relay;
    static newMultiHostName(multiHostName: MultiHostName): Relay;
    kind(): RelayKind;
    asSingleHostAddr(): SingleHostAddr | undefined;
    asSingleHostName(): SingleHostName | undefined;
    asMultiHostName(): MultiHostName | undefined;
}
//# sourceMappingURL=Relay.d.ts.map