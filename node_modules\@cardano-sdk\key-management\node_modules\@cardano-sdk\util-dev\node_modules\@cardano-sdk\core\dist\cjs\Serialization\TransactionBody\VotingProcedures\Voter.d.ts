import { Ed25519KeyHashHex } from '@cardano-sdk/crypto';
import { HexBlob } from '@cardano-sdk/util';
import { VoterKind } from './VoterKind';
import type * as Cardano from '../../../Cardano';
export declare class Voter {
    #private;
    constructor(kind: VoterKind, credential: Cardano.Credential);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): Voter;
    toCore(): Cardano.Voter;
    static fromCore(coreVoter: Cardano.Voter): Voter;
    static newConstitutionalCommitteeHotKey(credential: Cardano.Credential): Voter;
    static newDrep(credential: Cardano.Credential): Voter;
    static newStakingPool(keyHash: Ed25519KeyHashHex): Voter;
    kind(): VoterKind;
    toConstitutionalCommitteeHotCred(): Cardano.Credential | undefined;
    toDrepCred(): Cardano.Credential | undefined;
    toStakingPoolKeyHash(): Ed25519KeyHashHex | undefined;
    equals(other: Voter): boolean;
}
//# sourceMappingURL=Voter.d.ts.map