/// <reference types="pouchdb-core" />
/// <reference types="node" />
import { Address, AddressProps, Credential } from './Address';
import { OpaqueNumber } from '@cardano-sdk/util';
import { NetworkId } from '../ChainId';
import { Slot } from '../types';
export declare type TxIndex = OpaqueNumber<'TxIndex'>;
export declare const TxIndex: (value: number) => TxIndex;
export declare type CertIndex = OpaqueNumber<'CertIndex'>;
export declare const CertIndex: (value: number) => CertIndex;
export declare type Pointer = {
    slot: Slot;
    txIndex: TxIndex;
    certIndex: CertIndex;
};
export declare class PointerAddress {
    #private;
    private constructor();
    static fromCredentials(networkId: NetworkId, payment: Credential, pointer: Pointer): PointerAddress;
    getPaymentCredential(): Credential;
    getStakePointer(): Pointer;
    toAddress(): Address;
    static fromAddress(addr: Address): PointerAddress | undefined;
    static packParts(props: AddressProps): Buffer;
    static unpackParts(type: number, data: Uint8Array): Address;
}
//# sourceMappingURL=PointerAddress.d.ts.map