import { HexBlob } from '@cardano-sdk/util';
import { NativeScript } from './NativeScript.js';
import type * as Cardano from '../../../Cardano/index.js';
export declare class ScriptAny {
    #private;
    constructor(nativeScripts: Array<NativeScript>);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): ScriptAny;
    toCore(): Cardano.RequireAnyOfScript;
    static fromCore(script: Cardano.RequireAnyOfScript): ScriptAny;
    nativeScripts(): Array<NativeScript>;
    setNativeScripts(nativeScripts: Array<NativeScript>): void;
}
//# sourceMappingURL=ScriptAny.d.ts.map