import { Cardano } from '../..';
export interface TokenMetadataSizedIcon {
    size: number;
    icon: string;
}
export interface TokenMetadata {
    assetId: Cardano.AssetId;
    name?: string;
    ticker?: string;
    icon?: string;
    sizedIcons?: TokenMetadataSizedIcon[];
    url?: string;
    desc?: string;
    decimals?: number;
    ref?: string;
    version?: '1.0';
}
//# sourceMappingURL=TokenMetadata.d.ts.map