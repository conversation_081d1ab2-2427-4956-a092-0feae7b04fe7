"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@foxglove";
exports.ids = ["vendor-chunks/@foxglove"];
exports.modules = {

/***/ "(ssr)/./node_modules/@foxglove/crc/dist/esm/src/index.js":
/*!**********************************************************!*\
  !*** ./node_modules/@foxglove/crc/dist/esm/src/index.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   crc32: () => (/* binding */ crc32),\n/* harmony export */   crc32Final: () => (/* binding */ crc32Final),\n/* harmony export */   crc32GenerateTables: () => (/* binding */ crc32GenerateTables),\n/* harmony export */   crc32Init: () => (/* binding */ crc32Init),\n/* harmony export */   crc32Update: () => (/* binding */ crc32Update)\n/* harmony export */ });\n/**\n * Compute CRC32 lookup tables as described at:\n * https://github.com/komrad36/CRC#option-6-1-byte-tabular\n *\n * An iteration of CRC computation can be performed on 8 bits of input at once. By pre-computing a\n * table of the values of CRC(?) for all 2^8 = 256 possible byte values, during the final\n * computation we can replace a loop over 8 bits with a single lookup in the table.\n *\n * For further speedup, we can also pre-compute the values of CRC(?0) for all possible bytes when a\n * zero byte is appended. Then we can process two bytes of input at once by computing CRC(AB) =\n * CRC(A0) ^ CRC(B), using one lookup in the CRC(?0) table and one lookup in the CRC(?) table.\n *\n * The same technique applies for any number of bytes to be processed at once, although the speed\n * improvements diminish.\n *\n * @param polynomial The binary representation of the polynomial to use (reversed, i.e. most\n * significant bit represents x^0).\n * @param numTables The number of bytes of input that will be processed at once.\n */\nfunction crc32GenerateTables({ polynomial, numTables, }) {\n    const table = new Uint32Array(256 * numTables);\n    for (let i = 0; i < 256; i++) {\n        let r = i;\n        r = ((r & 1) * polynomial) ^ (r >>> 1);\n        r = ((r & 1) * polynomial) ^ (r >>> 1);\n        r = ((r & 1) * polynomial) ^ (r >>> 1);\n        r = ((r & 1) * polynomial) ^ (r >>> 1);\n        r = ((r & 1) * polynomial) ^ (r >>> 1);\n        r = ((r & 1) * polynomial) ^ (r >>> 1);\n        r = ((r & 1) * polynomial) ^ (r >>> 1);\n        r = ((r & 1) * polynomial) ^ (r >>> 1);\n        table[i] = r;\n    }\n    for (let i = 256; i < table.length; i++) {\n        const value = table[i - 256];\n        table[i] = table[value & 0xff] ^ (value >>> 8);\n    }\n    return table;\n}\nconst CRC32_TABLE = crc32GenerateTables({ polynomial: 0xedb88320, numTables: 8 });\n/**\n * Initialize a CRC32 to all 1 bits.\n */\nfunction crc32Init() {\n    return ~0;\n}\n/**\n * Update a streaming CRC32 calculation.\n *\n * For performance, this implementation processes the data 8 bytes at a time, using the algorithm\n * presented at: https://github.com/komrad36/CRC#option-9-8-byte-tabular\n */\nfunction crc32Update(prev, data) {\n    const byteLength = data.byteLength;\n    const view = new DataView(data.buffer, data.byteOffset, byteLength);\n    let r = prev;\n    let offset = 0;\n    // Process bytes one by one until we reach 4-byte alignment, which will speed up uint32 access.\n    const toAlign = -view.byteOffset & 3;\n    for (; offset < toAlign && offset < byteLength; offset++) {\n        r = CRC32_TABLE[(r ^ view.getUint8(offset)) & 0xff] ^ (r >>> 8);\n    }\n    if (offset === byteLength) {\n        return r;\n    }\n    offset = toAlign;\n    // Process 8 bytes (2 uint32s) at a time.\n    let remainingBytes = byteLength - offset;\n    for (; remainingBytes >= 8; offset += 8, remainingBytes -= 8) {\n        r ^= view.getUint32(offset, true);\n        const r2 = view.getUint32(offset + 4, true);\n        r =\n            CRC32_TABLE[0 * 256 + ((r2 >>> 24) & 0xff)] ^\n                CRC32_TABLE[1 * 256 + ((r2 >>> 16) & 0xff)] ^\n                CRC32_TABLE[2 * 256 + ((r2 >>> 8) & 0xff)] ^\n                CRC32_TABLE[3 * 256 + ((r2 >>> 0) & 0xff)] ^\n                CRC32_TABLE[4 * 256 + ((r >>> 24) & 0xff)] ^\n                CRC32_TABLE[5 * 256 + ((r >>> 16) & 0xff)] ^\n                CRC32_TABLE[6 * 256 + ((r >>> 8) & 0xff)] ^\n                CRC32_TABLE[7 * 256 + ((r >>> 0) & 0xff)];\n    }\n    // Process any remaining bytes one by one. (Perf note: inexplicably, using a temporary variable\n    // `i` rather than reusing `offset` here is faster in V8.)\n    for (let i = offset; i < byteLength; i++) {\n        r = CRC32_TABLE[(r ^ view.getUint8(i)) & 0xff] ^ (r >>> 8);\n    }\n    return r;\n}\n/**\n * Finalize a CRC32 by inverting the output value. An unsigned right-shift of 0 is used to ensure the result is a positive number.\n */\nfunction crc32Final(prev) {\n    return (prev ^ ~0) >>> 0;\n}\n/**\n * Calculate a one-shot CRC32. If the data is being accumulated incrementally, use the functions\n * `crc32Init`, `crc32Update`, and `crc32Final` instead.\n */\nfunction crc32(data) {\n    return crc32Final(crc32Update(crc32Init(), data));\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@foxglove/crc/dist/esm/src/index.js\n");

/***/ })

};
;