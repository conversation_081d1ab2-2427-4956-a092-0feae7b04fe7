import * as Cardano from '../../Cardano';
import { HexBlob } from '@cardano-sdk/util';
export declare class Registration {
    #private;
    constructor(credential: Cardano.Credential, deposit: Cardano.Lovelace);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): Registration;
    toCore(): Cardano.NewStakeAddressCertificate;
    static fromCore(cert: Cardano.NewStakeAddressCertificate): Registration;
    stakeCredential(): Cardano.Credential;
    setStakeCredential(credential: Cardano.Credential): void;
    deposit(): Cardano.Lovelace;
    setDeposit(deposit: Cardano.Lovelace): void;
}
//# sourceMappingURL=Registration.d.ts.map