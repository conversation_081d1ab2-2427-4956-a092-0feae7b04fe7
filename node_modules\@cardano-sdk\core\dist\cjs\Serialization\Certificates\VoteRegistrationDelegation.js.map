{"version": 3, "file": "VoteRegistrationDelegation.js", "sourceRoot": "", "sources": ["../../../../src/Serialization/Certificates/VoteRegistrationDelegation.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,4DAA8C;AAC9C,kCAAiD;AACjD,uDAAoD;AACpD,2CAAgD;AAChD,iCAA8B;AAC9B,4CAAkE;AAClE,0CAA6C;AAE7C,MAAM,mBAAmB,GAAG,CAAC,CAAC;AAG9B,MAAa,0BAA0B;IAcrC,YAAY,eAAmC,EAAE,OAAyB,EAAE,IAAU;QAbtF,yDAAgC;QAChC,mDAAY;QACZ,sDAA2B;QAC3B,oDAAsC,SAAS,EAAC;QAW9C,uBAAA,IAAI,0CAAe,eAAe,MAAA,CAAC;QACnC,uBAAA,IAAI,uCAAY,OAAO,MAAA,CAAC;QACxB,uBAAA,IAAI,oCAAS,IAAI,MAAA,CAAC;IACpB,CAAC;IAOD,MAAM;QACJ,MAAM,MAAM,GAAG,IAAI,iBAAU,EAAE,CAAC;QAEhC,IAAI,uBAAA,IAAI,iDAAe;YAAE,OAAO,uBAAA,IAAI,iDAAe,CAAC;QAIpD,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAE1B,MAAM,CAAC,QAAQ,CAAC,iCAAe,CAAC,0BAA0B,CAAC,CAAC;QAO5D,MAAM,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC;QAC5C,MAAM,CAAC,QAAQ,CAAC,uBAAA,IAAI,8CAAY,CAAC,IAAI,CAAC,CAAC;QACvC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAA,IAAI,8CAAY,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;QAElE,MAAM,CAAC,iBAAiB,CAAC,IAAA,iBAAU,EAAC,uBAAA,IAAI,wCAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAC1D,MAAM,CAAC,QAAQ,CAAC,uBAAA,IAAI,2CAAS,CAAC,CAAC;QAE/B,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC;IAC9B,CAAC;IAQD,MAAM,CAAC,QAAQ,CAAC,IAAa;QAC3B,MAAM,MAAM,GAAG,IAAI,iBAAU,CAAC,IAAI,CAAC,CAAC;QAEpC,MAAM,MAAM,GAAG,MAAM,CAAC,cAAc,EAAE,CAAC;QAEvC,IAAI,MAAM,KAAK,CAAC;YACd,MAAM,IAAI,2BAAoB,CAAC,MAAM,EAAE,wDAAwD,MAAM,WAAW,CAAC,CAAC;QAEpH,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;QAEtC,IAAI,IAAI,KAAK,iCAAe,CAAC,0BAA0B;YACrD,MAAM,IAAI,2BAAoB,CAC5B,MAAM,EACN,6BAA6B,iCAAe,CAAC,0BAA0B,aAAa,IAAI,EAAE,CAC3F,CAAC;QAEJ,MAAM,UAAU,GAAG,MAAM,CAAC,cAAc,EAAE,CAAC;QAE3C,IAAI,UAAU,KAAK,mBAAmB;YACpC,MAAM,IAAI,2BAAoB,CAC5B,MAAM,EACN,wBAAwB,mBAAmB,kCAAkC,MAAM,WAAW,CAC/F,CAAC;QAEJ,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,CAA2B,CAAC;QAChE,MAAM,IAAI,GAAG,MAAM,CAAC,gBAAgB,CAAC,cAAO,CAAC,SAAS,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;QAEjF,MAAM,CAAC,YAAY,EAAE,CAAC;QAEtB,MAAM,IAAI,GAAG,WAAI,CAAC,QAAQ,CAAC,cAAO,CAAC,SAAS,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;QACzE,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;QAEjC,MAAM,CAAC,YAAY,EAAE,CAAC;QAEtB,MAAM,IAAI,GAAG,IAAI,0BAA0B,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QAC3E,uBAAA,IAAI,6CAAkB,IAAI,MAAA,CAAC;QAE3B,OAAO,IAAI,CAAC;IACd,CAAC;IAOD,MAAM;QACJ,OAAO;YACL,UAAU,EAAE,yBAAe,CAAC,0BAA0B;YACtD,IAAI,EAAE,uBAAA,IAAI,wCAAM,CAAC,MAAM,EAAE;YACzB,OAAO,EAAE,uBAAA,IAAI,2CAAS;YACtB,eAAe,EAAE,uBAAA,IAAI,8CAAY;SAClC,CAAC;IACJ,CAAC;IAOD,MAAM,CAAC,QAAQ,CAAC,KAAoD;QAClE,OAAO,IAAI,0BAA0B,CAAC,KAAK,CAAC,eAAe,EAAE,KAAK,CAAC,OAAO,EAAE,WAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;IACzG,CAAC;IAOD,eAAe;QACb,OAAO,uBAAA,IAAI,8CAAY,CAAC;IAC1B,CAAC;IAOD,OAAO;QACL,OAAO,uBAAA,IAAI,2CAAS,CAAC;IACvB,CAAC;IAOD,IAAI;QACF,OAAO,uBAAA,IAAI,wCAAM,CAAC;IACpB,CAAC;CACF;AAlJD,gEAkJC"}