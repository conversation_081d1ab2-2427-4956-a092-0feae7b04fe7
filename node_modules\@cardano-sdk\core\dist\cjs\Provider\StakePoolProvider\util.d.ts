export declare const PoolDataSortFields: readonly ["cost", "name", "margin", "pledge", "ticker"];
export declare const PoolMetricsSortFields: readonly ["blocks", "liveStake", "saturation"];
export declare const PoolAPYSortFields: readonly ["apy"];
export declare const PoolROSSortFields: readonly ["ros", "lastRos"];
export declare const isPoolDataSortField: (value: string) => boolean;
export declare const isPoolMetricsSortField: (value: string) => boolean;
export declare const isPoolAPYSortField: (value: string) => boolean;
export declare const isPoolROSSortField: (value: string) => boolean;
export declare const SortFields: ("name" | "ticker" | "pledge" | "cost" | "margin" | "blocks" | "liveStake" | "saturation" | "apy" | "ros" | "lastRos")[];
//# sourceMappingURL=util.d.ts.map