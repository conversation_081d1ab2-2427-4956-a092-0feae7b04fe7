import * as Cardano from '../../../Cardano';
import * as Crypto from '@cardano-sdk/crypto';
import { <PERSON><PERSON><PERSON>eader, CborWriter } from '../../CBOR';
import { CborSet, Hash } from '../../Common';
import { HexBlob } from '@cardano-sdk/util';
import { PoolMetadata } from './PoolMetadata';
import { Relay } from './Relay';
import { UnitInterval } from '../../Common/UnitInterval';
declare type PoolOwners = CborSet<Crypto.Ed25519KeyHashHex, Hash<Crypto.Ed25519KeyHashHex>>;
export declare class PoolParams {
    #private;
    static readonly subgroupCount = 9;
    constructor(operator: Crypto.Ed25519KeyHashHex, vrfKeyHash: Cardano.VrfVkHex, pledge: Cardano.Lovelace, cost: Cardano.Lovelace, margin: UnitInterval, rewardAccount: Cardano.RewardAddress, poolOwners: PoolOwners, relays: Array<Relay>, poolMetadata?: PoolMetadata);
    toCbor(): HexBlob;
    toFlattenedCbor(writer: CborWriter): HexBlob;
    static fromCbor(cbor: HexBlob): PoolParams;
    static fromFlattenedCbor(reader: CborReader): PoolParams;
    toCore(): Cardano.PoolParameters;
    static fromCore(params: Cardano.PoolParameters): PoolParams;
    operator(): Crypto.Ed25519KeyHashHex;
    setOperator(operator: Crypto.Ed25519KeyHashHex): void;
    vrfKeyHash(): Cardano.VrfVkHex;
    setVrfKeyHash(vrfKeyHash: Cardano.VrfVkHex): void;
    pledge(): Cardano.Lovelace;
    setPledge(pledge: Cardano.Lovelace): void;
    cost(): Cardano.Lovelace;
    setCost(cost: Cardano.Lovelace): void;
    margin(): UnitInterval;
    setMargin(margin: UnitInterval): void;
    rewardAccount(): Cardano.RewardAddress;
    setRewardAccount(rewardAccount: Cardano.RewardAddress): void;
    poolOwners(): PoolOwners;
    setPoolOwners(poolOwners: PoolOwners): void;
    relays(): Array<Relay>;
    setRelays(relays: Array<Relay>): void;
    poolMetadata(): PoolMetadata | undefined;
    setPoolMetadata(poolMetadata: PoolMetadata): void;
}
export {};
//# sourceMappingURL=PoolParams.d.ts.map