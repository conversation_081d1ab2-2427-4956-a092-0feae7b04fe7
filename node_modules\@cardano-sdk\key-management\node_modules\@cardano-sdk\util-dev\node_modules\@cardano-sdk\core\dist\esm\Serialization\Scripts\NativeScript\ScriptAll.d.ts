import { HexBlob } from '@cardano-sdk/util';
import { NativeScript } from './NativeScript.js';
import type * as Cardano from '../../../Cardano/index.js';
export declare class ScriptAll {
    #private;
    constructor(nativeScripts: Array<NativeScript>);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): ScriptAll;
    toCore(): Cardano.RequireAllOfScript;
    static fromCore(script: Cardano.RequireAllOfScript): ScriptAll;
    nativeScripts(): Array<NativeScript>;
    setNativeScripts(nativeScripts: Array<NativeScript>): void;
}
//# sourceMappingURL=ScriptAll.d.ts.map