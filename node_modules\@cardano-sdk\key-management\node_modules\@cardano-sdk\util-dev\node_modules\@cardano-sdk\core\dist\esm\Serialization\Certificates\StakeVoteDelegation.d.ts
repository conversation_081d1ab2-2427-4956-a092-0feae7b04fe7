import * as Crypto from '@cardano-sdk/crypto';
import { DRep } from './DRep/index.js';
import { HexBlob } from '@cardano-sdk/util';
import type * as Cardano from '../../Cardano/index.js';
export declare class StakeVoteDelegation {
    #private;
    constructor(stakeCredential: Cardano.Credential, drep: DRep, poolKeyHash: Crypto.Ed25519KeyHashHex);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): StakeVoteDelegation;
    toCore(): Cardano.StakeVoteDelegationCertificate;
    static fromCore(deleg: Cardano.StakeVoteDelegationCertificate): StakeVoteDelegation;
    stakeCredential(): Cardano.Credential;
    drep(): DRep;
    poolKeyHash(): Crypto.Ed25519KeyHashHex;
}
//# sourceMappingURL=StakeVoteDelegation.d.ts.map