"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@scure";
exports.ids = ["vendor-chunks/@scure"];
exports.modules = {

/***/ "(ssr)/./node_modules/@scure/base/lib/esm/index.js":
/*!***************************************************!*\
  !*** ./node_modules/@scure/base/lib/esm/index.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   base16: () => (/* binding */ base16),\n/* harmony export */   base32: () => (/* binding */ base32),\n/* harmony export */   base32crockford: () => (/* binding */ base32crockford),\n/* harmony export */   base32hex: () => (/* binding */ base32hex),\n/* harmony export */   base32hexnopad: () => (/* binding */ base32hexnopad),\n/* harmony export */   base32nopad: () => (/* binding */ base32nopad),\n/* harmony export */   base58: () => (/* binding */ base58),\n/* harmony export */   base58check: () => (/* binding */ base58check),\n/* harmony export */   base58flickr: () => (/* binding */ base58flickr),\n/* harmony export */   base58xmr: () => (/* binding */ base58xmr),\n/* harmony export */   base58xrp: () => (/* binding */ base58xrp),\n/* harmony export */   base64: () => (/* binding */ base64),\n/* harmony export */   base64nopad: () => (/* binding */ base64nopad),\n/* harmony export */   base64url: () => (/* binding */ base64url),\n/* harmony export */   base64urlnopad: () => (/* binding */ base64urlnopad),\n/* harmony export */   bech32: () => (/* binding */ bech32),\n/* harmony export */   bech32m: () => (/* binding */ bech32m),\n/* harmony export */   bytes: () => (/* binding */ bytes),\n/* harmony export */   bytesToString: () => (/* binding */ bytesToString),\n/* harmony export */   createBase58check: () => (/* binding */ createBase58check),\n/* harmony export */   hex: () => (/* binding */ hex),\n/* harmony export */   str: () => (/* binding */ str),\n/* harmony export */   stringToBytes: () => (/* binding */ stringToBytes),\n/* harmony export */   utf8: () => (/* binding */ utf8),\n/* harmony export */   utils: () => (/* binding */ utils)\n/* harmony export */ });\n/*! scure-base - MIT License (c) 2022 Paul Miller (paulmillr.com) */\nfunction isBytes(a) {\n    return a instanceof Uint8Array || (ArrayBuffer.isView(a) && a.constructor.name === 'Uint8Array');\n}\n/** Asserts something is Uint8Array. */\nfunction abytes(b, ...lengths) {\n    if (!isBytes(b))\n        throw new Error('Uint8Array expected');\n    if (lengths.length > 0 && !lengths.includes(b.length))\n        throw new Error('Uint8Array expected of length ' + lengths + ', got length=' + b.length);\n}\nfunction isArrayOf(isString, arr) {\n    if (!Array.isArray(arr))\n        return false;\n    if (arr.length === 0)\n        return true;\n    if (isString) {\n        return arr.every((item) => typeof item === 'string');\n    }\n    else {\n        return arr.every((item) => Number.isSafeInteger(item));\n    }\n}\n// no abytes: seems to have 10% slowdown. Why?!\nfunction afn(input) {\n    if (typeof input !== 'function')\n        throw new Error('function expected');\n    return true;\n}\nfunction astr(label, input) {\n    if (typeof input !== 'string')\n        throw new Error(`${label}: string expected`);\n    return true;\n}\nfunction anumber(n) {\n    if (!Number.isSafeInteger(n))\n        throw new Error(`invalid integer: ${n}`);\n}\nfunction aArr(input) {\n    if (!Array.isArray(input))\n        throw new Error('array expected');\n}\nfunction astrArr(label, input) {\n    if (!isArrayOf(true, input))\n        throw new Error(`${label}: array of strings expected`);\n}\nfunction anumArr(label, input) {\n    if (!isArrayOf(false, input))\n        throw new Error(`${label}: array of numbers expected`);\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction chain(...args) {\n    const id = (a) => a;\n    // Wrap call in closure so JIT can inline calls\n    const wrap = (a, b) => (c) => a(b(c));\n    // Construct chain of args[-1].encode(args[-2].encode([...]))\n    const encode = args.map((x) => x.encode).reduceRight(wrap, id);\n    // Construct chain of args[0].decode(args[1].decode(...))\n    const decode = args.map((x) => x.decode).reduce(wrap, id);\n    return { encode, decode };\n}\n/**\n * Encodes integer radix representation to array of strings using alphabet and back.\n * Could also be array of strings.\n * @__NO_SIDE_EFFECTS__\n */\nfunction alphabet(letters) {\n    // mapping 1 to \"b\"\n    const lettersA = typeof letters === 'string' ? letters.split('') : letters;\n    const len = lettersA.length;\n    astrArr('alphabet', lettersA);\n    // mapping \"b\" to 1\n    const indexes = new Map(lettersA.map((l, i) => [l, i]));\n    return {\n        encode: (digits) => {\n            aArr(digits);\n            return digits.map((i) => {\n                if (!Number.isSafeInteger(i) || i < 0 || i >= len)\n                    throw new Error(`alphabet.encode: digit index outside alphabet \"${i}\". Allowed: ${letters}`);\n                return lettersA[i];\n            });\n        },\n        decode: (input) => {\n            aArr(input);\n            return input.map((letter) => {\n                astr('alphabet.decode', letter);\n                const i = indexes.get(letter);\n                if (i === undefined)\n                    throw new Error(`Unknown letter: \"${letter}\". Allowed: ${letters}`);\n                return i;\n            });\n        },\n    };\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction join(separator = '') {\n    astr('join', separator);\n    return {\n        encode: (from) => {\n            astrArr('join.decode', from);\n            return from.join(separator);\n        },\n        decode: (to) => {\n            astr('join.decode', to);\n            return to.split(separator);\n        },\n    };\n}\n/**\n * Pad strings array so it has integer number of bits\n * @__NO_SIDE_EFFECTS__\n */\nfunction padding(bits, chr = '=') {\n    anumber(bits);\n    astr('padding', chr);\n    return {\n        encode(data) {\n            astrArr('padding.encode', data);\n            while ((data.length * bits) % 8)\n                data.push(chr);\n            return data;\n        },\n        decode(input) {\n            astrArr('padding.decode', input);\n            let end = input.length;\n            if ((end * bits) % 8)\n                throw new Error('padding: invalid, string should have whole number of bytes');\n            for (; end > 0 && input[end - 1] === chr; end--) {\n                const last = end - 1;\n                const byte = last * bits;\n                if (byte % 8 === 0)\n                    throw new Error('padding: invalid, string has too much padding');\n            }\n            return input.slice(0, end);\n        },\n    };\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction normalize(fn) {\n    afn(fn);\n    return { encode: (from) => from, decode: (to) => fn(to) };\n}\n/**\n * Slow: O(n^2) time complexity\n */\nfunction convertRadix(data, from, to) {\n    // base 1 is impossible\n    if (from < 2)\n        throw new Error(`convertRadix: invalid from=${from}, base cannot be less than 2`);\n    if (to < 2)\n        throw new Error(`convertRadix: invalid to=${to}, base cannot be less than 2`);\n    aArr(data);\n    if (!data.length)\n        return [];\n    let pos = 0;\n    const res = [];\n    const digits = Array.from(data, (d) => {\n        anumber(d);\n        if (d < 0 || d >= from)\n            throw new Error(`invalid integer: ${d}`);\n        return d;\n    });\n    const dlen = digits.length;\n    while (true) {\n        let carry = 0;\n        let done = true;\n        for (let i = pos; i < dlen; i++) {\n            const digit = digits[i];\n            const fromCarry = from * carry;\n            const digitBase = fromCarry + digit;\n            if (!Number.isSafeInteger(digitBase) ||\n                fromCarry / from !== carry ||\n                digitBase - digit !== fromCarry) {\n                throw new Error('convertRadix: carry overflow');\n            }\n            const div = digitBase / to;\n            carry = digitBase % to;\n            const rounded = Math.floor(div);\n            digits[i] = rounded;\n            if (!Number.isSafeInteger(rounded) || rounded * to + carry !== digitBase)\n                throw new Error('convertRadix: carry overflow');\n            if (!done)\n                continue;\n            else if (!rounded)\n                pos = i;\n            else\n                done = false;\n        }\n        res.push(carry);\n        if (done)\n            break;\n    }\n    for (let i = 0; i < data.length - 1 && data[i] === 0; i++)\n        res.push(0);\n    return res.reverse();\n}\nconst gcd = (a, b) => (b === 0 ? a : gcd(b, a % b));\nconst radix2carry = /* @__NO_SIDE_EFFECTS__ */ (from, to) => from + (to - gcd(from, to));\nconst powers = /* @__PURE__ */ (() => {\n    let res = [];\n    for (let i = 0; i < 40; i++)\n        res.push(2 ** i);\n    return res;\n})();\n/**\n * Implemented with numbers, because BigInt is 5x slower\n */\nfunction convertRadix2(data, from, to, padding) {\n    aArr(data);\n    if (from <= 0 || from > 32)\n        throw new Error(`convertRadix2: wrong from=${from}`);\n    if (to <= 0 || to > 32)\n        throw new Error(`convertRadix2: wrong to=${to}`);\n    if (radix2carry(from, to) > 32) {\n        throw new Error(`convertRadix2: carry overflow from=${from} to=${to} carryBits=${radix2carry(from, to)}`);\n    }\n    let carry = 0;\n    let pos = 0; // bitwise position in current element\n    const max = powers[from];\n    const mask = powers[to] - 1;\n    const res = [];\n    for (const n of data) {\n        anumber(n);\n        if (n >= max)\n            throw new Error(`convertRadix2: invalid data word=${n} from=${from}`);\n        carry = (carry << from) | n;\n        if (pos + from > 32)\n            throw new Error(`convertRadix2: carry overflow pos=${pos} from=${from}`);\n        pos += from;\n        for (; pos >= to; pos -= to)\n            res.push(((carry >> (pos - to)) & mask) >>> 0);\n        const pow = powers[pos];\n        if (pow === undefined)\n            throw new Error('invalid carry');\n        carry &= pow - 1; // clean carry, otherwise it will cause overflow\n    }\n    carry = (carry << (to - pos)) & mask;\n    if (!padding && pos >= from)\n        throw new Error('Excess padding');\n    if (!padding && carry > 0)\n        throw new Error(`Non-zero padding: ${carry}`);\n    if (padding && pos > 0)\n        res.push(carry >>> 0);\n    return res;\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction radix(num) {\n    anumber(num);\n    const _256 = 2 ** 8;\n    return {\n        encode: (bytes) => {\n            if (!isBytes(bytes))\n                throw new Error('radix.encode input should be Uint8Array');\n            return convertRadix(Array.from(bytes), _256, num);\n        },\n        decode: (digits) => {\n            anumArr('radix.decode', digits);\n            return Uint8Array.from(convertRadix(digits, num, _256));\n        },\n    };\n}\n/**\n * If both bases are power of same number (like `2**8 <-> 2**64`),\n * there is a linear algorithm. For now we have implementation for power-of-two bases only.\n * @__NO_SIDE_EFFECTS__\n */\nfunction radix2(bits, revPadding = false) {\n    anumber(bits);\n    if (bits <= 0 || bits > 32)\n        throw new Error('radix2: bits should be in (0..32]');\n    if (radix2carry(8, bits) > 32 || radix2carry(bits, 8) > 32)\n        throw new Error('radix2: carry overflow');\n    return {\n        encode: (bytes) => {\n            if (!isBytes(bytes))\n                throw new Error('radix2.encode input should be Uint8Array');\n            return convertRadix2(Array.from(bytes), 8, bits, !revPadding);\n        },\n        decode: (digits) => {\n            anumArr('radix2.decode', digits);\n            return Uint8Array.from(convertRadix2(digits, bits, 8, revPadding));\n        },\n    };\n}\nfunction unsafeWrapper(fn) {\n    afn(fn);\n    return function (...args) {\n        try {\n            return fn.apply(null, args);\n        }\n        catch (e) { }\n    };\n}\nfunction checksum(len, fn) {\n    anumber(len);\n    afn(fn);\n    return {\n        encode(data) {\n            if (!isBytes(data))\n                throw new Error('checksum.encode: input should be Uint8Array');\n            const sum = fn(data).slice(0, len);\n            const res = new Uint8Array(data.length + len);\n            res.set(data);\n            res.set(sum, data.length);\n            return res;\n        },\n        decode(data) {\n            if (!isBytes(data))\n                throw new Error('checksum.decode: input should be Uint8Array');\n            const payload = data.slice(0, -len);\n            const oldChecksum = data.slice(-len);\n            const newChecksum = fn(payload).slice(0, len);\n            for (let i = 0; i < len; i++)\n                if (newChecksum[i] !== oldChecksum[i])\n                    throw new Error('Invalid checksum');\n            return payload;\n        },\n    };\n}\n// prettier-ignore\nconst utils = {\n    alphabet, chain, checksum, convertRadix, convertRadix2, radix, radix2, join, padding,\n};\n// RFC 4648 aka RFC 3548\n// ---------------------\n/**\n * base16 encoding from RFC 4648.\n * @example\n * ```js\n * base16.encode(Uint8Array.from([0x12, 0xab]));\n * // => '12AB'\n * ```\n */\nconst base16 = chain(radix2(4), alphabet('0123456789ABCDEF'), join(''));\n/**\n * base32 encoding from RFC 4648. Has padding.\n * Use `base32nopad` for unpadded version.\n * Also check out `base32hex`, `base32hexnopad`, `base32crockford`.\n * @example\n * ```js\n * base32.encode(Uint8Array.from([0x12, 0xab]));\n * // => 'CKVQ===='\n * base32.decode('CKVQ====');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\nconst base32 = chain(radix2(5), alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZ234567'), padding(5), join(''));\n/**\n * base32 encoding from RFC 4648. No padding.\n * Use `base32` for padded version.\n * Also check out `base32hex`, `base32hexnopad`, `base32crockford`.\n * @example\n * ```js\n * base32nopad.encode(Uint8Array.from([0x12, 0xab]));\n * // => 'CKVQ'\n * base32nopad.decode('CKVQ');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\nconst base32nopad = chain(radix2(5), alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZ234567'), join(''));\n/**\n * base32 encoding from RFC 4648. Padded. Compared to ordinary `base32`, slightly different alphabet.\n * Use `base32hexnopad` for unpadded version.\n * @example\n * ```js\n * base32hex.encode(Uint8Array.from([0x12, 0xab]));\n * // => '2ALG===='\n * base32hex.decode('2ALG====');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\nconst base32hex = chain(radix2(5), alphabet('0123456789ABCDEFGHIJKLMNOPQRSTUV'), padding(5), join(''));\n/**\n * base32 encoding from RFC 4648. No padding. Compared to ordinary `base32`, slightly different alphabet.\n * Use `base32hex` for padded version.\n * @example\n * ```js\n * base32hexnopad.encode(Uint8Array.from([0x12, 0xab]));\n * // => '2ALG'\n * base32hexnopad.decode('2ALG');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\nconst base32hexnopad = chain(radix2(5), alphabet('0123456789ABCDEFGHIJKLMNOPQRSTUV'), join(''));\n/**\n * base32 encoding from RFC 4648. Doug Crockford's version.\n * https://www.crockford.com/base32.html\n * @example\n * ```js\n * base32crockford.encode(Uint8Array.from([0x12, 0xab]));\n * // => '2ANG'\n * base32crockford.decode('2ANG');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\nconst base32crockford = chain(radix2(5), alphabet('0123456789ABCDEFGHJKMNPQRSTVWXYZ'), join(''), normalize((s) => s.toUpperCase().replace(/O/g, '0').replace(/[IL]/g, '1')));\n// Built-in base64 conversion https://caniuse.com/mdn-javascript_builtins_uint8array_frombase64\n// prettier-ignore\nconst hasBase64Builtin = /* @__PURE__ */ (() => typeof Uint8Array.from([]).toBase64 === 'function' &&\n    typeof Uint8Array.fromBase64 === 'function')();\nconst decodeBase64Builtin = (s, isUrl) => {\n    astr('base64', s);\n    const re = isUrl ? /^[A-Za-z0-9=_-]+$/ : /^[A-Za-z0-9=+/]+$/;\n    const alphabet = isUrl ? 'base64url' : 'base64';\n    if (s.length > 0 && !re.test(s))\n        throw new Error('invalid base64');\n    return Uint8Array.fromBase64(s, { alphabet, lastChunkHandling: 'strict' });\n};\n/**\n * base64 from RFC 4648. Padded.\n * Use `base64nopad` for unpadded version.\n * Also check out `base64url`, `base64urlnopad`.\n * Falls back to built-in function, when available.\n * @example\n * ```js\n * base64.encode(Uint8Array.from([0x12, 0xab]));\n * // => 'Eqs='\n * base64.decode('Eqs=');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\n// prettier-ignore\nconst base64 = hasBase64Builtin ? {\n    encode(b) { abytes(b); return b.toBase64(); },\n    decode(s) { return decodeBase64Builtin(s, false); },\n} : chain(radix2(6), alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'), padding(6), join(''));\n/**\n * base64 from RFC 4648. No padding.\n * Use `base64` for padded version.\n * @example\n * ```js\n * base64nopad.encode(Uint8Array.from([0x12, 0xab]));\n * // => 'Eqs'\n * base64nopad.decode('Eqs');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\nconst base64nopad = chain(radix2(6), alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'), join(''));\n/**\n * base64 from RFC 4648, using URL-safe alphabet. Padded.\n * Use `base64urlnopad` for unpadded version.\n * Falls back to built-in function, when available.\n * @example\n * ```js\n * base64url.encode(Uint8Array.from([0x12, 0xab]));\n * // => 'Eqs='\n * base64url.decode('Eqs=');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\n// prettier-ignore\nconst base64url = hasBase64Builtin ? {\n    encode(b) { abytes(b); return b.toBase64({ alphabet: 'base64url' }); },\n    decode(s) { return decodeBase64Builtin(s, true); },\n} : chain(radix2(6), alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_'), padding(6), join(''));\n/**\n * base64 from RFC 4648, using URL-safe alphabet. No padding.\n * Use `base64url` for padded version.\n * @example\n * ```js\n * base64urlnopad.encode(Uint8Array.from([0x12, 0xab]));\n * // => 'Eqs'\n * base64urlnopad.decode('Eqs');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\nconst base64urlnopad = chain(radix2(6), alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_'), join(''));\n// base58 code\n// -----------\nconst genBase58 = /* @__NO_SIDE_EFFECTS__ */ (abc) => chain(radix(58), alphabet(abc), join(''));\n/**\n * base58: base64 without ambigous characters +, /, 0, O, I, l.\n * Quadratic (O(n^2)) - so, can't be used on large inputs.\n * @example\n * ```js\n * base58.decode('01abcdef');\n * // => '3UhJW'\n * ```\n */\nconst base58 = genBase58('123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz');\n/**\n * base58: flickr version. Check out `base58`.\n */\nconst base58flickr = genBase58('123456789abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ');\n/**\n * base58: XRP version. Check out `base58`.\n */\nconst base58xrp = genBase58('rpshnaf39wBUDNEGHJKLM4PQRST7VWXYZ2bcdeCg65jkm8oFqi1tuvAxyz');\n// Data len (index) -> encoded block len\nconst XMR_BLOCK_LEN = [0, 2, 3, 5, 6, 7, 9, 10, 11];\n/**\n * base58: XMR version. Check out `base58`.\n * Done in 8-byte blocks (which equals 11 chars in decoding). Last (non-full) block padded with '1' to size in XMR_BLOCK_LEN.\n * Block encoding significantly reduces quadratic complexity of base58.\n */\nconst base58xmr = {\n    encode(data) {\n        let res = '';\n        for (let i = 0; i < data.length; i += 8) {\n            const block = data.subarray(i, i + 8);\n            res += base58.encode(block).padStart(XMR_BLOCK_LEN[block.length], '1');\n        }\n        return res;\n    },\n    decode(str) {\n        let res = [];\n        for (let i = 0; i < str.length; i += 11) {\n            const slice = str.slice(i, i + 11);\n            const blockLen = XMR_BLOCK_LEN.indexOf(slice.length);\n            const block = base58.decode(slice);\n            for (let j = 0; j < block.length - blockLen; j++) {\n                if (block[j] !== 0)\n                    throw new Error('base58xmr: wrong padding');\n            }\n            res = res.concat(Array.from(block.slice(block.length - blockLen)));\n        }\n        return Uint8Array.from(res);\n    },\n};\n/**\n * Method, which creates base58check encoder.\n * Requires function, calculating sha256.\n */\nconst createBase58check = (sha256) => chain(checksum(4, (data) => sha256(sha256(data))), base58);\n/**\n * Use `createBase58check` instead.\n * @deprecated\n */\nconst base58check = createBase58check;\nconst BECH_ALPHABET = chain(alphabet('qpzry9x8gf2tvdw0s3jn54khce6mua7l'), join(''));\nconst POLYMOD_GENERATORS = [0x3b6a57b2, 0x26508e6d, 0x1ea119fa, 0x3d4233dd, 0x2a1462b3];\nfunction bech32Polymod(pre) {\n    const b = pre >> 25;\n    let chk = (pre & 0x1ffffff) << 5;\n    for (let i = 0; i < POLYMOD_GENERATORS.length; i++) {\n        if (((b >> i) & 1) === 1)\n            chk ^= POLYMOD_GENERATORS[i];\n    }\n    return chk;\n}\nfunction bechChecksum(prefix, words, encodingConst = 1) {\n    const len = prefix.length;\n    let chk = 1;\n    for (let i = 0; i < len; i++) {\n        const c = prefix.charCodeAt(i);\n        if (c < 33 || c > 126)\n            throw new Error(`Invalid prefix (${prefix})`);\n        chk = bech32Polymod(chk) ^ (c >> 5);\n    }\n    chk = bech32Polymod(chk);\n    for (let i = 0; i < len; i++)\n        chk = bech32Polymod(chk) ^ (prefix.charCodeAt(i) & 0x1f);\n    for (let v of words)\n        chk = bech32Polymod(chk) ^ v;\n    for (let i = 0; i < 6; i++)\n        chk = bech32Polymod(chk);\n    chk ^= encodingConst;\n    return BECH_ALPHABET.encode(convertRadix2([chk % powers[30]], 30, 5, false));\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction genBech32(encoding) {\n    const ENCODING_CONST = encoding === 'bech32' ? 1 : 0x2bc830a3;\n    const _words = radix2(5);\n    const fromWords = _words.decode;\n    const toWords = _words.encode;\n    const fromWordsUnsafe = unsafeWrapper(fromWords);\n    function encode(prefix, words, limit = 90) {\n        astr('bech32.encode prefix', prefix);\n        if (isBytes(words))\n            words = Array.from(words);\n        anumArr('bech32.encode', words);\n        const plen = prefix.length;\n        if (plen === 0)\n            throw new TypeError(`Invalid prefix length ${plen}`);\n        const actualLength = plen + 7 + words.length;\n        if (limit !== false && actualLength > limit)\n            throw new TypeError(`Length ${actualLength} exceeds limit ${limit}`);\n        const lowered = prefix.toLowerCase();\n        const sum = bechChecksum(lowered, words, ENCODING_CONST);\n        return `${lowered}1${BECH_ALPHABET.encode(words)}${sum}`;\n    }\n    function decode(str, limit = 90) {\n        astr('bech32.decode input', str);\n        const slen = str.length;\n        if (slen < 8 || (limit !== false && slen > limit))\n            throw new TypeError(`invalid string length: ${slen} (${str}). Expected (8..${limit})`);\n        // don't allow mixed case\n        const lowered = str.toLowerCase();\n        if (str !== lowered && str !== str.toUpperCase())\n            throw new Error(`String must be lowercase or uppercase`);\n        const sepIndex = lowered.lastIndexOf('1');\n        if (sepIndex === 0 || sepIndex === -1)\n            throw new Error(`Letter \"1\" must be present between prefix and data only`);\n        const prefix = lowered.slice(0, sepIndex);\n        const data = lowered.slice(sepIndex + 1);\n        if (data.length < 6)\n            throw new Error('Data must be at least 6 characters long');\n        const words = BECH_ALPHABET.decode(data).slice(0, -6);\n        const sum = bechChecksum(prefix, words, ENCODING_CONST);\n        if (!data.endsWith(sum))\n            throw new Error(`Invalid checksum in ${str}: expected \"${sum}\"`);\n        return { prefix, words };\n    }\n    const decodeUnsafe = unsafeWrapper(decode);\n    function decodeToBytes(str) {\n        const { prefix, words } = decode(str, false);\n        return { prefix, words, bytes: fromWords(words) };\n    }\n    function encodeFromBytes(prefix, bytes) {\n        return encode(prefix, toWords(bytes));\n    }\n    return {\n        encode,\n        decode,\n        encodeFromBytes,\n        decodeToBytes,\n        decodeUnsafe,\n        fromWords,\n        fromWordsUnsafe,\n        toWords,\n    };\n}\n/**\n * bech32 from BIP 173. Operates on words.\n * For high-level, check out scure-btc-signer:\n * https://github.com/paulmillr/scure-btc-signer.\n */\nconst bech32 = genBech32('bech32');\n/**\n * bech32m from BIP 350. Operates on words.\n * It was to mitigate `bech32` weaknesses.\n * For high-level, check out scure-btc-signer:\n * https://github.com/paulmillr/scure-btc-signer.\n */\nconst bech32m = genBech32('bech32m');\n/**\n * UTF-8-to-byte decoder. Uses built-in TextDecoder / TextEncoder.\n * @example\n * ```js\n * const b = utf8.decode(\"hey\"); // => new Uint8Array([ 104, 101, 121 ])\n * const str = utf8.encode(b); // \"hey\"\n * ```\n */\nconst utf8 = {\n    encode: (data) => new TextDecoder().decode(data),\n    decode: (str) => new TextEncoder().encode(str),\n};\n// Built-in hex conversion https://caniuse.com/mdn-javascript_builtins_uint8array_fromhex\n// prettier-ignore\nconst hasHexBuiltin = /* @__PURE__ */ (() => typeof Uint8Array.from([]).toHex === 'function' &&\n    typeof Uint8Array.fromHex === 'function')();\n// prettier-ignore\nconst hexBuiltin = {\n    encode(data) { abytes(data); return data.toHex(); },\n    decode(s) { astr('hex', s); return Uint8Array.fromHex(s); },\n};\n/**\n * hex string decoder. Uses built-in function, when available.\n * @example\n * ```js\n * const b = hex.decode(\"0102ff\"); // => new Uint8Array([ 1, 2, 255 ])\n * const str = hex.encode(b); // \"0102ff\"\n * ```\n */\nconst hex = hasHexBuiltin\n    ? hexBuiltin\n    : chain(radix2(4), alphabet('0123456789abcdef'), join(''), normalize((s) => {\n        if (typeof s !== 'string' || s.length % 2 !== 0)\n            throw new TypeError(`hex.decode: expected string, got ${typeof s} with length ${s.length}`);\n        return s.toLowerCase();\n    }));\n// prettier-ignore\nconst CODERS = {\n    utf8, hex, base16, base32, base64, base64url, base58, base58xmr\n};\nconst coderTypeError = 'Invalid encoding type. Available types: utf8, hex, base16, base32, base64, base64url, base58, base58xmr';\n/** @deprecated */\nconst bytesToString = (type, bytes) => {\n    if (typeof type !== 'string' || !CODERS.hasOwnProperty(type))\n        throw new TypeError(coderTypeError);\n    if (!isBytes(bytes))\n        throw new TypeError('bytesToString() expects Uint8Array');\n    return CODERS[type].encode(bytes);\n};\n/** @deprecated */\nconst str = bytesToString; // as in python, but for bytes only\n/** @deprecated */\nconst stringToBytes = (type, str) => {\n    if (!CODERS.hasOwnProperty(type))\n        throw new TypeError(coderTypeError);\n    if (typeof str !== 'string')\n        throw new TypeError('stringToBytes() expects string');\n    return CODERS[type].decode(str);\n};\n/** @deprecated */\nconst bytes = stringToBytes;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@scure/base/lib/esm/index.js\n");

/***/ })

};
;