"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/providers/WalletProvider.tsx":
/*!*****************************************************!*\
  !*** ./src/components/providers/WalletProvider.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalletProvider: () => (/* binding */ WalletProvider),\n/* harmony export */   useWallet: () => (/* binding */ useWallet)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useWallet,WalletProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n// Dynamically import BrowserWallet to avoid SSR issues\nlet BrowserWallet = null;\nif (true) {\n    __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_meshsdk_core_dist_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @meshsdk/core */ \"(app-pages-browser)/./node_modules/@meshsdk/core/dist/index.js\")).then((module)=>{\n        BrowserWallet = module.BrowserWallet;\n    });\n}\nconst WalletContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useWallet() {\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(WalletContext);\n    if (context === undefined) {\n        throw new Error('useWallet must be used within a WalletProvider');\n    }\n    return context;\n}\n_s(useWallet, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nfunction WalletProvider(param) {\n    let { children } = param;\n    _s1();\n    const [wallet, setWallet] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [connected, setConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [connecting, setConnecting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [address, setAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [balance, setBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const getAvailableWallets = ()=>{\n        var _window_cardano, _window_cardano1, _window_cardano2, _window_cardano3;\n        if (false) {}\n        const wallets = [];\n        if ((_window_cardano = window.cardano) === null || _window_cardano === void 0 ? void 0 : _window_cardano.lace) wallets.push('lace');\n        if ((_window_cardano1 = window.cardano) === null || _window_cardano1 === void 0 ? void 0 : _window_cardano1.eternl) wallets.push('eternl');\n        if ((_window_cardano2 = window.cardano) === null || _window_cardano2 === void 0 ? void 0 : _window_cardano2.nami) wallets.push('nami');\n        if ((_window_cardano3 = window.cardano) === null || _window_cardano3 === void 0 ? void 0 : _window_cardano3.flint) wallets.push('flint');\n        return wallets;\n    };\n    const connect = async (walletName)=>{\n        if (false) {}\n        setConnecting(true);\n        try {\n            const browserWallet = await BrowserWallet.enable(walletName);\n            setWallet(browserWallet);\n            // Get wallet address\n            const addresses = await browserWallet.getUsedAddresses();\n            if (addresses.length > 0) {\n                setAddress(addresses[0]);\n            }\n            // Get wallet balance\n            try {\n                const balance = await browserWallet.getBalance();\n                setBalance(balance);\n            } catch (error) {\n                console.warn('Could not fetch balance:', error);\n                setBalance('0');\n            }\n            setConnected(true);\n            // Store connection state\n            localStorage.setItem('vintrek_wallet', walletName);\n        } catch (error) {\n            console.error('Failed to connect wallet:', error);\n            throw error;\n        } finally{\n            setConnecting(false);\n        }\n    };\n    const disconnect = ()=>{\n        setWallet(null);\n        setConnected(false);\n        setAddress(null);\n        setBalance(null);\n        localStorage.removeItem('vintrek_wallet');\n    };\n    // Auto-reconnect on page load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WalletProvider.useEffect\": ()=>{\n            const savedWallet = localStorage.getItem('vintrek_wallet');\n            if (savedWallet && getAvailableWallets().includes(savedWallet)) {\n                connect(savedWallet).catch(console.error);\n            }\n        }\n    }[\"WalletProvider.useEffect\"], []);\n    const value = {\n        wallet,\n        connected,\n        connecting,\n        address,\n        balance,\n        connect,\n        disconnect,\n        getAvailableWallets\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WalletContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\providers\\\\WalletProvider.tsx\",\n        lineNumber: 121,\n        columnNumber: 5\n    }, this);\n}\n_s1(WalletProvider, \"dSBI9X6BQq4oMvol2/Gn4R+d8Wg=\");\n_c = WalletProvider;\nvar _c;\n$RefreshReg$(_c, \"WalletProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3Byb3ZpZGVycy9XYWxsZXRQcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUVpRjtBQUdqRix1REFBdUQ7QUFDdkQsSUFBSUksZ0JBQXFCO0FBQ3pCLElBQUksSUFBNkIsRUFBRTtJQUNqQyw4T0FBdUIsQ0FBQ0MsSUFBSSxDQUFDLENBQUNDO1FBQzVCRixnQkFBZ0JFLE9BQU9GLGFBQWE7SUFDdEM7QUFDRjtBQWFBLE1BQU1HLDhCQUFnQlAsb0RBQWFBLENBQWdDUTtBQUU1RCxTQUFTQzs7SUFDZCxNQUFNQyxVQUFVVCxpREFBVUEsQ0FBQ007SUFDM0IsSUFBSUcsWUFBWUYsV0FBVztRQUN6QixNQUFNLElBQUlHLE1BQU07SUFDbEI7SUFDQSxPQUFPRDtBQUNUO0dBTmdCRDtBQVlULFNBQVNHLGVBQWUsS0FBaUM7UUFBakMsRUFBRUMsUUFBUSxFQUF1QixHQUFqQzs7SUFDN0IsTUFBTSxDQUFDQyxRQUFRQyxVQUFVLEdBQUdiLCtDQUFRQSxDQUF1QjtJQUMzRCxNQUFNLENBQUNjLFdBQVdDLGFBQWEsR0FBR2YsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDZ0IsWUFBWUMsY0FBYyxHQUFHakIsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDa0IsU0FBU0MsV0FBVyxHQUFHbkIsK0NBQVFBLENBQWdCO0lBQ3RELE1BQU0sQ0FBQ29CLFNBQVNDLFdBQVcsR0FBR3JCLCtDQUFRQSxDQUFnQjtJQUV0RCxNQUFNc0Isc0JBQXNCO1lBSXRCQyxpQkFDQUEsa0JBQ0FBLGtCQUNBQTtRQU5KLElBQUksS0FBNkIsRUFBRSxFQUFTO1FBRTVDLE1BQU1DLFVBQVUsRUFBRTtRQUNsQixLQUFJRCxrQkFBQUEsT0FBT0UsT0FBTyxjQUFkRixzQ0FBQUEsZ0JBQWdCRyxJQUFJLEVBQUVGLFFBQVFHLElBQUksQ0FBQztRQUN2QyxLQUFJSixtQkFBQUEsT0FBT0UsT0FBTyxjQUFkRix1Q0FBQUEsaUJBQWdCSyxNQUFNLEVBQUVKLFFBQVFHLElBQUksQ0FBQztRQUN6QyxLQUFJSixtQkFBQUEsT0FBT0UsT0FBTyxjQUFkRix1Q0FBQUEsaUJBQWdCTSxJQUFJLEVBQUVMLFFBQVFHLElBQUksQ0FBQztRQUN2QyxLQUFJSixtQkFBQUEsT0FBT0UsT0FBTyxjQUFkRix1Q0FBQUEsaUJBQWdCTyxLQUFLLEVBQUVOLFFBQVFHLElBQUksQ0FBQztRQUV4QyxPQUFPSDtJQUNUO0lBRUEsTUFBTU8sVUFBVSxPQUFPQztRQUNyQixJQUFJLEtBQTZCLEVBQUU7UUFFbkNmLGNBQWM7UUFDZCxJQUFJO1lBQ0YsTUFBTWdCLGdCQUFnQixNQUFNL0IsY0FBY2dDLE1BQU0sQ0FBQ0Y7WUFDakRuQixVQUFVb0I7WUFFVixxQkFBcUI7WUFDckIsTUFBTUUsWUFBWSxNQUFNRixjQUFjRyxnQkFBZ0I7WUFDdEQsSUFBSUQsVUFBVUUsTUFBTSxHQUFHLEdBQUc7Z0JBQ3hCbEIsV0FBV2dCLFNBQVMsQ0FBQyxFQUFFO1lBQ3pCO1lBRUEscUJBQXFCO1lBQ3JCLElBQUk7Z0JBQ0YsTUFBTWYsVUFBVSxNQUFNYSxjQUFjSyxVQUFVO2dCQUM5Q2pCLFdBQVdEO1lBQ2IsRUFBRSxPQUFPbUIsT0FBTztnQkFDZEMsUUFBUUMsSUFBSSxDQUFDLDRCQUE0QkY7Z0JBQ3pDbEIsV0FBVztZQUNiO1lBRUFOLGFBQWE7WUFFYix5QkFBeUI7WUFDekIyQixhQUFhQyxPQUFPLENBQUMsa0JBQWtCWDtRQUN6QyxFQUFFLE9BQU9PLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDZCQUE2QkE7WUFDM0MsTUFBTUE7UUFDUixTQUFVO1lBQ1J0QixjQUFjO1FBQ2hCO0lBQ0Y7SUFFQSxNQUFNMkIsYUFBYTtRQUNqQi9CLFVBQVU7UUFDVkUsYUFBYTtRQUNiSSxXQUFXO1FBQ1hFLFdBQVc7UUFDWHFCLGFBQWFHLFVBQVUsQ0FBQztJQUMxQjtJQUVBLDhCQUE4QjtJQUM5QjVDLGdEQUFTQTtvQ0FBQztZQUNSLE1BQU02QyxjQUFjSixhQUFhSyxPQUFPLENBQUM7WUFDekMsSUFBSUQsZUFBZXhCLHNCQUFzQjBCLFFBQVEsQ0FBQ0YsY0FBYztnQkFDOURmLFFBQVFlLGFBQWFHLEtBQUssQ0FBQ1QsUUFBUUQsS0FBSztZQUMxQztRQUNGO21DQUFHLEVBQUU7SUFFTCxNQUFNVyxRQUEyQjtRQUMvQnRDO1FBQ0FFO1FBQ0FFO1FBQ0FFO1FBQ0FFO1FBQ0FXO1FBQ0FhO1FBQ0F0QjtJQUNGO0lBRUEscUJBQ0UsOERBQUNqQixjQUFjOEMsUUFBUTtRQUFDRCxPQUFPQTtrQkFDNUJ2Qzs7Ozs7O0FBR1A7SUF0RmdCRDtLQUFBQSIsInNvdXJjZXMiOlsiRDpcXGhhY2tvdGhvblxcQmxvY2tjaGFpblxcVmludHJla1xcc3JjXFxjb21wb25lbnRzXFxwcm92aWRlcnNcXFdhbGxldFByb3ZpZGVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgY3JlYXRlQ29udGV4dCwgdXNlQ29udGV4dCwgdXNlU3RhdGUsIHVzZUVmZmVjdCwgUmVhY3ROb2RlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgZHluYW1pYyBmcm9tICduZXh0L2R5bmFtaWMnXG5cbi8vIER5bmFtaWNhbGx5IGltcG9ydCBCcm93c2VyV2FsbGV0IHRvIGF2b2lkIFNTUiBpc3N1ZXNcbmxldCBCcm93c2VyV2FsbGV0OiBhbnkgPSBudWxsXG5pZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgaW1wb3J0KCdAbWVzaHNkay9jb3JlJykudGhlbigobW9kdWxlKSA9PiB7XG4gICAgQnJvd3NlcldhbGxldCA9IG1vZHVsZS5Ccm93c2VyV2FsbGV0XG4gIH0pXG59XG5cbmludGVyZmFjZSBXYWxsZXRDb250ZXh0VHlwZSB7XG4gIHdhbGxldDogYW55IHwgbnVsbFxuICBjb25uZWN0ZWQ6IGJvb2xlYW5cbiAgY29ubmVjdGluZzogYm9vbGVhblxuICBhZGRyZXNzOiBzdHJpbmcgfCBudWxsXG4gIGJhbGFuY2U6IHN0cmluZyB8IG51bGxcbiAgY29ubmVjdDogKHdhbGxldE5hbWU6IHN0cmluZykgPT4gUHJvbWlzZTx2b2lkPlxuICBkaXNjb25uZWN0OiAoKSA9PiB2b2lkXG4gIGdldEF2YWlsYWJsZVdhbGxldHM6ICgpID0+IHN0cmluZ1tdXG59XG5cbmNvbnN0IFdhbGxldENvbnRleHQgPSBjcmVhdGVDb250ZXh0PFdhbGxldENvbnRleHRUeXBlIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpXG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VXYWxsZXQoKSB7XG4gIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KFdhbGxldENvbnRleHQpXG4gIGlmIChjb250ZXh0ID09PSB1bmRlZmluZWQpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ3VzZVdhbGxldCBtdXN0IGJlIHVzZWQgd2l0aGluIGEgV2FsbGV0UHJvdmlkZXInKVxuICB9XG4gIHJldHVybiBjb250ZXh0XG59XG5cbmludGVyZmFjZSBXYWxsZXRQcm92aWRlclByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZVxufVxuXG5leHBvcnQgZnVuY3Rpb24gV2FsbGV0UHJvdmlkZXIoeyBjaGlsZHJlbiB9OiBXYWxsZXRQcm92aWRlclByb3BzKSB7XG4gIGNvbnN0IFt3YWxsZXQsIHNldFdhbGxldF0gPSB1c2VTdGF0ZTxCcm93c2VyV2FsbGV0IHwgbnVsbD4obnVsbClcbiAgY29uc3QgW2Nvbm5lY3RlZCwgc2V0Q29ubmVjdGVkXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbY29ubmVjdGluZywgc2V0Q29ubmVjdGluZ10gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW2FkZHJlc3MsIHNldEFkZHJlc3NdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbClcbiAgY29uc3QgW2JhbGFuY2UsIHNldEJhbGFuY2VdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbClcblxuICBjb25zdCBnZXRBdmFpbGFibGVXYWxsZXRzID0gKCkgPT4ge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuIFtdXG4gICAgXG4gICAgY29uc3Qgd2FsbGV0cyA9IFtdXG4gICAgaWYgKHdpbmRvdy5jYXJkYW5vPy5sYWNlKSB3YWxsZXRzLnB1c2goJ2xhY2UnKVxuICAgIGlmICh3aW5kb3cuY2FyZGFubz8uZXRlcm5sKSB3YWxsZXRzLnB1c2goJ2V0ZXJubCcpXG4gICAgaWYgKHdpbmRvdy5jYXJkYW5vPy5uYW1pKSB3YWxsZXRzLnB1c2goJ25hbWknKVxuICAgIGlmICh3aW5kb3cuY2FyZGFubz8uZmxpbnQpIHdhbGxldHMucHVzaCgnZmxpbnQnKVxuICAgIFxuICAgIHJldHVybiB3YWxsZXRzXG4gIH1cblxuICBjb25zdCBjb25uZWN0ID0gYXN5bmMgKHdhbGxldE5hbWU6IHN0cmluZykgPT4ge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuXG4gICAgXG4gICAgc2V0Q29ubmVjdGluZyh0cnVlKVxuICAgIHRyeSB7XG4gICAgICBjb25zdCBicm93c2VyV2FsbGV0ID0gYXdhaXQgQnJvd3NlcldhbGxldC5lbmFibGUod2FsbGV0TmFtZSlcbiAgICAgIHNldFdhbGxldChicm93c2VyV2FsbGV0KVxuICAgICAgXG4gICAgICAvLyBHZXQgd2FsbGV0IGFkZHJlc3NcbiAgICAgIGNvbnN0IGFkZHJlc3NlcyA9IGF3YWl0IGJyb3dzZXJXYWxsZXQuZ2V0VXNlZEFkZHJlc3NlcygpXG4gICAgICBpZiAoYWRkcmVzc2VzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgc2V0QWRkcmVzcyhhZGRyZXNzZXNbMF0pXG4gICAgICB9XG4gICAgICBcbiAgICAgIC8vIEdldCB3YWxsZXQgYmFsYW5jZVxuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgYmFsYW5jZSA9IGF3YWl0IGJyb3dzZXJXYWxsZXQuZ2V0QmFsYW5jZSgpXG4gICAgICAgIHNldEJhbGFuY2UoYmFsYW5jZSlcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUud2FybignQ291bGQgbm90IGZldGNoIGJhbGFuY2U6JywgZXJyb3IpXG4gICAgICAgIHNldEJhbGFuY2UoJzAnKVxuICAgICAgfVxuICAgICAgXG4gICAgICBzZXRDb25uZWN0ZWQodHJ1ZSlcbiAgICAgIFxuICAgICAgLy8gU3RvcmUgY29ubmVjdGlvbiBzdGF0ZVxuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ3ZpbnRyZWtfd2FsbGV0Jywgd2FsbGV0TmFtZSlcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGNvbm5lY3Qgd2FsbGV0OicsIGVycm9yKVxuICAgICAgdGhyb3cgZXJyb3JcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0Q29ubmVjdGluZyhmYWxzZSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBkaXNjb25uZWN0ID0gKCkgPT4ge1xuICAgIHNldFdhbGxldChudWxsKVxuICAgIHNldENvbm5lY3RlZChmYWxzZSlcbiAgICBzZXRBZGRyZXNzKG51bGwpXG4gICAgc2V0QmFsYW5jZShudWxsKVxuICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCd2aW50cmVrX3dhbGxldCcpXG4gIH1cblxuICAvLyBBdXRvLXJlY29ubmVjdCBvbiBwYWdlIGxvYWRcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBzYXZlZFdhbGxldCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd2aW50cmVrX3dhbGxldCcpXG4gICAgaWYgKHNhdmVkV2FsbGV0ICYmIGdldEF2YWlsYWJsZVdhbGxldHMoKS5pbmNsdWRlcyhzYXZlZFdhbGxldCkpIHtcbiAgICAgIGNvbm5lY3Qoc2F2ZWRXYWxsZXQpLmNhdGNoKGNvbnNvbGUuZXJyb3IpXG4gICAgfVxuICB9LCBbXSlcblxuICBjb25zdCB2YWx1ZTogV2FsbGV0Q29udGV4dFR5cGUgPSB7XG4gICAgd2FsbGV0LFxuICAgIGNvbm5lY3RlZCxcbiAgICBjb25uZWN0aW5nLFxuICAgIGFkZHJlc3MsXG4gICAgYmFsYW5jZSxcbiAgICBjb25uZWN0LFxuICAgIGRpc2Nvbm5lY3QsXG4gICAgZ2V0QXZhaWxhYmxlV2FsbGV0cyxcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPFdhbGxldENvbnRleHQuUHJvdmlkZXIgdmFsdWU9e3ZhbHVlfT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L1dhbGxldENvbnRleHQuUHJvdmlkZXI+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiQnJvd3NlcldhbGxldCIsInRoZW4iLCJtb2R1bGUiLCJXYWxsZXRDb250ZXh0IiwidW5kZWZpbmVkIiwidXNlV2FsbGV0IiwiY29udGV4dCIsIkVycm9yIiwiV2FsbGV0UHJvdmlkZXIiLCJjaGlsZHJlbiIsIndhbGxldCIsInNldFdhbGxldCIsImNvbm5lY3RlZCIsInNldENvbm5lY3RlZCIsImNvbm5lY3RpbmciLCJzZXRDb25uZWN0aW5nIiwiYWRkcmVzcyIsInNldEFkZHJlc3MiLCJiYWxhbmNlIiwic2V0QmFsYW5jZSIsImdldEF2YWlsYWJsZVdhbGxldHMiLCJ3aW5kb3ciLCJ3YWxsZXRzIiwiY2FyZGFubyIsImxhY2UiLCJwdXNoIiwiZXRlcm5sIiwibmFtaSIsImZsaW50IiwiY29ubmVjdCIsIndhbGxldE5hbWUiLCJicm93c2VyV2FsbGV0IiwiZW5hYmxlIiwiYWRkcmVzc2VzIiwiZ2V0VXNlZEFkZHJlc3NlcyIsImxlbmd0aCIsImdldEJhbGFuY2UiLCJlcnJvciIsImNvbnNvbGUiLCJ3YXJuIiwibG9jYWxTdG9yYWdlIiwic2V0SXRlbSIsImRpc2Nvbm5lY3QiLCJyZW1vdmVJdGVtIiwic2F2ZWRXYWxsZXQiLCJnZXRJdGVtIiwiaW5jbHVkZXMiLCJjYXRjaCIsInZhbHVlIiwiUHJvdmlkZXIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/providers/WalletProvider.tsx\n"));

/***/ })

});