import * as Cardano from '../../../Cardano';
import { HexBlob } from '@cardano-sdk/util';
export declare class MoveInstantaneousRewardToStakeCreds {
    #private;
    constructor(pot: Cardano.MirCertificatePot, credentials: Map<Cardano.Credential, bigint>);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): MoveInstantaneousRewardToStakeCreds;
    toCore(): Cardano.MirCertificate;
    static fromCore(cert: Cardano.MirCertificate): MoveInstantaneousRewardToStakeCreds;
    pot(): Cardano.MirCertificatePot;
    setPot(pot: Cardano.MirCertificatePot): void;
    getStakeCreds(): Map<Cardano.Credential, bigint> | undefined;
    setStakeCreds(credentials: Map<Cardano.Credential, bigint>): void;
}
//# sourceMappingURL=MoveInstantaneousRewardToStakeCreds.d.ts.map