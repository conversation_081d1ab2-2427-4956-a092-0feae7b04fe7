/// <reference types="pouchdb-core" />
/// <reference types="node" />
import { Address, AddressProps, Credential } from './Address';
import { NetworkId } from '../ChainId';
export declare class RewardAddress {
    #private;
    private constructor();
    static fromCredentials(networkId: NetworkId, payment: Credential): RewardAddress;
    getPaymentCredential(): Credential;
    toAddress(): Address;
    static fromAddress(addr: Address): RewardAddress | undefined;
    static packParts(props: AddressProps): Buffer;
    static unpackParts(type: number, data: Uint8Array): Address;
}
//# sourceMappingURL=RewardAddress.d.ts.map