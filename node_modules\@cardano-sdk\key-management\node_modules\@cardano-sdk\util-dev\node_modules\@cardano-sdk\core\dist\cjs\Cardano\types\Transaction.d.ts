import * as Crypto from '@cardano-sdk/crypto';
import { AuxiliaryData } from './AuxiliaryData';
import { Base64Blob, HexBlob, OpaqueString } from '@cardano-sdk/util';
import { Certificate, PoolRegistrationCertificate } from './Certificate';
import { ExUnits, Update, ValidityInterval } from './ProtocolParameters';
import { HydratedTxIn, TxIn, TxOut } from './Utxo';
import { Lovelace, TokenMap } from './Value';
import { NetworkId } from '../ChainId';
import { PartialBlockHeader } from './Block';
import { PlutusData } from './PlutusData';
import { ProposalProcedure, VotingProcedures } from './Governance';
import { RewardAccount } from '../Address';
import { Script } from './Script';
export declare type TransactionId = OpaqueString<'TransactionId'>;
export declare const TransactionId: {
    (value: string): TransactionId;
    fromHexBlob(value: HexBlob): TransactionId;
};
export interface Withdrawal {
    stakeAddress: RewardAccount;
    quantity: Lovelace;
}
export declare type HydratedPoolRegistrationCertificate = PoolRegistrationCertificate & {
    deposit?: Lovelace;
};
export declare type HydratedCertificate = Exclude<Certificate, PoolRegistrationCertificate> | HydratedPoolRegistrationCertificate;
export interface HydratedTxBody {
    inputs: HydratedTxIn[];
    collaterals?: HydratedTxIn[];
    outputs: TxOut[];
    fee: Lovelace;
    validityInterval?: ValidityInterval;
    withdrawals?: Withdrawal[];
    certificates?: HydratedCertificate[];
    mint?: TokenMap;
    scriptIntegrityHash?: Crypto.Hash32ByteBase16;
    requiredExtraSignatures?: Crypto.Ed25519KeyHashHex[];
    networkId?: NetworkId;
    update?: Update;
    auxiliaryDataHash?: Crypto.Hash32ByteBase16;
    totalCollateral?: Lovelace;
    collateralReturn?: TxOut;
    referenceInputs?: HydratedTxIn[];
    votingProcedures?: VotingProcedures;
    proposalProcedures?: ProposalProcedure[];
    treasuryValue?: Lovelace;
    donation?: Lovelace;
}
export interface TxBody extends Omit<HydratedTxBody, 'certificates' | 'inputs' | 'collaterals' | 'referenceInputs'> {
    certificates?: Certificate[];
    collaterals?: TxIn[];
    inputs: TxIn[];
    referenceInputs?: TxIn[];
}
export declare enum InputSource {
    inputs = "inputs",
    collaterals = "collaterals"
}
export declare enum RedeemerPurpose {
    spend = "spend",
    mint = "mint",
    certificate = "certificate",
    withdrawal = "withdrawal",
    propose = "propose",
    vote = "vote"
}
export interface Redeemer {
    index: number;
    purpose: RedeemerPurpose;
    data: PlutusData;
    executionUnits: ExUnits;
}
export declare type Signatures = Map<Crypto.Ed25519PublicKeyHex, Crypto.Ed25519SignatureHex>;
export declare type Signature = Crypto.Ed25519SignatureHex;
export declare type ChainCode = HexBlob;
export declare type AddressAttributes = Base64Blob;
export declare type VerificationKey = Crypto.Ed25519PublicKeyHex;
export interface BootstrapWitness {
    signature: Signature;
    chainCode?: ChainCode;
    addressAttributes?: AddressAttributes;
    key: VerificationKey;
}
export declare type Witness = {
    redeemers?: Redeemer[];
    signatures: Signatures;
    scripts?: Script[];
    bootstrap?: BootstrapWitness[];
    datums?: PlutusData[];
};
export interface Tx<TBody extends TxBody = TxBody> {
    id: TransactionId;
    body: TBody;
    witness: Witness;
    auxiliaryData?: AuxiliaryData;
    isValid?: boolean;
}
interface TxWithInputSource<TBody extends TxBody = TxBody> extends Omit<Tx<TBody>, 'isValid'> {
    inputSource: InputSource;
}
export interface OnChainTx<TBody extends TxBody = TxBody> extends Omit<TxWithInputSource<TBody>, 'witness' | 'auxiliaryData'> {
    witness: Omit<Witness, 'scripts'>;
    auxiliaryData?: Omit<AuxiliaryData, 'scripts'>;
}
export interface HydratedTx extends TxWithInputSource<HydratedTxBody> {
    index: number;
    blockHeader: PartialBlockHeader;
    body: HydratedTxBody;
    txSize: number;
}
export declare type TxBodyWithHash = {
    hash: TransactionId;
    body: TxBody;
};
export {};
//# sourceMappingURL=Transaction.d.ts.map