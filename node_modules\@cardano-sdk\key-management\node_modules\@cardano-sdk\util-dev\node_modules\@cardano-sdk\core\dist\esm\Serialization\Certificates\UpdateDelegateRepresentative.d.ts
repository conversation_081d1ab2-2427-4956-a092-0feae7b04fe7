import { Anchor } from '../Common/index.js';
import { HexBlob } from '@cardano-sdk/util';
import type * as Cardano from '../../Cardano/index.js';
export declare class UpdateDelegateRepresentative {
    #private;
    constructor(drepCredential: Cardano.Credential, anchor?: Anchor);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): UpdateDelegateRepresentative;
    toCore(): Cardano.UpdateDelegateRepresentativeCertificate;
    static fromCore(cert: Cardano.UpdateDelegateRepresentativeCertificate): UpdateDelegateRepresentative;
    credential(): Cardano.Credential;
    anchor(): Anchor | undefined;
}
//# sourceMappingURL=UpdateDelegateRepresentative.d.ts.map