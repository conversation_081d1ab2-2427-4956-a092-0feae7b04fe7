import type * as Cardano from '../Cardano';
import type { AssetInfo } from '../Asset';
import type { AssetProvider } from '../Provider';
import type { Logger } from 'ts-log';
import type { Milliseconds } from './time';
declare type TryGetAssetInfosProps = {
    assetIds: Cardano.AssetId[];
    assetProvider: AssetProvider;
    timeout: Milliseconds;
    logger: Logger;
};
export declare const tryGetAssetInfos: ({ assetIds, assetProvider, logger, timeout }: TryGetAssetInfosProps) => Promise<AssetInfo[]>;
export {};
//# sourceMappingURL=tryGetAssetInfos.d.ts.map