import * as Cardano from '../../../Cardano';
import { HexBlob } from '@cardano-sdk/util';
import { TransactionMetadatum } from './TransactionMetadatum';
export declare class GeneralTransactionMetadata {
    #private;
    constructor(metadata: Map<bigint, TransactionMetadatum>);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): GeneralTransactionMetadata;
    toCore(): Cardano.TxMetadata;
    static fromCore(metadata: Cardano.TxMetadata): GeneralTransactionMetadata;
    metadata(): Map<bigint, TransactionMetadatum> | undefined;
    setMetadata(metadata: Map<bigint, TransactionMetadatum>): void;
    equals(other: GeneralTransactionMetadata): boolean;
}
//# sourceMappingURL=GeneralTransactionMetadata.d.ts.map