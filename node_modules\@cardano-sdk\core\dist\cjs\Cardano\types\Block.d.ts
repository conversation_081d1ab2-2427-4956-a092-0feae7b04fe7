import * as Crypto from '@cardano-sdk/crypto';
import { OpaqueNumber, OpaqueString } from '@cardano-sdk/util';
import { Lovelace } from './Value';
import { OnChainTx } from './Transaction';
import { PoolId } from './StakePool/primitives';
export declare type BlockSize = OpaqueNumber<'BlockSize'>;
export declare const BlockSize: (value: number) => BlockSize;
export declare type BlockNo = OpaqueNumber<'BlockNo'>;
export declare const BlockNo: (value: number) => BlockNo;
export declare type EpochNo = OpaqueNumber<'EpochNo'>;
export declare const EpochNo: (value: number) => EpochNo;
export declare type Slot = OpaqueNumber<'Slot'>;
export declare const Slot: (value: number) => Slot;
export declare type BlockId = OpaqueString<'BlockId'>;
export interface PartialBlockHeader {
    blockNo: BlockNo;
    slot: Slot;
    hash: BlockId;
}
export declare type Tip = PartialBlockHeader;
export declare const BlockId: (value: string) => BlockId;
export declare type VrfVkBech32 = OpaqueString<'VrfVkBech32'>;
export declare const VrfVkBech32: {
    (value: string): VrfVkBech32;
    fromHex(value: string): VrfVkBech32;
};
export declare type GenesisDelegate = OpaqueString<'GenesisDelegate'>;
export declare const GenesisDelegate: (value: string) => GenesisDelegate;
export declare type SlotLeader = PoolId | GenesisDelegate;
export declare const SlotLeader: (value: string) => SlotLeader;
export interface BlockInfo {
    header: PartialBlockHeader;
    fees?: Lovelace;
    totalOutput: Lovelace;
    txCount: number;
    size?: BlockSize;
    previousBlock?: BlockId;
    vrf?: VrfVkBech32;
    issuerVk?: Crypto.Ed25519PublicKeyHex;
}
export interface Block extends BlockInfo {
    body: OnChainTx[];
}
export interface ExtendedBlockInfo extends Required<Omit<BlockInfo, 'issuerVk' | 'previousBlock'>>, Pick<BlockInfo, 'previousBlock'> {
    slotLeader: SlotLeader;
    date: Date;
    epoch: EpochNo;
    epochSlot: number;
    nextBlock?: BlockId;
    confirmations: number;
}
//# sourceMappingURL=Block.d.ts.map