import { AssetFingerprint, AssetName, Certificate, CertificateType, HydratedTxIn, Lovelace, Metadatum, PolicyId, PoolRegistrationCertificate, PoolRetirementCertificate, Script, StakeAddressCertificate, StakeDelegationCertificate, Tx, TxIn, TxOut, Value } from '../Cardano/types/index.js';
import { InputResolver, PaymentAddress, RewardAccount } from '../Cardano/Address/index.js';
export declare type Inspector<Inspection> = (tx: Tx) => Promise<Inspection>;
export declare type Inspectors = {
    [k: string]: Inspector<unknown>;
};
export declare type TxInspector<T extends Inspectors> = (tx: Tx) => Promise<{
    [k in keyof T]: Awaited<ReturnType<T[k]>>;
}>;
export declare type SendReceiveValueInspection = Value;
export declare type DelegationInspection = StakeDelegationCertificate[];
export declare type StakeRegistrationInspection = StakeAddressCertificate[];
export declare type PoolRegistrationInspection = PoolRegistrationCertificate[];
export declare type PoolRetirementInspection = PoolRetirementCertificate[];
export declare type WithdrawalInspection = Lovelace;
export interface SentInspection {
    inputs: HydratedTxIn[];
    certificates: Certificate[];
}
export declare type SignedCertificatesInspection = Certificate[];
export interface MintedAsset {
    script?: Script;
    policyId: PolicyId;
    fingerprint: AssetFingerprint;
    assetName: AssetName;
    quantity: bigint;
}
export declare type AssetsMintedInspection = MintedAsset[];
export declare type MetadataInspection = Metadatum;
interface SentInspectorArgs {
    addresses?: PaymentAddress[];
    rewardAccounts?: RewardAccount[];
    inputResolver: InputResolver;
}
export declare type SentInspector = (args: SentInspectorArgs) => Inspector<SentInspection>;
export declare type TotalAddressInputsValueInspector = (ownAddresses: PaymentAddress[], inputResolver: InputResolver) => Inspector<SendReceiveValueInspection>;
export declare type SendReceiveValueInspector = (ownAddresses: PaymentAddress[]) => Inspector<SendReceiveValueInspection>;
export declare type DelegationInspector = Inspector<DelegationInspection>;
export declare type StakeRegistrationInspector = Inspector<StakeRegistrationInspection>;
export declare type WithdrawalInspector = Inspector<WithdrawalInspection>;
export declare type SignedCertificatesInspector = (rewardAccounts: RewardAccount[], certificateTypes?: CertificateType[]) => Inspector<SignedCertificatesInspection>;
export declare type AssetsMintedInspector = Inspector<AssetsMintedInspection>;
export declare type MetadataInspector = Inspector<MetadataInspection>;
export declare type PoolRegistrationInspector = Inspector<PoolRegistrationInspection>;
export declare type PoolRetirementInspector = Inspector<PoolRetirementInspection>;
declare type ResolvedInput = TxIn & TxOut;
export declare type ResolutionResult = {
    resolvedInputs: ResolvedInput[];
    unresolvedInputs: TxIn[];
};
export declare const resolveInputs: (txIns: TxIn[], inputResolver: InputResolver) => Promise<ResolutionResult>;
export declare const totalAddressInputsValueInspector: TotalAddressInputsValueInspector;
export declare const totalAddressOutputsValueInspector: SendReceiveValueInspector;
export declare const getCertificatesByType: (tx: Tx, rewardAccounts: RewardAccount[], certificateTypes?: readonly CertificateType[]) => Certificate[];
export declare const signedCertificatesInspector: SignedCertificatesInspector;
export declare const sentInspector: SentInspector;
export declare const valueSentInspector: TotalAddressInputsValueInspector;
export declare const valueReceivedInspector: TotalAddressInputsValueInspector;
export declare const delegationInspector: DelegationInspector;
export declare const stakeKeyDeregistrationInspector: StakeRegistrationInspector;
export declare const stakeKeyRegistrationInspector: StakeRegistrationInspector;
export declare const poolRegistrationInspector: PoolRegistrationInspector;
export declare const poolRetirementInspector: PoolRetirementInspector;
export declare const withdrawalInspector: WithdrawalInspector;
export interface MatchQuantityCriteria {
    (quantity: bigint): boolean;
}
export declare const mintInspector: (matchQuantityCriteria: MatchQuantityCriteria) => AssetsMintedInspector;
export declare const assetsMintedInspector: AssetsMintedInspector;
export declare const assetsBurnedInspector: AssetsMintedInspector;
export declare const metadataInspector: MetadataInspector;
export declare const createTxInspector: <T extends Inspectors>(inspectors: T) => TxInspector<T>;
export {};
//# sourceMappingURL=txInspector.d.ts.map