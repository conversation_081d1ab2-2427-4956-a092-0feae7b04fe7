import { CborSet, Credential, UnitInterval } from '../../Common/index.js';
import { GovernanceActionId } from '../../Common/GovernanceActionId.js';
import { HexBlob } from '@cardano-sdk/util';
import type * as Cardano from '../../../Cardano/index.js';
declare type CredentialSet = CborSet<ReturnType<Credential['toCore']>, Credential>;
export declare class UpdateCommittee {
    #private;
    constructor(membersToBeRemoved: CredentialSet, membersToBeAdded: [Cardano.Credential, number][], newQuorum: UnitInterval, govActionId?: GovernanceActionId);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): UpdateCommittee;
    toCore(): Cardano.UpdateCommittee;
    static fromCore(updateCommittee: Cardano.UpdateCommittee): UpdateCommittee;
    govActionId(): GovernanceActionId | undefined;
    membersToBeRemoved(): Cardano.Credential[];
    membersToBeAdded(): [Cardano.Credential, number][];
    newQuorum(): UnitInterval;
}
export {};
//# sourceMappingURL=UpdateCommittee.d.ts.map