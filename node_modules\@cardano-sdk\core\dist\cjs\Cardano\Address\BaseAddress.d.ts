/// <reference types="pouchdb-core" />
/// <reference types="node" />
import { Address, AddressProps, Credential } from './Address';
import { NetworkId } from '../ChainId';
export declare class BaseAddress {
    #private;
    private constructor();
    static fromCredentials(networkId: NetworkId, payment: Credential, stake: Credential): BaseAddress;
    getPaymentCredential(): Credential;
    getStakeCredential(): Credential;
    toAddress(): Address;
    static fromAddress(addr: Address): BaseAddress | undefined;
    static packParts(props: AddressProps): Buffer;
    static unpackParts(type: number, data: Uint8Array): Address;
}
//# sourceMappingURL=BaseAddress.d.ts.map