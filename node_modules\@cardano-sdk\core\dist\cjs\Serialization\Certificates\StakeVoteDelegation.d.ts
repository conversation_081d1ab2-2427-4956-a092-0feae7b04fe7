import * as Cardano from '../../Cardano';
import * as Crypto from '@cardano-sdk/crypto';
import { DRep } from './DRep';
import { HexBlob } from '@cardano-sdk/util';
export declare class StakeVoteDelegation {
    #private;
    constructor(stakeCredential: Cardano.Credential, drep: DRep, poolKeyHash: Crypto.Ed25519KeyHashHex);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): StakeVoteDelegation;
    toCore(): Cardano.StakeVoteDelegationCertificate;
    static fromCore(deleg: Cardano.StakeVoteDelegationCertificate): StakeVoteDelegation;
    stakeCredential(): Cardano.Credential;
    drep(): DRep;
    poolKeyHash(): Crypto.Ed25519KeyHashHex;
}
//# sourceMappingURL=StakeVoteDelegation.d.ts.map