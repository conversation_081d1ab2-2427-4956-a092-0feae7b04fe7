import { HealthCheckResponse, NetworkInfoProvider, Provider } from './Provider/index.js';
import { Observable } from 'rxjs';
export declare type AsyncReturnType<F extends () => unknown> = F extends () => Promise<infer R> ? R : never;
export declare type NetworkInfoMethods = Exclude<keyof NetworkInfoProvider, 'healthCheck'>;
export declare type NetworkInfoResponses = {
    [m in NetworkInfoMethods]: AsyncReturnType<NetworkInfoProvider[m]>;
};
export interface WSMessage {
    clientId?: string;
    networkInfo?: Partial<NetworkInfoResponses>;
}
export declare class WsProvider implements Provider {
    health$: Observable<HealthCheckResponse>;
    private healthSubject$;
    private reason?;
    constructor();
    protected emitHealth(reason?: string, overwrite?: boolean): void;
    healthCheck(): Promise<HealthCheckResponse>;
}
//# sourceMappingURL=WebSocket.d.ts.map