"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/providers/WalletProvider.tsx":
/*!*****************************************************!*\
  !*** ./src/components/providers/WalletProvider.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalletProvider: () => (/* binding */ WalletProvider),\n/* harmony export */   useWallet: () => (/* binding */ useWallet)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useWallet,WalletProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n// Dynamically import BrowserWallet to avoid SSR issues\nlet BrowserWallet = null;\nif (true) {\n    __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_meshsdk_core_dist_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @meshsdk/core */ \"(app-pages-browser)/./node_modules/@meshsdk/core/dist/index.js\")).then((module)=>{\n        BrowserWallet = module.BrowserWallet;\n    });\n}\nconst WalletContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useWallet() {\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(WalletContext);\n    if (context === undefined) {\n        throw new Error('useWallet must be used within a WalletProvider');\n    }\n    return context;\n}\n_s(useWallet, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nfunction WalletProvider(param) {\n    let { children } = param;\n    _s1();\n    const [wallet, setWallet] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [connected, setConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [connecting, setConnecting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [address, setAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [balance, setBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const getAvailableWallets = ()=>{\n        var _window_cardano, _window_cardano1, _window_cardano2, _window_cardano3;\n        if (false) {}\n        const wallets = [];\n        if ((_window_cardano = window.cardano) === null || _window_cardano === void 0 ? void 0 : _window_cardano.lace) wallets.push('lace');\n        if ((_window_cardano1 = window.cardano) === null || _window_cardano1 === void 0 ? void 0 : _window_cardano1.eternl) wallets.push('eternl');\n        if ((_window_cardano2 = window.cardano) === null || _window_cardano2 === void 0 ? void 0 : _window_cardano2.nami) wallets.push('nami');\n        if ((_window_cardano3 = window.cardano) === null || _window_cardano3 === void 0 ? void 0 : _window_cardano3.flint) wallets.push('flint');\n        return wallets;\n    };\n    const connect = async (walletName)=>{\n        if ( false || !BrowserWallet) return;\n        setConnecting(true);\n        try {\n            const browserWallet = await BrowserWallet.enable(walletName);\n            setWallet(browserWallet);\n            // Get wallet address\n            const addresses = await browserWallet.getUsedAddresses();\n            if (addresses.length > 0) {\n                setAddress(addresses[0]);\n            }\n            // Get wallet balance\n            try {\n                const balance = await browserWallet.getBalance();\n                setBalance(balance);\n            } catch (error) {\n                console.warn('Could not fetch balance:', error);\n                setBalance('0');\n            }\n            setConnected(true);\n            // Store connection state\n            localStorage.setItem('vintrek_wallet', walletName);\n        } catch (error) {\n            console.error('Failed to connect wallet:', error);\n            throw error;\n        } finally{\n            setConnecting(false);\n        }\n    };\n    const disconnect = ()=>{\n        setWallet(null);\n        setConnected(false);\n        setAddress(null);\n        setBalance(null);\n        localStorage.removeItem('vintrek_wallet');\n    };\n    // Auto-reconnect on page load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WalletProvider.useEffect\": ()=>{\n            const savedWallet = localStorage.getItem('vintrek_wallet');\n            if (savedWallet && getAvailableWallets().includes(savedWallet)) {\n                connect(savedWallet).catch(console.error);\n            }\n        }\n    }[\"WalletProvider.useEffect\"], []);\n    const value = {\n        wallet,\n        connected,\n        connecting,\n        address,\n        balance,\n        connect,\n        disconnect,\n        getAvailableWallets\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WalletContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\providers\\\\WalletProvider.tsx\",\n        lineNumber: 121,\n        columnNumber: 5\n    }, this);\n}\n_s1(WalletProvider, \"dSBI9X6BQq4oMvol2/Gn4R+d8Wg=\");\n_c = WalletProvider;\nvar _c;\n$RefreshReg$(_c, \"WalletProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/providers/WalletProvider.tsx\n"));

/***/ })

});