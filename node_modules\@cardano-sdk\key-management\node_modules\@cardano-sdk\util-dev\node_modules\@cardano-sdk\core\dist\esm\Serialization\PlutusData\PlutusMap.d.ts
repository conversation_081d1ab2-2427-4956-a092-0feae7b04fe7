import { HexBlob } from '@cardano-sdk/util';
import { PlutusData } from './PlutusData.js';
import { PlutusList } from './PlutusList.js';
export declare class PlutusMap {
    #private;
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): PlutusMap;
    getLength(): number;
    insert(key: PlutusData, value: PlutusData): void;
    get(key: PlutusData): PlutusData | undefined;
    getKeys(): PlutusList;
    equals(other: PlutusMap): boolean;
}
//# sourceMappingURL=PlutusMap.d.ts.map