import { ChainSyncError, ChainSyncErrorCode, GeneralCardanoNodeError, GeneralCardanoNodeErrorCode, IncompleteWithdrawalsData, OutsideOfValidityIntervalData, StateQueryError, StateQueryErrorCode, TxSubmissionError, TxSubmissionErrorCode, UnknownOutputReferencesData, ValueNotConservedData } from '../types';
export declare const asTxSubmissionError: (error: unknown) => TxSubmissionError<unknown> | null;
export declare const asChainSyncError: (error: unknown) => ChainSyncError<unknown> | null;
export declare const asStateQueryError: (error: unknown) => StateQueryError<unknown> | null;
export declare const asGeneralCardanoNodeError: (error: unknown) => GeneralCardanoNodeError<unknown> | null;
export declare const asCardanoNodeError: (error: unknown) => GeneralCardanoNodeError<unknown> | TxSubmissionError<unknown> | StateQueryError<unknown> | ChainSyncError<unknown>;
export declare const isChainSyncErrorCode: (code: unknown) => code is ChainSyncErrorCode;
export declare const isTxSubmissionErrorCode: (code: unknown) => code is TxSubmissionErrorCode;
export declare const isStateQueryErrorCode: (code: unknown) => code is StateQueryErrorCode;
export declare const isGeneralCardanoNodeErrorCode: (code: unknown) => code is GeneralCardanoNodeErrorCode;
export declare const asChainSyncErrorCode: (code: unknown) => ChainSyncErrorCode | null;
export declare const asStateQueryErrorCode: (code: unknown) => StateQueryErrorCode | null;
export declare const asGeneralCardanoNodeErrorCode: (code: unknown) => GeneralCardanoNodeErrorCode | null;
export declare const asTxSubmissionErrorCode: (code: unknown) => TxSubmissionErrorCode | null;
export declare const isOutsideOfValidityIntervalError: (error: unknown) => error is TxSubmissionError<OutsideOfValidityIntervalData | null>;
export declare const isValueNotConservedError: (error: unknown) => error is TxSubmissionError<ValueNotConservedData | null>;
export declare const isIncompleteWithdrawalsError: (error: unknown) => error is TxSubmissionError<IncompleteWithdrawalsData | null>;
export declare const isUnknownOutputReferences: (error: unknown) => error is TxSubmissionError<UnknownOutputReferencesData | null>;
//# sourceMappingURL=cardanoNodeErrors.d.ts.map