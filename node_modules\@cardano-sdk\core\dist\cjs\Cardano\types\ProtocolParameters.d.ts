import * as Crypto from '@cardano-sdk/crypto';
import { EpochNo, Slot } from './Block';
import { Fraction } from '.';
import { PlutusLanguageVersion } from './Script';
export interface ProtocolVersion {
    major: number;
    minor: number;
    patch?: number;
}
export declare type CostModel = Array<number>;
export declare type CostModels = Map<PlutusLanguageVersion, CostModel>;
export interface Prices {
    memory: number;
    steps: number;
}
export interface ExUnits {
    memory: number;
    steps: number;
}
export interface ValidityInterval {
    invalidBefore?: Slot;
    invalidHereafter?: Slot;
}
export interface TxFeePolicy {
    coefficient: string;
    constant: number;
}
export interface SoftforkRule {
    initThreshold: string;
    minThreshold: string;
    decrementThreshold: string;
}
declare type ProtocolParametersByron = {
    heavyDlgThreshold: string;
    maxBlockSize: number;
    maxHeaderSize: number;
    maxProposalSize: number;
    maxTxSize: number;
    mpcThreshold: string;
    scriptVersion: number;
    slotDuration: number;
    unlockStakeEpoch: number;
    updateProposalThreshold: string;
    updateProposalTimeToLive: number;
    updateVoteThreshold: string;
    txFeePolicy: TxFeePolicy;
    softforkRule: SoftforkRule;
};
declare type NewProtocolParamsInShelley = {
    minFeeCoefficient: number;
    minFeeConstant: number;
    maxBlockBodySize: number;
    maxBlockHeaderSize: number;
    stakeKeyDeposit: number;
    poolDeposit: number | null;
    poolRetirementEpochBound: number;
    desiredNumberOfPools: number;
    poolInfluence: string;
    monetaryExpansion: string;
    treasuryExpansion: string;
    decentralizationParameter: string;
    minUtxoValue: number;
    minPoolCost: number;
    extraEntropy: 'neutral' | string;
    protocolVersion: ProtocolVersion;
};
declare type ShelleyProtocolParams = Pick<ProtocolParametersByron, 'maxTxSize'> & NewProtocolParamsInShelley;
declare type NewProtocolParamsInAlonzo = {
    coinsPerUtxoWord: number;
    maxValueSize: number;
    collateralPercentage: number;
    maxCollateralInputs: number;
    costModels: CostModels;
    prices: Prices;
    maxExecutionUnitsPerTransaction: ExUnits;
    maxExecutionUnitsPerBlock: ExUnits;
};
declare type AlonzoProtocolParams = Omit<ShelleyProtocolParams, 'minUtxoValue'> & NewProtocolParamsInAlonzo;
declare type NewProtocolParamsInBabbage = {
    coinsPerUtxoByte: number;
};
declare type BabbageProtocolParameters = Omit<AlonzoProtocolParams, 'coinsPerUtxoWord' | 'extraEntropy'> & NewProtocolParamsInBabbage;
export interface PoolVotingThresholds {
    motionNoConfidence: Fraction;
    committeeNormal: Fraction;
    committeeNoConfidence: Fraction;
    hardForkInitiation: Fraction;
}
export interface DelegateRepresentativeThresholds extends PoolVotingThresholds {
    updateConstitution: Fraction;
    ppNetworkGroup: Fraction;
    ppEconomicGroup: Fraction;
    ppTechnicalGroup: Fraction;
    ppGovernanceGroup: Fraction;
    treasuryWithdrawal: Fraction;
}
declare type NewProtocolParamsInConway = {
    poolVotingThresholds: PoolVotingThresholds;
    dRepVotingThresholds: DelegateRepresentativeThresholds;
    minCommitteeSize: number;
    committeeTermLimit: number;
    governanceActionValidityPeriod: EpochNo;
    governanceActionDeposit: number;
    dRepDeposit: number;
    dRepInactivityPeriod: EpochNo;
};
declare type ConwayProtocolParameters = BabbageProtocolParameters & NewProtocolParamsInConway;
export declare type ProtocolParameters = ConwayProtocolParameters;
export declare type ProtocolParametersUpdate = Partial<ProtocolParameters & Pick<AlonzoProtocolParams, 'extraEntropy'>>;
export declare type GenesisDelegateKeyHash = Crypto.Hash28ByteBase16;
export declare type ProposedProtocolParameterUpdates = Map<GenesisDelegateKeyHash, ProtocolParametersUpdate>;
export declare type Update = {
    epoch: EpochNo;
    proposedProtocolParameterUpdates: ProposedProtocolParameterUpdates;
};
export {};
//# sourceMappingURL=ProtocolParameters.d.ts.map