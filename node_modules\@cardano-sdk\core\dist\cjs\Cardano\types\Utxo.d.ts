import * as Crypto from '@cardano-sdk/crypto';
import { PaymentAddress } from './../Address';
import { PlutusData } from './PlutusData';
import { Script } from './Script';
import { TransactionId } from './Transaction';
import { Value } from './Value';
export declare type DatumHash = Crypto.Hash32ByteBase16;
export interface TxIn {
    txId: TransactionId;
    index: number;
}
export interface HydratedTxIn extends TxIn {
    address: PaymentAddress;
}
export interface TxOut {
    address: PaymentAddress;
    value: Value;
    datumHash?: DatumHash;
    datum?: PlutusData;
    scriptReference?: Script;
}
export declare type Utxo = [HydratedTxIn, TxOut];
//# sourceMappingURL=Utxo.d.ts.map