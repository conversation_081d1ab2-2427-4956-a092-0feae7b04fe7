/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fraction.js";
exports.ids = ["vendor-chunks/fraction.js"];
exports.modules = {

/***/ "(ssr)/./node_modules/fraction.js/fraction.js":
/*!**********************************************!*\
  !*** ./node_modules/fraction.js/fraction.js ***!
  \**********************************************/
/***/ (function(module, exports) {

eval("var __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;/**\n * @license Fraction.js v4.0.1 09/09/2015\n * http://www.xarg.org/2014/03/rational-numbers-in-javascript/\n *\n * Copyright (c) 2015, Robert Eisele (<EMAIL>)\n * Dual licensed under the MIT or GPL Version 2 licenses.\n **/\n\n\n/**\n *\n * This class offers the possibility to calculate fractions.\n * You can pass a fraction in different formats. Either as array, as double, as string or as an integer.\n *\n * Array/Object form\n * [ 0 => <nominator>, 1 => <denominator> ]\n * [ n => <nominator>, d => <denominator> ]\n *\n * Integer form\n * - Single integer value\n *\n * Double form\n * - Single double value\n *\n * String form\n * 123.456 - a simple double\n * 123/456 - a string fraction\n * 123.'456' - a double with repeating decimal places\n * 123.(456) - synonym\n * 123.45'6' - a double with repeating last place\n * 123.45(6) - synonym\n *\n * Example:\n *\n * var f = new Fraction(\"9.4'31'\");\n * f.mul([-4, 3]).div(4.9);\n *\n */\n\n(function (root) {\n\n  \"use strict\";\n\n  // Maximum search depth for cyclic rational numbers. 2000 should be more than enough.\n  // Example: 1/7 = 0.(142857) has 6 repeating decimal places.\n  // If MAX_CYCLE_LEN gets reduced, long cycles will not be detected and toString() only gets the first 10 digits\n  var MAX_CYCLE_LEN = 2000;\n\n  // Parsed data to avoid calling \"new\" all the time\n  var P = {\n    \"s\": 1,\n    \"n\": 0,\n    \"d\": 1\n  };\n\n  function createError(name) {\n    var errorConstructor = function () {\n      var temp = Error.apply(this, arguments);\n      temp.name = this.name = name;\n      this.stack = temp.stack;\n      this.message = temp.message;\n    };\n\n    var IntermediateInheritor = function () {};\n    IntermediateInheritor.prototype = Error.prototype;\n    errorConstructor.prototype = new IntermediateInheritor();\n\n    return errorConstructor;\n  }\n\n  var DivisionByZero = Fraction['DivisionByZero'] = createError('DivisionByZero');\n  var InvalidParameter = Fraction['InvalidParameter'] = createError('InvalidParameter');\n\n  function assign(n, s) {\n\n    if (isNaN(n = parseInt(n, 10))) {\n      throwInvalidParam();\n    }\n    return n * s;\n  }\n\n  function throwInvalidParam() {\n    throw new InvalidParameter();\n  }\n\n  var parse = function (p1, p2) {\n\n    var n = 0, d = 1, s = 1;\n    var v = 0, w = 0, x = 0, y = 1, z = 1;\n\n    var A = 0, B = 1;\n    var C = 1, D = 1;\n\n    var N = 10000000;\n    var M;\n\n    if (p1 === undefined || p1 === null) {\n      /* void */\n    } else if (p2 !== undefined) {\n      n = p1;\n      d = p2;\n      s = n * d;\n    } else\n      switch (typeof p1) {\n\n        case \"object\":\n        {\n          if (\"d\" in p1 && \"n\" in p1) {\n            n = p1[\"n\"];\n            d = p1[\"d\"];\n            if (\"s\" in p1)\n              n*= p1[\"s\"];\n          } else if (0 in p1) {\n            n = p1[0];\n            if (1 in p1)\n              d = p1[1];\n          } else {\n            throwInvalidParam();\n          }\n          s = n * d;\n          break;\n        }\n        case \"number\":\n        {\n          if (p1 < 0) {\n            s = p1;\n            p1 = -p1;\n          }\n\n          if (p1 % 1 === 0) {\n            n = p1;\n          } else if (p1 > 0) { // check for != 0, scale would become NaN (log(0)), which converges really slow\n\n            if (p1 >= 1) {\n              z = Math.pow(10, Math.floor(1 + Math.log(p1) / Math.LN10));\n              p1/= z;\n            }\n\n            // Using Farey Sequences\n            // http://www.johndcook.com/blog/2010/10/20/best-rational-approximation/\n\n            while (B <= N && D <= N) {\n              M = (A + C) / (B + D);\n\n              if (p1 === M) {\n                if (B + D <= N) {\n                  n = A + C;\n                  d = B + D;\n                } else if (D > B) {\n                  n = C;\n                  d = D;\n                } else {\n                  n = A;\n                  d = B;\n                }\n                break;\n\n              } else {\n\n                if (p1 > M) {\n                  A+= C;\n                  B+= D;\n                } else {\n                  C+= A;\n                  D+= B;\n                }\n\n                if (B > N) {\n                  n = C;\n                  d = D;\n                } else {\n                  n = A;\n                  d = B;\n                }\n              }\n            }\n            n*= z;\n          } else if (isNaN(p1) || isNaN(p2)) {\n            d = n = NaN;\n          }\n          break;\n        }\n        case \"string\":\n        {\n          B = p1.match(/\\d+|./g);\n\n          if (B[A] === '-') {// Check for minus sign at the beginning\n            s = -1;\n            A++;\n          } else if (B[A] === '+') {// Check for plus sign at the beginning\n            A++;\n          }\n\n          if (B.length === A + 1) { // Check if it's just a simple number \"1234\"\n            w = assign(B[A++], s);\n          } else if (B[A + 1] === '.' || B[A] === '.') { // Check if it's a decimal number\n\n            if (B[A] !== '.') { // Handle 0.5 and .5\n              v = assign(B[A++], s);\n            }\n            A++;\n\n            // Check for decimal places\n            if (A + 1 === B.length || B[A + 1] === '(' && B[A + 3] === ')' || B[A + 1] === \"'\" && B[A + 3] === \"'\") {\n              w = assign(B[A], s);\n              y = Math.pow(10, B[A].length);\n              A++;\n            }\n\n            // Check for repeating places\n            if (B[A] === '(' && B[A + 2] === ')' || B[A] === \"'\" && B[A + 2] === \"'\") {\n              x = assign(B[A + 1], s);\n              z = Math.pow(10, B[A + 1].length) - 1;\n              A+= 3;\n            }\n\n          } else if (B[A + 1] === '/' || B[A + 1] === ':') { // Check for a simple fraction \"123/456\" or \"123:456\"\n            w = assign(B[A], s);\n            y = assign(B[A + 2], 1);\n            A+= 3;\n          } else if (B[A + 3] === '/' && B[A + 1] === ' ') { // Check for a complex fraction \"123 1/2\"\n            v = assign(B[A], s);\n            w = assign(B[A + 2], s);\n            y = assign(B[A + 4], 1);\n            A+= 5;\n          }\n\n          if (B.length <= A) { // Check for more tokens on the stack\n            d = y * z;\n            s = /* void */\n                    n = x + d * v + z * w;\n            break;\n          }\n\n          /* Fall through on error */\n        }\n        default:\n          throwInvalidParam();\n      }\n\n    if (d === 0) {\n      throw new DivisionByZero();\n    }\n\n    P[\"s\"] = s < 0 ? -1 : 1;\n    P[\"n\"] = Math.abs(n);\n    P[\"d\"] = Math.abs(d);\n  };\n\n  var modpow = function (b, e, m) {\n\n    for (var r = 1; e > 0; b = (b * b) % m, e >>= 1) {\n\n      if (e & 1) {\n        r = (r * b) % m;\n      }\n    }\n    return r;\n  };\n\n  var cycleLen = function (n, d) {\n\n    for (; d % 2 === 0;\n            d/= 2) {}\n\n    for (; d % 5 === 0;\n            d/= 5) {}\n\n    if (d === 1) // Catch non-cyclic numbers\n      return 0;\n\n    // If we would like to compute really large numbers quicker, we could make use of Fermat's little theorem:\n    // 10^(d-1) % d == 1\n    // However, we don't need such large numbers and MAX_CYCLE_LEN should be the capstone,\n    // as we want to translate the numbers to strings.\n\n    var rem = 10 % d;\n\n    for (var t = 1; rem !== 1; t++) {\n      rem = rem * 10 % d;\n\n      if (t > MAX_CYCLE_LEN)\n        return 0; // Returning 0 here means that we don't print it as a cyclic number. It's likely that the answer is `d-1`\n    }\n    return t;\n  };\n\n  var cycleStart = function (n, d, len) {\n\n    var rem1 = 1;\n    var rem2 = modpow(10, len, d);\n\n    for (var t = 0; t < 300; t++) { // s < ~log10(Number.MAX_VALUE)\n      // Solve 10^s == 10^(s+t) (mod d)\n\n      if (rem1 === rem2)\n        return t;\n\n      rem1 = rem1 * 10 % d;\n      rem2 = rem2 * 10 % d;\n    }\n    return 0;\n  };\n\n  var gcd = function (a, b) {\n\n    if (!a) return b;\n    if (!b) return a;\n\n    while (1) {\n      a%= b;\n      if (!a) return b;\n      b%= a;\n      if (!b) return a;\n    }\n  };\n\n  /**\n   * Module constructor\n   *\n   * @constructor\n   * @param {number|Fraction} a\n   * @param {number=} b\n   */\n  function Fraction(a, b) {\n\n    if (!(this instanceof Fraction)) {\n      return new Fraction(a, b);\n    }\n\n    parse(a, b);\n\n    if (Fraction['REDUCE']) {\n      a = gcd(P[\"d\"], P[\"n\"]); // Abuse a\n    } else {\n      a = 1;\n    }\n\n    this[\"s\"] = P[\"s\"];\n    this[\"n\"] = P[\"n\"] / a;\n    this[\"d\"] = P[\"d\"] / a;\n  }\n\n  /**\n   * Boolean global variable to be able to disable automatic reduction of the fraction\n   *\n   */\n  Fraction['REDUCE'] = 1;\n\n  Fraction.prototype = {\n\n    \"s\": 1,\n    \"n\": 0,\n    \"d\": 1,\n\n    /**\n     * Calculates the absolute value\n     *\n     * Ex: new Fraction(-4).abs() => 4\n     **/\n    \"abs\": function () {\n\n      return new Fraction(this[\"n\"], this[\"d\"]);\n    },\n\n    /**\n     * Inverts the sign of the current fraction\n     *\n     * Ex: new Fraction(-4).neg() => 4\n     **/\n    \"neg\": function () {\n\n      return new Fraction(-this[\"s\"] * this[\"n\"], this[\"d\"]);\n    },\n\n    /**\n     * Adds two rational numbers\n     *\n     * Ex: new Fraction({n: 2, d: 3}).add(\"14.9\") => 467 / 30\n     **/\n    \"add\": function (a, b) {\n\n      parse(a, b);\n      return new Fraction(\n              this[\"s\"] * this[\"n\"] * P[\"d\"] + P[\"s\"] * this[\"d\"] * P[\"n\"],\n              this[\"d\"] * P[\"d\"]\n              );\n    },\n\n    /**\n     * Subtracts two rational numbers\n     *\n     * Ex: new Fraction({n: 2, d: 3}).add(\"14.9\") => -427 / 30\n     **/\n    \"sub\": function (a, b) {\n\n      parse(a, b);\n      return new Fraction(\n              this[\"s\"] * this[\"n\"] * P[\"d\"] - P[\"s\"] * this[\"d\"] * P[\"n\"],\n              this[\"d\"] * P[\"d\"]\n              );\n    },\n\n    /**\n     * Multiplies two rational numbers\n     *\n     * Ex: new Fraction(\"-17.(345)\").mul(3) => 5776 / 111\n     **/\n    \"mul\": function (a, b) {\n\n      parse(a, b);\n      return new Fraction(\n              this[\"s\"] * P[\"s\"] * this[\"n\"] * P[\"n\"],\n              this[\"d\"] * P[\"d\"]\n              );\n    },\n\n    /**\n     * Divides two rational numbers\n     *\n     * Ex: new Fraction(\"-17.(345)\").inverse().div(3)\n     **/\n    \"div\": function (a, b) {\n\n      parse(a, b);\n      return new Fraction(\n              this[\"s\"] * P[\"s\"] * this[\"n\"] * P[\"d\"],\n              this[\"d\"] * P[\"n\"]\n              );\n    },\n\n    /**\n     * Clones the actual object\n     *\n     * Ex: new Fraction(\"-17.(345)\").clone()\n     **/\n    \"clone\": function () {\n      return new Fraction(this);\n    },\n\n    /**\n     * Calculates the modulo of two rational numbers - a more precise fmod\n     *\n     * Ex: new Fraction('4.(3)').mod([7, 8]) => (13/3) % (7/8) = (5/6)\n     **/\n    \"mod\": function (a, b) {\n\n      if (isNaN(this['n']) || isNaN(this['d'])) {\n        return new Fraction(NaN);\n      }\n\n      if (a === undefined) {\n        return new Fraction(this[\"s\"] * this[\"n\"] % this[\"d\"], 1);\n      }\n\n      parse(a, b);\n      if (0 === P[\"n\"] && 0 === this[\"d\"]) {\n        Fraction(0, 0); // Throw DivisionByZero\n      }\n\n      /*\n       * First silly attempt, kinda slow\n       *\n       return that[\"sub\"]({\n       \"n\": num[\"n\"] * Math.floor((this.n / this.d) / (num.n / num.d)),\n       \"d\": num[\"d\"],\n       \"s\": this[\"s\"]\n       });*/\n\n      /*\n       * New attempt: a1 / b1 = a2 / b2 * q + r\n       * => b2 * a1 = a2 * b1 * q + b1 * b2 * r\n       * => (b2 * a1 % a2 * b1) / (b1 * b2)\n       */\n      return new Fraction(\n              (this[\"s\"] * P[\"d\"] * this[\"n\"]) % (P[\"n\"] * this[\"d\"]),\n              P[\"d\"] * this[\"d\"]\n              );\n    },\n\n    /**\n     * Calculates the fractional gcd of two rational numbers\n     *\n     * Ex: new Fraction(5,8).gcd(3,7) => 1/56\n     */\n    \"gcd\": function (a, b) {\n\n      parse(a, b);\n\n      // gcd(a / b, c / d) = gcd(a, c) / lcm(b, d)\n\n      return new Fraction(gcd(P[\"n\"], this[\"n\"]), P[\"d\"] * this[\"d\"] / gcd(P[\"d\"], this[\"d\"]));\n    },\n\n    /**\n     * Calculates the fractional lcm of two rational numbers\n     *\n     * Ex: new Fraction(5,8).lcm(3,7) => 15\n     */\n    \"lcm\": function (a, b) {\n\n      parse(a, b);\n\n      // lcm(a / b, c / d) = lcm(a, c) / gcd(b, d)\n\n      if (P[\"n\"] === 0 && this[\"n\"] === 0) {\n        return new Fraction;\n      }\n      return new Fraction(P[\"n\"] * this[\"n\"] / gcd(P[\"n\"], this[\"n\"]), gcd(P[\"d\"], this[\"d\"]));\n    },\n\n    /**\n     * Calculates the ceil of a rational number\n     *\n     * Ex: new Fraction('4.(3)').ceil() => (5 / 1)\n     **/\n    \"ceil\": function (places) {\n\n      places = Math.pow(10, places || 0);\n\n      if (isNaN(this[\"n\"]) || isNaN(this[\"d\"])) {\n        return new Fraction(NaN);\n      }\n      return new Fraction(Math.ceil(places * this[\"s\"] * this[\"n\"] / this[\"d\"]), places);\n    },\n\n    /**\n     * Calculates the floor of a rational number\n     *\n     * Ex: new Fraction('4.(3)').floor() => (4 / 1)\n     **/\n    \"floor\": function (places) {\n\n      places = Math.pow(10, places || 0);\n\n      if (isNaN(this[\"n\"]) || isNaN(this[\"d\"])) {\n        return new Fraction(NaN);\n      }\n      return new Fraction(Math.floor(places * this[\"s\"] * this[\"n\"] / this[\"d\"]), places);\n    },\n\n    /**\n     * Rounds a rational numbers\n     *\n     * Ex: new Fraction('4.(3)').round() => (4 / 1)\n     **/\n    \"round\": function (places) {\n\n      places = Math.pow(10, places || 0);\n\n      if (isNaN(this[\"n\"]) || isNaN(this[\"d\"])) {\n        return new Fraction(NaN);\n      }\n      return new Fraction(Math.round(places * this[\"s\"] * this[\"n\"] / this[\"d\"]), places);\n    },\n\n    /**\n     * Gets the inverse of the fraction, means numerator and denumerator are exchanged\n     *\n     * Ex: new Fraction([-3, 4]).inverse() => -4 / 3\n     **/\n    \"inverse\": function () {\n\n      return new Fraction(this[\"s\"] * this[\"d\"], this[\"n\"]);\n    },\n\n    /**\n     * Calculates the fraction to some integer exponent\n     *\n     * Ex: new Fraction(-1,2).pow(-3) => -8\n     */\n    \"pow\": function (m) {\n\n      if (m < 0) {\n        return new Fraction(Math.pow(this['s'] * this[\"d\"], -m), Math.pow(this[\"n\"], -m));\n      } else {\n        return new Fraction(Math.pow(this['s'] * this[\"n\"], m), Math.pow(this[\"d\"], m));\n      }\n    },\n\n    /**\n     * Check if two rational numbers are the same\n     *\n     * Ex: new Fraction(19.6).equals([98, 5]);\n     **/\n    \"equals\": function (a, b) {\n\n      parse(a, b);\n      return this[\"s\"] * this[\"n\"] * P[\"d\"] === P[\"s\"] * P[\"n\"] * this[\"d\"]; // Same as compare() === 0\n    },\n\n    /**\n     * Check if two rational numbers are the same\n     *\n     * Ex: new Fraction(19.6).equals([98, 5]);\n     **/\n    \"compare\": function (a, b) {\n\n      parse(a, b);\n      var t = (this[\"s\"] * this[\"n\"] * P[\"d\"] - P[\"s\"] * P[\"n\"] * this[\"d\"]);\n      return (0 < t) - (t < 0);\n    },\n\n    /**\n     * Check if two rational numbers are divisible\n     *\n     * Ex: new Fraction(19.6).divisible(1.5);\n     */\n    \"divisible\": function (a, b) {\n\n      parse(a, b);\n      return !(!(P[\"n\"] * this[\"d\"]) || ((this[\"n\"] * P[\"d\"]) % (P[\"n\"] * this[\"d\"])));\n    },\n\n    /**\n     * Returns a decimal representation of the fraction\n     *\n     * Ex: new Fraction(\"100.'91823'\").valueOf() => 100.91823918239183\n     **/\n    'valueOf': function () {\n\n      return this[\"s\"] * this[\"n\"] / this[\"d\"];\n    },\n\n    /**\n     * Returns a string-fraction representation of a Fraction object\n     *\n     * Ex: new Fraction(\"1.'3'\").toFraction() => \"4 1/3\"\n     **/\n    'toFraction': function (excludeWhole) {\n\n      var whole, str = \"\";\n      var n = this[\"n\"];\n      var d = this[\"d\"];\n      if (this[\"s\"] < 0) {\n        str+= '-';\n      }\n\n      if (d === 1) {\n        str+= n;\n      } else {\n\n        if (excludeWhole && (whole = Math.floor(n / d)) > 0) {\n          str+= whole;\n          str+= \" \";\n          n%= d;\n        }\n\n        str+= n;\n        str+= '/';\n        str+= d;\n      }\n      return str;\n    },\n\n    /**\n     * Returns a latex representation of a Fraction object\n     *\n     * Ex: new Fraction(\"1.'3'\").toLatex() => \"\\frac{4}{3}\"\n     **/\n    'toLatex': function (excludeWhole) {\n\n      var whole, str = \"\";\n      var n = this[\"n\"];\n      var d = this[\"d\"];\n      if (this[\"s\"] < 0) {\n        str+= '-';\n      }\n\n      if (d === 1) {\n        str+= n;\n      } else {\n\n        if (excludeWhole && (whole = Math.floor(n / d)) > 0) {\n          str+= whole;\n          n%= d;\n        }\n\n        str+= \"\\\\frac{\";\n        str+= n;\n        str+= '}{';\n        str+= d;\n        str+= '}';\n      }\n      return str;\n    },\n\n    /**\n     * Returns an array of continued fraction elements\n     *\n     * Ex: new Fraction(\"7/8\").toContinued() => [0,1,7]\n     */\n    'toContinued': function () {\n\n      var t;\n      var a = this['n'];\n      var b = this['d'];\n      var res = [];\n\n      do {\n        res.push(Math.floor(a / b));\n        t = a % b;\n        a = b;\n        b = t;\n      } while (a !== 1);\n\n      return res;\n    },\n\n    /**\n     * Creates a string representation of a fraction with all digits\n     *\n     * Ex: new Fraction(\"100.'91823'\").toString() => \"100.(91823)\"\n     **/\n    'toString': function () {\n\n      var g;\n      var N = this[\"n\"];\n      var D = this[\"d\"];\n\n      if (isNaN(N) || isNaN(D)) {\n        return \"NaN\";\n      }\n\n      if (!Fraction['REDUCE']) {\n        g = gcd(N, D);\n        N/= g;\n        D/= g;\n      }\n\n      var dec = 15; // 15 = decimal places when no repitation\n\n      var cycLen = cycleLen(N, D); // Cycle length\n      var cycOff = cycleStart(N, D, cycLen); // Cycle start\n\n      var str = this['s'] === -1 ? \"-\" : \"\";\n\n      str+= N / D | 0;\n\n      N%= D;\n      N*= 10;\n\n      if (N)\n        str+= \".\";\n\n      if (cycLen) {\n\n        for (var i = cycOff; i--; ) {\n          str+= N / D | 0;\n          N%= D;\n          N*= 10;\n        }\n        str+= \"(\";\n        for (var i = cycLen; i--; ) {\n          str+= N / D | 0;\n          N%= D;\n          N*= 10;\n        }\n        str+= \")\";\n      } else {\n        for (var i = dec; N && i--; ) {\n          str+= N / D | 0;\n          N%= D;\n          N*= 10;\n        }\n      }\n      return str;\n    }\n  };\n\n  if (true) {\n    !(__WEBPACK_AMD_DEFINE_ARRAY__ = [], __WEBPACK_AMD_DEFINE_RESULT__ = (function () {\n      return Fraction;\n    }).apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n  } else {}\n\n})(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fraction.js/fraction.js\n");

/***/ })

};
;