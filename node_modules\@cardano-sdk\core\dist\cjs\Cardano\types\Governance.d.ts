import * as Crypto from '@cardano-sdk/crypto';
import { Credential, CredentialType, RewardAccount } from '../Address';
import { EpochNo, Fraction, ProtocolVersion, TransactionId } from '.';
import { Lovelace } from './Value';
import { ProtocolParametersUpdate } from './ProtocolParameters';
export declare type Anchor = {
    url: string;
    dataHash: Crypto.Hash32ByteBase16;
};
export declare enum GovernanceActionType {
    parameter_change_action = "parameter_change_action",
    hard_fork_initiation_action = "hard_fork_initiation_action",
    treasury_withdrawals_action = "treasury_withdrawals_action",
    no_confidence = "no_confidence",
    update_committee = "update_committee",
    new_constitution = "new_constitution",
    info_action = "info_action"
}
export declare type GovernanceActionId = {
    id: TransactionId;
    actionIndex: number;
};
export declare type CommitteeMember = {
    coldCredential: Credential;
    epoch: EpochNo;
};
export declare type Committee = {
    members: Array<CommitteeMember>;
    quorumThreshold: Fraction;
};
export declare type Constitution = {
    anchor: Anchor;
    scriptHash: Crypto.Hash28ByteBase16 | null;
};
export declare type ParameterChangeAction = {
    __typename: GovernanceActionType.parameter_change_action;
    governanceActionId: GovernanceActionId | null;
    protocolParamUpdate: ProtocolParametersUpdate;
    policyHash: Crypto.Hash28ByteBase16 | null;
};
export declare type HardForkInitiationAction = {
    __typename: GovernanceActionType.hard_fork_initiation_action;
    governanceActionId: GovernanceActionId | null;
    protocolVersion: Pick<ProtocolVersion, 'major' | 'minor'>;
};
export declare type TreasuryWithdrawalsAction = {
    __typename: GovernanceActionType.treasury_withdrawals_action;
    withdrawals: Set<{
        rewardAccount: RewardAccount;
        coin: Lovelace;
    }>;
    policyHash: Crypto.Hash28ByteBase16 | null;
};
export declare type NoConfidence = {
    __typename: GovernanceActionType.no_confidence;
    governanceActionId: GovernanceActionId | null;
};
export declare type UpdateCommittee = {
    __typename: GovernanceActionType.update_committee;
    governanceActionId: GovernanceActionId | null;
    membersToBeRemoved: Set<Credential>;
    membersToBeAdded: Set<CommitteeMember>;
    newQuorumThreshold: Fraction;
};
export declare type NewConstitution = {
    __typename: GovernanceActionType.new_constitution;
    governanceActionId: GovernanceActionId | null;
    constitution: Constitution;
};
export declare type InfoAction = {
    __typename: GovernanceActionType.info_action;
};
export declare type GovernanceAction = ParameterChangeAction | HardForkInitiationAction | TreasuryWithdrawalsAction | NoConfidence | UpdateCommittee | NewConstitution | InfoAction;
export declare enum Vote {
    no = 0,
    yes = 1,
    abstain = 2
}
export declare type VotingProcedure = {
    vote: Vote;
    anchor: Anchor | null;
};
export declare enum VoterType {
    ccHotKeyHash = "ccHotKeyHash",
    ccHotScriptHash = "ccHotScriptHash",
    dRepKeyHash = "dRepKeyHash",
    dRepScriptHash = "dRepScriptHash",
    stakePoolKeyHash = "stakePoolKeyHash"
}
export declare type ConstitutionalCommitteeKeyHashVoter = {
    __typename: VoterType.ccHotKeyHash;
    credential: {
        type: CredentialType.KeyHash;
        hash: Credential['hash'];
    };
};
export declare type ConstitutionalCommitteeScriptHashVoter = {
    __typename: VoterType.ccHotScriptHash;
    credential: {
        type: CredentialType.ScriptHash;
        hash: Credential['hash'];
    };
};
export declare type DrepKeyHashVoter = {
    __typename: VoterType.dRepKeyHash;
    credential: {
        type: CredentialType.KeyHash;
        hash: Credential['hash'];
    };
};
export declare type DrepScriptHashVoter = {
    __typename: VoterType.dRepScriptHash;
    credential: {
        type: CredentialType.ScriptHash;
        hash: Credential['hash'];
    };
};
export declare type StakePoolKeyHashVoter = {
    __typename: VoterType.stakePoolKeyHash;
    credential: {
        type: CredentialType.KeyHash;
        hash: Credential['hash'];
    };
};
export declare type Voter = ConstitutionalCommitteeKeyHashVoter | ConstitutionalCommitteeScriptHashVoter | DrepKeyHashVoter | DrepScriptHashVoter | StakePoolKeyHashVoter;
export declare type VotingProcedureVote = {
    actionId: GovernanceActionId;
    votingProcedure: VotingProcedure;
};
export declare type VotingProcedures = Array<{
    voter: Voter;
    votes: VotingProcedureVote[];
}>;
export declare type ProposalProcedure = {
    deposit: Lovelace;
    rewardAccount: RewardAccount;
    governanceAction: GovernanceAction;
    anchor: Anchor;
};
export declare type AlwaysAbstain = {
    __typename: 'AlwaysAbstain';
};
export declare type AlwaysNoConfidence = {
    __typename: 'AlwaysNoConfidence';
};
export declare type DelegateRepresentative = Credential | AlwaysAbstain | AlwaysNoConfidence;
export declare const isDRepCredential: (deleg: DelegateRepresentative) => deleg is Credential;
export declare const isDRepAlwaysAbstain: (deleg: DelegateRepresentative) => deleg is AlwaysAbstain;
export declare const isDRepAlwaysNoConfidence: (deleg: DelegateRepresentative) => deleg is AlwaysNoConfidence;
//# sourceMappingURL=Governance.d.ts.map