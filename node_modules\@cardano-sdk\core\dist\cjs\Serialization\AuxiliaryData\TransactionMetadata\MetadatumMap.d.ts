import { HexBlob } from '@cardano-sdk/util';
import { MetadatumList } from './MetadatumList';
import { TransactionMetadatum } from './TransactionMetadatum';
export declare class MetadatumMap {
    #private;
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): MetadatumMap;
    getLength(): number;
    insert(key: TransactionMetadatum, value: TransactionMetadatum): void;
    get(key: TransactionMetadatum): TransactionMetadatum | undefined;
    getKeys(): MetadatumList;
    equals(other: MetadatumMap): boolean;
}
//# sourceMappingURL=MetadatumMap.d.ts.map