import { OpaqueString } from '@cardano-sdk/util';
export declare type AssetId = OpaqueString<'AssetId'>;
export declare type AssetName = OpaqueString<'AssetName'>;
export declare const AssetName: {
    (value: string): AssetName;
    toUTF8(assetName: AssetName, stripInvisibleCharacters?: boolean): string;
};
export declare const AssetId: {
    (value: string): AssetId;
    getPolicyId(id: AssetId): PolicyId;
    getAssetName(id: AssetId): AssetName;
    fromParts(policyId: PolicyId, assetName: AssetName): AssetId;
    getAssetNameAsText(id: AssetId): string;
};
export declare type PolicyId = OpaqueString<'PolicyId'>;
export declare const PolicyId: (value: string) => PolicyId;
export declare type AssetFingerprint = OpaqueString<'AssetFingerprint'>;
export declare const AssetFingerprint: {
    (value: string): AssetFingerprint;
    fromParts(policyId: PolicyId, assetName: AssetName): AssetFingerprint;
};
//# sourceMappingURL=Asset.d.ts.map