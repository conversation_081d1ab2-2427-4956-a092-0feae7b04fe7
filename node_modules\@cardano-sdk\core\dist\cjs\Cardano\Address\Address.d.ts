import { <PERSON>Address } from './BaseAddress';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ddressContent } from './ByronAddress';
import { EnterpriseAddress } from './EnterpriseAddress';
import { Hash28ByteBase16 } from '@cardano-sdk/crypto';
import { HexBlob } from '@cardano-sdk/util';
import { NetworkId } from '../ChainId';
import { PaymentAddress } from './PaymentAddress';
import { Pointer, PointerAddress } from './PointerAddress';
import { RewardAccount } from './RewardAccount';
import { RewardAddress } from './RewardAddress';
export declare enum AddressType {
    BasePaymentKeyStakeKey = 0,
    BasePaymentScriptStakeKey = 1,
    BasePaymentKeyStakeScript = 2,
    BasePaymentScriptStakeScript = 3,
    PointerKey = 4,
    PointerScript = 5,
    EnterpriseKey = 6,
    EnterpriseScript = 7,
    Byron = 8,
    RewardKey = 14,
    RewardScript = 15
}
export declare enum CredentialType {
    KeyHash = 0,
    ScriptHash = 1
}
export declare type Credential = {
    type: CredentialType;
    hash: Hash28ByteBase16;
};
export declare type AddressHeader = {
    type: AddressType;
    networkId: NetworkId;
};
export declare type AddressProps = {
    type: AddressType;
    networkId?: NetworkId;
    pointer?: Pointer;
    paymentPart?: Credential;
    delegationPart?: Credential;
    byronAddressContent?: ByronAddressContent;
};
export declare class Address {
    #private;
    constructor(props: AddressProps);
    static fromBytes(hex: HexBlob): Address;
    toBytes(): HexBlob;
    static fromBase58(base58Address: string): Address;
    toBase58(): PaymentAddress;
    toBech32(): PaymentAddress | RewardAccount;
    static fromBech32(bech32: string): Address;
    static fromString(address: string): Address | null;
    static isValidBech32(bech32: string): boolean;
    static isValidByron(base58: string): boolean;
    static isValid(address: string): boolean;
    asByron(): ByronAddress | undefined;
    asReward(): RewardAddress | undefined;
    asPointer(): PointerAddress | undefined;
    asEnterprise(): EnterpriseAddress | undefined;
    asBase(): BaseAddress | undefined;
    getType(): AddressType;
    getNetworkId(): NetworkId;
    getProps(): AddressProps;
    private static getBech32Prefix;
}
export declare const isAddress: (input: string) => boolean;
//# sourceMappingURL=Address.d.ts.map