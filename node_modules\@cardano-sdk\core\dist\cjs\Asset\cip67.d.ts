import * as Cardano from '../Cardano';
import { OpaqueNumber } from '@cardano-sdk/util';
export declare type AssetNameLabel = OpaqueNumber<'AssetNameLabelNum'>;
export declare const AssetNameLabel: {
    (value: number): AssetNameLabel;
    decode(assetName: Cardano.AssetName): DecodedAssetName | null;
    encode(assetName: Cardano.AssetName, labelNum: AssetNameLabel): Cardano.AssetName;
};
export declare const AssetNameLabelNum: {
    ReferenceNFT: AssetNameLabel;
    UserFT: AssetNameLabel;
    UserNFT: AssetNameLabel;
    UserRFT: AssetNameLabel;
    VirtualHandle: AssetNameLabel;
};
export interface DecodedAssetName {
    label: AssetNameLabel;
    content: Cardano.AssetName;
}
//# sourceMappingURL=cip67.d.ts.map