/// <reference types="lodash" />
import { CustomError } from 'ts-custom-error';
import { EpochNo, Slot } from '../Cardano/types/Block.js';
import type { EraSummary } from '../CardanoNode/index.js';
import type { NetworkInfoProvider } from '../Provider/NetworkInfoProvider/index.js';
import type { NetworkMagics } from '../Cardano/index.js';
export interface SlotDate {
    slot: Slot;
    date: Date;
}
export interface EpochInfo {
    epochNo: EpochNo;
    firstSlot: SlotDate;
    lastSlot: SlotDate;
}
export declare class EraSummaryError extends CustomError {
}
export declare type EraSummariesMap = {
    [key in NetworkMagics]: EraSummary[];
};
export declare const createSlotEpochCalc: (eraSummaries: EraSummary[]) => (slotNo: Slot) => EpochNo;
export declare const createSlotTimeCalc: (eraSummaries: EraSummary[]) => (slotNo: Slot) => Date;
export declare const createSlotEpochInfoCalc: (eraSummaries: EraSummary[]) => (slot: Slot) => EpochInfo;
export declare const epochSlotsCalc: ((epochNo: EpochNo, eraSummaries: EraSummary[]) => {
    eraSummary: EraSummary;
    firstSlot: Slot;
    lastSlot: Slot;
}) & import("lodash").MemoizedFunction;
export declare const epochSlotsCalcFactory: ((provider: NetworkInfoProvider) => ((epochNo: EpochNo) => Promise<{
    eraSummary: EraSummary;
    firstSlot: Slot;
    lastSlot: Slot;
}>) & import("lodash").MemoizedFunction) & import("lodash").MemoizedFunction;
export declare type SlotTimeCalc = ReturnType<typeof createSlotTimeCalc>;
export declare type SlotEpochCalc = ReturnType<typeof createSlotEpochCalc>;
export declare type SlotEpochInfoCalc = ReturnType<typeof createSlotEpochInfoCalc>;
//# sourceMappingURL=slotCalc.d.ts.map