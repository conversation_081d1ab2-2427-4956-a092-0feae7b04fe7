import { HexBlob } from '@cardano-sdk/util';
import { Redeemer } from './Redeemer.js';
import type { Cardano } from '../../../index.js';
export declare class Redeemers {
    #private;
    private constructor();
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): Redeemers;
    toCore(): Cardano.Redeemer[];
    static fromCore(redeemers: Cardano.Redeemer[]): Redeemers;
    values(): readonly Redeemer[];
    setValues(redeemers: Redeemer[]): void;
    size(): number;
}
//# sourceMappingURL=Redeemers.d.ts.map