{"version": 3, "file": "UpdateDelegateRepresentative.js", "sourceRoot": "", "sources": ["../../../../src/Serialization/Certificates/UpdateDelegateRepresentative.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,4DAA8C;AAC9C,sCAAmC;AACnC,kCAAkE;AAClE,uDAAoD;AACpD,2CAAgD;AAChD,4CAAkE;AAClE,0CAA6C;AAE7C,MAAM,mBAAmB,GAAG,CAAC,CAAC;AAG9B,MAAa,4BAA4B;IAWvC,YAAY,cAAkC,EAAE,MAAe;QAV/D,+DAAoC;QACpC,uDAA4B;QAC5B,sDAAsC,SAAS,EAAC;QAS9C,uBAAA,IAAI,gDAAmB,cAAc,MAAA,CAAC;QACtC,uBAAA,IAAI,wCAAW,MAAM,MAAA,CAAC;IACxB,CAAC;IAOD,MAAM;QACJ,MAAM,MAAM,GAAG,IAAI,iBAAU,EAAE,CAAC;QAEhC,IAAI,uBAAA,IAAI,mDAAe;YAAE,OAAO,uBAAA,IAAI,mDAAe,CAAC;QAIpD,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;QAE1B,MAAM,CAAC,QAAQ,CAAC,iCAAe,CAAC,UAAU,CAAC,CAAC;QAO5C,MAAM,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC;QAC5C,MAAM,CAAC,QAAQ,CAAC,uBAAA,IAAI,oDAAgB,CAAC,IAAI,CAAC,CAAC;QAC3C,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAA,IAAI,oDAAgB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;QAEtE,IAAI,uBAAA,IAAI,4CAAQ,EAAE;YAChB,MAAM,CAAC,iBAAiB,CAAC,IAAA,iBAAU,EAAC,uBAAA,IAAI,4CAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;SAC7D;aAAM;YACL,MAAM,CAAC,SAAS,EAAE,CAAC;SACpB;QAED,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC;IAC9B,CAAC;IAQD,MAAM,CAAC,QAAQ,CAAC,IAAa;QAC3B,MAAM,MAAM,GAAG,IAAI,iBAAU,CAAC,IAAI,CAAC,CAAC;QAEpC,MAAM,MAAM,GAAG,MAAM,CAAC,cAAc,EAAE,CAAC;QAEvC,IAAI,MAAM,KAAK,CAAC;YACd,MAAM,IAAI,2BAAoB,CAC5B,MAAM,EACN,wBAAwB,mBAAmB,kCAAkC,MAAM,WAAW,CAC/F,CAAC;QAEJ,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;QAEtC,IAAI,IAAI,KAAK,iCAAe,CAAC,UAAU;YACrC,MAAM,IAAI,2BAAoB,CAC5B,MAAM,EACN,6BAA6B,iCAAe,CAAC,UAAU,aAAa,IAAI,EAAE,CAC3E,CAAC;QAEJ,MAAM,UAAU,GAAG,MAAM,CAAC,cAAc,EAAE,CAAC;QAE3C,IAAI,UAAU,KAAK,mBAAmB;YACpC,MAAM,IAAI,2BAAoB,CAC5B,MAAM,EACN,wBAAwB,mBAAmB,kCAAkC,MAAM,WAAW,CAC/F,CAAC;QAEJ,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,CAA2B,CAAC;QAChE,MAAM,IAAI,GAAG,MAAM,CAAC,gBAAgB,CAAC,cAAO,CAAC,SAAS,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;QAEjF,MAAM,CAAC,YAAY,EAAE,CAAC;QAEtB,IAAI,MAAM,CAAC;QAEX,IAAI,MAAM,CAAC,SAAS,EAAE,KAAK,sBAAe,CAAC,IAAI,EAAE;YAC/C,MAAM,CAAC,QAAQ,EAAE,CAAC;SACnB;aAAM;YACL,MAAM,GAAG,eAAM,CAAC,QAAQ,CAAC,cAAO,CAAC,SAAS,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;SACxE;QAED,MAAM,CAAC,YAAY,EAAE,CAAC;QAEtB,MAAM,IAAI,GAAG,IAAI,4BAA4B,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC;QACtE,uBAAA,IAAI,+CAAkB,IAAI,MAAA,CAAC;QAE3B,OAAO,IAAI,CAAC;IACd,CAAC;IAOD,MAAM;QACJ,OAAO;YACL,UAAU,EAAE,yBAAe,CAAC,4BAA4B;YACxD,MAAM,EAAE,uBAAA,IAAI,4CAAQ,CAAC,CAAC,CAAC,uBAAA,IAAI,4CAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,IAAI;YACnD,cAAc,EAAE,uBAAA,IAAI,oDAAgB;SACrC,CAAC;IACJ,CAAC;IAOD,MAAM,CAAC,QAAQ,CAAC,IAAqD;QACnE,OAAO,IAAI,4BAA4B,CACrC,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,eAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CACvD,CAAC;IACJ,CAAC;IAOD,UAAU;QACR,OAAO,uBAAA,IAAI,oDAAgB,CAAC;IAC9B,CAAC;IAOD,MAAM;QACJ,OAAO,uBAAA,IAAI,4CAAQ,CAAC;IACtB,CAAC;CACF;AAlJD,oEAkJC"}