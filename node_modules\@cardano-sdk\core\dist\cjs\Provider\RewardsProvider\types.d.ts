import { Cardano } from '../..';
import { Provider } from '../Provider';
import { Range } from '@cardano-sdk/util';
export interface Reward {
    epoch: Cardano.EpochNo;
    rewards: Cardano.Lovelace;
    poolId?: Cardano.PoolId;
}
export interface RewardsHistoryArgs {
    rewardAccounts: Cardano.RewardAccount[];
    epochs?: Range<Cardano.EpochNo>;
}
export interface RewardAccountBalanceArgs {
    rewardAccount: Cardano.RewardAccount;
}
export interface RewardsProvider extends Provider {
    rewardsHistory: (args: RewardsHistoryArgs) => Promise<Map<Cardano.RewardAccount, Reward[]>>;
    rewardAccountBalance: (args: RewardAccountBalanceArgs) => Promise<Cardano.Lovelace>;
}
//# sourceMappingURL=types.d.ts.map