import { ConstrPlutusData, PlutusData, PlutusList, PlutusMap } from '../types';
import { Logger } from 'ts-log';
export declare const isPlutusBoundedBytes: (plutusData: unknown) => plutusData is Uint8Array;
export declare const isAnyPlutusDataCollection: (plutusData: unknown) => plutusData is PlutusList | PlutusMap | ConstrPlutusData;
export declare const isPlutusList: (plutusData: unknown) => plutusData is PlutusList;
export declare const isPlutusMap: (plutusData: unknown) => plutusData is PlutusMap;
export declare const isConstrPlutusData: (plutusData: unknown) => plutusData is ConstrPlutusData;
export declare const isPlutusBigInt: (plutusData: unknown) => plutusData is bigint;
export declare const tryConvertPlutusMapToUtf8Record: (map: PlutusMap, logger: Logger) => Partial<Record<string, string | PlutusData>>;
//# sourceMappingURL=plutusDataUtils.d.ts.map