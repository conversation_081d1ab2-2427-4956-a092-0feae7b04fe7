import * as Cardano from '../../Cardano';
import { Anchor } from '../Common';
import { HexBlob } from '@cardano-sdk/util';
export declare class RegisterDelegateRepresentative {
    #private;
    constructor(drepCredential: Cardano.Credential, deposit: Cardano.Lovelace, anchor?: Anchor);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): RegisterDelegateRepresentative;
    toCore(): Cardano.RegisterDelegateRepresentativeCertificate;
    static fromCore(cert: Cardano.RegisterDelegateRepresentativeCertificate): RegisterDelegateRepresentative;
    credential(): Cardano.Credential;
    deposit(): Cardano.Lovelace;
    anchor(): Anchor | undefined;
}
//# sourceMappingURL=RegisterDelegateRepresentative.d.ts.map