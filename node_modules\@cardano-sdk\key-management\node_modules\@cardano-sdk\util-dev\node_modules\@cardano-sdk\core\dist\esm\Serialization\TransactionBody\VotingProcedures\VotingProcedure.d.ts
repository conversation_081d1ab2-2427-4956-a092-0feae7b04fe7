import { Anchor } from '../../Common/Anchor.js';
import { HexBlob } from '@cardano-sdk/util';
import type * as Cardano from '../../../Cardano/index.js';
export declare class VotingProcedure {
    #private;
    constructor(vote: Cardano.Vote, anchor?: Anchor);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): VotingProcedure;
    toCore(): Cardano.VotingProcedure;
    static fromCore(votingProcedure: Cardano.VotingProcedure): VotingProcedure;
    vote(): Cardano.Vote;
    setVote(vote: Cardano.Vote): void;
    anchor(): Anchor | undefined;
    setAnchor(anchor: Anchor | undefined): void;
}
//# sourceMappingURL=VotingProcedure.d.ts.map