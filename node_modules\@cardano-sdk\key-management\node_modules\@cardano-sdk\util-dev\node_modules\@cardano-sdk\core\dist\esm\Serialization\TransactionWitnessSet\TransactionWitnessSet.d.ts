import { BootstrapWitness } from './BootstrapWitness.js';
import { CborSet } from '../Common/index.js';
import { HexBlob } from '@cardano-sdk/util';
import { NativeScript, PlutusV1Script, PlutusV2Script, PlutusV3Script } from '../Scripts/index.js';
import { PlutusData } from '../PlutusData/PlutusData.js';
import { Redeemers } from './Redeemer/index.js';
import { VkeyWitness } from './VkeyWitness.js';
import type * as Cardano from '../../Cardano/index.js';
export declare class TransactionWitnessSet {
    #private;
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): TransactionWitnessSet;
    toCore(): Cardano.Witness;
    static fromCore(coreWitness: Cardano.Witness): TransactionWitnessSet;
    setVkeys(vkeys: CborSet<ReturnType<VkeyWitness['toCore']>, VkeyWitness>): void;
    vkeys(): CborSet<[import("@cardano-sdk/crypto").Ed25519PublicKeyHex, import("@cardano-sdk/crypto").Ed25519SignatureHex], VkeyWitness> | undefined;
    setNativeScripts(nativeScripts: CborSet<ReturnType<NativeScript['toCore']>, NativeScript>): void;
    nativeScripts(): CborSet<Cardano.NativeScript, NativeScript> | undefined;
    setBootstraps(bootstraps: CborSet<ReturnType<BootstrapWitness['toCore']>, BootstrapWitness>): void;
    bootstraps(): CborSet<Cardano.BootstrapWitness, BootstrapWitness> | undefined;
    setPlutusV1Scripts(plutusV1Scripts: CborSet<ReturnType<PlutusV1Script['toCore']>, PlutusV1Script>): void;
    plutusV1Scripts(): CborSet<Cardano.PlutusScript, PlutusV1Script> | undefined;
    setPlutusData(plutusData: CborSet<ReturnType<PlutusData['toCore']>, PlutusData>): void;
    plutusData(): CborSet<Cardano.PlutusData, PlutusData> | undefined;
    setRedeemers(redeemers: Redeemers): void;
    redeemers(): Redeemers | undefined;
    setPlutusV2Scripts(plutusV2Scripts: CborSet<ReturnType<PlutusV2Script['toCore']>, PlutusV2Script>): void;
    plutusV2Scripts(): CborSet<Cardano.PlutusScript, PlutusV2Script> | undefined;
    setPlutusV3Scripts(plutusV3Scripts: CborSet<ReturnType<PlutusV3Script['toCore']>, PlutusV3Script>): void;
    plutusV3Scripts(): CborSet<Cardano.PlutusScript, PlutusV3Script> | undefined;
}
//# sourceMappingURL=TransactionWitnessSet.d.ts.map