import { Inspector } from './txInspector.js';
import type * as Cardano from '../Cardano/index.js';
import type { AssetInfo } from '../Asset/index.js';
import type { AssetProvider } from '../Provider/index.js';
import type { Logger } from 'ts-log';
import type { Milliseconds } from './time.js';
export declare type AssetInfoWithAmount = {
    amount: Cardano.Lovelace;
    assetInfo: AssetInfo;
};
export declare type TokenTransferValue = {
    assets: Map<Cardano.AssetId, AssetInfoWithAmount>;
    coins: Cardano.Lovelace;
};
export declare type TokenTransferInspection = {
    fromAddress: Map<Cardano.PaymentAddress, TokenTransferValue>;
    toAddress: Map<Cardano.PaymentAddress, TokenTransferValue>;
};
export interface TokenTransferInspectorArgs {
    inputResolver: Cardano.InputResolver;
    fromAddressAssetProvider: AssetProvider;
    toAddressAssetProvider: AssetProvider;
    timeout: Milliseconds;
    logger: Logger;
}
export declare type TokenTransferInspector = (args: TokenTransferInspectorArgs) => Inspector<TokenTransferInspection>;
export declare const tokenTransferInspector: TokenTransferInspector;
//# sourceMappingURL=tokenTransferInspector.d.ts.map