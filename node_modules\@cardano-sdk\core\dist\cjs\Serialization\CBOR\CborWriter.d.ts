import { CborTag } from './CborTag';
import { HexBlob } from '@cardano-sdk/util';
export declare class CborWriter {
    #private;
    writeBigInteger(value: bigint): CborWriter;
    writeBoolean(value: boolean): CborWriter;
    writeByteString(value: Uint8Array): CborWriter;
    writeTextString(value: string): CborWriter;
    writeEncodedValue(value: Uint8Array): CborWriter;
    writeStartArray(length?: number): CborWriter;
    writeEndArray(): CborWriter;
    writeStartMap(length?: number): CborWriter;
    writeEndMap(): CborWriter;
    writeInt(value: number | bigint): CborWriter;
    writeFloat(value: number): CborWriter;
    writeNull(): CborWriter;
    writeUndefined(): CborWriter;
    writeTag(tag: CborTag | number): CborWriter;
    encodeAsHex(): HexBlob;
    encode(): Uint8Array;
    reset(): void;
}
//# sourceMappingURL=CborWriter.d.ts.map