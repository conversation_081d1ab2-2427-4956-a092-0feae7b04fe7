import * as Crypto from '@cardano-sdk/crypto';
import { HexBlob } from '@cardano-sdk/util';
import type * as Cardano from '../../../Cardano';
export declare class ScriptPubkey {
    #private;
    constructor(keyHash: Crypto.Ed25519KeyHashHex);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): ScriptPubkey;
    toCore(): Cardano.RequireSignatureScript;
    static fromCore(script: Cardano.RequireSignatureScript): ScriptPubkey;
    keyHash(): Crypto.Ed25519KeyHashHex;
    setKeyHash(keyHash: Crypto.Ed25519KeyHashHex): void;
}
//# sourceMappingURL=ScriptPubkey.d.ts.map