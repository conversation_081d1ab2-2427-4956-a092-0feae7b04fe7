import * as Cardano from '../../../../Cardano';
import { HexBlob } from '@cardano-sdk/util';
export declare class MultiHostName {
    #private;
    constructor(dnsName: string);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): MultiHostName;
    toCore(): Cardano.RelayByNameMultihost;
    static fromCore(relay: Cardano.RelayByNameMultihost): MultiHostName;
    dnsName(): string;
    setDnsName(dnsName: string): void;
}
//# sourceMappingURL=MultiHostName.d.ts.map