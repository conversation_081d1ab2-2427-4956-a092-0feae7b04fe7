import { Address, AddressProps } from './Address';
import { Hash28ByteBase16 } from '@cardano-sdk/crypto';
import { HexBlob } from '@cardano-sdk/util';
export declare type ByronAttributes = {
    derivationPath?: HexBlob;
    magic?: number;
};
export declare enum ByronAddressType {
    PubKey = 0,
    Script = 1,
    Redeem = 2
}
export declare type ByronAddressContent = {
    root: Hash28ByteBase16;
    attrs: ByronAttributes;
    type: ByronAddressType;
};
export declare class ByronAddress {
    #private;
    private constructor();
    static fromCredentials(root: Hash28ByteBase16, attrs: ByronAttributes, type: ByronAddressType): <PERSON><PERSON>ddress;
    getAttributes(): ByronAttributes;
    getRoot(): Hash28ByteBase16;
    getByronAddressType(): ByronAddressType;
    toAddress(): Address;
    static fromAddress(addr: Address): <PERSON><PERSON><PERSON><PERSON> | undefined;
    static packParts(props: AddressProps): Uint8Array;
    static unpackParts(type: number, data: Uint8Array): Address;
}
//# sourceMappingURL=ByronAddress.d.ts.map