import * as Cardano from '../../../Cardano';
import { HexBlob } from '@cardano-sdk/util';
import { MoveInstantaneousRewardToOtherPot } from './MoveInstantaneousRewardToOtherPot';
import { MoveInstantaneousRewardToStakeCreds } from './MoveInstantaneousRewardToStakeCreds';
export declare class MoveInstantaneousReward {
    #private;
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): MoveInstantaneousReward;
    toCore(): Cardano.MirCertificate;
    static fromCore(cert: Cardano.MirCertificate): MoveInstantaneousReward;
    static newToOtherPot(mirCert: MoveInstantaneousRewardToOtherPot): MoveInstantaneousReward;
    static newToStakeCreds(mirCert: MoveInstantaneousRewardToStakeCreds): MoveInstantaneousReward;
    kind(): Cardano.MirCertificateKind;
    asToOtherPot(): MoveInstantaneousRewardToOtherPot | undefined;
    asToStakeCreds(): MoveInstantaneousRewardToStakeCreds | undefined;
}
//# sourceMappingURL=MoveInstantaneousReward.d.ts.map