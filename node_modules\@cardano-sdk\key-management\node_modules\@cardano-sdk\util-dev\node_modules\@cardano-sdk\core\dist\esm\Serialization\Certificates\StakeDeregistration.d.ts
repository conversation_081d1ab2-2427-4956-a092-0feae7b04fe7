import { HexBlob } from '@cardano-sdk/util';
import type * as Cardano from '../../Cardano/index.js';
export declare class StakeDeregistration {
    #private;
    constructor(credential: Cardano.Credential);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): StakeDeregistration;
    toCore(): Cardano.StakeAddressCertificate;
    static fromCore(cert: Cardano.StakeAddressCertificate): StakeDeregistration;
    stakeCredential(): Cardano.Credential;
    setStakeCredential(credential: Cardano.Credential): void;
}
//# sourceMappingURL=StakeDeregistration.d.ts.map