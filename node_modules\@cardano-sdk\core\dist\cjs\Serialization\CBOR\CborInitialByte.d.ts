import { CborAdditionalInfo } from './CborAdditionalInfo';
import { CborMajorType } from './CborMajorType';
export declare class CborInitialByte {
    #private;
    static readonly IndefiniteLengthBreakByte = 255;
    static readonly AdditionalInformationMask = 31;
    CborInitialByte(majorType: CborMajorType, additionalInfo: CborAdditionalInfo): void;
    static from(initialByte: number): CborInitialByte;
    getInitialByte(): number;
    getMajorType(): CborMajorType;
    getAdditionalInfo(): CborAdditionalInfo;
}
//# sourceMappingURL=CborInitialByte.d.ts.map