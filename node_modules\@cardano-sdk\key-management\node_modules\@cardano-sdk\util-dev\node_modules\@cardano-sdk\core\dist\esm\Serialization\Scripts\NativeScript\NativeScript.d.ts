import * as Crypto from '@cardano-sdk/crypto';
import { HexBlob } from '@cardano-sdk/util';
import { NativeScriptKind } from '../../../Cardano/types/Script.js';
import { ScriptAll } from './ScriptAll.js';
import { ScriptAny } from './ScriptAny.js';
import { ScriptNOfK } from './ScriptNOfK.js';
import { ScriptPubkey } from './ScriptPubkey.js';
import { TimelockExpiry } from './TimelockExpiry.js';
import { TimelockStart } from './TimelockStart.js';
import type * as Cardano from '../../../Cardano/index.js';
export declare class NativeScript {
    #private;
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): NativeScript;
    toCore(): Cardano.NativeScript;
    static fromCore(script: Cardano.NativeScript): NativeScript;
    hash(): Crypto.Hash28ByteBase16;
    kind(): NativeScriptKind;
    static newScriptPubkey(scriptPubkey: ScriptPubkey): NativeScript;
    static newScriptAll(scriptAll: ScriptAll): NativeScript;
    static newScriptAny(scriptAny: ScriptAny): NativeScript;
    static newScriptNOfK(scriptNOfK: ScriptNOfK): NativeScript;
    static newTimelockStart(timelockStart: TimelockStart): NativeScript;
    static newTimelockExpiry(timelockExpiry: TimelockExpiry): NativeScript;
    asScriptPubkey(): ScriptPubkey | undefined;
    asScriptAll(): ScriptAll | undefined;
    asScriptAny(): ScriptAny | undefined;
    asScriptNOfK(): ScriptNOfK | undefined;
    asTimelockStart(): TimelockStart | undefined;
    asTimelockExpiry(): TimelockExpiry | undefined;
}
//# sourceMappingURL=NativeScript.d.ts.map