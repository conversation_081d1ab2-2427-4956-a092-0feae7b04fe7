import type { Metadatum, MetadatumMap } from '../Cardano/types/AuxiliaryData.js';
export declare const asMetadatumMap: (metadatum: Metadatum | undefined) => MetadatumMap | null;
export declare const asMetadatumArray: (metadatum: Metadatum | undefined) => Metadatum[] | null;
export declare const jsonToMetadatum: (json: any) => Metadatum;
export declare const metadatumToJson: (metadatum: Metadatum) => any;
//# sourceMappingURL=metadatum.d.ts.map