'use client'

import { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import dynamic from 'next/dynamic'

// Dynamically import BrowserWallet to avoid SSR issues
let BrowserWallet: any = null
if (typeof window !== 'undefined') {
  import('@meshsdk/core').then((module) => {
    BrowserWallet = module.BrowserWallet
  })
}

interface WalletContextType {
  wallet: any | null
  connected: boolean
  connecting: boolean
  address: string | null
  balance: string | null
  connect: (walletName: string) => Promise<void>
  disconnect: () => void
  getAvailableWallets: () => string[]
}

const WalletContext = createContext<WalletContextType | undefined>(undefined)

export function useWallet() {
  const context = useContext(WalletContext)
  if (context === undefined) {
    throw new Error('useWallet must be used within a WalletProvider')
  }
  return context
}

interface WalletProviderProps {
  children: ReactNode
}

export function WalletProvider({ children }: WalletProviderProps) {
  const [wallet, setWallet] = useState<any | null>(null)
  const [connected, setConnected] = useState(false)
  const [connecting, setConnecting] = useState(false)
  const [address, setAddress] = useState<string | null>(null)
  const [balance, setBalance] = useState<string | null>(null)

  const getAvailableWallets = () => {
    if (typeof window === 'undefined') return []
    
    const wallets = []
    if (window.cardano?.lace) wallets.push('lace')
    if (window.cardano?.eternl) wallets.push('eternl')
    if (window.cardano?.nami) wallets.push('nami')
    if (window.cardano?.flint) wallets.push('flint')
    
    return wallets
  }

  const connect = async (walletName: string) => {
    if (typeof window === 'undefined' || !BrowserWallet) return

    setConnecting(true)
    try {
      const browserWallet = await BrowserWallet.enable(walletName)
      setWallet(browserWallet)
      
      // Get wallet address
      const addresses = await browserWallet.getUsedAddresses()
      if (addresses.length > 0) {
        setAddress(addresses[0])
      }
      
      // Get wallet balance
      try {
        const balance = await browserWallet.getBalance()
        setBalance(balance)
      } catch (error) {
        console.warn('Could not fetch balance:', error)
        setBalance('0')
      }
      
      setConnected(true)
      
      // Store connection state
      localStorage.setItem('vintrek_wallet', walletName)
    } catch (error) {
      console.error('Failed to connect wallet:', error)
      throw error
    } finally {
      setConnecting(false)
    }
  }

  const disconnect = () => {
    setWallet(null)
    setConnected(false)
    setAddress(null)
    setBalance(null)
    localStorage.removeItem('vintrek_wallet')
  }

  // Auto-reconnect on page load
  useEffect(() => {
    const savedWallet = localStorage.getItem('vintrek_wallet')
    if (savedWallet && getAvailableWallets().includes(savedWallet)) {
      connect(savedWallet).catch(console.error)
    }
  }, [])

  const value: WalletContextType = {
    wallet,
    connected,
    connecting,
    address,
    balance,
    connect,
    disconnect,
    getAvailableWallets,
  }

  return (
    <WalletContext.Provider value={value}>
      {children}
    </WalletContext.Provider>
  )
}
