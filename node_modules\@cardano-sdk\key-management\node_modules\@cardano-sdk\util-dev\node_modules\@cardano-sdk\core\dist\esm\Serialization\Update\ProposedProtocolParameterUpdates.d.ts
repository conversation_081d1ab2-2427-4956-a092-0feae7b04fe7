import { HexBlob } from '@cardano-sdk/util';
import { ProtocolParamUpdate } from './ProtocolParamUpdate.js';
import type * as Cardano from '../../Cardano/index.js';
export declare class ProposedProtocolParameterUpdates {
    #private;
    constructor(proposedUpdates: Map<Cardano.GenesisDelegateKeyHash, ProtocolParamUpdate>);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): ProposedProtocolParameterUpdates;
    toCore(): Cardano.ProposedProtocolParameterUpdates;
    static fromCore(updates: Cardano.ProposedProtocolParameterUpdates): ProposedProtocolParameterUpdates;
    size(): number;
    insert(key: Cardano.GenesisDelegateKeyHash, value: ProtocolParamUpdate): void;
    get(key: Cardano.GenesisDelegateKeyHash): ProtocolParamUpdate | undefined;
    keys(): Array<Cardano.GenesisDelegateKeyHash>;
}
//# sourceMappingURL=ProposedProtocolParameterUpdates.d.ts.map