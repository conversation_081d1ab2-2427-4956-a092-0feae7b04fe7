import { DRep } from './DRep';
import { HexBlob } from '@cardano-sdk/util';
import type * as Cardano from '../../Cardano';
export declare class VoteRegistrationDelegation {
    #private;
    constructor(stakeCredential: Cardano.Credential, deposit: Cardano.Lovelace, dRep: DRep);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): VoteRegistrationDelegation;
    toCore(): Cardano.VoteRegistrationDelegationCertificate;
    static fromCore(deleg: Cardano.VoteRegistrationDelegationCertificate): VoteRegistrationDelegation;
    stakeCredential(): Cardano.Credential;
    deposit(): Cardano.Lovelace;
    dRep(): DRep;
}
//# sourceMappingURL=VoteRegistrationDelegation.d.ts.map