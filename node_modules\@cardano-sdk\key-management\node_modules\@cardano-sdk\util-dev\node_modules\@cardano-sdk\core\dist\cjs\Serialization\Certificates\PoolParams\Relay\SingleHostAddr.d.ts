import { HexBlob } from '@cardano-sdk/util';
import type * as Cardano from '../../../../Cardano';
export declare class SingleHostAddr {
    #private;
    constructor(port?: number, ipv4?: string, ipv6?: string);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): SingleHostAddr;
    toCore(): Cardano.RelayByAddress;
    static fromCore(relay: Cardano.RelayByAddress): SingleHostAddr;
    port(): number | undefined;
    setPort(port: number | undefined): void;
    ipv4(): string | undefined;
    setIpv4(ipV4: string | undefined): void;
    ipv6(): string | undefined;
    setIpv6(ipV6: string | undefined): void;
}
//# sourceMappingURL=SingleHostAddr.d.ts.map