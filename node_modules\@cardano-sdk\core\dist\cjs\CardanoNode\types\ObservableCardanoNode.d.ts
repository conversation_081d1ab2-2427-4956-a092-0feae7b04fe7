import type { Card<PERSON>, HealthCheckResponse, TxCBOR } from '../..';
import type { EraSummary } from './CardanoNode';
import type { Observable } from 'rxjs';
export declare type Point = Pick<Cardano.Tip, 'hash' | 'slot'>;
export declare type Origin = 'origin';
export declare type TipOrOrigin = Cardano.Tip | Origin;
export declare type PointOrOrigin = Point | Origin;
export declare type Intersection = {
    point: PointOrOrigin;
    tip: TipOrOrigin;
};
export declare enum ChainSyncEventType {
    RollForward = 0,
    RollBackward = 1
}
export declare type RequestNext = () => void;
export interface WithRequestNext {
    requestNext: RequestNext;
}
export interface ChainSyncRollForward extends WithRequestNext {
    tip: Cardano.Tip;
    eventType: ChainSyncEventType.RollForward;
    block: Cardano.Block;
}
export interface ChainSyncRollBackward extends WithRequestNext {
    eventType: ChainSyncEventType.RollBackward;
    point: PointOrOrigin;
    tip: TipOrOrigin;
}
export declare type ChainSyncEvent = ChainSyncRollForward | ChainSyncRollBackward;
export interface ObservableChainSync {
    chainSync$: Observable<ChainSyncEvent>;
    intersection: Intersection;
}
export interface ObservableCardanoNode {
    eraSummaries$: Observable<EraSummary[]>;
    genesisParameters$: Observable<Cardano.CompactGenesis>;
    healthCheck$: Observable<HealthCheckResponse>;
    findIntersect(points: PointOrOrigin[]): Observable<ObservableChainSync>;
    submitTx(tx: TxCBOR): Observable<Cardano.TransactionId>;
}
export declare const ObservableCardanoNode: {
    readonly bufferChainSyncEvent: <T extends WithRequestNext>(length: number) => (source$: Observable<T>) => Observable<T>;
};
//# sourceMappingURL=ObservableCardanoNode.d.ts.map