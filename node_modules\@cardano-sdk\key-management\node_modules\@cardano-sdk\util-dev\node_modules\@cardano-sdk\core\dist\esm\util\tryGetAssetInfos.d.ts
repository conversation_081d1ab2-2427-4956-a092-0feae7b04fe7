import type * as Cardano from '../Cardano/index.js';
import type { AssetInfo } from '../Asset/index.js';
import type { AssetProvider } from '../Provider/index.js';
import type { Logger } from 'ts-log';
import type { Milliseconds } from './time.js';
declare type TryGetAssetInfosProps = {
    assetIds: Cardano.AssetId[];
    assetProvider: AssetProvider;
    timeout: Milliseconds;
    logger: Logger;
};
export declare const tryGetAssetInfos: ({ assetIds, assetProvider, logger, timeout }: TryGetAssetInfosProps) => Promise<AssetInfo[]>;
export {};
//# sourceMappingURL=tryGetAssetInfos.d.ts.map