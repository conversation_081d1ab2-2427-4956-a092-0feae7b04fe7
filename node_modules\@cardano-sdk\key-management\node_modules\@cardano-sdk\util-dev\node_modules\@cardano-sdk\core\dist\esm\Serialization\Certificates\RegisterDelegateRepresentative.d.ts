import { Anchor } from '../Common/index.js';
import { HexBlob } from '@cardano-sdk/util';
import type * as Cardano from '../../Cardano/index.js';
export declare class RegisterDelegateRepresentative {
    #private;
    constructor(drepCredential: Cardano.Credential, deposit: Cardano.Lovelace, anchor?: Anchor);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): RegisterDelegateRepresentative;
    toCore(): Cardano.RegisterDelegateRepresentativeCertificate;
    static fromCore(cert: Cardano.RegisterDelegateRepresentativeCertificate): RegisterDelegateRepresentative;
    credential(): Cardano.Credential;
    deposit(): Cardano.Lovelace;
    anchor(): Anchor | undefined;
}
//# sourceMappingURL=RegisterDelegateRepresentative.d.ts.map