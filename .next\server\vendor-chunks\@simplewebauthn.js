"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@simplewebauthn";
exports.ids = ["vendor-chunks/@simplewebauthn"];
exports.modules = {

/***/ "(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/base64URLStringToBuffer.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/@simplewebauthn/browser/esm/helpers/base64URLStringToBuffer.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   base64URLStringToBuffer: () => (/* binding */ base64URLStringToBuffer)\n/* harmony export */ });\n/**\n * Convert from a Base64URL-encoded string to an Array Buffer. Best used when converting a\n * credential ID from a JSON string to an ArrayBuffer, like in allowCredentials or\n * excludeCredentials\n *\n * Helper method to compliment `bufferToBase64URLString`\n */\nfunction base64URLStringToBuffer(base64URLString) {\n    // Convert from Base64URL to Base64\n    const base64 = base64URLString.replace(/-/g, '+').replace(/_/g, '/');\n    /**\n     * Pad with '=' until it's a multiple of four\n     * (4 - (85 % 4 = 1) = 3) % 4 = 3 padding\n     * (4 - (86 % 4 = 2) = 2) % 4 = 2 padding\n     * (4 - (87 % 4 = 3) = 1) % 4 = 1 padding\n     * (4 - (88 % 4 = 0) = 4) % 4 = 0 padding\n     */\n    const padLength = (4 - (base64.length % 4)) % 4;\n    const padded = base64.padEnd(base64.length + padLength, '=');\n    // Convert to a binary string\n    const binary = atob(padded);\n    // Convert binary string to buffer\n    const buffer = new ArrayBuffer(binary.length);\n    const bytes = new Uint8Array(buffer);\n    for (let i = 0; i < binary.length; i++) {\n        bytes[i] = binary.charCodeAt(i);\n    }\n    return buffer;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/base64URLStringToBuffer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/browserSupportsWebAuthn.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/@simplewebauthn/browser/esm/helpers/browserSupportsWebAuthn.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _browserSupportsWebAuthnInternals: () => (/* binding */ _browserSupportsWebAuthnInternals),\n/* harmony export */   browserSupportsWebAuthn: () => (/* binding */ browserSupportsWebAuthn)\n/* harmony export */ });\n/**\n * Determine if the browser is capable of Webauthn\n */\nfunction browserSupportsWebAuthn() {\n    return _browserSupportsWebAuthnInternals.stubThis(globalThis?.PublicKeyCredential !== undefined &&\n        typeof globalThis.PublicKeyCredential === 'function');\n}\n/**\n * Make it possible to stub the return value during testing\n * @ignore Don't include this in docs output\n */\nconst _browserSupportsWebAuthnInternals = {\n    stubThis: (value) => value,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNpbXBsZXdlYmF1dGhuL2Jyb3dzZXIvZXNtL2hlbHBlcnMvYnJvd3NlclN1cHBvcnRzV2ViQXV0aG4uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBIiwic291cmNlcyI6WyJEOlxcaGFja290aG9uXFxCbG9ja2NoYWluXFxWaW50cmVrXFxub2RlX21vZHVsZXNcXEBzaW1wbGV3ZWJhdXRoblxcYnJvd3NlclxcZXNtXFxoZWxwZXJzXFxicm93c2VyU3VwcG9ydHNXZWJBdXRobi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIERldGVybWluZSBpZiB0aGUgYnJvd3NlciBpcyBjYXBhYmxlIG9mIFdlYmF1dGhuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBicm93c2VyU3VwcG9ydHNXZWJBdXRobigpIHtcbiAgICByZXR1cm4gX2Jyb3dzZXJTdXBwb3J0c1dlYkF1dGhuSW50ZXJuYWxzLnN0dWJUaGlzKGdsb2JhbFRoaXM/LlB1YmxpY0tleUNyZWRlbnRpYWwgIT09IHVuZGVmaW5lZCAmJlxuICAgICAgICB0eXBlb2YgZ2xvYmFsVGhpcy5QdWJsaWNLZXlDcmVkZW50aWFsID09PSAnZnVuY3Rpb24nKTtcbn1cbi8qKlxuICogTWFrZSBpdCBwb3NzaWJsZSB0byBzdHViIHRoZSByZXR1cm4gdmFsdWUgZHVyaW5nIHRlc3RpbmdcbiAqIEBpZ25vcmUgRG9uJ3QgaW5jbHVkZSB0aGlzIGluIGRvY3Mgb3V0cHV0XG4gKi9cbmV4cG9ydCBjb25zdCBfYnJvd3NlclN1cHBvcnRzV2ViQXV0aG5JbnRlcm5hbHMgPSB7XG4gICAgc3R1YlRoaXM6ICh2YWx1ZSkgPT4gdmFsdWUsXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/browserSupportsWebAuthn.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/browserSupportsWebAuthnAutofill.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/@simplewebauthn/browser/esm/helpers/browserSupportsWebAuthnAutofill.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _browserSupportsWebAuthnAutofillInternals: () => (/* binding */ _browserSupportsWebAuthnAutofillInternals),\n/* harmony export */   browserSupportsWebAuthnAutofill: () => (/* binding */ browserSupportsWebAuthnAutofill)\n/* harmony export */ });\n/* harmony import */ var _browserSupportsWebAuthn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./browserSupportsWebAuthn.js */ \"(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/browserSupportsWebAuthn.js\");\n\n/**\n * Determine if the browser supports conditional UI, so that WebAuthn credentials can\n * be shown to the user in the browser's typical password autofill popup.\n */\nfunction browserSupportsWebAuthnAutofill() {\n    if (!(0,_browserSupportsWebAuthn_js__WEBPACK_IMPORTED_MODULE_0__.browserSupportsWebAuthn)()) {\n        return _browserSupportsWebAuthnAutofillInternals.stubThis(new Promise((resolve) => resolve(false)));\n    }\n    /**\n     * I don't like the `as unknown` here but there's a `declare var PublicKeyCredential` in\n     * TS' DOM lib that's making it difficult for me to just go `as PublicKeyCredentialFuture` as I\n     * want. I think I'm fine with this for now since it's _supposed_ to be temporary, until TS types\n     * have a chance to catch up.\n     */\n    const globalPublicKeyCredential = globalThis\n        .PublicKeyCredential;\n    if (globalPublicKeyCredential?.isConditionalMediationAvailable === undefined) {\n        return _browserSupportsWebAuthnAutofillInternals.stubThis(new Promise((resolve) => resolve(false)));\n    }\n    return _browserSupportsWebAuthnAutofillInternals.stubThis(globalPublicKeyCredential.isConditionalMediationAvailable());\n}\n// Make it possible to stub the return value during testing\nconst _browserSupportsWebAuthnAutofillInternals = {\n    stubThis: (value) => value,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/browserSupportsWebAuthnAutofill.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/bufferToBase64URLString.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/@simplewebauthn/browser/esm/helpers/bufferToBase64URLString.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bufferToBase64URLString: () => (/* binding */ bufferToBase64URLString)\n/* harmony export */ });\n/**\n * Convert the given array buffer into a Base64URL-encoded string. Ideal for converting various\n * credential response ArrayBuffers to string for sending back to the server as JSON.\n *\n * Helper method to compliment `base64URLStringToBuffer`\n */\nfunction bufferToBase64URLString(buffer) {\n    const bytes = new Uint8Array(buffer);\n    let str = '';\n    for (const charCode of bytes) {\n        str += String.fromCharCode(charCode);\n    }\n    const base64String = btoa(str);\n    return base64String.replace(/\\+/g, '-').replace(/\\//g, '_').replace(/=/g, '');\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNpbXBsZXdlYmF1dGhuL2Jyb3dzZXIvZXNtL2hlbHBlcnMvYnVmZmVyVG9CYXNlNjRVUkxTdHJpbmcuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXGhhY2tvdGhvblxcQmxvY2tjaGFpblxcVmludHJla1xcbm9kZV9tb2R1bGVzXFxAc2ltcGxld2ViYXV0aG5cXGJyb3dzZXJcXGVzbVxcaGVscGVyc1xcYnVmZmVyVG9CYXNlNjRVUkxTdHJpbmcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBDb252ZXJ0IHRoZSBnaXZlbiBhcnJheSBidWZmZXIgaW50byBhIEJhc2U2NFVSTC1lbmNvZGVkIHN0cmluZy4gSWRlYWwgZm9yIGNvbnZlcnRpbmcgdmFyaW91c1xuICogY3JlZGVudGlhbCByZXNwb25zZSBBcnJheUJ1ZmZlcnMgdG8gc3RyaW5nIGZvciBzZW5kaW5nIGJhY2sgdG8gdGhlIHNlcnZlciBhcyBKU09OLlxuICpcbiAqIEhlbHBlciBtZXRob2QgdG8gY29tcGxpbWVudCBgYmFzZTY0VVJMU3RyaW5nVG9CdWZmZXJgXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBidWZmZXJUb0Jhc2U2NFVSTFN0cmluZyhidWZmZXIpIHtcbiAgICBjb25zdCBieXRlcyA9IG5ldyBVaW50OEFycmF5KGJ1ZmZlcik7XG4gICAgbGV0IHN0ciA9ICcnO1xuICAgIGZvciAoY29uc3QgY2hhckNvZGUgb2YgYnl0ZXMpIHtcbiAgICAgICAgc3RyICs9IFN0cmluZy5mcm9tQ2hhckNvZGUoY2hhckNvZGUpO1xuICAgIH1cbiAgICBjb25zdCBiYXNlNjRTdHJpbmcgPSBidG9hKHN0cik7XG4gICAgcmV0dXJuIGJhc2U2NFN0cmluZy5yZXBsYWNlKC9cXCsvZywgJy0nKS5yZXBsYWNlKC9cXC8vZywgJ18nKS5yZXBsYWNlKC89L2csICcnKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/bufferToBase64URLString.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/identifyAuthenticationError.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/@simplewebauthn/browser/esm/helpers/identifyAuthenticationError.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   identifyAuthenticationError: () => (/* binding */ identifyAuthenticationError)\n/* harmony export */ });\n/* harmony import */ var _isValidDomain_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./isValidDomain.js */ \"(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/isValidDomain.js\");\n/* harmony import */ var _webAuthnError_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./webAuthnError.js */ \"(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/webAuthnError.js\");\n\n\n/**\n * Attempt to intuit _why_ an error was raised after calling `navigator.credentials.get()`\n */\nfunction identifyAuthenticationError({ error, options, }) {\n    const { publicKey } = options;\n    if (!publicKey) {\n        throw Error('options was missing required publicKey property');\n    }\n    if (error.name === 'AbortError') {\n        if (options.signal instanceof AbortSignal) {\n            // https://www.w3.org/TR/webauthn-2/#sctn-createCredential (Step 16)\n            return new _webAuthnError_js__WEBPACK_IMPORTED_MODULE_1__.WebAuthnError({\n                message: 'Authentication ceremony was sent an abort signal',\n                code: 'ERROR_CEREMONY_ABORTED',\n                cause: error,\n            });\n        }\n    }\n    else if (error.name === 'NotAllowedError') {\n        /**\n         * Pass the error directly through. Platforms are overloading this error beyond what the spec\n         * defines and we don't want to overwrite potentially useful error messages.\n         */\n        return new _webAuthnError_js__WEBPACK_IMPORTED_MODULE_1__.WebAuthnError({\n            message: error.message,\n            code: 'ERROR_PASSTHROUGH_SEE_CAUSE_PROPERTY',\n            cause: error,\n        });\n    }\n    else if (error.name === 'SecurityError') {\n        const effectiveDomain = globalThis.location.hostname;\n        if (!(0,_isValidDomain_js__WEBPACK_IMPORTED_MODULE_0__.isValidDomain)(effectiveDomain)) {\n            // https://www.w3.org/TR/webauthn-2/#sctn-discover-from-external-source (Step 5)\n            return new _webAuthnError_js__WEBPACK_IMPORTED_MODULE_1__.WebAuthnError({\n                message: `${globalThis.location.hostname} is an invalid domain`,\n                code: 'ERROR_INVALID_DOMAIN',\n                cause: error,\n            });\n        }\n        else if (publicKey.rpId !== effectiveDomain) {\n            // https://www.w3.org/TR/webauthn-2/#sctn-discover-from-external-source (Step 6)\n            return new _webAuthnError_js__WEBPACK_IMPORTED_MODULE_1__.WebAuthnError({\n                message: `The RP ID \"${publicKey.rpId}\" is invalid for this domain`,\n                code: 'ERROR_INVALID_RP_ID',\n                cause: error,\n            });\n        }\n    }\n    else if (error.name === 'UnknownError') {\n        // https://www.w3.org/TR/webauthn-2/#sctn-op-get-assertion (Step 1)\n        // https://www.w3.org/TR/webauthn-2/#sctn-op-get-assertion (Step 12)\n        return new _webAuthnError_js__WEBPACK_IMPORTED_MODULE_1__.WebAuthnError({\n            message: 'The authenticator was unable to process the specified options, or could not create a new assertion signature',\n            code: 'ERROR_AUTHENTICATOR_GENERAL_ERROR',\n            cause: error,\n        });\n    }\n    return error;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/identifyAuthenticationError.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/identifyRegistrationError.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/@simplewebauthn/browser/esm/helpers/identifyRegistrationError.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   identifyRegistrationError: () => (/* binding */ identifyRegistrationError)\n/* harmony export */ });\n/* harmony import */ var _isValidDomain_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./isValidDomain.js */ \"(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/isValidDomain.js\");\n/* harmony import */ var _webAuthnError_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./webAuthnError.js */ \"(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/webAuthnError.js\");\n\n\n/**\n * Attempt to intuit _why_ an error was raised after calling `navigator.credentials.create()`\n */\nfunction identifyRegistrationError({ error, options, }) {\n    const { publicKey } = options;\n    if (!publicKey) {\n        throw Error('options was missing required publicKey property');\n    }\n    if (error.name === 'AbortError') {\n        if (options.signal instanceof AbortSignal) {\n            // https://www.w3.org/TR/webauthn-2/#sctn-createCredential (Step 16)\n            return new _webAuthnError_js__WEBPACK_IMPORTED_MODULE_1__.WebAuthnError({\n                message: 'Registration ceremony was sent an abort signal',\n                code: 'ERROR_CEREMONY_ABORTED',\n                cause: error,\n            });\n        }\n    }\n    else if (error.name === 'ConstraintError') {\n        if (publicKey.authenticatorSelection?.requireResidentKey === true) {\n            // https://www.w3.org/TR/webauthn-2/#sctn-op-make-cred (Step 4)\n            return new _webAuthnError_js__WEBPACK_IMPORTED_MODULE_1__.WebAuthnError({\n                message: 'Discoverable credentials were required but no available authenticator supported it',\n                code: 'ERROR_AUTHENTICATOR_MISSING_DISCOVERABLE_CREDENTIAL_SUPPORT',\n                cause: error,\n            });\n        }\n        else if (\n        // @ts-ignore: `mediation` doesn't yet exist on CredentialCreationOptions but it's possible as of Sept 2024\n        options.mediation === 'conditional' &&\n            publicKey.authenticatorSelection?.userVerification === 'required') {\n            // https://w3c.github.io/webauthn/#sctn-createCredential (Step 22.4)\n            return new _webAuthnError_js__WEBPACK_IMPORTED_MODULE_1__.WebAuthnError({\n                message: 'User verification was required during automatic registration but it could not be performed',\n                code: 'ERROR_AUTO_REGISTER_USER_VERIFICATION_FAILURE',\n                cause: error,\n            });\n        }\n        else if (publicKey.authenticatorSelection?.userVerification === 'required') {\n            // https://www.w3.org/TR/webauthn-2/#sctn-op-make-cred (Step 5)\n            return new _webAuthnError_js__WEBPACK_IMPORTED_MODULE_1__.WebAuthnError({\n                message: 'User verification was required but no available authenticator supported it',\n                code: 'ERROR_AUTHENTICATOR_MISSING_USER_VERIFICATION_SUPPORT',\n                cause: error,\n            });\n        }\n    }\n    else if (error.name === 'InvalidStateError') {\n        // https://www.w3.org/TR/webauthn-2/#sctn-createCredential (Step 20)\n        // https://www.w3.org/TR/webauthn-2/#sctn-op-make-cred (Step 3)\n        return new _webAuthnError_js__WEBPACK_IMPORTED_MODULE_1__.WebAuthnError({\n            message: 'The authenticator was previously registered',\n            code: 'ERROR_AUTHENTICATOR_PREVIOUSLY_REGISTERED',\n            cause: error,\n        });\n    }\n    else if (error.name === 'NotAllowedError') {\n        /**\n         * Pass the error directly through. Platforms are overloading this error beyond what the spec\n         * defines and we don't want to overwrite potentially useful error messages.\n         */\n        return new _webAuthnError_js__WEBPACK_IMPORTED_MODULE_1__.WebAuthnError({\n            message: error.message,\n            code: 'ERROR_PASSTHROUGH_SEE_CAUSE_PROPERTY',\n            cause: error,\n        });\n    }\n    else if (error.name === 'NotSupportedError') {\n        const validPubKeyCredParams = publicKey.pubKeyCredParams.filter((param) => param.type === 'public-key');\n        if (validPubKeyCredParams.length === 0) {\n            // https://www.w3.org/TR/webauthn-2/#sctn-createCredential (Step 10)\n            return new _webAuthnError_js__WEBPACK_IMPORTED_MODULE_1__.WebAuthnError({\n                message: 'No entry in pubKeyCredParams was of type \"public-key\"',\n                code: 'ERROR_MALFORMED_PUBKEYCREDPARAMS',\n                cause: error,\n            });\n        }\n        // https://www.w3.org/TR/webauthn-2/#sctn-op-make-cred (Step 2)\n        return new _webAuthnError_js__WEBPACK_IMPORTED_MODULE_1__.WebAuthnError({\n            message: 'No available authenticator supported any of the specified pubKeyCredParams algorithms',\n            code: 'ERROR_AUTHENTICATOR_NO_SUPPORTED_PUBKEYCREDPARAMS_ALG',\n            cause: error,\n        });\n    }\n    else if (error.name === 'SecurityError') {\n        const effectiveDomain = globalThis.location.hostname;\n        if (!(0,_isValidDomain_js__WEBPACK_IMPORTED_MODULE_0__.isValidDomain)(effectiveDomain)) {\n            // https://www.w3.org/TR/webauthn-2/#sctn-createCredential (Step 7)\n            return new _webAuthnError_js__WEBPACK_IMPORTED_MODULE_1__.WebAuthnError({\n                message: `${globalThis.location.hostname} is an invalid domain`,\n                code: 'ERROR_INVALID_DOMAIN',\n                cause: error,\n            });\n        }\n        else if (publicKey.rp.id !== effectiveDomain) {\n            // https://www.w3.org/TR/webauthn-2/#sctn-createCredential (Step 8)\n            return new _webAuthnError_js__WEBPACK_IMPORTED_MODULE_1__.WebAuthnError({\n                message: `The RP ID \"${publicKey.rp.id}\" is invalid for this domain`,\n                code: 'ERROR_INVALID_RP_ID',\n                cause: error,\n            });\n        }\n    }\n    else if (error.name === 'TypeError') {\n        if (publicKey.user.id.byteLength < 1 || publicKey.user.id.byteLength > 64) {\n            // https://www.w3.org/TR/webauthn-2/#sctn-createCredential (Step 5)\n            return new _webAuthnError_js__WEBPACK_IMPORTED_MODULE_1__.WebAuthnError({\n                message: 'User ID was not between 1 and 64 characters',\n                code: 'ERROR_INVALID_USER_ID_LENGTH',\n                cause: error,\n            });\n        }\n    }\n    else if (error.name === 'UnknownError') {\n        // https://www.w3.org/TR/webauthn-2/#sctn-op-make-cred (Step 1)\n        // https://www.w3.org/TR/webauthn-2/#sctn-op-make-cred (Step 8)\n        return new _webAuthnError_js__WEBPACK_IMPORTED_MODULE_1__.WebAuthnError({\n            message: 'The authenticator was unable to process the specified options, or could not create a new credential',\n            code: 'ERROR_AUTHENTICATOR_GENERAL_ERROR',\n            cause: error,\n        });\n    }\n    return error;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/identifyRegistrationError.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/isValidDomain.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@simplewebauthn/browser/esm/helpers/isValidDomain.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isValidDomain: () => (/* binding */ isValidDomain)\n/* harmony export */ });\n/**\n * A simple test to determine if a hostname is a properly-formatted domain name\n *\n * A \"valid domain\" is defined here: https://url.spec.whatwg.org/#valid-domain\n *\n * Regex sourced from here:\n * https://www.oreilly.com/library/view/regular-expressions-cookbook/9781449327453/ch08s15.html\n */\nfunction isValidDomain(hostname) {\n    return (\n    // Consider localhost valid as well since it's okay wrt Secure Contexts\n    hostname === 'localhost' ||\n        /^([a-z0-9]+(-[a-z0-9]+)*\\.)+[a-z]{2,}$/i.test(hostname));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNpbXBsZXdlYmF1dGhuL2Jyb3dzZXIvZXNtL2hlbHBlcnMvaXNWYWxpZERvbWFpbi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsMkNBQTJDLEdBQUc7QUFDOUMiLCJzb3VyY2VzIjpbIkQ6XFxoYWNrb3Rob25cXEJsb2NrY2hhaW5cXFZpbnRyZWtcXG5vZGVfbW9kdWxlc1xcQHNpbXBsZXdlYmF1dGhuXFxicm93c2VyXFxlc21cXGhlbHBlcnNcXGlzVmFsaWREb21haW4uanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBBIHNpbXBsZSB0ZXN0IHRvIGRldGVybWluZSBpZiBhIGhvc3RuYW1lIGlzIGEgcHJvcGVybHktZm9ybWF0dGVkIGRvbWFpbiBuYW1lXG4gKlxuICogQSBcInZhbGlkIGRvbWFpblwiIGlzIGRlZmluZWQgaGVyZTogaHR0cHM6Ly91cmwuc3BlYy53aGF0d2cub3JnLyN2YWxpZC1kb21haW5cbiAqXG4gKiBSZWdleCBzb3VyY2VkIGZyb20gaGVyZTpcbiAqIGh0dHBzOi8vd3d3Lm9yZWlsbHkuY29tL2xpYnJhcnkvdmlldy9yZWd1bGFyLWV4cHJlc3Npb25zLWNvb2tib29rLzk3ODE0NDkzMjc0NTMvY2gwOHMxNS5odG1sXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBpc1ZhbGlkRG9tYWluKGhvc3RuYW1lKSB7XG4gICAgcmV0dXJuIChcbiAgICAvLyBDb25zaWRlciBsb2NhbGhvc3QgdmFsaWQgYXMgd2VsbCBzaW5jZSBpdCdzIG9rYXkgd3J0IFNlY3VyZSBDb250ZXh0c1xuICAgIGhvc3RuYW1lID09PSAnbG9jYWxob3N0JyB8fFxuICAgICAgICAvXihbYS16MC05XSsoLVthLXowLTldKykqXFwuKStbYS16XXsyLH0kL2kudGVzdChob3N0bmFtZSkpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/isValidDomain.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/platformAuthenticatorIsAvailable.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/@simplewebauthn/browser/esm/helpers/platformAuthenticatorIsAvailable.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   platformAuthenticatorIsAvailable: () => (/* binding */ platformAuthenticatorIsAvailable)\n/* harmony export */ });\n/* harmony import */ var _browserSupportsWebAuthn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./browserSupportsWebAuthn.js */ \"(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/browserSupportsWebAuthn.js\");\n\n/**\n * Determine whether the browser can communicate with a built-in authenticator, like\n * Touch ID, Android fingerprint scanner, or Windows Hello.\n *\n * This method will _not_ be able to tell you the name of the platform authenticator.\n */\nfunction platformAuthenticatorIsAvailable() {\n    if (!(0,_browserSupportsWebAuthn_js__WEBPACK_IMPORTED_MODULE_0__.browserSupportsWebAuthn)()) {\n        return new Promise((resolve) => resolve(false));\n    }\n    return PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNpbXBsZXdlYmF1dGhuL2Jyb3dzZXIvZXNtL2hlbHBlcnMvcGxhdGZvcm1BdXRoZW50aWNhdG9ySXNBdmFpbGFibGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBdUU7QUFDdkU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUCxTQUFTLG9GQUF1QjtBQUNoQztBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXGhhY2tvdGhvblxcQmxvY2tjaGFpblxcVmludHJla1xcbm9kZV9tb2R1bGVzXFxAc2ltcGxld2ViYXV0aG5cXGJyb3dzZXJcXGVzbVxcaGVscGVyc1xccGxhdGZvcm1BdXRoZW50aWNhdG9ySXNBdmFpbGFibGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgYnJvd3NlclN1cHBvcnRzV2ViQXV0aG4gfSBmcm9tICcuL2Jyb3dzZXJTdXBwb3J0c1dlYkF1dGhuLmpzJztcbi8qKlxuICogRGV0ZXJtaW5lIHdoZXRoZXIgdGhlIGJyb3dzZXIgY2FuIGNvbW11bmljYXRlIHdpdGggYSBidWlsdC1pbiBhdXRoZW50aWNhdG9yLCBsaWtlXG4gKiBUb3VjaCBJRCwgQW5kcm9pZCBmaW5nZXJwcmludCBzY2FubmVyLCBvciBXaW5kb3dzIEhlbGxvLlxuICpcbiAqIFRoaXMgbWV0aG9kIHdpbGwgX25vdF8gYmUgYWJsZSB0byB0ZWxsIHlvdSB0aGUgbmFtZSBvZiB0aGUgcGxhdGZvcm0gYXV0aGVudGljYXRvci5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHBsYXRmb3JtQXV0aGVudGljYXRvcklzQXZhaWxhYmxlKCkge1xuICAgIGlmICghYnJvd3NlclN1cHBvcnRzV2ViQXV0aG4oKSkge1xuICAgICAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUpID0+IHJlc29sdmUoZmFsc2UpKTtcbiAgICB9XG4gICAgcmV0dXJuIFB1YmxpY0tleUNyZWRlbnRpYWwuaXNVc2VyVmVyaWZ5aW5nUGxhdGZvcm1BdXRoZW50aWNhdG9yQXZhaWxhYmxlKCk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/platformAuthenticatorIsAvailable.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/toAuthenticatorAttachment.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/@simplewebauthn/browser/esm/helpers/toAuthenticatorAttachment.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toAuthenticatorAttachment: () => (/* binding */ toAuthenticatorAttachment)\n/* harmony export */ });\nconst attachments = ['cross-platform', 'platform'];\n/**\n * If possible coerce a `string` value into a known `AuthenticatorAttachment`\n */\nfunction toAuthenticatorAttachment(attachment) {\n    if (!attachment) {\n        return;\n    }\n    if (attachments.indexOf(attachment) < 0) {\n        return;\n    }\n    return attachment;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNpbXBsZXdlYmF1dGhuL2Jyb3dzZXIvZXNtL2hlbHBlcnMvdG9BdXRoZW50aWNhdG9yQXR0YWNobWVudC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxoYWNrb3Rob25cXEJsb2NrY2hhaW5cXFZpbnRyZWtcXG5vZGVfbW9kdWxlc1xcQHNpbXBsZXdlYmF1dGhuXFxicm93c2VyXFxlc21cXGhlbHBlcnNcXHRvQXV0aGVudGljYXRvckF0dGFjaG1lbnQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgYXR0YWNobWVudHMgPSBbJ2Nyb3NzLXBsYXRmb3JtJywgJ3BsYXRmb3JtJ107XG4vKipcbiAqIElmIHBvc3NpYmxlIGNvZXJjZSBhIGBzdHJpbmdgIHZhbHVlIGludG8gYSBrbm93biBgQXV0aGVudGljYXRvckF0dGFjaG1lbnRgXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB0b0F1dGhlbnRpY2F0b3JBdHRhY2htZW50KGF0dGFjaG1lbnQpIHtcbiAgICBpZiAoIWF0dGFjaG1lbnQpIHtcbiAgICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBpZiAoYXR0YWNobWVudHMuaW5kZXhPZihhdHRhY2htZW50KSA8IDApIHtcbiAgICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICByZXR1cm4gYXR0YWNobWVudDtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/toAuthenticatorAttachment.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/toPublicKeyCredentialDescriptor.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/@simplewebauthn/browser/esm/helpers/toPublicKeyCredentialDescriptor.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toPublicKeyCredentialDescriptor: () => (/* binding */ toPublicKeyCredentialDescriptor)\n/* harmony export */ });\n/* harmony import */ var _base64URLStringToBuffer_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./base64URLStringToBuffer.js */ \"(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/base64URLStringToBuffer.js\");\n\nfunction toPublicKeyCredentialDescriptor(descriptor) {\n    const { id } = descriptor;\n    return {\n        ...descriptor,\n        id: (0,_base64URLStringToBuffer_js__WEBPACK_IMPORTED_MODULE_0__.base64URLStringToBuffer)(id),\n        /**\n         * `descriptor.transports` is an array of our `AuthenticatorTransportFuture` that includes newer\n         * transports that TypeScript's DOM lib is ignorant of. Convince TS that our list of transports\n         * are fine to pass to WebAuthn since browsers will recognize the new value.\n         */\n        transports: descriptor.transports,\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNpbXBsZXdlYmF1dGhuL2Jyb3dzZXIvZXNtL2hlbHBlcnMvdG9QdWJsaWNLZXlDcmVkZW50aWFsRGVzY3JpcHRvci5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF1RTtBQUNoRTtBQUNQLFlBQVksS0FBSztBQUNqQjtBQUNBO0FBQ0EsWUFBWSxvRkFBdUI7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXGhhY2tvdGhvblxcQmxvY2tjaGFpblxcVmludHJla1xcbm9kZV9tb2R1bGVzXFxAc2ltcGxld2ViYXV0aG5cXGJyb3dzZXJcXGVzbVxcaGVscGVyc1xcdG9QdWJsaWNLZXlDcmVkZW50aWFsRGVzY3JpcHRvci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBiYXNlNjRVUkxTdHJpbmdUb0J1ZmZlciB9IGZyb20gJy4vYmFzZTY0VVJMU3RyaW5nVG9CdWZmZXIuanMnO1xuZXhwb3J0IGZ1bmN0aW9uIHRvUHVibGljS2V5Q3JlZGVudGlhbERlc2NyaXB0b3IoZGVzY3JpcHRvcikge1xuICAgIGNvbnN0IHsgaWQgfSA9IGRlc2NyaXB0b3I7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgLi4uZGVzY3JpcHRvcixcbiAgICAgICAgaWQ6IGJhc2U2NFVSTFN0cmluZ1RvQnVmZmVyKGlkKSxcbiAgICAgICAgLyoqXG4gICAgICAgICAqIGBkZXNjcmlwdG9yLnRyYW5zcG9ydHNgIGlzIGFuIGFycmF5IG9mIG91ciBgQXV0aGVudGljYXRvclRyYW5zcG9ydEZ1dHVyZWAgdGhhdCBpbmNsdWRlcyBuZXdlclxuICAgICAgICAgKiB0cmFuc3BvcnRzIHRoYXQgVHlwZVNjcmlwdCdzIERPTSBsaWIgaXMgaWdub3JhbnQgb2YuIENvbnZpbmNlIFRTIHRoYXQgb3VyIGxpc3Qgb2YgdHJhbnNwb3J0c1xuICAgICAgICAgKiBhcmUgZmluZSB0byBwYXNzIHRvIFdlYkF1dGhuIHNpbmNlIGJyb3dzZXJzIHdpbGwgcmVjb2duaXplIHRoZSBuZXcgdmFsdWUuXG4gICAgICAgICAqL1xuICAgICAgICB0cmFuc3BvcnRzOiBkZXNjcmlwdG9yLnRyYW5zcG9ydHMsXG4gICAgfTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/toPublicKeyCredentialDescriptor.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/webAuthnAbortService.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/@simplewebauthn/browser/esm/helpers/webAuthnAbortService.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WebAuthnAbortService: () => (/* binding */ WebAuthnAbortService)\n/* harmony export */ });\nclass BaseWebAuthnAbortService {\n    constructor(){\n        Object.defineProperty(this, \"controller\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n    }\n    createNewAbortSignal() {\n        // Abort any existing calls to navigator.credentials.create() or navigator.credentials.get()\n        if (this.controller) {\n            const abortError = new Error('Cancelling existing WebAuthn API call for new one');\n            abortError.name = 'AbortError';\n            this.controller.abort(abortError);\n        }\n        const newController = new AbortController();\n        this.controller = newController;\n        return newController.signal;\n    }\n    cancelCeremony() {\n        if (this.controller) {\n            const abortError = new Error('Manually cancelling existing WebAuthn API call');\n            abortError.name = 'AbortError';\n            this.controller.abort(abortError);\n            this.controller = undefined;\n        }\n    }\n}\n/**\n * A service singleton to help ensure that only a single WebAuthn ceremony is active at a time.\n *\n * Users of **@simplewebauthn/browser** shouldn't typically need to use this, but it can help e.g.\n * developers building projects that use client-side routing to better control the behavior of\n * their UX in response to router navigation events.\n */ const WebAuthnAbortService = new BaseWebAuthnAbortService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/webAuthnAbortService.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/webAuthnError.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@simplewebauthn/browser/esm/helpers/webAuthnError.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WebAuthnError: () => (/* binding */ WebAuthnError)\n/* harmony export */ });\n/**\n * A custom Error used to return a more nuanced error detailing _why_ one of the eight documented\n * errors in the spec was raised after calling `navigator.credentials.create()` or\n * `navigator.credentials.get()`:\n *\n * - `AbortError`\n * - `ConstraintError`\n * - `InvalidStateError`\n * - `NotAllowedError`\n * - `NotSupportedError`\n * - `SecurityError`\n * - `TypeError`\n * - `UnknownError`\n *\n * Error messages were determined through investigation of the spec to determine under which\n * scenarios a given error would be raised.\n */\nclass WebAuthnError extends Error {\n    constructor({ message, code, cause, name, }) {\n        // @ts-ignore: help Rollup understand that `cause` is okay to set\n        super(message, { cause });\n        Object.defineProperty(this, \"code\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.name = name ?? cause.name;\n        this.code = code;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/webAuthnError.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@simplewebauthn/browser/esm/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/@simplewebauthn/browser/esm/index.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WebAuthnAbortService: () => (/* reexport safe */ _helpers_webAuthnAbortService_js__WEBPACK_IMPORTED_MODULE_7__.WebAuthnAbortService),\n/* harmony export */   WebAuthnError: () => (/* reexport safe */ _helpers_webAuthnError_js__WEBPACK_IMPORTED_MODULE_8__.WebAuthnError),\n/* harmony export */   _browserSupportsWebAuthnAutofillInternals: () => (/* reexport safe */ _helpers_browserSupportsWebAuthnAutofill_js__WEBPACK_IMPORTED_MODULE_4__._browserSupportsWebAuthnAutofillInternals),\n/* harmony export */   _browserSupportsWebAuthnInternals: () => (/* reexport safe */ _helpers_browserSupportsWebAuthn_js__WEBPACK_IMPORTED_MODULE_2__._browserSupportsWebAuthnInternals),\n/* harmony export */   base64URLStringToBuffer: () => (/* reexport safe */ _helpers_base64URLStringToBuffer_js__WEBPACK_IMPORTED_MODULE_5__.base64URLStringToBuffer),\n/* harmony export */   browserSupportsWebAuthn: () => (/* reexport safe */ _helpers_browserSupportsWebAuthn_js__WEBPACK_IMPORTED_MODULE_2__.browserSupportsWebAuthn),\n/* harmony export */   browserSupportsWebAuthnAutofill: () => (/* reexport safe */ _helpers_browserSupportsWebAuthnAutofill_js__WEBPACK_IMPORTED_MODULE_4__.browserSupportsWebAuthnAutofill),\n/* harmony export */   bufferToBase64URLString: () => (/* reexport safe */ _helpers_bufferToBase64URLString_js__WEBPACK_IMPORTED_MODULE_6__.bufferToBase64URLString),\n/* harmony export */   platformAuthenticatorIsAvailable: () => (/* reexport safe */ _helpers_platformAuthenticatorIsAvailable_js__WEBPACK_IMPORTED_MODULE_3__.platformAuthenticatorIsAvailable),\n/* harmony export */   startAuthentication: () => (/* reexport safe */ _methods_startAuthentication_js__WEBPACK_IMPORTED_MODULE_1__.startAuthentication),\n/* harmony export */   startRegistration: () => (/* reexport safe */ _methods_startRegistration_js__WEBPACK_IMPORTED_MODULE_0__.startRegistration)\n/* harmony export */ });\n/* harmony import */ var _methods_startRegistration_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./methods/startRegistration.js */ \"(ssr)/./node_modules/@simplewebauthn/browser/esm/methods/startRegistration.js\");\n/* harmony import */ var _methods_startAuthentication_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./methods/startAuthentication.js */ \"(ssr)/./node_modules/@simplewebauthn/browser/esm/methods/startAuthentication.js\");\n/* harmony import */ var _helpers_browserSupportsWebAuthn_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./helpers/browserSupportsWebAuthn.js */ \"(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/browserSupportsWebAuthn.js\");\n/* harmony import */ var _helpers_platformAuthenticatorIsAvailable_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./helpers/platformAuthenticatorIsAvailable.js */ \"(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/platformAuthenticatorIsAvailable.js\");\n/* harmony import */ var _helpers_browserSupportsWebAuthnAutofill_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./helpers/browserSupportsWebAuthnAutofill.js */ \"(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/browserSupportsWebAuthnAutofill.js\");\n/* harmony import */ var _helpers_base64URLStringToBuffer_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./helpers/base64URLStringToBuffer.js */ \"(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/base64URLStringToBuffer.js\");\n/* harmony import */ var _helpers_bufferToBase64URLString_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./helpers/bufferToBase64URLString.js */ \"(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/bufferToBase64URLString.js\");\n/* harmony import */ var _helpers_webAuthnAbortService_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./helpers/webAuthnAbortService.js */ \"(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/webAuthnAbortService.js\");\n/* harmony import */ var _helpers_webAuthnError_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./helpers/webAuthnError.js */ \"(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/webAuthnError.js\");\n/* harmony import */ var _types_index_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./types/index.js */ \"(ssr)/./node_modules/@simplewebauthn/browser/esm/types/index.js\");\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNpbXBsZXdlYmF1dGhuL2Jyb3dzZXIvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUErQztBQUNFO0FBQ0k7QUFDUztBQUNEO0FBQ1I7QUFDQTtBQUNIO0FBQ1A7QUFDViIsInNvdXJjZXMiOlsiRDpcXGhhY2tvdGhvblxcQmxvY2tjaGFpblxcVmludHJla1xcbm9kZV9tb2R1bGVzXFxAc2ltcGxld2ViYXV0aG5cXGJyb3dzZXJcXGVzbVxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi9tZXRob2RzL3N0YXJ0UmVnaXN0cmF0aW9uLmpzJztcbmV4cG9ydCAqIGZyb20gJy4vbWV0aG9kcy9zdGFydEF1dGhlbnRpY2F0aW9uLmpzJztcbmV4cG9ydCAqIGZyb20gJy4vaGVscGVycy9icm93c2VyU3VwcG9ydHNXZWJBdXRobi5qcyc7XG5leHBvcnQgKiBmcm9tICcuL2hlbHBlcnMvcGxhdGZvcm1BdXRoZW50aWNhdG9ySXNBdmFpbGFibGUuanMnO1xuZXhwb3J0ICogZnJvbSAnLi9oZWxwZXJzL2Jyb3dzZXJTdXBwb3J0c1dlYkF1dGhuQXV0b2ZpbGwuanMnO1xuZXhwb3J0ICogZnJvbSAnLi9oZWxwZXJzL2Jhc2U2NFVSTFN0cmluZ1RvQnVmZmVyLmpzJztcbmV4cG9ydCAqIGZyb20gJy4vaGVscGVycy9idWZmZXJUb0Jhc2U2NFVSTFN0cmluZy5qcyc7XG5leHBvcnQgKiBmcm9tICcuL2hlbHBlcnMvd2ViQXV0aG5BYm9ydFNlcnZpY2UuanMnO1xuZXhwb3J0ICogZnJvbSAnLi9oZWxwZXJzL3dlYkF1dGhuRXJyb3IuanMnO1xuZXhwb3J0ICogZnJvbSAnLi90eXBlcy9pbmRleC5qcyc7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@simplewebauthn/browser/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@simplewebauthn/browser/esm/methods/startAuthentication.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@simplewebauthn/browser/esm/methods/startAuthentication.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   startAuthentication: () => (/* binding */ startAuthentication)\n/* harmony export */ });\n/* harmony import */ var _helpers_bufferToBase64URLString_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../helpers/bufferToBase64URLString.js */ \"(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/bufferToBase64URLString.js\");\n/* harmony import */ var _helpers_base64URLStringToBuffer_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../helpers/base64URLStringToBuffer.js */ \"(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/base64URLStringToBuffer.js\");\n/* harmony import */ var _helpers_browserSupportsWebAuthn_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../helpers/browserSupportsWebAuthn.js */ \"(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/browserSupportsWebAuthn.js\");\n/* harmony import */ var _helpers_browserSupportsWebAuthnAutofill_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../helpers/browserSupportsWebAuthnAutofill.js */ \"(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/browserSupportsWebAuthnAutofill.js\");\n/* harmony import */ var _helpers_toPublicKeyCredentialDescriptor_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../helpers/toPublicKeyCredentialDescriptor.js */ \"(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/toPublicKeyCredentialDescriptor.js\");\n/* harmony import */ var _helpers_identifyAuthenticationError_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../helpers/identifyAuthenticationError.js */ \"(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/identifyAuthenticationError.js\");\n/* harmony import */ var _helpers_webAuthnAbortService_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../helpers/webAuthnAbortService.js */ \"(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/webAuthnAbortService.js\");\n/* harmony import */ var _helpers_toAuthenticatorAttachment_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../helpers/toAuthenticatorAttachment.js */ \"(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/toAuthenticatorAttachment.js\");\n\n\n\n\n\n\n\n\n/**\n * Begin authenticator \"login\" via WebAuthn assertion\n *\n * @param optionsJSON Output from **@simplewebauthn/server**'s `generateAuthenticationOptions()`\n * @param useBrowserAutofill (Optional) Initialize conditional UI to enable logging in via browser autofill prompts. Defaults to `false`.\n * @param verifyBrowserAutofillInput (Optional) Ensure a suitable `<input>` element is present when `useBrowserAutofill` is `true`. Defaults to `true`.\n */\nasync function startAuthentication(options) {\n    // @ts-ignore: Intentionally check for old call structure to warn about improper API call\n    if (!options.optionsJSON && options.challenge) {\n        console.warn('startAuthentication() was not called correctly. It will try to continue with the provided options, but this call should be refactored to use the expected call structure instead. See https://simplewebauthn.dev/docs/packages/browser#typeerror-cannot-read-properties-of-undefined-reading-challenge for more information.');\n        // @ts-ignore: Reassign the options, passed in as a positional argument, to the expected variable\n        options = { optionsJSON: options };\n    }\n    const { optionsJSON, useBrowserAutofill = false, verifyBrowserAutofillInput = true, } = options;\n    if (!(0,_helpers_browserSupportsWebAuthn_js__WEBPACK_IMPORTED_MODULE_2__.browserSupportsWebAuthn)()) {\n        throw new Error('WebAuthn is not supported in this browser');\n    }\n    // We need to avoid passing empty array to avoid blocking retrieval\n    // of public key\n    let allowCredentials;\n    if (optionsJSON.allowCredentials?.length !== 0) {\n        allowCredentials = optionsJSON.allowCredentials?.map(_helpers_toPublicKeyCredentialDescriptor_js__WEBPACK_IMPORTED_MODULE_4__.toPublicKeyCredentialDescriptor);\n    }\n    // We need to convert some values to Uint8Arrays before passing the credentials to the navigator\n    const publicKey = {\n        ...optionsJSON,\n        challenge: (0,_helpers_base64URLStringToBuffer_js__WEBPACK_IMPORTED_MODULE_1__.base64URLStringToBuffer)(optionsJSON.challenge),\n        allowCredentials,\n    };\n    // Prepare options for `.get()`\n    const getOptions = {};\n    /**\n     * Set up the page to prompt the user to select a credential for authentication via the browser's\n     * input autofill mechanism.\n     */\n    if (useBrowserAutofill) {\n        if (!(await (0,_helpers_browserSupportsWebAuthnAutofill_js__WEBPACK_IMPORTED_MODULE_3__.browserSupportsWebAuthnAutofill)())) {\n            throw Error('Browser does not support WebAuthn autofill');\n        }\n        // Check for an <input> with \"webauthn\" in its `autocomplete` attribute\n        const eligibleInputs = document.querySelectorAll(\"input[autocomplete$='webauthn']\");\n        // WebAuthn autofill requires at least one valid input\n        if (eligibleInputs.length < 1 && verifyBrowserAutofillInput) {\n            throw Error('No <input> with \"webauthn\" as the only or last value in its `autocomplete` attribute was detected');\n        }\n        // `CredentialMediationRequirement` doesn't know about \"conditional\" yet as of\n        // typescript@4.6.3\n        getOptions.mediation = 'conditional';\n        // Conditional UI requires an empty allow list\n        publicKey.allowCredentials = [];\n    }\n    // Finalize options\n    getOptions.publicKey = publicKey;\n    // Set up the ability to cancel this request if the user attempts another\n    getOptions.signal = _helpers_webAuthnAbortService_js__WEBPACK_IMPORTED_MODULE_6__.WebAuthnAbortService.createNewAbortSignal();\n    // Wait for the user to complete assertion\n    let credential;\n    try {\n        credential = (await navigator.credentials.get(getOptions));\n    }\n    catch (err) {\n        throw (0,_helpers_identifyAuthenticationError_js__WEBPACK_IMPORTED_MODULE_5__.identifyAuthenticationError)({ error: err, options: getOptions });\n    }\n    if (!credential) {\n        throw new Error('Authentication was not completed');\n    }\n    const { id, rawId, response, type } = credential;\n    let userHandle = undefined;\n    if (response.userHandle) {\n        userHandle = (0,_helpers_bufferToBase64URLString_js__WEBPACK_IMPORTED_MODULE_0__.bufferToBase64URLString)(response.userHandle);\n    }\n    // Convert values to base64 to make it easier to send back to the server\n    return {\n        id,\n        rawId: (0,_helpers_bufferToBase64URLString_js__WEBPACK_IMPORTED_MODULE_0__.bufferToBase64URLString)(rawId),\n        response: {\n            authenticatorData: (0,_helpers_bufferToBase64URLString_js__WEBPACK_IMPORTED_MODULE_0__.bufferToBase64URLString)(response.authenticatorData),\n            clientDataJSON: (0,_helpers_bufferToBase64URLString_js__WEBPACK_IMPORTED_MODULE_0__.bufferToBase64URLString)(response.clientDataJSON),\n            signature: (0,_helpers_bufferToBase64URLString_js__WEBPACK_IMPORTED_MODULE_0__.bufferToBase64URLString)(response.signature),\n            userHandle,\n        },\n        type,\n        clientExtensionResults: credential.getClientExtensionResults(),\n        authenticatorAttachment: (0,_helpers_toAuthenticatorAttachment_js__WEBPACK_IMPORTED_MODULE_7__.toAuthenticatorAttachment)(credential.authenticatorAttachment),\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@simplewebauthn/browser/esm/methods/startAuthentication.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@simplewebauthn/browser/esm/methods/startRegistration.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@simplewebauthn/browser/esm/methods/startRegistration.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   startRegistration: () => (/* binding */ startRegistration)\n/* harmony export */ });\n/* harmony import */ var _helpers_bufferToBase64URLString_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../helpers/bufferToBase64URLString.js */ \"(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/bufferToBase64URLString.js\");\n/* harmony import */ var _helpers_base64URLStringToBuffer_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../helpers/base64URLStringToBuffer.js */ \"(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/base64URLStringToBuffer.js\");\n/* harmony import */ var _helpers_browserSupportsWebAuthn_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../helpers/browserSupportsWebAuthn.js */ \"(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/browserSupportsWebAuthn.js\");\n/* harmony import */ var _helpers_toPublicKeyCredentialDescriptor_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../helpers/toPublicKeyCredentialDescriptor.js */ \"(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/toPublicKeyCredentialDescriptor.js\");\n/* harmony import */ var _helpers_identifyRegistrationError_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../helpers/identifyRegistrationError.js */ \"(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/identifyRegistrationError.js\");\n/* harmony import */ var _helpers_webAuthnAbortService_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../helpers/webAuthnAbortService.js */ \"(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/webAuthnAbortService.js\");\n/* harmony import */ var _helpers_toAuthenticatorAttachment_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../helpers/toAuthenticatorAttachment.js */ \"(ssr)/./node_modules/@simplewebauthn/browser/esm/helpers/toAuthenticatorAttachment.js\");\n\n\n\n\n\n\n\n/**\n * Begin authenticator \"registration\" via WebAuthn attestation\n *\n * @param optionsJSON Output from **@simplewebauthn/server**'s `generateRegistrationOptions()`\n * @param useAutoRegister (Optional) Try to silently create a passkey with the password manager that the user just signed in with. Defaults to `false`.\n */\nasync function startRegistration(options) {\n    // @ts-ignore: Intentionally check for old call structure to warn about improper API call\n    if (!options.optionsJSON && options.challenge) {\n        console.warn('startRegistration() was not called correctly. It will try to continue with the provided options, but this call should be refactored to use the expected call structure instead. See https://simplewebauthn.dev/docs/packages/browser#typeerror-cannot-read-properties-of-undefined-reading-challenge for more information.');\n        // @ts-ignore: Reassign the options, passed in as a positional argument, to the expected variable\n        options = { optionsJSON: options };\n    }\n    const { optionsJSON, useAutoRegister = false } = options;\n    if (!(0,_helpers_browserSupportsWebAuthn_js__WEBPACK_IMPORTED_MODULE_2__.browserSupportsWebAuthn)()) {\n        throw new Error('WebAuthn is not supported in this browser');\n    }\n    // We need to convert some values to Uint8Arrays before passing the credentials to the navigator\n    const publicKey = {\n        ...optionsJSON,\n        challenge: (0,_helpers_base64URLStringToBuffer_js__WEBPACK_IMPORTED_MODULE_1__.base64URLStringToBuffer)(optionsJSON.challenge),\n        user: {\n            ...optionsJSON.user,\n            id: (0,_helpers_base64URLStringToBuffer_js__WEBPACK_IMPORTED_MODULE_1__.base64URLStringToBuffer)(optionsJSON.user.id),\n        },\n        excludeCredentials: optionsJSON.excludeCredentials?.map(_helpers_toPublicKeyCredentialDescriptor_js__WEBPACK_IMPORTED_MODULE_3__.toPublicKeyCredentialDescriptor),\n    };\n    // Prepare options for `.create()`\n    const createOptions = {};\n    /**\n     * Try to use conditional create to register a passkey for the user with the password manager\n     * the user just used to authenticate with. The user won't be shown any prominent UI by the\n     * browser.\n     */\n    if (useAutoRegister) {\n        // @ts-ignore: `mediation` doesn't yet exist on CredentialCreationOptions but it's possible as of Sept 2024\n        createOptions.mediation = 'conditional';\n    }\n    // Finalize options\n    createOptions.publicKey = publicKey;\n    // Set up the ability to cancel this request if the user attempts another\n    createOptions.signal = _helpers_webAuthnAbortService_js__WEBPACK_IMPORTED_MODULE_5__.WebAuthnAbortService.createNewAbortSignal();\n    // Wait for the user to complete attestation\n    let credential;\n    try {\n        credential = (await navigator.credentials.create(createOptions));\n    }\n    catch (err) {\n        throw (0,_helpers_identifyRegistrationError_js__WEBPACK_IMPORTED_MODULE_4__.identifyRegistrationError)({ error: err, options: createOptions });\n    }\n    if (!credential) {\n        throw new Error('Registration was not completed');\n    }\n    const { id, rawId, response, type } = credential;\n    // Continue to play it safe with `getTransports()` for now, even when L3 types say it's required\n    let transports = undefined;\n    if (typeof response.getTransports === 'function') {\n        transports = response.getTransports();\n    }\n    // L3 says this is required, but browser and webview support are still not guaranteed.\n    let responsePublicKeyAlgorithm = undefined;\n    if (typeof response.getPublicKeyAlgorithm === 'function') {\n        try {\n            responsePublicKeyAlgorithm = response.getPublicKeyAlgorithm();\n        }\n        catch (error) {\n            warnOnBrokenImplementation('getPublicKeyAlgorithm()', error);\n        }\n    }\n    let responsePublicKey = undefined;\n    if (typeof response.getPublicKey === 'function') {\n        try {\n            const _publicKey = response.getPublicKey();\n            if (_publicKey !== null) {\n                responsePublicKey = (0,_helpers_bufferToBase64URLString_js__WEBPACK_IMPORTED_MODULE_0__.bufferToBase64URLString)(_publicKey);\n            }\n        }\n        catch (error) {\n            warnOnBrokenImplementation('getPublicKey()', error);\n        }\n    }\n    // L3 says this is required, but browser and webview support are still not guaranteed.\n    let responseAuthenticatorData;\n    if (typeof response.getAuthenticatorData === 'function') {\n        try {\n            responseAuthenticatorData = (0,_helpers_bufferToBase64URLString_js__WEBPACK_IMPORTED_MODULE_0__.bufferToBase64URLString)(response.getAuthenticatorData());\n        }\n        catch (error) {\n            warnOnBrokenImplementation('getAuthenticatorData()', error);\n        }\n    }\n    return {\n        id,\n        rawId: (0,_helpers_bufferToBase64URLString_js__WEBPACK_IMPORTED_MODULE_0__.bufferToBase64URLString)(rawId),\n        response: {\n            attestationObject: (0,_helpers_bufferToBase64URLString_js__WEBPACK_IMPORTED_MODULE_0__.bufferToBase64URLString)(response.attestationObject),\n            clientDataJSON: (0,_helpers_bufferToBase64URLString_js__WEBPACK_IMPORTED_MODULE_0__.bufferToBase64URLString)(response.clientDataJSON),\n            transports,\n            publicKeyAlgorithm: responsePublicKeyAlgorithm,\n            publicKey: responsePublicKey,\n            authenticatorData: responseAuthenticatorData,\n        },\n        type,\n        clientExtensionResults: credential.getClientExtensionResults(),\n        authenticatorAttachment: (0,_helpers_toAuthenticatorAttachment_js__WEBPACK_IMPORTED_MODULE_6__.toAuthenticatorAttachment)(credential.authenticatorAttachment),\n    };\n}\n/**\n * Visibly warn when we detect an issue related to a passkey provider intercepting WebAuthn API\n * calls\n */\nfunction warnOnBrokenImplementation(methodName, cause) {\n    console.warn(`The browser extension that intercepted this WebAuthn API call incorrectly implemented ${methodName}. You should report this error to them.\\n`, cause);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@simplewebauthn/browser/esm/methods/startRegistration.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@simplewebauthn/browser/esm/types/index.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@simplewebauthn/browser/esm/types/index.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHNpbXBsZXdlYmF1dGhuL2Jyb3dzZXIvZXNtL3R5cGVzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7QUFBVSIsInNvdXJjZXMiOlsiRDpcXGhhY2tvdGhvblxcQmxvY2tjaGFpblxcVmludHJla1xcbm9kZV9tb2R1bGVzXFxAc2ltcGxld2ViYXV0aG5cXGJyb3dzZXJcXGVzbVxcdHlwZXNcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7fTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@simplewebauthn/browser/esm/types/index.js\n");

/***/ })

};
;