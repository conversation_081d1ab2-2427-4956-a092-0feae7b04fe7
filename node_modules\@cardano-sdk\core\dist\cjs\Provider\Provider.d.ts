import { Cardano } from '..';
import { Logger } from 'ts-log';
import { Percent } from '@cardano-sdk/util';
import { Tip } from '../Cardano';
export declare type HealthCheckResponse = {
    ok: boolean;
    localNode?: {
        ledgerTip?: Tip;
        networkSync?: Percent;
    };
    projectedTip?: Cardano.Tip;
    reason?: string;
};
export interface ProviderDependencies {
    logger: Logger;
}
export interface Provider {
    healthCheck(): Promise<HealthCheckResponse>;
}
export declare type HttpProviderConfigPaths<T extends Provider> = {
    [methodName in keyof T]: string;
};
//# sourceMappingURL=Provider.d.ts.map