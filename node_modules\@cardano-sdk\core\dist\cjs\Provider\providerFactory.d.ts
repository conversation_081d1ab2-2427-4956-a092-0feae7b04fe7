import { Logger } from 'ts-log';
export interface ProviderFactoryMethod<T> {
    (params: any, logger: Logger): Promise<T>;
}
export declare class ProviderFactory<T> {
    #private;
    register(name: string, providerFactoryMethod: ProviderFactoryMethod<T>): void;
    create(name: string, params: any, logger: Logger): Promise<T>;
    getProviders(): Array<string>;
}
//# sourceMappingURL=providerFactory.d.ts.map