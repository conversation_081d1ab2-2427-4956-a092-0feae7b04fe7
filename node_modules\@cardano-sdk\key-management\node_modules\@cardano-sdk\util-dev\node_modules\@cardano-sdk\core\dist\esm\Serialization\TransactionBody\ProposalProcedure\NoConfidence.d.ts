import { GovernanceActionId } from '../../Common/GovernanceActionId.js';
import { HexBlob } from '@cardano-sdk/util';
import type * as Cardano from '../../../Cardano/index.js';
export declare class NoConfidence {
    #private;
    constructor(govActionId?: GovernanceActionId);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): NoConfidence;
    toCore(): Cardano.NoConfidence;
    static fromCore(noConfidence: Cardano.NoConfidence): NoConfidence;
    govActionId(): GovernanceActionId | undefined;
}
//# sourceMappingURL=NoConfidence.d.ts.map