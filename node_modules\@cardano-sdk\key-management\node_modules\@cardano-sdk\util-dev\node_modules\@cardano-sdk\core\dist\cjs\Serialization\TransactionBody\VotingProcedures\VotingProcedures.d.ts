import { GovernanceActionId } from '../../Common/GovernanceActionId';
import { HexBlob } from '@cardano-sdk/util';
import { Voter } from './Voter';
import { VotingProcedure } from './VotingProcedure';
import type * as Cardano from '../../../Cardano';
export declare class VotingProcedures {
    #private;
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): VotingProcedures;
    toCore(): Cardano.VotingProcedures;
    static fromCore(votingProcedures: Cardano.VotingProcedures): VotingProcedures;
    insert(voter: Voter, actionId: GovernanceActionId, votingProcedure: VotingProcedure): void;
    get(voter: Voter, governanceActionId: GovernanceActionId): VotingProcedure | undefined;
    getVoters(): Array<Voter>;
    getGovernanceActionIdsByVoter(voter: Voter): Array<GovernanceActionId>;
}
//# sourceMappingURL=VotingProcedures.d.ts.map