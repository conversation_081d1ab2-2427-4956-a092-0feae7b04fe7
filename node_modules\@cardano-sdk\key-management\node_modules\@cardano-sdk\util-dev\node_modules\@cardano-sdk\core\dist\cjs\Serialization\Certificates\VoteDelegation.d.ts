import { DRep } from './DRep';
import { HexBlob } from '@cardano-sdk/util';
import type * as Cardano from '../../Cardano';
export declare class VoteDelegation {
    #private;
    constructor(stakeCredential: Cardano.Credential, dRep: DRep);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): VoteDelegation;
    toCore(): Cardano.VoteDelegationCertificate;
    static fromCore(deleg: Cardano.VoteDelegationCertificate): VoteDelegation;
    stakeCredential(): Cardano.Credential;
    dRep(): DRep;
}
//# sourceMappingURL=VoteDelegation.d.ts.map