import { OpaqueNumber } from '@cardano-sdk/util';
export declare type Milliseconds = OpaqueNumber<'Milliseconds'>;
export declare const Milliseconds: {
    (value: number): Milliseconds;
    toSeconds(value: Milliseconds): Seconds;
};
export declare type Seconds = OpaqueNumber<'Seconds'>;
export declare const Seconds: {
    (value: number): Seconds;
    toMinutes(value: Seconds): Minutes;
    toMilliseconds(value: Seconds): Milliseconds;
};
export declare type Minutes = OpaqueNumber<'Minutes'>;
export declare const Minutes: {
    (value: number): Minutes;
    toHours(value: Minutes): Hours;
    toSeconds(value: Minutes): Seconds;
};
export declare type Hours = OpaqueNumber<'Hours'>;
export declare const Hours: {
    (value: number): Hours;
    toDays(value: Hours): Days;
    toMinutes(value: Hours): Minutes;
};
export declare type Days = OpaqueNumber<'Days'>;
export declare const Days: {
    (value: number): Days;
    toHours(value: Days): Hours;
};
export declare class TimeSpan {
    private readonly elapsed;
    constructor(elapsed: Milliseconds);
    static fromMilliseconds(milliseconds: Milliseconds): TimeSpan;
    static fromSeconds(seconds: Seconds): TimeSpan;
    static fromMinutes(minutes: Minutes): TimeSpan;
    static fromHours(hours: Hours): TimeSpan;
    getTotalDays(): Days;
    getTotalHours(): Hours;
    getTotalMinutes(): Minutes;
    getTotalSeconds(): Seconds;
    getTotalMilliseconds(): Milliseconds;
    getDays(): Days;
    getHours(): Hours;
    getMinutes(): Minutes;
    getSeconds(): Seconds;
    getMilliseconds(): Milliseconds;
    toString(): string;
}
//# sourceMappingURL=time.d.ts.map