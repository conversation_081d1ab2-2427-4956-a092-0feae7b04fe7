"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/blockchain.ts":
/*!*******************************!*\
  !*** ./src/lib/blockchain.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BLOCKCHAIN_CONFIG: () => (/* binding */ BLOCKCHAIN_CONFIG),\n/* harmony export */   BlockchainService: () => (/* binding */ BlockchainService),\n/* harmony export */   TREK_TOKEN: () => (/* binding */ TREK_TOKEN),\n/* harmony export */   blockchainService: () => (/* binding */ blockchainService),\n/* harmony export */   formatAdaToLovelace: () => (/* binding */ formatAdaToLovelace),\n/* harmony export */   formatLovelaceToAda: () => (/* binding */ formatLovelaceToAda),\n/* harmony export */   generateAssetName: () => (/* binding */ generateAssetName)\n/* harmony export */ });\n// Dynamic imports for client-side only\nlet BrowserWallet = null;\nlet Transaction = null;\nlet AssetMetadata = null;\nif (true) {\n    __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_meshsdk_core_dist_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @meshsdk/core */ \"(app-pages-browser)/./node_modules/@meshsdk/core/dist/index.js\")).then((module)=>{\n        BrowserWallet = module.BrowserWallet;\n        Transaction = module.Transaction;\n        AssetMetadata = module.AssetMetadata;\n    });\n}\n// Blockchain configuration\nconst BLOCKCHAIN_CONFIG = {\n    network: \"testnet\" || 0,\n    blockfrostApiKey: \"testnetYourProjectIdHere\" || 0,\n    blockfrostUrl: \"https://cardano-testnet.blockfrost.io/api/v0\" || 0,\n    nftPolicyId: \"placeholder_nft_policy_id\" || 0,\n    tokenPolicyId: \"placeholder_token_policy_id\" || 0,\n    scriptAddress: \"addr_test1placeholder_script_address\" || 0\n};\n// TREK Token configuration\nconst TREK_TOKEN = {\n    symbol: 'TREK',\n    decimals: 6,\n    policyId: BLOCKCHAIN_CONFIG.tokenPolicyId,\n    assetName: '54524b'\n};\n// Blockchain service class\nclass BlockchainService {\n    setWallet(wallet) {\n        this.wallet = wallet;\n    }\n    // Get wallet balance in ADA\n    async getWalletBalance() {\n        if (!this.wallet) throw new Error('Wallet not connected');\n        try {\n            const balance = await this.wallet.getBalance();\n            return balance;\n        } catch (error) {\n            console.error('Error fetching wallet balance:', error);\n            throw error;\n        }\n    }\n    // Get wallet assets (NFTs and tokens)\n    async getWalletAssets() {\n        if (!this.wallet) throw new Error('Wallet not connected');\n        try {\n            const assets = await this.wallet.getAssets();\n            return assets;\n        } catch (error) {\n            console.error('Error fetching wallet assets:', error);\n            throw error;\n        }\n    }\n    // Get TREK token balance\n    async getTrekTokenBalance() {\n        try {\n            const assets = await this.getWalletAssets();\n            const trekAsset = assets.find((asset)=>asset.unit.startsWith(TREK_TOKEN.policyId));\n            if (trekAsset) {\n                return parseInt(trekAsset.quantity) / Math.pow(10, TREK_TOKEN.decimals);\n            }\n            return 0;\n        } catch (error) {\n            console.error('Error fetching TREK token balance:', error);\n            return 0;\n        }\n    }\n    // Get trail NFTs owned by wallet\n    async getTrailNFTs() {\n        try {\n            const assets = await this.getWalletAssets();\n            const nfts = assets.filter((asset)=>asset.unit.startsWith(BLOCKCHAIN_CONFIG.nftPolicyId) && asset.quantity === '1');\n            // Fetch metadata for each NFT\n            const nftsWithMetadata = await Promise.all(nfts.map(async (nft)=>{\n                try {\n                    const metadata = await this.fetchNFTMetadata(nft.unit);\n                    return {\n                        ...nft,\n                        metadata\n                    };\n                } catch (error) {\n                    console.error(\"Error fetching metadata for NFT \".concat(nft.unit, \":\"), error);\n                    return {\n                        ...nft,\n                        metadata: null\n                    };\n                }\n            }));\n            return nftsWithMetadata;\n        } catch (error) {\n            console.error('Error fetching trail NFTs:', error);\n            return [];\n        }\n    }\n    // Fetch NFT metadata from blockchain\n    async fetchNFTMetadata(assetId) {\n        try {\n            const response = await fetch(\"\".concat(BLOCKCHAIN_CONFIG.blockfrostUrl, \"/assets/\").concat(assetId), {\n                headers: {\n                    'project_id': BLOCKCHAIN_CONFIG.blockfrostApiKey\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const assetData = await response.json();\n            if (assetData.onchain_metadata) {\n                return assetData.onchain_metadata;\n            }\n            return null;\n        } catch (error) {\n            console.error('Error fetching NFT metadata:', error);\n            return null;\n        }\n    }\n    // Create booking transaction\n    async createBookingTransaction(trailId, amount, date) {\n        if (!this.wallet) throw new Error('Wallet not connected');\n        try {\n            const walletAddress = await this.wallet.getChangeAddress();\n            // Create booking metadata\n            const bookingMetadata = {\n                trail_id: trailId,\n                booking_date: date,\n                amount: amount,\n                timestamp: new Date().toISOString(),\n                hiker_address: walletAddress\n            };\n            // Build transaction (simplified - in production, this would interact with smart contracts)\n            const tx = new Transaction({\n                initiator: this.wallet\n            });\n            // Add metadata to transaction\n            tx.setMetadata(674, bookingMetadata);\n            // Send payment to script address (or trail operator)\n            tx.sendLovelace(BLOCKCHAIN_CONFIG.scriptAddress, (amount * 1000000).toString() // Convert ADA to Lovelace\n            );\n            const unsignedTx = await tx.build();\n            const signedTx = await this.wallet.signTx(unsignedTx);\n            const txHash = await this.wallet.submitTx(signedTx);\n            return txHash;\n        } catch (error) {\n            console.error('Error creating booking transaction:', error);\n            throw error;\n        }\n    }\n    // Mint trail completion NFT\n    async mintTrailNFT(trailData) {\n        if (!this.wallet) throw new Error('Wallet not connected');\n        try {\n            const walletAddress = await this.wallet.getChangeAddress();\n            const completionDate = new Date().toISOString();\n            // Create NFT metadata\n            const metadata = {\n                name: \"\".concat(trailData.trailName, \" Completion Certificate\"),\n                description: \"Proof of completion for \".concat(trailData.trailName, \" trail in \").concat(trailData.location),\n                image: \"ipfs://QmTrailNFTImage\".concat(Date.now()),\n                attributes: {\n                    trail_name: trailData.trailName,\n                    location: trailData.location,\n                    difficulty: trailData.difficulty,\n                    completion_date: completionDate,\n                    coordinates: trailData.coordinates,\n                    hiker_address: walletAddress\n                }\n            };\n            // Generate unique asset name\n            const assetName = \"VinTrekNFT\".concat(Date.now());\n            // Build minting transaction (simplified)\n            const tx = new Transaction({\n                initiator: this.wallet\n            });\n            // Add minting logic here (requires smart contract integration)\n            // This is a placeholder - actual implementation would use Mesh SDK's minting functions\n            const unsignedTx = await tx.build();\n            const signedTx = await this.wallet.signTx(unsignedTx);\n            const txHash = await this.wallet.submitTx(signedTx);\n            return txHash;\n        } catch (error) {\n            console.error('Error minting trail NFT:', error);\n            throw error;\n        }\n    }\n    // Mint TREK tokens as rewards\n    async mintTrekTokens(amount, reason) {\n        if (!this.wallet) throw new Error('Wallet not connected');\n        try {\n            const walletAddress = await this.wallet.getChangeAddress();\n            // Create reward metadata\n            const rewardMetadata = {\n                recipient: walletAddress,\n                amount: amount,\n                reason: reason,\n                timestamp: new Date().toISOString()\n            };\n            // Build token minting transaction (simplified)\n            const tx = new Transaction({\n                initiator: this.wallet\n            });\n            // Add metadata\n            tx.setMetadata(674, rewardMetadata);\n            // Add token minting logic here (requires smart contract integration)\n            const unsignedTx = await tx.build();\n            const signedTx = await this.wallet.signTx(unsignedTx);\n            const txHash = await this.wallet.submitTx(signedTx);\n            return txHash;\n        } catch (error) {\n            console.error('Error minting TREK tokens:', error);\n            throw error;\n        }\n    }\n    // Verify transaction on blockchain\n    async verifyTransaction(txHash) {\n        try {\n            const response = await fetch(\"\".concat(BLOCKCHAIN_CONFIG.blockfrostUrl, \"/txs/\").concat(txHash), {\n                headers: {\n                    'project_id': BLOCKCHAIN_CONFIG.blockfrostApiKey\n                }\n            });\n            if (response.ok) {\n                const txData = await response.json();\n                return txData.block !== null // Transaction is confirmed if it's in a block\n                ;\n            }\n            return false;\n        } catch (error) {\n            console.error('Error verifying transaction:', error);\n            return false;\n        }\n    }\n    constructor(wallet = null){\n        this.wallet = null;\n        this.wallet = wallet;\n    }\n}\n// Utility functions\nconst formatLovelaceToAda = (lovelace)=>{\n    return parseInt(lovelace) / 1000000;\n};\nconst formatAdaToLovelace = (ada)=>{\n    return (ada * 1000000).toString();\n};\nconst generateAssetName = (prefix)=>{\n    const timestamp = Date.now();\n    const random = Math.random().toString(36).substr(2, 9);\n    return \"\".concat(prefix).concat(timestamp).concat(random);\n};\n// Export singleton instance\nconst blockchainService = new BlockchainService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/blockchain.ts\n"));

/***/ })

});