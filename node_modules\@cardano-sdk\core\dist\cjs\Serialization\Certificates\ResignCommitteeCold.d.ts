import * as Cardano from '../../Cardano';
import { Anchor } from '../Common';
import { HexBlob } from '@cardano-sdk/util';
export declare class ResignCommitteeCold {
    #private;
    constructor(committeeColdCred: Cardano.Credential, anchor?: Anchor);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): ResignCommitteeCold;
    toCore(): Cardano.ResignCommitteeColdCertificate;
    static fromCore(cert: Cardano.ResignCommitteeColdCertificate): ResignCommitteeCold;
    coldCredential(): Cardano.Credential;
    anchor(): Anchor | undefined;
}
//# sourceMappingURL=ResignCommitteeCold.d.ts.map