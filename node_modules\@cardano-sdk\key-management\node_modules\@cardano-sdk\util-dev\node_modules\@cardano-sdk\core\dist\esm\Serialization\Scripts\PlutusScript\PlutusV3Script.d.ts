import * as Crypto from '@cardano-sdk/crypto';
import { HexBlob } from '@cardano-sdk/util';
import type * as Cardano from '../../../Cardano/index.js';
export declare class PlutusV3Script {
    #private;
    constructor(compiledByteCode: HexBlob);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): PlutusV3Script;
    toCore(): Cardano.PlutusScript;
    static fromCore(plutusScript: Cardano.PlutusScript): PlutusV3Script;
    hash(): Crypto.Hash28ByteBase16;
    rawBytes(): HexBlob;
    setRawBytes(bytes: HexBlob): void;
}
//# sourceMappingURL=PlutusV3Script.d.ts.map