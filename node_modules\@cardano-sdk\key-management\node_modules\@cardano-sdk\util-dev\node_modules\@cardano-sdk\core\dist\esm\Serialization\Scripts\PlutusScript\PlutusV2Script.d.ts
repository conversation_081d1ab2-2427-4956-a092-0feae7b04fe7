import * as Crypto from '@cardano-sdk/crypto';
import { HexBlob } from '@cardano-sdk/util';
import type * as Cardano from '../../../Cardano/index.js';
export declare class PlutusV2Script {
    #private;
    constructor(compiledByteCode: HexBlob);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): PlutusV2Script;
    toCore(): Cardano.PlutusScript;
    static fromCore(plutusScript: Cardano.PlutusScript): PlutusV2Script;
    hash(): Crypto.Hash28ByteBase16;
    rawBytes(): HexBlob;
    setRawBytes(bytes: HexBlob): void;
}
//# sourceMappingURL=PlutusV2Script.d.ts.map