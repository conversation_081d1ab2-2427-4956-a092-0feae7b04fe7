export declare enum CborReaderState {
    Undefined = 0,
    UnsignedInteger = 1,
    NegativeInteger = 2,
    ByteString = 3,
    StartIndefiniteLengthByteString = 4,
    EndIndefiniteLengthByteString = 5,
    TextString = 6,
    StartIndefiniteLengthTextString = 7,
    EndIndefiniteLengthTextString = 8,
    StartArray = 9,
    EndArray = 10,
    StartMap = 11,
    EndMap = 12,
    Tag = 13,
    SimpleValue = 14,
    HalfPrecisionFloat = 15,
    SinglePrecisionFloat = 16,
    DoublePrecisionFloat = 17,
    Null = 18,
    Boolean = 19,
    Finished = 20
}
//# sourceMappingURL=CborReaderState.d.ts.map