import { HexBlob } from '@cardano-sdk/util';
import { UnitInterval } from '../Common/index.js';
import type * as Cardano from '../../Cardano/index.js';
export declare class PoolVotingThresholds {
    #private;
    constructor(motionNoConfidence: UnitInterval, committeeNormal: UnitInterval, committeeNoConfidence: UnitInterval, hardForkInitiation: UnitInterval, securityRelevantParam: UnitInterval);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): PoolVotingThresholds;
    toCore(): Cardano.PoolVotingThresholds;
    static fromCore(core: Cardano.PoolVotingThresholds): PoolVotingThresholds;
    setMotionNoConfidence(threshold: UnitInterval): void;
    setCommitteeNormal(threshold: UnitInterval): void;
    setCommitteeNoConfidence(threshold: UnitInterval): void;
    setHardForkInitiation(threshold: UnitInterval): void;
    motionNoConfidence(): UnitInterval;
    committeeNormal(): UnitInterval;
    committeeNoConfidence(): UnitInterval;
    hardForkInitiation(): UnitInterval;
    securityRelevantParam(): UnitInterval;
}
//# sourceMappingURL=PoolVotingThresholds.d.ts.map