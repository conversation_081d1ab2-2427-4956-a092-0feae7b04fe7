import { HexBlob } from '@cardano-sdk/util';
import { TransactionInput, TransactionOutput } from './TransactionBody';
import type * as Cardano from '../Cardano';
export declare class TransactionUnspentOutput {
    #private;
    constructor(input: TransactionInput, output: TransactionOutput);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): TransactionUnspentOutput;
    toCore(): [Cardano.TxIn, Cardano.TxOut];
    static fromCore(core: [Cardano.TxIn, Cardano.TxOut]): TransactionUnspentOutput;
    input(): TransactionInput;
    setInput(input: TransactionInput): void;
    output(): TransactionOutput;
    setOutput(output: TransactionOutput): void;
}
//# sourceMappingURL=TransactionUnspentOutput.d.ts.map