'use client'

import { useState, useEffect } from 'react'
import { useWallet } from '@/components/providers/WalletProvider'
import { blockchainService } from '@/lib/blockchain'
import { Wallet, Trophy, Coins, MapPin, Calendar, ExternalLink } from 'lucide-react'

interface NFTData {
  unit: string
  quantity: string
  metadata: any
}

export default function DashboardPage() {
  const { connected, wallet, address } = useWallet()
  const [loading, setLoading] = useState(true)
  const [trekBalance, setTrekBalance] = useState(0)
  const [nfts, setNfts] = useState<NFTData[]>([])
  const [adaBalance, setAdaBalance] = useState('0')

  useEffect(() => {
    if (connected && wallet) {
      loadDashboardData()
    }
  }, [connected, wallet])

  const loadDashboardData = async () => {
    if (!wallet) return
    
    setLoading(true)
    try {
      blockchainService.setWallet(wallet)
      
      // Load wallet data
      const [balance, trekTokens, trailNFTs] = await Promise.all([
        blockchainService.getWalletBalance(),
        blockchainService.getTrekTokenBalance(),
        blockchainService.getTrailNFTs(),
      ])
      
      setAdaBalance(balance)
      setTrekBalance(trekTokens)
      setNfts(trailNFTs)
    } catch (error) {
      console.error('Error loading dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatAddress = (addr: string) => {
    return `${addr.slice(0, 8)}...${addr.slice(-8)}`
  }

  const formatAda = (lovelace: string) => {
    try {
      const ada = parseInt(lovelace) / 1000000
      return `${ada.toFixed(2)} ADA`
    } catch {
      return '0.00 ADA'
    }
  }

  if (!connected) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <Wallet className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Connect Your Wallet</h2>
          <p className="text-gray-600 mb-6">Please connect your Cardano wallet to view your dashboard.</p>
          <button 
            onClick={() => window.location.href = '/'}
            className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors"
          >
            Go to Home
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <button 
                onClick={() => window.location.href = '/'}
                className="flex items-center space-x-2 hover:opacity-80 transition-opacity"
              >
                <MapPin className="h-8 w-8 text-green-600" />
                <span className="text-2xl font-bold text-gray-900">VinTrek</span>
              </button>
            </div>
            <nav className="hidden md:flex space-x-8">
              <a href="/" className="text-gray-700 hover:text-green-600 transition-colors">Home</a>
              <a href="/trails" className="text-gray-700 hover:text-green-600 transition-colors">Trails</a>
              <a href="/dashboard" className="text-green-600 font-medium">Dashboard</a>
              <a href="/rewards" className="text-gray-700 hover:text-green-600 transition-colors">Rewards</a>
            </nav>
            <div className="flex items-center space-x-2 text-sm">
              <Wallet className="h-4 w-4 text-green-600" />
              <span className="text-gray-700">{address ? formatAddress(address) : 'Connected'}</span>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Dashboard Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Your Adventure Dashboard</h1>
          <p className="text-gray-600">Track your hiking achievements, NFT collection, and TREK token rewards.</p>
        </div>

        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
          </div>
        ) : (
          <>
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              {/* ADA Balance */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">ADA Balance</p>
                    <p className="text-2xl font-bold text-gray-900">{formatAda(adaBalance)}</p>
                  </div>
                  <div className="bg-blue-100 p-3 rounded-full">
                    <Wallet className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </div>

              {/* TREK Tokens */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">TREK Tokens</p>
                    <p className="text-2xl font-bold text-gray-900">{trekBalance.toLocaleString()}</p>
                  </div>
                  <div className="bg-yellow-100 p-3 rounded-full">
                    <Coins className="h-6 w-6 text-yellow-600" />
                  </div>
                </div>
              </div>

              {/* Trail NFTs */}
              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Trail NFTs</p>
                    <p className="text-2xl font-bold text-gray-900">{nfts.length}</p>
                  </div>
                  <div className="bg-green-100 p-3 rounded-full">
                    <Trophy className="h-6 w-6 text-green-600" />
                  </div>
                </div>
              </div>
            </div>

            {/* NFT Collection */}
            <div className="bg-white rounded-lg shadow-md p-6 mb-8">
              <h2 className="text-xl font-bold text-gray-900 mb-4">Your Trail NFT Collection</h2>
              
              {nfts.length === 0 ? (
                <div className="text-center py-12">
                  <Trophy className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Trail NFTs Yet</h3>
                  <p className="text-gray-600 mb-4">Complete your first trail to mint your first NFT certificate!</p>
                  <button 
                    onClick={() => window.location.href = '/trails'}
                    className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors"
                  >
                    Explore Trails
                  </button>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {nfts.map((nft, index) => (
                    <div key={nft.unit} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                      <div className="aspect-square bg-gradient-to-br from-green-100 to-blue-100 rounded-lg mb-4 flex items-center justify-center">
                        <Trophy className="h-12 w-12 text-green-600" />
                      </div>
                      
                      <h3 className="font-semibold text-gray-900 mb-2">
                        {nft.metadata?.name || `Trail NFT #${index + 1}`}
                      </h3>
                      
                      {nft.metadata?.attributes && (
                        <div className="space-y-1 text-sm text-gray-600">
                          <div className="flex items-center">
                            <MapPin className="h-3 w-3 mr-1" />
                            <span>{nft.metadata.attributes.location}</span>
                          </div>
                          <div className="flex items-center">
                            <Calendar className="h-3 w-3 mr-1" />
                            <span>{new Date(nft.metadata.attributes.completion_date).toLocaleDateString()}</span>
                          </div>
                          <div className="flex items-center">
                            <span className={`px-2 py-1 rounded-full text-xs ${
                              nft.metadata.attributes.difficulty === 'Easy' ? 'bg-green-100 text-green-800' :
                              nft.metadata.attributes.difficulty === 'Moderate' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {nft.metadata.attributes.difficulty}
                            </span>
                          </div>
                        </div>
                      )}
                      
                      <button 
                        onClick={() => window.open(`https://cardanoscan.io/token/${nft.unit}`, '_blank')}
                        className="mt-3 w-full flex items-center justify-center space-x-2 text-blue-600 hover:text-blue-700 transition-colors"
                      >
                        <ExternalLink className="h-4 w-4" />
                        <span>View on Explorer</span>
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Recent Activity */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4">Recent Activity</h2>
              <div className="text-center py-8">
                <Calendar className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-600">Activity tracking coming soon!</p>
                <p className="text-sm text-gray-500 mt-2">
                  We're working on bringing you detailed activity logs and achievement tracking.
                </p>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  )
}
