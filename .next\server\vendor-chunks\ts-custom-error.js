"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/ts-custom-error";
exports.ids = ["vendor-chunks/ts-custom-error"];
exports.modules = {

/***/ "(ssr)/./node_modules/ts-custom-error/dist/custom-error.mjs":
/*!************************************************************!*\
  !*** ./node_modules/ts-custom-error/dist/custom-error.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomError: () => (/* binding */ CustomError),\n/* harmony export */   customErrorFactory: () => (/* binding */ customErrorFactory)\n/* harmony export */ });\nfunction fixProto(target, prototype) {\n  var setPrototypeOf = Object.setPrototypeOf;\n  setPrototypeOf ? setPrototypeOf(target, prototype) : target.__proto__ = prototype;\n}\nfunction fixStack(target, fn) {\n  if (fn === void 0) {\n    fn = target.constructor;\n  }\n\n  var captureStackTrace = Error.captureStackTrace;\n  captureStackTrace && captureStackTrace(target, fn);\n}\n\nvar __extends =  false || function () {\n  var _extendStatics = function extendStatics(d, b) {\n    _extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) {\n        if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];\n      }\n    };\n\n    return _extendStatics(d, b);\n  };\n\n  return function (d, b) {\n    if (typeof b !== \"function\" && b !== null) throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n\n    _extendStatics(d, b);\n\n    function __() {\n      this.constructor = d;\n    }\n\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\n\nvar CustomError = function (_super) {\n  __extends(CustomError, _super);\n\n  function CustomError(message, options) {\n    var _newTarget = this.constructor;\n\n    var _this = _super.call(this, message, options) || this;\n\n    Object.defineProperty(_this, 'name', {\n      value: _newTarget.name,\n      enumerable: false,\n      configurable: true\n    });\n    fixProto(_this, _newTarget.prototype);\n    fixStack(_this);\n    return _this;\n  }\n\n  return CustomError;\n}(Error);\n\nvar __spreadArray =  false || function (to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n};\nfunction customErrorFactory(fn, parent) {\n  if (parent === void 0) {\n    parent = Error;\n  }\n\n  function CustomError() {\n    var args = [];\n\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n\n    if (!(this instanceof CustomError)) return new (CustomError.bind.apply(CustomError, __spreadArray([void 0], args, false)))();\n    parent.apply(this, args);\n    Object.defineProperty(this, 'name', {\n      value: fn.name || parent.name,\n      enumerable: false,\n      configurable: true\n    });\n    fn.apply(this, args);\n    fixStack(this, CustomError);\n  }\n\n  return Object.defineProperties(CustomError, {\n    prototype: {\n      value: Object.create(parent.prototype, {\n        constructor: {\n          value: CustomError,\n          writable: true,\n          configurable: true\n        }\n      })\n    }\n  });\n}\n\n\n//# sourceMappingURL=custom-error.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ts-custom-error/dist/custom-error.mjs\n");

/***/ })

};
;