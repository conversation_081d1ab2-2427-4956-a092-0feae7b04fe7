"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@stricahq";
exports.ids = ["vendor-chunks/@stricahq"];
exports.modules = {

/***/ "(ssr)/./node_modules/@stricahq/bip32ed25519/dist/Bip32PrivateKey.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@stricahq/bip32ed25519/dist/Bip32PrivateKey.js ***!
  \*********************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/* eslint-disable no-bitwise */\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst buffer_1 = __webpack_require__(/*! buffer */ \"buffer\");\nconst bn_js_1 = __importDefault(__webpack_require__(/*! bn.js */ \"(ssr)/./node_modules/bn.js/lib/bn.js\"));\nconst pbkdf2_1 = __webpack_require__(/*! pbkdf2 */ \"(ssr)/./node_modules/pbkdf2/index.js\");\nconst Bip32PublicKey_1 = __importDefault(__webpack_require__(/*! ./Bip32PublicKey */ \"(ssr)/./node_modules/@stricahq/bip32ed25519/dist/Bip32PublicKey.js\"));\nconst PrivateKey_1 = __importDefault(__webpack_require__(/*! ./PrivateKey */ \"(ssr)/./node_modules/@stricahq/bip32ed25519/dist/PrivateKey.js\"));\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/@stricahq/bip32ed25519/dist/utils.js\");\nconst EDDSA = __webpack_require__(/*! ./ed25519e */ \"(ssr)/./node_modules/@stricahq/bip32ed25519/dist/ed25519e/index.js\");\nconst eddsa = new EDDSA();\nclass Bip32PrivateKey {\n    constructor(xprv) {\n        this.xprv = xprv;\n    }\n    static fromEntropy(entropy) {\n        return new Promise((resolve, reject) => {\n            pbkdf2_1.pbkdf2(\"\", entropy, 4096, 96, \"sha512\", (err, xprv) => {\n                if (err) {\n                    reject(err);\n                }\n                // The lowest three bits of the first octet are cleared\n                // 248 or 0xf8 or 0b11111000\n                xprv[0] &= 0b11111000;\n                // the highest bit of the last octet is cleared\n                // 31 or 0x1f or 0b00011111\n                // AND the third highest bit is cleared too\n                xprv[31] &= 0b00011111;\n                // and the second highest bit of the last octet is set\n                // 64 or 0x40 or 0b01000000\n                xprv[31] |= 0b01000000;\n                resolve(new Bip32PrivateKey(xprv));\n            });\n        });\n    }\n    derive(index) {\n        const kl = this.xprv.slice(0, 32);\n        const kr = this.xprv.slice(32, 64);\n        const cc = this.xprv.slice(64, 96);\n        let z;\n        let i;\n        if (index < utils_1.HARDENED_OFFSET) {\n            const data = buffer_1.Buffer.allocUnsafe(1 + 32 + 4);\n            data.writeUInt32LE(index, 1 + 32);\n            const keyPair = eddsa.keyFromSecret(kl.toString(\"hex\"));\n            const vk = buffer_1.Buffer.from(keyPair.pubBytes());\n            vk.copy(data, 1);\n            data[0] = 0x02;\n            z = utils_1.hmac512(cc, data);\n            data[0] = 0x03;\n            i = utils_1.hmac512(cc, data);\n        }\n        else {\n            const data = buffer_1.Buffer.allocUnsafe(1 + 64 + 4);\n            data.writeUInt32LE(index, 1 + 64);\n            kl.copy(data, 1);\n            kr.copy(data, 1 + 32);\n            data[0] = 0x00;\n            z = utils_1.hmac512(cc, data);\n            data[0] = 0x01;\n            i = utils_1.hmac512(cc, data);\n        }\n        const chainCode = i.slice(32, 64);\n        const zl = z.slice(0, 32);\n        const zr = z.slice(32, 64);\n        const left = new bn_js_1.default(kl, 16, \"le\")\n            .add(new bn_js_1.default(zl.slice(0, 28), 16, \"le\").mul(new bn_js_1.default(8)))\n            .toArrayLike(buffer_1.Buffer, \"le\", 32);\n        let right = new bn_js_1.default(kr, 16, \"le\")\n            .add(new bn_js_1.default(zr, 16, \"le\"))\n            .toArrayLike(buffer_1.Buffer, \"le\")\n            .slice(0, 32);\n        if (right.length !== 32) {\n            right = buffer_1.Buffer.from(right.toString(\"hex\").padEnd(32, \"0\"), \"hex\");\n        }\n        const xprv = buffer_1.Buffer.concat([left, right, chainCode]);\n        return new Bip32PrivateKey(xprv);\n    }\n    deriveHardened(index) {\n        return this.derive(index + utils_1.HARDENED_OFFSET);\n    }\n    derivePath(path) {\n        const splitPath = path.split(\"/\");\n        // @ts-ignore\n        return splitPath.reduce((hdkey, indexStr, i) => {\n            if (i === 0 && indexStr === \"m\") {\n                return hdkey;\n            }\n            if (indexStr.slice(-1) === `'`) {\n                const index = parseInt(indexStr.slice(0, -1), 10);\n                return hdkey.deriveHardened(index);\n            }\n            const index = parseInt(indexStr, 10);\n            return hdkey.derive(index);\n        }, this);\n    }\n    toBip32PublicKey() {\n        const keyPair = eddsa.keyFromSecret(this.xprv.slice(0, 32).toString(\"hex\"));\n        const vk = buffer_1.Buffer.from(keyPair.pubBytes());\n        return new Bip32PublicKey_1.default(buffer_1.Buffer.concat([vk, this.xprv.slice(64, 96)]));\n    }\n    toBytes() {\n        return this.xprv;\n    }\n    toPrivateKey() {\n        const keyPair = eddsa.keyFromSecret(this.xprv.slice(0, 64));\n        return new PrivateKey_1.default(buffer_1.Buffer.from(keyPair.privBytes()));\n    }\n}\nexports[\"default\"] = Bip32PrivateKey;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHN0cmljYWhxL2JpcDMyZWQyNTUxOS9kaXN0L0JpcDMyUHJpdmF0ZUtleS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQSw2Q0FBNkM7QUFDN0M7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsaUJBQWlCLG1CQUFPLENBQUMsc0JBQVE7QUFDakMsZ0NBQWdDLG1CQUFPLENBQUMsbURBQU87QUFDL0MsaUJBQWlCLG1CQUFPLENBQUMsb0RBQVE7QUFDakMseUNBQXlDLG1CQUFPLENBQUMsNEZBQWtCO0FBQ25FLHFDQUFxQyxtQkFBTyxDQUFDLG9GQUFjO0FBQzNELGdCQUFnQixtQkFBTyxDQUFDLDBFQUFTO0FBQ2pDLGNBQWMsbUJBQU8sQ0FBQyxzRkFBWTtBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYixTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBZSIsInNvdXJjZXMiOlsiRDpcXGhhY2tvdGhvblxcQmxvY2tjaGFpblxcVmludHJla1xcbm9kZV9tb2R1bGVzXFxAc3RyaWNhaHFcXGJpcDMyZWQyNTUxOVxcZGlzdFxcQmlwMzJQcml2YXRlS2V5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLyogZXNsaW50LWRpc2FibGUgbm8tYml0d2lzZSAqL1xudmFyIF9faW1wb3J0RGVmYXVsdCA9ICh0aGlzICYmIHRoaXMuX19pbXBvcnREZWZhdWx0KSB8fCBmdW5jdGlvbiAobW9kKSB7XG4gICAgcmV0dXJuIChtb2QgJiYgbW9kLl9fZXNNb2R1bGUpID8gbW9kIDogeyBcImRlZmF1bHRcIjogbW9kIH07XG59O1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuY29uc3QgYnVmZmVyXzEgPSByZXF1aXJlKFwiYnVmZmVyXCIpO1xuY29uc3QgYm5fanNfMSA9IF9faW1wb3J0RGVmYXVsdChyZXF1aXJlKFwiYm4uanNcIikpO1xuY29uc3QgcGJrZGYyXzEgPSByZXF1aXJlKFwicGJrZGYyXCIpO1xuY29uc3QgQmlwMzJQdWJsaWNLZXlfMSA9IF9faW1wb3J0RGVmYXVsdChyZXF1aXJlKFwiLi9CaXAzMlB1YmxpY0tleVwiKSk7XG5jb25zdCBQcml2YXRlS2V5XzEgPSBfX2ltcG9ydERlZmF1bHQocmVxdWlyZShcIi4vUHJpdmF0ZUtleVwiKSk7XG5jb25zdCB1dGlsc18xID0gcmVxdWlyZShcIi4vdXRpbHNcIik7XG5jb25zdCBFRERTQSA9IHJlcXVpcmUoXCIuL2VkMjU1MTllXCIpO1xuY29uc3QgZWRkc2EgPSBuZXcgRUREU0EoKTtcbmNsYXNzIEJpcDMyUHJpdmF0ZUtleSB7XG4gICAgY29uc3RydWN0b3IoeHBydikge1xuICAgICAgICB0aGlzLnhwcnYgPSB4cHJ2O1xuICAgIH1cbiAgICBzdGF0aWMgZnJvbUVudHJvcHkoZW50cm9weSkge1xuICAgICAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgICAgICAgcGJrZGYyXzEucGJrZGYyKFwiXCIsIGVudHJvcHksIDQwOTYsIDk2LCBcInNoYTUxMlwiLCAoZXJyLCB4cHJ2KSA9PiB7XG4gICAgICAgICAgICAgICAgaWYgKGVycikge1xuICAgICAgICAgICAgICAgICAgICByZWplY3QoZXJyKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgLy8gVGhlIGxvd2VzdCB0aHJlZSBiaXRzIG9mIHRoZSBmaXJzdCBvY3RldCBhcmUgY2xlYXJlZFxuICAgICAgICAgICAgICAgIC8vIDI0OCBvciAweGY4IG9yIDBiMTExMTEwMDBcbiAgICAgICAgICAgICAgICB4cHJ2WzBdICY9IDBiMTExMTEwMDA7XG4gICAgICAgICAgICAgICAgLy8gdGhlIGhpZ2hlc3QgYml0IG9mIHRoZSBsYXN0IG9jdGV0IGlzIGNsZWFyZWRcbiAgICAgICAgICAgICAgICAvLyAzMSBvciAweDFmIG9yIDBiMDAwMTExMTFcbiAgICAgICAgICAgICAgICAvLyBBTkQgdGhlIHRoaXJkIGhpZ2hlc3QgYml0IGlzIGNsZWFyZWQgdG9vXG4gICAgICAgICAgICAgICAgeHBydlszMV0gJj0gMGIwMDAxMTExMTtcbiAgICAgICAgICAgICAgICAvLyBhbmQgdGhlIHNlY29uZCBoaWdoZXN0IGJpdCBvZiB0aGUgbGFzdCBvY3RldCBpcyBzZXRcbiAgICAgICAgICAgICAgICAvLyA2NCBvciAweDQwIG9yIDBiMDEwMDAwMDBcbiAgICAgICAgICAgICAgICB4cHJ2WzMxXSB8PSAwYjAxMDAwMDAwO1xuICAgICAgICAgICAgICAgIHJlc29sdmUobmV3IEJpcDMyUHJpdmF0ZUtleSh4cHJ2KSk7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIGRlcml2ZShpbmRleCkge1xuICAgICAgICBjb25zdCBrbCA9IHRoaXMueHBydi5zbGljZSgwLCAzMik7XG4gICAgICAgIGNvbnN0IGtyID0gdGhpcy54cHJ2LnNsaWNlKDMyLCA2NCk7XG4gICAgICAgIGNvbnN0IGNjID0gdGhpcy54cHJ2LnNsaWNlKDY0LCA5Nik7XG4gICAgICAgIGxldCB6O1xuICAgICAgICBsZXQgaTtcbiAgICAgICAgaWYgKGluZGV4IDwgdXRpbHNfMS5IQVJERU5FRF9PRkZTRVQpIHtcbiAgICAgICAgICAgIGNvbnN0IGRhdGEgPSBidWZmZXJfMS5CdWZmZXIuYWxsb2NVbnNhZmUoMSArIDMyICsgNCk7XG4gICAgICAgICAgICBkYXRhLndyaXRlVUludDMyTEUoaW5kZXgsIDEgKyAzMik7XG4gICAgICAgICAgICBjb25zdCBrZXlQYWlyID0gZWRkc2Eua2V5RnJvbVNlY3JldChrbC50b1N0cmluZyhcImhleFwiKSk7XG4gICAgICAgICAgICBjb25zdCB2ayA9IGJ1ZmZlcl8xLkJ1ZmZlci5mcm9tKGtleVBhaXIucHViQnl0ZXMoKSk7XG4gICAgICAgICAgICB2ay5jb3B5KGRhdGEsIDEpO1xuICAgICAgICAgICAgZGF0YVswXSA9IDB4MDI7XG4gICAgICAgICAgICB6ID0gdXRpbHNfMS5obWFjNTEyKGNjLCBkYXRhKTtcbiAgICAgICAgICAgIGRhdGFbMF0gPSAweDAzO1xuICAgICAgICAgICAgaSA9IHV0aWxzXzEuaG1hYzUxMihjYywgZGF0YSk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBjb25zdCBkYXRhID0gYnVmZmVyXzEuQnVmZmVyLmFsbG9jVW5zYWZlKDEgKyA2NCArIDQpO1xuICAgICAgICAgICAgZGF0YS53cml0ZVVJbnQzMkxFKGluZGV4LCAxICsgNjQpO1xuICAgICAgICAgICAga2wuY29weShkYXRhLCAxKTtcbiAgICAgICAgICAgIGtyLmNvcHkoZGF0YSwgMSArIDMyKTtcbiAgICAgICAgICAgIGRhdGFbMF0gPSAweDAwO1xuICAgICAgICAgICAgeiA9IHV0aWxzXzEuaG1hYzUxMihjYywgZGF0YSk7XG4gICAgICAgICAgICBkYXRhWzBdID0gMHgwMTtcbiAgICAgICAgICAgIGkgPSB1dGlsc18xLmhtYWM1MTIoY2MsIGRhdGEpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGNoYWluQ29kZSA9IGkuc2xpY2UoMzIsIDY0KTtcbiAgICAgICAgY29uc3QgemwgPSB6LnNsaWNlKDAsIDMyKTtcbiAgICAgICAgY29uc3QgenIgPSB6LnNsaWNlKDMyLCA2NCk7XG4gICAgICAgIGNvbnN0IGxlZnQgPSBuZXcgYm5fanNfMS5kZWZhdWx0KGtsLCAxNiwgXCJsZVwiKVxuICAgICAgICAgICAgLmFkZChuZXcgYm5fanNfMS5kZWZhdWx0KHpsLnNsaWNlKDAsIDI4KSwgMTYsIFwibGVcIikubXVsKG5ldyBibl9qc18xLmRlZmF1bHQoOCkpKVxuICAgICAgICAgICAgLnRvQXJyYXlMaWtlKGJ1ZmZlcl8xLkJ1ZmZlciwgXCJsZVwiLCAzMik7XG4gICAgICAgIGxldCByaWdodCA9IG5ldyBibl9qc18xLmRlZmF1bHQoa3IsIDE2LCBcImxlXCIpXG4gICAgICAgICAgICAuYWRkKG5ldyBibl9qc18xLmRlZmF1bHQoenIsIDE2LCBcImxlXCIpKVxuICAgICAgICAgICAgLnRvQXJyYXlMaWtlKGJ1ZmZlcl8xLkJ1ZmZlciwgXCJsZVwiKVxuICAgICAgICAgICAgLnNsaWNlKDAsIDMyKTtcbiAgICAgICAgaWYgKHJpZ2h0Lmxlbmd0aCAhPT0gMzIpIHtcbiAgICAgICAgICAgIHJpZ2h0ID0gYnVmZmVyXzEuQnVmZmVyLmZyb20ocmlnaHQudG9TdHJpbmcoXCJoZXhcIikucGFkRW5kKDMyLCBcIjBcIiksIFwiaGV4XCIpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHhwcnYgPSBidWZmZXJfMS5CdWZmZXIuY29uY2F0KFtsZWZ0LCByaWdodCwgY2hhaW5Db2RlXSk7XG4gICAgICAgIHJldHVybiBuZXcgQmlwMzJQcml2YXRlS2V5KHhwcnYpO1xuICAgIH1cbiAgICBkZXJpdmVIYXJkZW5lZChpbmRleCkge1xuICAgICAgICByZXR1cm4gdGhpcy5kZXJpdmUoaW5kZXggKyB1dGlsc18xLkhBUkRFTkVEX09GRlNFVCk7XG4gICAgfVxuICAgIGRlcml2ZVBhdGgocGF0aCkge1xuICAgICAgICBjb25zdCBzcGxpdFBhdGggPSBwYXRoLnNwbGl0KFwiL1wiKTtcbiAgICAgICAgLy8gQHRzLWlnbm9yZVxuICAgICAgICByZXR1cm4gc3BsaXRQYXRoLnJlZHVjZSgoaGRrZXksIGluZGV4U3RyLCBpKSA9PiB7XG4gICAgICAgICAgICBpZiAoaSA9PT0gMCAmJiBpbmRleFN0ciA9PT0gXCJtXCIpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gaGRrZXk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoaW5kZXhTdHIuc2xpY2UoLTEpID09PSBgJ2ApIHtcbiAgICAgICAgICAgICAgICBjb25zdCBpbmRleCA9IHBhcnNlSW50KGluZGV4U3RyLnNsaWNlKDAsIC0xKSwgMTApO1xuICAgICAgICAgICAgICAgIHJldHVybiBoZGtleS5kZXJpdmVIYXJkZW5lZChpbmRleCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25zdCBpbmRleCA9IHBhcnNlSW50KGluZGV4U3RyLCAxMCk7XG4gICAgICAgICAgICByZXR1cm4gaGRrZXkuZGVyaXZlKGluZGV4KTtcbiAgICAgICAgfSwgdGhpcyk7XG4gICAgfVxuICAgIHRvQmlwMzJQdWJsaWNLZXkoKSB7XG4gICAgICAgIGNvbnN0IGtleVBhaXIgPSBlZGRzYS5rZXlGcm9tU2VjcmV0KHRoaXMueHBydi5zbGljZSgwLCAzMikudG9TdHJpbmcoXCJoZXhcIikpO1xuICAgICAgICBjb25zdCB2ayA9IGJ1ZmZlcl8xLkJ1ZmZlci5mcm9tKGtleVBhaXIucHViQnl0ZXMoKSk7XG4gICAgICAgIHJldHVybiBuZXcgQmlwMzJQdWJsaWNLZXlfMS5kZWZhdWx0KGJ1ZmZlcl8xLkJ1ZmZlci5jb25jYXQoW3ZrLCB0aGlzLnhwcnYuc2xpY2UoNjQsIDk2KV0pKTtcbiAgICB9XG4gICAgdG9CeXRlcygpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMueHBydjtcbiAgICB9XG4gICAgdG9Qcml2YXRlS2V5KCkge1xuICAgICAgICBjb25zdCBrZXlQYWlyID0gZWRkc2Eua2V5RnJvbVNlY3JldCh0aGlzLnhwcnYuc2xpY2UoMCwgNjQpKTtcbiAgICAgICAgcmV0dXJuIG5ldyBQcml2YXRlS2V5XzEuZGVmYXVsdChidWZmZXJfMS5CdWZmZXIuZnJvbShrZXlQYWlyLnByaXZCeXRlcygpKSk7XG4gICAgfVxufVxuZXhwb3J0cy5kZWZhdWx0ID0gQmlwMzJQcml2YXRlS2V5O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stricahq/bip32ed25519/dist/Bip32PrivateKey.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@stricahq/bip32ed25519/dist/Bip32PublicKey.js":
/*!********************************************************************!*\
  !*** ./node_modules/@stricahq/bip32ed25519/dist/Bip32PublicKey.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n/* eslint-disable no-bitwise */\nconst buffer_1 = __webpack_require__(/*! buffer */ \"buffer\");\nconst bn_js_1 = __importDefault(__webpack_require__(/*! bn.js */ \"(ssr)/./node_modules/bn.js/lib/bn.js\"));\nconst PublicKey_1 = __importDefault(__webpack_require__(/*! ./PublicKey */ \"(ssr)/./node_modules/@stricahq/bip32ed25519/dist/PublicKey.js\"));\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/@stricahq/bip32ed25519/dist/utils.js\");\nconst EDDSA = __webpack_require__(/*! ./ed25519e */ \"(ssr)/./node_modules/@stricahq/bip32ed25519/dist/ed25519e/index.js\");\nconst eddsa = new EDDSA();\nclass Bip32PublicKey {\n    constructor(xpub) {\n        this.xpub = xpub;\n    }\n    derive(index) {\n        const pk = this.xpub.slice(0, 32);\n        const cc = this.xpub.slice(32, 64);\n        const data = buffer_1.Buffer.allocUnsafe(1 + 32 + 4);\n        data.writeUInt32LE(index, 1 + 32);\n        let z;\n        let i;\n        if (index < utils_1.HARDENED_OFFSET) {\n            pk.copy(data, 1);\n            data[0] = 0x02;\n            z = utils_1.hmac512(cc, data);\n            data[0] = 0x03;\n            i = utils_1.hmac512(cc, data);\n        }\n        else {\n            throw new Error(\"can not derive hardened public key\");\n        }\n        const chainCode = i.slice(32, 64);\n        const zl = z.slice(0, 32);\n        const left = new bn_js_1.default(zl.slice(0, 28), 16, \"le\").mul(new bn_js_1.default(8));\n        const p = eddsa.g.mul(left);\n        const pp = eddsa.decodePoint(pk.toString(\"hex\"));\n        const point = pp.add(p);\n        return new Bip32PublicKey(buffer_1.Buffer.concat([buffer_1.Buffer.from(eddsa.encodePoint(point)), chainCode]));\n    }\n    toPublicKey() {\n        const key = eddsa.keyFromPublic(this.xpub.slice(0, 32));\n        return new PublicKey_1.default(buffer_1.Buffer.from(key.pubBytes()));\n    }\n    toBytes() {\n        return this.xpub;\n    }\n    static fromBytes(xpub) {\n        return new Bip32PublicKey(xpub);\n    }\n}\nexports[\"default\"] = Bip32PublicKey;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stricahq/bip32ed25519/dist/Bip32PublicKey.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@stricahq/bip32ed25519/dist/PrivateKey.js":
/*!****************************************************************!*\
  !*** ./node_modules/@stricahq/bip32ed25519/dist/PrivateKey.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst buffer_1 = __webpack_require__(/*! buffer */ \"buffer\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/@stricahq/bip32ed25519/dist/utils.js\");\nconst PublicKey_1 = __importDefault(__webpack_require__(/*! ./PublicKey */ \"(ssr)/./node_modules/@stricahq/bip32ed25519/dist/PublicKey.js\"));\nconst EDDSA = __webpack_require__(/*! ./ed25519e */ \"(ssr)/./node_modules/@stricahq/bip32ed25519/dist/ed25519e/index.js\");\nconst eddsa = new EDDSA();\nclass PrivateKey {\n    constructor(privKey) {\n        this.privKey = privKey;\n    }\n    static fromSecretKey(secretKey) {\n        let extendedSecret = utils_1.sha512(secretKey);\n        extendedSecret[0] &= 248;\n        extendedSecret[31] &= 63;\n        extendedSecret[31] |= 64;\n        return new PrivateKey(extendedSecret);\n    }\n    toBytes() {\n        return this.privKey;\n    }\n    toPublicKey() {\n        const keyPair = eddsa.keyFromSecret(this.privKey);\n        return new PublicKey_1.default(buffer_1.Buffer.from(keyPair.pubBytes()));\n    }\n    sign(data) {\n        const keyPair = eddsa.keyFromSecret(this.privKey);\n        const signature = keyPair.sign(data.toString(\"hex\"));\n        return buffer_1.Buffer.from(signature.toBytes());\n    }\n    verify(signature, message) {\n        const keyPair = eddsa.keyFromSecret(this.privKey);\n        return keyPair.verify(message.toString(\"hex\"), signature.toString(\"hex\"));\n    }\n}\nexports[\"default\"] = PrivateKey;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHN0cmljYWhxL2JpcDMyZWQyNTUxOS9kaXN0L1ByaXZhdGVLZXkuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBLDZDQUE2QztBQUM3QztBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxpQkFBaUIsbUJBQU8sQ0FBQyxzQkFBUTtBQUNqQyxnQkFBZ0IsbUJBQU8sQ0FBQywwRUFBUztBQUNqQyxvQ0FBb0MsbUJBQU8sQ0FBQyxrRkFBYTtBQUN6RCxjQUFjLG1CQUFPLENBQUMsc0ZBQVk7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFlIiwic291cmNlcyI6WyJEOlxcaGFja290aG9uXFxCbG9ja2NoYWluXFxWaW50cmVrXFxub2RlX21vZHVsZXNcXEBzdHJpY2FocVxcYmlwMzJlZDI1NTE5XFxkaXN0XFxQcml2YXRlS2V5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xudmFyIF9faW1wb3J0RGVmYXVsdCA9ICh0aGlzICYmIHRoaXMuX19pbXBvcnREZWZhdWx0KSB8fCBmdW5jdGlvbiAobW9kKSB7XG4gICAgcmV0dXJuIChtb2QgJiYgbW9kLl9fZXNNb2R1bGUpID8gbW9kIDogeyBcImRlZmF1bHRcIjogbW9kIH07XG59O1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuY29uc3QgYnVmZmVyXzEgPSByZXF1aXJlKFwiYnVmZmVyXCIpO1xuY29uc3QgdXRpbHNfMSA9IHJlcXVpcmUoXCIuL3V0aWxzXCIpO1xuY29uc3QgUHVibGljS2V5XzEgPSBfX2ltcG9ydERlZmF1bHQocmVxdWlyZShcIi4vUHVibGljS2V5XCIpKTtcbmNvbnN0IEVERFNBID0gcmVxdWlyZShcIi4vZWQyNTUxOWVcIik7XG5jb25zdCBlZGRzYSA9IG5ldyBFRERTQSgpO1xuY2xhc3MgUHJpdmF0ZUtleSB7XG4gICAgY29uc3RydWN0b3IocHJpdktleSkge1xuICAgICAgICB0aGlzLnByaXZLZXkgPSBwcml2S2V5O1xuICAgIH1cbiAgICBzdGF0aWMgZnJvbVNlY3JldEtleShzZWNyZXRLZXkpIHtcbiAgICAgICAgbGV0IGV4dGVuZGVkU2VjcmV0ID0gdXRpbHNfMS5zaGE1MTIoc2VjcmV0S2V5KTtcbiAgICAgICAgZXh0ZW5kZWRTZWNyZXRbMF0gJj0gMjQ4O1xuICAgICAgICBleHRlbmRlZFNlY3JldFszMV0gJj0gNjM7XG4gICAgICAgIGV4dGVuZGVkU2VjcmV0WzMxXSB8PSA2NDtcbiAgICAgICAgcmV0dXJuIG5ldyBQcml2YXRlS2V5KGV4dGVuZGVkU2VjcmV0KTtcbiAgICB9XG4gICAgdG9CeXRlcygpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMucHJpdktleTtcbiAgICB9XG4gICAgdG9QdWJsaWNLZXkoKSB7XG4gICAgICAgIGNvbnN0IGtleVBhaXIgPSBlZGRzYS5rZXlGcm9tU2VjcmV0KHRoaXMucHJpdktleSk7XG4gICAgICAgIHJldHVybiBuZXcgUHVibGljS2V5XzEuZGVmYXVsdChidWZmZXJfMS5CdWZmZXIuZnJvbShrZXlQYWlyLnB1YkJ5dGVzKCkpKTtcbiAgICB9XG4gICAgc2lnbihkYXRhKSB7XG4gICAgICAgIGNvbnN0IGtleVBhaXIgPSBlZGRzYS5rZXlGcm9tU2VjcmV0KHRoaXMucHJpdktleSk7XG4gICAgICAgIGNvbnN0IHNpZ25hdHVyZSA9IGtleVBhaXIuc2lnbihkYXRhLnRvU3RyaW5nKFwiaGV4XCIpKTtcbiAgICAgICAgcmV0dXJuIGJ1ZmZlcl8xLkJ1ZmZlci5mcm9tKHNpZ25hdHVyZS50b0J5dGVzKCkpO1xuICAgIH1cbiAgICB2ZXJpZnkoc2lnbmF0dXJlLCBtZXNzYWdlKSB7XG4gICAgICAgIGNvbnN0IGtleVBhaXIgPSBlZGRzYS5rZXlGcm9tU2VjcmV0KHRoaXMucHJpdktleSk7XG4gICAgICAgIHJldHVybiBrZXlQYWlyLnZlcmlmeShtZXNzYWdlLnRvU3RyaW5nKFwiaGV4XCIpLCBzaWduYXR1cmUudG9TdHJpbmcoXCJoZXhcIikpO1xuICAgIH1cbn1cbmV4cG9ydHMuZGVmYXVsdCA9IFByaXZhdGVLZXk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stricahq/bip32ed25519/dist/PrivateKey.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@stricahq/bip32ed25519/dist/PublicKey.js":
/*!***************************************************************!*\
  !*** ./node_modules/@stricahq/bip32ed25519/dist/PublicKey.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/@stricahq/bip32ed25519/dist/utils.js\");\nconst EDDSA = __webpack_require__(/*! ./ed25519e */ \"(ssr)/./node_modules/@stricahq/bip32ed25519/dist/ed25519e/index.js\");\nconst eddsa = new EDDSA();\nclass PublicKey {\n    constructor(pubKey) {\n        this.pubKey = pubKey;\n    }\n    toBytes() {\n        return this.pubKey;\n    }\n    hash() {\n        return utils_1.hash28(this.pubKey);\n    }\n    verify(signature, data) {\n        const keyPair = eddsa.keyFromPublic(this.pubKey.toString(\"hex\"));\n        return keyPair.verify(data.toString(\"hex\"), signature.toString(\"hex\"));\n    }\n}\nexports[\"default\"] = PublicKey;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHN0cmljYWhxL2JpcDMyZWQyNTUxOS9kaXN0L1B1YmxpY0tleS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxnQkFBZ0IsbUJBQU8sQ0FBQywwRUFBUztBQUNqQyxjQUFjLG1CQUFPLENBQUMsc0ZBQVk7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBZSIsInNvdXJjZXMiOlsiRDpcXGhhY2tvdGhvblxcQmxvY2tjaGFpblxcVmludHJla1xcbm9kZV9tb2R1bGVzXFxAc3RyaWNhaHFcXGJpcDMyZWQyNTUxOVxcZGlzdFxcUHVibGljS2V5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuY29uc3QgdXRpbHNfMSA9IHJlcXVpcmUoXCIuL3V0aWxzXCIpO1xuY29uc3QgRUREU0EgPSByZXF1aXJlKFwiLi9lZDI1NTE5ZVwiKTtcbmNvbnN0IGVkZHNhID0gbmV3IEVERFNBKCk7XG5jbGFzcyBQdWJsaWNLZXkge1xuICAgIGNvbnN0cnVjdG9yKHB1YktleSkge1xuICAgICAgICB0aGlzLnB1YktleSA9IHB1YktleTtcbiAgICB9XG4gICAgdG9CeXRlcygpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMucHViS2V5O1xuICAgIH1cbiAgICBoYXNoKCkge1xuICAgICAgICByZXR1cm4gdXRpbHNfMS5oYXNoMjgodGhpcy5wdWJLZXkpO1xuICAgIH1cbiAgICB2ZXJpZnkoc2lnbmF0dXJlLCBkYXRhKSB7XG4gICAgICAgIGNvbnN0IGtleVBhaXIgPSBlZGRzYS5rZXlGcm9tUHVibGljKHRoaXMucHViS2V5LnRvU3RyaW5nKFwiaGV4XCIpKTtcbiAgICAgICAgcmV0dXJuIGtleVBhaXIudmVyaWZ5KGRhdGEudG9TdHJpbmcoXCJoZXhcIiksIHNpZ25hdHVyZS50b1N0cmluZyhcImhleFwiKSk7XG4gICAgfVxufVxuZXhwb3J0cy5kZWZhdWx0ID0gUHVibGljS2V5O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stricahq/bip32ed25519/dist/PublicKey.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@stricahq/bip32ed25519/dist/ed25519e/index.js":
/*!********************************************************************!*\
  !*** ./node_modules/@stricahq/bip32ed25519/dist/ed25519e/index.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n/**\n * Copyright 2021 Ashish Prajapati\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * *** Includes code to override elliptic.js implementation for ed25519 ***\n *\n * LICENSE\n *\n * This software is licensed under the MIT License.\n *\n * Copyright Fedor Indutny, 2014.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy of this software\n * and associated documentation files (the \"Software\"), to deal in the Software without restriction,\n * including without limitation the rights to use, copy, modify, merge, publish, distribute,\n * sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED,\n * INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR\n * PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\n * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n * OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n *\n */\nconst hash = __webpack_require__(/*! hash.js */ \"(ssr)/./node_modules/hash.js/lib/hash.js\");\nconst elliptic = __webpack_require__(/*! elliptic */ \"(ssr)/./node_modules/elliptic/lib/elliptic.js\");\nconst utils = elliptic.utils;\nconst parseBytes = utils.parseBytes;\nconst KeyPair = __webpack_require__(/*! ./key */ \"(ssr)/./node_modules/@stricahq/bip32ed25519/dist/ed25519e/key.js\");\nconst Signature = __webpack_require__(/*! ./signature */ \"(ssr)/./node_modules/@stricahq/bip32ed25519/dist/ed25519e/signature.js\");\nfunction EDDSA() {\n    if (!(this instanceof EDDSA))\n        return new EDDSA();\n    const curve = elliptic.curves.ed25519.curve;\n    this.curve = curve;\n    this.g = curve.g;\n    this.g.precompute(curve.n.bitLength() + 1);\n    this.pointClass = curve.point().constructor;\n    this.encodingLength = Math.ceil(curve.n.bitLength() / 8);\n    this.hash = hash.sha512;\n}\nmodule.exports = EDDSA;\n/**\n * @param {Array|String} message - message bytes\n * @param {Array|String|KeyPair} secret - secret bytes or a keypair\n * @returns {Signature} - signature\n */\nEDDSA.prototype.signExtended = function sign(message, secret) {\n    message = parseBytes(message);\n    const key = this.keyFromSecret(secret);\n    const r = this.hashInt(key.messagePrefix(), message);\n    const R = this.g.mul(r);\n    const Rencoded = this.encodePoint(R);\n    const s_ = this.hashInt(Rencoded, key.pubBytes(), message).mul(key.kl());\n    const S = r.add(s_).umod(this.curve.n);\n    return this.makeSignature({ R, S, Rencoded });\n};\n/**\n * @param {Array} message - message bytes\n * @param {Array|String|Signature} sig - sig bytes\n * @param {Array|String|Point|KeyPair} pub - public key\n * @returns {Boolean} - true if public key matches sig of message\n */\nEDDSA.prototype.verify = function verify(message, sig, pub) {\n    message = parseBytes(message);\n    sig = this.makeSignature(sig);\n    const key = this.keyFromPublic(pub);\n    const h = this.hashInt(sig.Rencoded(), key.pubBytes(), message);\n    const SG = this.g.mul(sig.S());\n    const RplusAh = sig.R().add(key.pub().mul(h));\n    return RplusAh.eq(SG);\n};\nEDDSA.prototype.hashInt = function hashInt() {\n    const hash = this.hash();\n    for (let i = 0; i < arguments.length; i++)\n        hash.update(arguments[i]);\n    return utils.intFromLE(hash.digest()).umod(this.curve.n);\n};\nEDDSA.prototype.keyFromPublic = function keyFromPublic(pub) {\n    return KeyPair.fromPublic(this, utils.parseBytes(pub));\n};\nEDDSA.prototype.keyFromSecret = function keyFromSecret(secret) {\n    return KeyPair.fromSecret(this, utils.parseBytes(secret));\n};\nEDDSA.prototype.makeSignature = function makeSignature(sig) {\n    if (sig instanceof Signature)\n        return sig;\n    return new Signature(this, sig);\n};\n/**\n * * https://tools.ietf.org/html/draft-josefsson-eddsa-ed25519-03#section-5.2\n *\n * EDDSA defines methods for encoding and decoding points and integers. These are\n * helper convenience methods, that pass along to utility functions implied\n * parameters.\n *\n */\nEDDSA.prototype.encodePoint = function encodePoint(point) {\n    const enc = point.getY().toArray(\"le\", this.encodingLength);\n    enc[this.encodingLength - 1] |= point.getX().isOdd() ? 0x80 : 0;\n    return enc;\n};\nEDDSA.prototype.decodePoint = function decodePoint(bytes) {\n    bytes = utils.parseBytes(bytes);\n    const lastIx = bytes.length - 1;\n    const normed = bytes.slice(0, lastIx).concat(bytes[lastIx] & ~0x80);\n    const xIsOdd = (bytes[lastIx] & 0x80) !== 0;\n    const y = utils.intFromLE(normed);\n    return this.curve.pointFromY(y, xIsOdd);\n};\nEDDSA.prototype.encodeInt = function encodeInt(num) {\n    return num.toArray(\"le\", this.encodingLength);\n};\nEDDSA.prototype.decodeInt = function decodeInt(bytes) {\n    return utils.intFromLE(bytes);\n};\nEDDSA.prototype.isPoint = function isPoint(val) {\n    return val instanceof this.pointClass;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stricahq/bip32ed25519/dist/ed25519e/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@stricahq/bip32ed25519/dist/ed25519e/key.js":
/*!******************************************************************!*\
  !*** ./node_modules/@stricahq/bip32ed25519/dist/ed25519e/key.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n/**\n * Copyright 2021 Ashish Prajapati\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * *** Includes code to override elliptic.js implementation for ed25519 ***\n *\n * LICENSE\n *\n * This software is licensed under the MIT License.\n *\n * Copyright Fedor Indutny, 2014.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy of this software\n * and associated documentation files (the \"Software\"), to deal in the Software without restriction,\n * including without limitation the rights to use, copy, modify, merge, publish, distribute,\n * sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED,\n * INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR\n * PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\n * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n * OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n *\n */\nconst elliptic = __webpack_require__(/*! elliptic */ \"(ssr)/./node_modules/elliptic/lib/elliptic.js\");\nconst utils = elliptic.utils;\nconst assert = utils.assert;\nconst parseBytes = utils.parseBytes;\nconst cachedProperty = utils.cachedProperty;\nfunction KeyPair(eddsa, params) {\n    this.eddsa = eddsa;\n    this._secret = parseBytes(params.secret);\n    if (eddsa.isPoint(params.pub)) {\n        this._pub = params.pub;\n    }\n    else {\n        this._pubBytes = parseBytes(params.pub);\n    }\n}\nKeyPair.fromPublic = function fromPublic(eddsa, pub) {\n    if (pub instanceof KeyPair) {\n        return pub;\n    }\n    return new KeyPair(eddsa, { pub });\n};\nKeyPair.fromSecret = function fromSecret(eddsa, secret) {\n    if (secret instanceof KeyPair) {\n        return secret;\n    }\n    return new KeyPair(eddsa, { secret });\n};\nKeyPair.prototype.secret = function secret() {\n    return this._secret.slice(0, 32);\n};\ncachedProperty(KeyPair, \"pubBytes\", function pubBytes() {\n    return this.eddsa.encodePoint(this.pub());\n});\ncachedProperty(KeyPair, \"pub\", function pub() {\n    if (this._pubBytes) {\n        return this.eddsa.decodePoint(this._pubBytes);\n    }\n    return this.eddsa.g.mul(this.kl());\n});\ncachedProperty(KeyPair, \"privBytes\", function privBytes() {\n    return this._secret;\n});\ncachedProperty(KeyPair, \"priv\", function priv() {\n    return this.eddsa.decodeInt(this.privBytes());\n});\ncachedProperty(KeyPair, \"kl\", function priv() {\n    return this.eddsa.decodeInt(this.privBytes().slice(0, 32));\n});\ncachedProperty(KeyPair, \"messagePrefix\", function messagePrefix() {\n    return this._secret.slice(32, 64);\n});\nKeyPair.prototype.sign = function sign(message) {\n    assert(this._secret, \"KeyPair can only verify\");\n    return this.eddsa.signExtended(message, this);\n};\nKeyPair.prototype.verify = function verify(message, sig) {\n    return this.eddsa.verify(message, sig, this);\n};\nmodule.exports = KeyPair;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stricahq/bip32ed25519/dist/ed25519e/key.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@stricahq/bip32ed25519/dist/ed25519e/signature.js":
/*!************************************************************************!*\
  !*** ./node_modules/@stricahq/bip32ed25519/dist/ed25519e/signature.js ***!
  \************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n/**\n * Copyright 2021 Ashish Prajapati\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n * *** Includes code to override elliptic.js implementation for ed25519 ***\n *\n * LICENSE\n *\n * This software is licensed under the MIT License.\n *\n * Copyright Fedor Indutny, 2014.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy of this software\n * and associated documentation files (the \"Software\"), to deal in the Software without restriction,\n * including without limitation the rights to use, copy, modify, merge, publish, distribute,\n * sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED,\n * INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR\n * PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\n * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n * OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n *\n */\nconst BN = __webpack_require__(/*! bn.js */ \"(ssr)/./node_modules/bn.js/lib/bn.js\");\nconst elliptic = __webpack_require__(/*! elliptic */ \"(ssr)/./node_modules/elliptic/lib/elliptic.js\");\nconst utils = elliptic.utils;\nconst assert = utils.assert;\nconst cachedProperty = utils.cachedProperty;\nconst parseBytes = utils.parseBytes;\n/**\n * @param {EDDSA} eddsa - eddsa instance\n * @param {Array<Bytes>|Object} sig -\n * @param {Array<Bytes>|Point} [sig.R] - R point as Point or bytes\n * @param {Array<Bytes>|bn} [sig.S] - S scalar as bn or bytes\n * @param {Array<Bytes>} [sig.Rencoded] - R point encoded\n * @param {Array<Bytes>} [sig.Sencoded] - S scalar encoded\n */\nfunction Signature(eddsa, sig) {\n    this.eddsa = eddsa;\n    if (typeof sig !== \"object\")\n        sig = parseBytes(sig);\n    if (Array.isArray(sig)) {\n        sig = {\n            R: sig.slice(0, eddsa.encodingLength),\n            S: sig.slice(eddsa.encodingLength),\n        };\n    }\n    assert(sig.R && sig.S, \"Signature without R or S\");\n    if (eddsa.isPoint(sig.R))\n        this._R = sig.R;\n    if (BN.isBN(sig.S))\n        this._S = sig.S;\n    this._Rencoded = Array.isArray(sig.R) ? sig.R : sig.Rencoded;\n    this._Sencoded = Array.isArray(sig.S) ? sig.S : sig.Sencoded;\n}\ncachedProperty(Signature, \"S\", function S() {\n    return this.eddsa.decodeInt(this.Sencoded());\n});\ncachedProperty(Signature, \"R\", function R() {\n    return this.eddsa.decodePoint(this.Rencoded());\n});\ncachedProperty(Signature, \"Rencoded\", function Rencoded() {\n    return this.eddsa.encodePoint(this.R());\n});\ncachedProperty(Signature, \"Sencoded\", function Sencoded() {\n    return this.eddsa.encodeInt(this.S());\n});\nSignature.prototype.toBytes = function toBytes() {\n    return this.Rencoded().concat(this.Sencoded());\n};\nSignature.prototype.toHex = function toHex() {\n    return utils.encode(this.toBytes(), \"hex\").toUpperCase();\n};\nmodule.exports = Signature;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stricahq/bip32ed25519/dist/ed25519e/signature.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@stricahq/bip32ed25519/dist/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/@stricahq/bip32ed25519/dist/index.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PrivateKey = exports.PublicKey = exports.Bip32PublicKey = exports.Bip32PrivateKey = void 0;\nvar Bip32PrivateKey_1 = __webpack_require__(/*! ./Bip32PrivateKey */ \"(ssr)/./node_modules/@stricahq/bip32ed25519/dist/Bip32PrivateKey.js\");\nObject.defineProperty(exports, \"Bip32PrivateKey\", ({ enumerable: true, get: function () { return __importDefault(Bip32PrivateKey_1).default; } }));\nvar Bip32PublicKey_1 = __webpack_require__(/*! ./Bip32PublicKey */ \"(ssr)/./node_modules/@stricahq/bip32ed25519/dist/Bip32PublicKey.js\");\nObject.defineProperty(exports, \"Bip32PublicKey\", ({ enumerable: true, get: function () { return __importDefault(Bip32PublicKey_1).default; } }));\nvar PublicKey_1 = __webpack_require__(/*! ./PublicKey */ \"(ssr)/./node_modules/@stricahq/bip32ed25519/dist/PublicKey.js\");\nObject.defineProperty(exports, \"PublicKey\", ({ enumerable: true, get: function () { return __importDefault(PublicKey_1).default; } }));\nvar PrivateKey_1 = __webpack_require__(/*! ./PrivateKey */ \"(ssr)/./node_modules/@stricahq/bip32ed25519/dist/PrivateKey.js\");\nObject.defineProperty(exports, \"PrivateKey\", ({ enumerable: true, get: function () { return __importDefault(PrivateKey_1).default; } }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stricahq/bip32ed25519/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@stricahq/bip32ed25519/dist/utils.js":
/*!***********************************************************!*\
  !*** ./node_modules/@stricahq/bip32ed25519/dist/utils.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.HARDENED_OFFSET = exports.sha512 = exports.hmac512 = exports.hash28 = void 0;\nconst buffer_1 = __webpack_require__(/*! buffer */ \"buffer\");\nconst blakejs_1 = __webpack_require__(/*! blakejs */ \"(ssr)/./node_modules/blakejs/index.js\");\nconst hashJs = __importStar(__webpack_require__(/*! hash.js */ \"(ssr)/./node_modules/hash.js/lib/hash.js\"));\nconst hash28 = function (data) {\n    const hash = blakejs_1.blake2b(data, undefined, 28);\n    return buffer_1.Buffer.from(hash);\n};\nexports.hash28 = hash28;\nconst hmac512 = function (key, data) {\n    const digest = hashJs\n        .hmac(hashJs.sha512, key)\n        .update(data)\n        .digest();\n    return buffer_1.Buffer.from(digest);\n};\nexports.hmac512 = hmac512;\nconst sha512 = function (data) {\n    const digest = hashJs.sha512().update(data).digest();\n    return buffer_1.Buffer.from(digest);\n};\nexports.sha512 = sha512;\nexports.HARDENED_OFFSET = 0x80000000;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHN0cmljYWhxL2JpcDMyZWQyNTUxOS9kaXN0L3V0aWxzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBLG1DQUFtQyxvQ0FBb0MsZ0JBQWdCO0FBQ3ZGLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0EsMENBQTBDLDRCQUE0QjtBQUN0RSxDQUFDO0FBQ0Q7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsdUJBQXVCLEdBQUcsY0FBYyxHQUFHLGVBQWUsR0FBRyxjQUFjO0FBQzNFLGlCQUFpQixtQkFBTyxDQUFDLHNCQUFRO0FBQ2pDLGtCQUFrQixtQkFBTyxDQUFDLHNEQUFTO0FBQ25DLDRCQUE0QixtQkFBTyxDQUFDLHlEQUFTO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkLHVCQUF1QiIsInNvdXJjZXMiOlsiRDpcXGhhY2tvdGhvblxcQmxvY2tjaGFpblxcVmludHJla1xcbm9kZV9tb2R1bGVzXFxAc3RyaWNhaHFcXGJpcDMyZWQyNTUxOVxcZGlzdFxcdXRpbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG52YXIgX19jcmVhdGVCaW5kaW5nID0gKHRoaXMgJiYgdGhpcy5fX2NyZWF0ZUJpbmRpbmcpIHx8IChPYmplY3QuY3JlYXRlID8gKGZ1bmN0aW9uKG8sIG0sIGssIGsyKSB7XG4gICAgaWYgKGsyID09PSB1bmRlZmluZWQpIGsyID0gaztcbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkobywgazIsIHsgZW51bWVyYWJsZTogdHJ1ZSwgZ2V0OiBmdW5jdGlvbigpIHsgcmV0dXJuIG1ba107IH0gfSk7XG59KSA6IChmdW5jdGlvbihvLCBtLCBrLCBrMikge1xuICAgIGlmIChrMiA9PT0gdW5kZWZpbmVkKSBrMiA9IGs7XG4gICAgb1trMl0gPSBtW2tdO1xufSkpO1xudmFyIF9fc2V0TW9kdWxlRGVmYXVsdCA9ICh0aGlzICYmIHRoaXMuX19zZXRNb2R1bGVEZWZhdWx0KSB8fCAoT2JqZWN0LmNyZWF0ZSA/IChmdW5jdGlvbihvLCB2KSB7XG4gICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KG8sIFwiZGVmYXVsdFwiLCB7IGVudW1lcmFibGU6IHRydWUsIHZhbHVlOiB2IH0pO1xufSkgOiBmdW5jdGlvbihvLCB2KSB7XG4gICAgb1tcImRlZmF1bHRcIl0gPSB2O1xufSk7XG52YXIgX19pbXBvcnRTdGFyID0gKHRoaXMgJiYgdGhpcy5fX2ltcG9ydFN0YXIpIHx8IGZ1bmN0aW9uIChtb2QpIHtcbiAgICBpZiAobW9kICYmIG1vZC5fX2VzTW9kdWxlKSByZXR1cm4gbW9kO1xuICAgIHZhciByZXN1bHQgPSB7fTtcbiAgICBpZiAobW9kICE9IG51bGwpIGZvciAodmFyIGsgaW4gbW9kKSBpZiAoayAhPT0gXCJkZWZhdWx0XCIgJiYgT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKG1vZCwgaykpIF9fY3JlYXRlQmluZGluZyhyZXN1bHQsIG1vZCwgayk7XG4gICAgX19zZXRNb2R1bGVEZWZhdWx0KHJlc3VsdCwgbW9kKTtcbiAgICByZXR1cm4gcmVzdWx0O1xufTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuSEFSREVORURfT0ZGU0VUID0gZXhwb3J0cy5zaGE1MTIgPSBleHBvcnRzLmhtYWM1MTIgPSBleHBvcnRzLmhhc2gyOCA9IHZvaWQgMDtcbmNvbnN0IGJ1ZmZlcl8xID0gcmVxdWlyZShcImJ1ZmZlclwiKTtcbmNvbnN0IGJsYWtlanNfMSA9IHJlcXVpcmUoXCJibGFrZWpzXCIpO1xuY29uc3QgaGFzaEpzID0gX19pbXBvcnRTdGFyKHJlcXVpcmUoXCJoYXNoLmpzXCIpKTtcbmNvbnN0IGhhc2gyOCA9IGZ1bmN0aW9uIChkYXRhKSB7XG4gICAgY29uc3QgaGFzaCA9IGJsYWtlanNfMS5ibGFrZTJiKGRhdGEsIHVuZGVmaW5lZCwgMjgpO1xuICAgIHJldHVybiBidWZmZXJfMS5CdWZmZXIuZnJvbShoYXNoKTtcbn07XG5leHBvcnRzLmhhc2gyOCA9IGhhc2gyODtcbmNvbnN0IGhtYWM1MTIgPSBmdW5jdGlvbiAoa2V5LCBkYXRhKSB7XG4gICAgY29uc3QgZGlnZXN0ID0gaGFzaEpzXG4gICAgICAgIC5obWFjKGhhc2hKcy5zaGE1MTIsIGtleSlcbiAgICAgICAgLnVwZGF0ZShkYXRhKVxuICAgICAgICAuZGlnZXN0KCk7XG4gICAgcmV0dXJuIGJ1ZmZlcl8xLkJ1ZmZlci5mcm9tKGRpZ2VzdCk7XG59O1xuZXhwb3J0cy5obWFjNTEyID0gaG1hYzUxMjtcbmNvbnN0IHNoYTUxMiA9IGZ1bmN0aW9uIChkYXRhKSB7XG4gICAgY29uc3QgZGlnZXN0ID0gaGFzaEpzLnNoYTUxMigpLnVwZGF0ZShkYXRhKS5kaWdlc3QoKTtcbiAgICByZXR1cm4gYnVmZmVyXzEuQnVmZmVyLmZyb20oZGlnZXN0KTtcbn07XG5leHBvcnRzLnNoYTUxMiA9IHNoYTUxMjtcbmV4cG9ydHMuSEFSREVORURfT0ZGU0VUID0gMHg4MDAwMDAwMDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stricahq/bip32ed25519/dist/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@stricahq/cbors/dist/BufferList.js":
/*!*********************************************************!*\
  !*** ./node_modules/@stricahq/cbors/dist/BufferList.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst buffer_1 = __webpack_require__(/*! buffer */ \"buffer\");\nclass BufferList {\n    constructor() {\n        this.buf = buffer_1.Buffer.alloc(0);\n    }\n    get length() {\n        return this.buf.length;\n    }\n    read(n) {\n        if (n === 0) {\n            return buffer_1.Buffer.alloc(0);\n        }\n        if (n < 0) {\n            throw new Error('invalid length');\n        }\n        const chunk = this.buf.slice(0, n);\n        this.buf = this.buf.slice(n);\n        if (chunk.length < n) {\n            throw new Error('Not enough buffer');\n        }\n        return chunk;\n    }\n    push(chunk) {\n        if (!chunk.length)\n            return;\n        this.buf = buffer_1.Buffer.concat([this.buf, chunk]);\n    }\n}\nexports[\"default\"] = BufferList;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHN0cmljYWhxL2Nib3JzL2Rpc3QvQnVmZmVyTGlzdC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxpQkFBaUIsbUJBQU8sQ0FBQyxzQkFBUTtBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBZSIsInNvdXJjZXMiOlsiRDpcXGhhY2tvdGhvblxcQmxvY2tjaGFpblxcVmludHJla1xcbm9kZV9tb2R1bGVzXFxAc3RyaWNhaHFcXGNib3JzXFxkaXN0XFxCdWZmZXJMaXN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuY29uc3QgYnVmZmVyXzEgPSByZXF1aXJlKFwiYnVmZmVyXCIpO1xuY2xhc3MgQnVmZmVyTGlzdCB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIHRoaXMuYnVmID0gYnVmZmVyXzEuQnVmZmVyLmFsbG9jKDApO1xuICAgIH1cbiAgICBnZXQgbGVuZ3RoKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5idWYubGVuZ3RoO1xuICAgIH1cbiAgICByZWFkKG4pIHtcbiAgICAgICAgaWYgKG4gPT09IDApIHtcbiAgICAgICAgICAgIHJldHVybiBidWZmZXJfMS5CdWZmZXIuYWxsb2MoMCk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKG4gPCAwKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ2ludmFsaWQgbGVuZ3RoJyk7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgY2h1bmsgPSB0aGlzLmJ1Zi5zbGljZSgwLCBuKTtcbiAgICAgICAgdGhpcy5idWYgPSB0aGlzLmJ1Zi5zbGljZShuKTtcbiAgICAgICAgaWYgKGNodW5rLmxlbmd0aCA8IG4pIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignTm90IGVub3VnaCBidWZmZXInKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gY2h1bms7XG4gICAgfVxuICAgIHB1c2goY2h1bmspIHtcbiAgICAgICAgaWYgKCFjaHVuay5sZW5ndGgpXG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIHRoaXMuYnVmID0gYnVmZmVyXzEuQnVmZmVyLmNvbmNhdChbdGhpcy5idWYsIGNodW5rXSk7XG4gICAgfVxufVxuZXhwb3J0cy5kZWZhdWx0ID0gQnVmZmVyTGlzdDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stricahq/cbors/dist/BufferList.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@stricahq/cbors/dist/CborArray.js":
/*!********************************************************!*\
  !*** ./node_modules/@stricahq/cbors/dist/CborArray.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nclass CborArray extends Array {\n    constructor() {\n        super(...arguments);\n        this.byteSpan = [0, 0];\n    }\n    setByteSpan(byteSpan) {\n        this.byteSpan = byteSpan;\n    }\n    getByteSpan() {\n        return this.byteSpan;\n    }\n}\nexports[\"default\"] = CborArray;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHN0cmljYWhxL2Nib3JzL2Rpc3QvQ2JvckFycmF5LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFlIiwic291cmNlcyI6WyJEOlxcaGFja290aG9uXFxCbG9ja2NoYWluXFxWaW50cmVrXFxub2RlX21vZHVsZXNcXEBzdHJpY2FocVxcY2JvcnNcXGRpc3RcXENib3JBcnJheS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmNsYXNzIENib3JBcnJheSBleHRlbmRzIEFycmF5IHtcbiAgICBjb25zdHJ1Y3RvcigpIHtcbiAgICAgICAgc3VwZXIoLi4uYXJndW1lbnRzKTtcbiAgICAgICAgdGhpcy5ieXRlU3BhbiA9IFswLCAwXTtcbiAgICB9XG4gICAgc2V0Qnl0ZVNwYW4oYnl0ZVNwYW4pIHtcbiAgICAgICAgdGhpcy5ieXRlU3BhbiA9IGJ5dGVTcGFuO1xuICAgIH1cbiAgICBnZXRCeXRlU3BhbigpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuYnl0ZVNwYW47XG4gICAgfVxufVxuZXhwb3J0cy5kZWZhdWx0ID0gQ2JvckFycmF5O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stricahq/cbors/dist/CborArray.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@stricahq/cbors/dist/CborMap.js":
/*!******************************************************!*\
  !*** ./node_modules/@stricahq/cbors/dist/CborMap.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nclass CborMap extends Map {\n    constructor() {\n        super(...arguments);\n        this.byteSpan = [0, 0];\n    }\n    setByteSpan(byteSpan) {\n        this.byteSpan = byteSpan;\n    }\n    getByteSpan() {\n        return this.byteSpan;\n    }\n}\nexports[\"default\"] = CborMap;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHN0cmljYWhxL2Nib3JzL2Rpc3QvQ2Jvck1hcC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBZSIsInNvdXJjZXMiOlsiRDpcXGhhY2tvdGhvblxcQmxvY2tjaGFpblxcVmludHJla1xcbm9kZV9tb2R1bGVzXFxAc3RyaWNhaHFcXGNib3JzXFxkaXN0XFxDYm9yTWFwLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuY2xhc3MgQ2Jvck1hcCBleHRlbmRzIE1hcCB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIHN1cGVyKC4uLmFyZ3VtZW50cyk7XG4gICAgICAgIHRoaXMuYnl0ZVNwYW4gPSBbMCwgMF07XG4gICAgfVxuICAgIHNldEJ5dGVTcGFuKGJ5dGVTcGFuKSB7XG4gICAgICAgIHRoaXMuYnl0ZVNwYW4gPSBieXRlU3BhbjtcbiAgICB9XG4gICAgZ2V0Qnl0ZVNwYW4oKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmJ5dGVTcGFuO1xuICAgIH1cbn1cbmV4cG9ydHMuZGVmYXVsdCA9IENib3JNYXA7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stricahq/cbors/dist/CborMap.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@stricahq/cbors/dist/CborTag.js":
/*!******************************************************!*\
  !*** ./node_modules/@stricahq/cbors/dist/CborTag.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nclass CborTag {\n    constructor(value, tag) {\n        this.byteSpan = [0, 0];\n        this.value = value;\n        this.tag = tag;\n    }\n    setByteSpan(byteSpan) {\n        this.byteSpan = byteSpan;\n    }\n    getByteSpan() {\n        return this.byteSpan;\n    }\n}\nexports[\"default\"] = CborTag;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHN0cmljYWhxL2Nib3JzL2Rpc3QvQ2JvclRhZy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFlIiwic291cmNlcyI6WyJEOlxcaGFja290aG9uXFxCbG9ja2NoYWluXFxWaW50cmVrXFxub2RlX21vZHVsZXNcXEBzdHJpY2FocVxcY2JvcnNcXGRpc3RcXENib3JUYWcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5jbGFzcyBDYm9yVGFnIHtcbiAgICBjb25zdHJ1Y3Rvcih2YWx1ZSwgdGFnKSB7XG4gICAgICAgIHRoaXMuYnl0ZVNwYW4gPSBbMCwgMF07XG4gICAgICAgIHRoaXMudmFsdWUgPSB2YWx1ZTtcbiAgICAgICAgdGhpcy50YWcgPSB0YWc7XG4gICAgfVxuICAgIHNldEJ5dGVTcGFuKGJ5dGVTcGFuKSB7XG4gICAgICAgIHRoaXMuYnl0ZVNwYW4gPSBieXRlU3BhbjtcbiAgICB9XG4gICAgZ2V0Qnl0ZVNwYW4oKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmJ5dGVTcGFuO1xuICAgIH1cbn1cbmV4cG9ydHMuZGVmYXVsdCA9IENib3JUYWc7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stricahq/cbors/dist/CborTag.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@stricahq/cbors/dist/Decoder.js":
/*!******************************************************!*\
  !*** ./node_modules/@stricahq/cbors/dist/Decoder.js ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/* eslint-disable no-underscore-dangle */\n/* eslint-disable no-bitwise */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst buffer_1 = __webpack_require__(/*! buffer */ \"buffer\");\nconst stream = __importStar(__webpack_require__(/*! stream */ \"stream\"));\nconst bignumber_js_1 = __importDefault(__webpack_require__(/*! bignumber.js */ \"(ssr)/./node_modules/bignumber.js/bignumber.js\"));\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/@stricahq/cbors/dist/utils.js\");\nconst BufferList_1 = __importDefault(__webpack_require__(/*! ./BufferList */ \"(ssr)/./node_modules/@stricahq/cbors/dist/BufferList.js\"));\nconst SimpleValue_1 = __importDefault(__webpack_require__(/*! ./SimpleValue */ \"(ssr)/./node_modules/@stricahq/cbors/dist/SimpleValue.js\"));\nconst CborTag_1 = __importDefault(__webpack_require__(/*! ./CborTag */ \"(ssr)/./node_modules/@stricahq/cbors/dist/CborTag.js\"));\nconst CborArray_1 = __importDefault(__webpack_require__(/*! ./CborArray */ \"(ssr)/./node_modules/@stricahq/cbors/dist/CborArray.js\"));\nconst CborMap_1 = __importDefault(__webpack_require__(/*! ./CborMap */ \"(ssr)/./node_modules/@stricahq/cbors/dist/CborMap.js\"));\nconst readFloat16 = (value) => {\n    const sign = value & 0x8000;\n    let exponent = value & 0x7c00;\n    const fraction = value & 0x03ff;\n    if (exponent === 0x7c00)\n        exponent = 0xff << 10;\n    else if (exponent !== 0)\n        exponent += (127 - 15) << 10;\n    else if (fraction !== 0)\n        return (sign ? -1 : 1) * fraction * utils_1.POW_2_24;\n    const buf = buffer_1.Buffer.alloc(4);\n    buf.writeUInt32BE((sign << 16) | (exponent << 13) | (fraction << 13));\n    return buf.readFloatBE(0);\n};\nconst isBreakPoint = (value) => {\n    if (value !== 0xff)\n        return false;\n    return true;\n};\nclass Decoder extends stream.Transform {\n    constructor() {\n        super({\n            writableObjectMode: false,\n            readableObjectMode: true,\n        });\n        this.needed = null;\n        this.fresh = true;\n        this._parser = this.parse();\n        this.offset = 0;\n        this.usedBytes = [];\n        this.bl = new BufferList_1.default();\n        this.restart();\n    }\n    static decode(inputBytes) {\n        const decoder = new Decoder();\n        const bs = new BufferList_1.default();\n        bs.push(inputBytes);\n        const parser = decoder.parse();\n        let state = parser.next();\n        while (!state.done) {\n            const b = bs.read(state.value);\n            if (b == null || b.length !== state.value) {\n                throw new Error('Insufficient data');\n            }\n            state = parser.next(b);\n        }\n        if (bs.length > 0) {\n            throw new Error('Remaining Bytes');\n        }\n        return {\n            bytes: inputBytes,\n            value: state.value,\n        };\n    }\n    readUInt64(f, g, startByte) {\n        const bigNum = (0, utils_1.getBigNum)(f, g);\n        if (bignumber_js_1.default.isBigNumber(bigNum)) {\n            return (0, utils_1.addSpanBytesToObject)(bigNum, [startByte, this.offset]);\n        }\n        return bigNum;\n    }\n    updateTracker(bytes) {\n        this.usedBytes.push(bytes);\n        this.offset += bytes.length;\n    }\n    *readIndefiniteStringLength(majorType, startByte) {\n        let bytes = yield 1;\n        let length;\n        const number = bytes.readUInt8(0);\n        if (number === 0xff) {\n            length = -1;\n        }\n        else {\n            const ai = number & 0x1f;\n            // read length\n            const lengthReader = this.readLength(ai, startByte);\n            let lengthStatus = lengthReader.next();\n            while (!lengthStatus.done) {\n                bytes = yield lengthStatus.value;\n                lengthStatus = lengthReader.next(bytes);\n            }\n            length = lengthStatus.value;\n            //\n            if (length < 0 || number >> 5 !== majorType) {\n                throw new Error('Invalid indefinite length encoding');\n            }\n        }\n        return length;\n    }\n    *readLength(additionalInformation, startByte) {\n        if (additionalInformation < 24) {\n            return additionalInformation;\n        }\n        if (additionalInformation === 24) {\n            const bytes = yield 1;\n            return bytes.readUInt8(0);\n        }\n        if (additionalInformation === 25) {\n            const bytes = yield 2;\n            return bytes.readUInt16BE(0);\n        }\n        if (additionalInformation === 26) {\n            const bytes = yield 4;\n            return bytes.readUInt32BE(0);\n        }\n        if (additionalInformation === 27) {\n            const fBytes = yield 4;\n            const f = fBytes.readUInt32BE(0);\n            const gBytes = yield 4;\n            const g = gBytes.readUInt32BE(0);\n            return this.readUInt64(f, g, startByte);\n        }\n        if (additionalInformation === 31) {\n            return -1;\n        }\n        throw new Error('Invalid length encoding');\n    }\n    _transform(fresh, encoding, cb) {\n        this.bl.push(fresh);\n        while (this.bl.length >= this.needed) {\n            let ret = null;\n            let chunk;\n            if (this.needed === null) {\n                chunk = undefined;\n            }\n            else {\n                chunk = this.bl.read(this.needed);\n            }\n            try {\n                ret = this._parser.next(chunk);\n            }\n            catch (e) {\n                return cb(e);\n            }\n            if (this.needed) {\n                this.fresh = false;\n            }\n            if (ret.done) {\n                this.push({\n                    bytes: this.usedBytes,\n                    value: ret.value,\n                });\n                this.restart();\n            }\n            else {\n                this.needed = ret.value || Infinity;\n            }\n        }\n        return cb();\n    }\n    *parse(suppliedBytes) {\n        let startByte = this.offset;\n        let bytes;\n        if (suppliedBytes) {\n            bytes = suppliedBytes;\n            startByte -= suppliedBytes.length;\n        }\n        else {\n            bytes = yield 1;\n            this.updateTracker(bytes);\n        }\n        const value = bytes.readUInt8(0);\n        const majorType = value >> 5;\n        const additionalInformation = value & 0x1f;\n        let length;\n        if (majorType === 7) {\n            if (additionalInformation === 25) {\n                bytes = yield 2;\n                this.updateTracker(bytes);\n                const number = bytes.readUInt16BE(0);\n                return readFloat16(number);\n            }\n            if (additionalInformation === 26) {\n                bytes = yield 4;\n                this.updateTracker(bytes);\n                return bytes.readFloatBE(0);\n            }\n            if (additionalInformation === 27) {\n                bytes = yield 8;\n                this.updateTracker(bytes);\n                return bytes.readDoubleBE(0);\n            }\n        }\n        // read length\n        const lengthReader = this.readLength(additionalInformation, startByte);\n        let lengthStatus = lengthReader.next();\n        while (!lengthStatus.done) {\n            bytes = yield lengthStatus.value;\n            this.updateTracker(bytes);\n            lengthStatus = lengthReader.next(bytes);\n        }\n        length = lengthStatus.value;\n        //\n        if (length < 0 && (majorType < 2 || majorType > 6))\n            throw new Error('Invalid length');\n        switch (majorType) {\n            case 0:\n                return length;\n            case 1: {\n                if (length === Number.MAX_SAFE_INTEGER) {\n                    const bigNum = new bignumber_js_1.default(-1).minus(new bignumber_js_1.default(Number.MAX_SAFE_INTEGER.toString(16), 16));\n                    return (0, utils_1.addSpanBytesToObject)(bigNum, [startByte, this.offset]);\n                }\n                if (bignumber_js_1.default.isBigNumber(length)) {\n                    const bigNum = new bignumber_js_1.default(-1).minus(length);\n                    return (0, utils_1.addSpanBytesToObject)(bigNum, [startByte, this.offset]);\n                }\n                return -1 - length;\n            }\n            case 2: {\n                if (length < 0) {\n                    const chunks = [];\n                    {\n                        // read indefinite length\n                        const inDefLengthReader = this.readIndefiniteStringLength(majorType, startByte);\n                        let inDefLengthStatus = inDefLengthReader.next();\n                        while (!inDefLengthStatus.done) {\n                            bytes = yield inDefLengthStatus.value;\n                            this.updateTracker(bytes);\n                            inDefLengthStatus = inDefLengthReader.next(bytes);\n                        }\n                        length = inDefLengthStatus.value;\n                        //\n                    }\n                    while (length >= 0) {\n                        bytes = yield length;\n                        this.updateTracker(bytes);\n                        chunks.push(bytes);\n                        {\n                            // read indefinite length\n                            const inDefLengthReader = this.readIndefiniteStringLength(majorType, startByte);\n                            let inDefLengthStatus = inDefLengthReader.next();\n                            while (!inDefLengthStatus.done) {\n                                bytes = yield inDefLengthStatus.value;\n                                this.updateTracker(bytes);\n                                inDefLengthStatus = inDefLengthReader.next(bytes);\n                            }\n                            length = inDefLengthStatus.value;\n                            //\n                        }\n                    }\n                    const buf = buffer_1.Buffer.concat(chunks);\n                    return (0, utils_1.addSpanBytesToObject)(buf, [startByte, this.offset]);\n                }\n                bytes = yield length;\n                this.updateTracker(bytes);\n                return (0, utils_1.addSpanBytesToObject)(bytes, [startByte, this.offset]);\n            }\n            case 3: {\n                const stringBuf = [];\n                if (length < 0) {\n                    {\n                        // read indefinite length\n                        const inDefLengthReader = this.readIndefiniteStringLength(majorType, startByte);\n                        let inDefLengthStatus = inDefLengthReader.next();\n                        while (!inDefLengthStatus.done) {\n                            bytes = yield inDefLengthStatus.value;\n                            this.updateTracker(bytes);\n                            inDefLengthStatus = inDefLengthReader.next(bytes);\n                        }\n                        length = inDefLengthStatus.value;\n                        //\n                    }\n                    while (length >= 0) {\n                        bytes = yield length;\n                        this.updateTracker(bytes);\n                        stringBuf.push(bytes);\n                        //\n                        {\n                            // read indefinite length\n                            const inDefLengthReader = this.readIndefiniteStringLength(majorType, startByte);\n                            let inDefLengthStatus = inDefLengthReader.next();\n                            while (!inDefLengthStatus.done) {\n                                bytes = yield inDefLengthStatus.value;\n                                this.updateTracker(bytes);\n                                inDefLengthStatus = inDefLengthReader.next(bytes);\n                            }\n                            length = inDefLengthStatus.value;\n                            //\n                        }\n                    }\n                    const string = (0, utils_1.utf8Decoder)(buffer_1.Buffer.concat(stringBuf));\n                    return string;\n                }\n                bytes = yield length;\n                this.updateTracker(bytes);\n                const string = (0, utils_1.utf8Decoder)(bytes);\n                return string;\n            }\n            case 4: {\n                if (length < 0) {\n                    const ary = new CborArray_1.default();\n                    bytes = yield 1;\n                    this.updateTracker(bytes);\n                    let bp = bytes.readUInt8(0);\n                    while (!isBreakPoint(bp)) {\n                        ary.push(yield* this.parse(bytes));\n                        bytes = yield 1;\n                        this.updateTracker(bytes);\n                        bp = bytes.readUInt8(0);\n                    }\n                    ary.setByteSpan([startByte, this.offset]);\n                    return ary;\n                }\n                const ary = new CborArray_1.default();\n                for (let i = 0; i < length; i += 1) {\n                    ary.push(yield* this.parse());\n                }\n                ary.setByteSpan([startByte, this.offset]);\n                return ary;\n            }\n            case 5: {\n                if (length < 0) {\n                    const obj = new CborMap_1.default();\n                    bytes = yield 1;\n                    this.updateTracker(bytes);\n                    let bp = bytes.readUInt8(0);\n                    while (!isBreakPoint(bp)) {\n                        const key = yield* this.parse(bytes);\n                        const val = yield* this.parse();\n                        obj.set(key, val);\n                        bytes = yield 1;\n                        this.updateTracker(bytes);\n                        bp = bytes.readUInt8(0);\n                    }\n                    obj.setByteSpan([startByte, this.offset]);\n                    return obj;\n                }\n                const obj = new CborMap_1.default();\n                for (let i = 0; i < length; i += 1) {\n                    const key = yield* this.parse();\n                    const val = yield* this.parse();\n                    obj.set(key, val);\n                }\n                obj.setByteSpan([startByte, this.offset]);\n                return obj;\n            }\n            case 6: {\n                const tag = new CborTag_1.default(yield* this.parse(), length);\n                tag.setByteSpan([startByte, this.offset]);\n                return tag;\n            }\n            case 7: {\n                switch (length) {\n                    case 20:\n                        return false;\n                    case 21:\n                        return true;\n                    case 22:\n                        return null;\n                    case 23:\n                        return undefined;\n                    default:\n                        return new SimpleValue_1.default(length);\n                }\n            }\n            default: {\n                throw new Error('Invalid CBOR encoding');\n            }\n        }\n    }\n    restart() {\n        this.needed = null;\n        this._parser = this.parse();\n        this.fresh = true;\n        this.offset = 0;\n        this.usedBytes = [];\n    }\n    _flush(cb) {\n        cb(this.fresh ? null : new Error('unexpected end of input'));\n    }\n}\nexports[\"default\"] = Decoder;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stricahq/cbors/dist/Decoder.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@stricahq/cbors/dist/SimpleValue.js":
/*!**********************************************************!*\
  !*** ./node_modules/@stricahq/cbors/dist/SimpleValue.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nclass SimpleValue {\n    constructor(value) {\n        this.value = value;\n    }\n}\nexports[\"default\"] = SimpleValue;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHN0cmljYWhxL2Nib3JzL2Rpc3QvU2ltcGxlVmFsdWUuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFlIiwic291cmNlcyI6WyJEOlxcaGFja290aG9uXFxCbG9ja2NoYWluXFxWaW50cmVrXFxub2RlX21vZHVsZXNcXEBzdHJpY2FocVxcY2JvcnNcXGRpc3RcXFNpbXBsZVZhbHVlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuY2xhc3MgU2ltcGxlVmFsdWUge1xuICAgIGNvbnN0cnVjdG9yKHZhbHVlKSB7XG4gICAgICAgIHRoaXMudmFsdWUgPSB2YWx1ZTtcbiAgICB9XG59XG5leHBvcnRzLmRlZmF1bHQgPSBTaW1wbGVWYWx1ZTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stricahq/cbors/dist/SimpleValue.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@stricahq/cbors/dist/encode.js":
/*!*****************************************************!*\
  !*** ./node_modules/@stricahq/cbors/dist/encode.js ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n/* eslint-disable no-bitwise */\nconst buffer_1 = __webpack_require__(/*! buffer */ \"buffer\");\nconst bignumber_js_1 = __importDefault(__webpack_require__(/*! bignumber.js */ \"(ssr)/./node_modules/bignumber.js/bignumber.js\"));\nconst _1 = __webpack_require__(/*! . */ \"(ssr)/./node_modules/@stricahq/cbors/dist/index.js\");\nconst helpers_1 = __webpack_require__(/*! ./helpers */ \"(ssr)/./node_modules/@stricahq/cbors/dist/helpers.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/@stricahq/cbors/dist/utils.js\");\nconst NAN_BUF = buffer_1.Buffer.from('f97e00', 'hex');\nconst BREAK = buffer_1.Buffer.from('ff', 'hex');\nexports[\"default\"] = (input, options = { collapseBigNumber: true }) => {\n    const outBufAry = [];\n    function pushFloat64(value) {\n        const buf = buffer_1.Buffer.allocUnsafe(8);\n        buf.writeDoubleBE(value);\n        outBufAry.push(buf);\n    }\n    function pushUInt8(value) {\n        const buf = buffer_1.Buffer.allocUnsafe(1);\n        buf.writeUInt8(value, 0);\n        outBufAry.push(buf);\n    }\n    function pushBuffer(value) {\n        outBufAry.push(value);\n    }\n    function pushUInt16(value) {\n        const buf = buffer_1.Buffer.allocUnsafe(2);\n        buf.writeUInt16BE(value, 0);\n        outBufAry.push(buf);\n    }\n    function pushUInt32(value) {\n        const buf = buffer_1.Buffer.allocUnsafe(4);\n        buf.writeUInt32BE(value, 0);\n        outBufAry.push(buf);\n    }\n    function pushUInt64(value) {\n        const low = value % utils_1.POW_2_32;\n        const high = (value - low) / utils_1.POW_2_32;\n        const buf = buffer_1.Buffer.allocUnsafe(8);\n        buf.writeUInt32BE(high, 0);\n        buf.writeUInt32BE(low, 4);\n        outBufAry.push(buf);\n    }\n    function pushTypeAndLength(type, length) {\n        if (length < 24) {\n            pushUInt8((type << 5) | length);\n        }\n        else if (length < 0x100) {\n            pushUInt8((type << 5) | 24);\n            pushUInt8(length);\n        }\n        else if (length < 0x10000) {\n            pushUInt8((type << 5) | 25);\n            pushUInt16(length);\n        }\n        else if (length < 0x100000000) {\n            pushUInt8((type << 5) | 26);\n            pushUInt32(length);\n        }\n        else {\n            pushUInt8((type << 5) | 27);\n            pushUInt64(length);\n        }\n    }\n    function pushIntNum(value) {\n        if (Object.is(value, -0)) {\n            return pushBuffer(buffer_1.Buffer.from('f98000', 'hex'));\n        }\n        if (value >= 0 && value <= utils_1.POW_2_53) {\n            return pushTypeAndLength(0, value);\n        }\n        if (-utils_1.POW_2_53 <= value && value < 0) {\n            return pushTypeAndLength(1, -(value + 1));\n        }\n    }\n    function pushBigInt(value) {\n        let valueM = value;\n        let type = 0;\n        let tag = 2;\n        if (valueM.isNegative()) {\n            valueM = valueM.negated().minus(1);\n            type = 1;\n            tag = 3;\n        }\n        if (options.collapseBigNumber && valueM.lte(utils_1.MAX_BIG_NUM_INT64)) {\n            if (valueM.lte(utils_1.MAX_BIG_NUM_INT32)) {\n                return pushTypeAndLength(type, valueM.toNumber());\n            }\n            pushUInt8((type << 5) | 27);\n            pushUInt32(valueM.dividedToIntegerBy(utils_1.SHIFT32).toNumber());\n            pushUInt32(valueM.mod(utils_1.SHIFT32).toNumber());\n        }\n        else {\n            let str = valueM.toString(16);\n            if (str.length % 2) {\n                str = `0${str}`;\n            }\n            // push tag\n            pushTypeAndLength(6, tag);\n            // push buffer\n            const buf = buffer_1.Buffer.from(str, 'hex');\n            pushTypeAndLength(2, buf.length);\n            pushBuffer(buf);\n        }\n    }\n    function pushBigNumber(value) {\n        if (value.isNaN()) {\n            pushBuffer(NAN_BUF);\n        }\n        else if (value.isInteger()) {\n            pushBigInt(value);\n        }\n        else {\n            // push decimal\n            pushTypeAndLength(6, 4);\n            pushTypeAndLength(4, 2);\n            const dec = value.decimalPlaces();\n            const slide = value.shiftedBy(dec);\n            pushIntNum(-dec);\n            if (slide.abs().isLessThan(utils_1.MAX_BIG_NUM_INT)) {\n                pushIntNum(slide.toNumber());\n            }\n            else {\n                pushBigInt(slide);\n            }\n        }\n    }\n    function encodeItem(value) {\n        if (value === false)\n            return pushUInt8(0xf4);\n        if (value === true)\n            return pushUInt8(0xf5);\n        if (value === null)\n            return pushUInt8(0xf6);\n        if (value === undefined)\n            return pushUInt8(0xf7);\n        switch (typeof value) {\n            case 'number': {\n                if (Math.round(value) === value) {\n                    return pushIntNum(value);\n                }\n                pushUInt8(0xfb);\n                return pushFloat64(value);\n            }\n            case 'string': {\n                const strBuff = buffer_1.Buffer.from(value, 'utf8');\n                pushTypeAndLength(3, strBuff.length);\n                return pushBuffer(strBuff);\n            }\n            default: {\n                if (Array.isArray(value)) {\n                    if (value instanceof helpers_1.IndefiniteArray) {\n                        pushUInt8((4 << 5) | 31);\n                    }\n                    else {\n                        pushTypeAndLength(4, value.length);\n                    }\n                    for (const v of value) {\n                        encodeItem(v);\n                    }\n                    if (value instanceof helpers_1.IndefiniteArray) {\n                        pushBuffer(BREAK);\n                    }\n                }\n                else if (value instanceof buffer_1.Buffer) {\n                    pushTypeAndLength(2, value.length);\n                    pushBuffer(value);\n                }\n                else if (value instanceof ArrayBuffer) {\n                    const buf = buffer_1.Buffer.from(value);\n                    pushTypeAndLength(2, buf.length);\n                    pushBuffer(buf);\n                }\n                else if (value instanceof Uint8ClampedArray) {\n                    const buf = buffer_1.Buffer.from(value);\n                    pushTypeAndLength(2, buf.length);\n                    pushBuffer(buf);\n                }\n                else if (value instanceof Uint8Array) {\n                    const buf = buffer_1.Buffer.from(value);\n                    pushTypeAndLength(2, buf.length);\n                    pushBuffer(buf);\n                }\n                else if (bignumber_js_1.default.isBigNumber(value)) {\n                    pushBigNumber(value);\n                }\n                else if (value instanceof _1.CborTag) {\n                    pushTypeAndLength(6, value.tag);\n                    encodeItem(value.value);\n                }\n                else {\n                    let entries;\n                    if (value instanceof Map) {\n                        entries = [...value.entries()];\n                    }\n                    else {\n                        entries = [...Object.entries(value)];\n                    }\n                    if (value instanceof helpers_1.IndefiniteMap) {\n                        pushUInt8((5 << 5) | 31);\n                    }\n                    else {\n                        pushTypeAndLength(5, entries.length);\n                    }\n                    for (const [key, v] of entries) {\n                        encodeItem(key);\n                        encodeItem(v);\n                    }\n                    if (value instanceof helpers_1.IndefiniteMap) {\n                        pushBuffer(BREAK);\n                    }\n                }\n            }\n        }\n    }\n    encodeItem(input);\n    return buffer_1.Buffer.concat(outBufAry);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stricahq/cbors/dist/encode.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@stricahq/cbors/dist/helpers.js":
/*!******************************************************!*\
  !*** ./node_modules/@stricahq/cbors/dist/helpers.js ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.CborTag = exports.IndefiniteArray = exports.IndefiniteMap = void 0;\n/* eslint-disable max-classes-per-file */\nclass IndefiniteMap extends Map {\n}\nexports.IndefiniteMap = IndefiniteMap;\nclass IndefiniteArray extends Array {\n}\nexports.IndefiniteArray = IndefiniteArray;\nvar CborTag_1 = __webpack_require__(/*! ./CborTag */ \"(ssr)/./node_modules/@stricahq/cbors/dist/CborTag.js\");\nObject.defineProperty(exports, \"CborTag\", ({ enumerable: true, get: function () { return __importDefault(CborTag_1).default; } }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHN0cmljYWhxL2Nib3JzL2Rpc3QvaGVscGVycy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0EsNkNBQTZDO0FBQzdDO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGVBQWUsR0FBRyx1QkFBdUIsR0FBRyxxQkFBcUI7QUFDakU7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQSx1QkFBdUI7QUFDdkIsZ0JBQWdCLG1CQUFPLENBQUMsdUVBQVc7QUFDbkMsMkNBQTBDLEVBQUUscUNBQXFDLDhDQUE4QyxFQUFDIiwic291cmNlcyI6WyJEOlxcaGFja290aG9uXFxCbG9ja2NoYWluXFxWaW50cmVrXFxub2RlX21vZHVsZXNcXEBzdHJpY2FocVxcY2JvcnNcXGRpc3RcXGhlbHBlcnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG52YXIgX19pbXBvcnREZWZhdWx0ID0gKHRoaXMgJiYgdGhpcy5fX2ltcG9ydERlZmF1bHQpIHx8IGZ1bmN0aW9uIChtb2QpIHtcbiAgICByZXR1cm4gKG1vZCAmJiBtb2QuX19lc01vZHVsZSkgPyBtb2QgOiB7IFwiZGVmYXVsdFwiOiBtb2QgfTtcbn07XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLkNib3JUYWcgPSBleHBvcnRzLkluZGVmaW5pdGVBcnJheSA9IGV4cG9ydHMuSW5kZWZpbml0ZU1hcCA9IHZvaWQgMDtcbi8qIGVzbGludC1kaXNhYmxlIG1heC1jbGFzc2VzLXBlci1maWxlICovXG5jbGFzcyBJbmRlZmluaXRlTWFwIGV4dGVuZHMgTWFwIHtcbn1cbmV4cG9ydHMuSW5kZWZpbml0ZU1hcCA9IEluZGVmaW5pdGVNYXA7XG5jbGFzcyBJbmRlZmluaXRlQXJyYXkgZXh0ZW5kcyBBcnJheSB7XG59XG5leHBvcnRzLkluZGVmaW5pdGVBcnJheSA9IEluZGVmaW5pdGVBcnJheTtcbnZhciBDYm9yVGFnXzEgPSByZXF1aXJlKFwiLi9DYm9yVGFnXCIpO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiQ2JvclRhZ1wiLCB7IGVudW1lcmFibGU6IHRydWUsIGdldDogZnVuY3Rpb24gKCkgeyByZXR1cm4gX19pbXBvcnREZWZhdWx0KENib3JUYWdfMSkuZGVmYXVsdDsgfSB9KTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stricahq/cbors/dist/helpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@stricahq/cbors/dist/index.js":
/*!****************************************************!*\
  !*** ./node_modules/@stricahq/cbors/dist/index.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Decoder = exports.Encoder = void 0;\nconst encode_1 = __importDefault(__webpack_require__(/*! ./encode */ \"(ssr)/./node_modules/@stricahq/cbors/dist/encode.js\"));\n// to maintain similar API as Decoder\nexports.Encoder = {\n    encode: encode_1.default,\n};\nvar Decoder_1 = __webpack_require__(/*! ./Decoder */ \"(ssr)/./node_modules/@stricahq/cbors/dist/Decoder.js\");\nObject.defineProperty(exports, \"Decoder\", ({ enumerable: true, get: function () { return __importDefault(Decoder_1).default; } }));\n__exportStar(__webpack_require__(/*! ./helpers */ \"(ssr)/./node_modules/@stricahq/cbors/dist/helpers.js\"), exports);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stricahq/cbors/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@stricahq/cbors/dist/utils.js":
/*!****************************************************!*\
  !*** ./node_modules/@stricahq/cbors/dist/utils.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.utf8Decoder = exports.addSpanBytesToObject = exports.getBigNum = exports.MAX_BIG_NUM_INT64 = exports.MAX_BIG_NUM_INT32 = exports.MAX_BIG_NUM_INT = exports.POW_2_53 = exports.POW_2_32 = exports.POW_2_24 = exports.SHIFT32 = void 0;\nconst bignumber_js_1 = __importDefault(__webpack_require__(/*! bignumber.js */ \"(ssr)/./node_modules/bignumber.js/bignumber.js\"));\nconst MAX_SAFE_HIGH = 0x1fffff;\nexports.SHIFT32 = 0x100000000;\nexports.POW_2_24 = 5.960464477539063e-8;\nexports.POW_2_32 = 4294967296;\nexports.POW_2_53 = 9007199254740992;\nexports.MAX_BIG_NUM_INT = new bignumber_js_1.default('0x20000000000000');\nexports.MAX_BIG_NUM_INT32 = new bignumber_js_1.default('0xffffffff');\nexports.MAX_BIG_NUM_INT64 = new bignumber_js_1.default('0xffffffffffffffff');\nconst getBigNum = (f, g) => {\n    if (f > MAX_SAFE_HIGH) {\n        return new bignumber_js_1.default(f).times(exports.SHIFT32).plus(g);\n    }\n    return f * exports.SHIFT32 + g;\n};\nexports.getBigNum = getBigNum;\nconst addSpanBytesToObject = (obj, span) => {\n    const spanObj = obj;\n    spanObj.byteSpan = span;\n    spanObj.getByteSpan = function () {\n        return this.byteSpan;\n    };\n    return spanObj;\n};\nexports.addSpanBytesToObject = addSpanBytesToObject;\nconst td = new TextDecoder('utf8', { fatal: true, ignoreBOM: true });\nconst utf8Decoder = (buf) => td.decode(buf);\nexports.utf8Decoder = utf8Decoder;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stricahq/cbors/dist/utils.js\n");

/***/ })

};
;