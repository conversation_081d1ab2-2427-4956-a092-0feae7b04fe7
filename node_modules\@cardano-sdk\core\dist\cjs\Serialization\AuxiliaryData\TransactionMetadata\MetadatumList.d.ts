import { HexBlob } from '@cardano-sdk/util';
import { TransactionMetadatum } from './TransactionMetadatum';
export declare class MetadatumList {
    #private;
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): MetadatumList;
    getLength(): number;
    get(index: number): TransactionMetadatum;
    add(elem: TransactionMetadatum): void;
    equals(other: MetadatumList): boolean;
}
//# sourceMappingURL=MetadatumList.d.ts.map