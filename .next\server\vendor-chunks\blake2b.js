/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/blake2b";
exports.ids = ["vendor-chunks/blake2b"];
exports.modules = {

/***/ "(ssr)/./node_modules/blake2b/index.js":
/*!***************************************!*\
  !*** ./node_modules/blake2b/index.js ***!
  \***************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var assert = __webpack_require__(/*! nanoassert */ \"(ssr)/./node_modules/nanoassert/index.js\")\nvar b2wasm = __webpack_require__(/*! blake2b-wasm */ \"(ssr)/./node_modules/blake2b-wasm/index.js\")\n\n// 64-bit unsigned addition\n// Sets v[a,a+1] += v[b,b+1]\n// v should be a Uint32Array\nfunction ADD64AA (v, a, b) {\n  var o0 = v[a] + v[b]\n  var o1 = v[a + 1] + v[b + 1]\n  if (o0 >= 0x100000000) {\n    o1++\n  }\n  v[a] = o0\n  v[a + 1] = o1\n}\n\n// 64-bit unsigned addition\n// Sets v[a,a+1] += b\n// b0 is the low 32 bits of b, b1 represents the high 32 bits\nfunction ADD64AC (v, a, b0, b1) {\n  var o0 = v[a] + b0\n  if (b0 < 0) {\n    o0 += 0x100000000\n  }\n  var o1 = v[a + 1] + b1\n  if (o0 >= 0x100000000) {\n    o1++\n  }\n  v[a] = o0\n  v[a + 1] = o1\n}\n\n// Little-endian byte access\nfunction B2B_GET32 (arr, i) {\n  return (arr[i] ^\n  (arr[i + 1] << 8) ^\n  (arr[i + 2] << 16) ^\n  (arr[i + 3] << 24))\n}\n\n// G Mixing function\n// The ROTRs are inlined for speed\nfunction B2B_G (a, b, c, d, ix, iy) {\n  var x0 = m[ix]\n  var x1 = m[ix + 1]\n  var y0 = m[iy]\n  var y1 = m[iy + 1]\n\n  ADD64AA(v, a, b) // v[a,a+1] += v[b,b+1] ... in JS we must store a uint64 as two uint32s\n  ADD64AC(v, a, x0, x1) // v[a, a+1] += x ... x0 is the low 32 bits of x, x1 is the high 32 bits\n\n  // v[d,d+1] = (v[d,d+1] xor v[a,a+1]) rotated to the right by 32 bits\n  var xor0 = v[d] ^ v[a]\n  var xor1 = v[d + 1] ^ v[a + 1]\n  v[d] = xor1\n  v[d + 1] = xor0\n\n  ADD64AA(v, c, d)\n\n  // v[b,b+1] = (v[b,b+1] xor v[c,c+1]) rotated right by 24 bits\n  xor0 = v[b] ^ v[c]\n  xor1 = v[b + 1] ^ v[c + 1]\n  v[b] = (xor0 >>> 24) ^ (xor1 << 8)\n  v[b + 1] = (xor1 >>> 24) ^ (xor0 << 8)\n\n  ADD64AA(v, a, b)\n  ADD64AC(v, a, y0, y1)\n\n  // v[d,d+1] = (v[d,d+1] xor v[a,a+1]) rotated right by 16 bits\n  xor0 = v[d] ^ v[a]\n  xor1 = v[d + 1] ^ v[a + 1]\n  v[d] = (xor0 >>> 16) ^ (xor1 << 16)\n  v[d + 1] = (xor1 >>> 16) ^ (xor0 << 16)\n\n  ADD64AA(v, c, d)\n\n  // v[b,b+1] = (v[b,b+1] xor v[c,c+1]) rotated right by 63 bits\n  xor0 = v[b] ^ v[c]\n  xor1 = v[b + 1] ^ v[c + 1]\n  v[b] = (xor1 >>> 31) ^ (xor0 << 1)\n  v[b + 1] = (xor0 >>> 31) ^ (xor1 << 1)\n}\n\n// Initialization Vector\nvar BLAKE2B_IV32 = new Uint32Array([\n  0xF3BCC908, 0x6A09E667, 0x84CAA73B, 0xBB67AE85,\n  0xFE94F82B, 0x3C6EF372, 0x5F1D36F1, 0xA54FF53A,\n  0xADE682D1, 0x510E527F, 0x2B3E6C1F, 0x9B05688C,\n  0xFB41BD6B, 0x1F83D9AB, 0x137E2179, 0x5BE0CD19\n])\n\nvar SIGMA8 = [\n  0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15,\n  14, 10, 4, 8, 9, 15, 13, 6, 1, 12, 0, 2, 11, 7, 5, 3,\n  11, 8, 12, 0, 5, 2, 15, 13, 10, 14, 3, 6, 7, 1, 9, 4,\n  7, 9, 3, 1, 13, 12, 11, 14, 2, 6, 5, 10, 4, 0, 15, 8,\n  9, 0, 5, 7, 2, 4, 10, 15, 14, 1, 11, 12, 6, 8, 3, 13,\n  2, 12, 6, 10, 0, 11, 8, 3, 4, 13, 7, 5, 15, 14, 1, 9,\n  12, 5, 1, 15, 14, 13, 4, 10, 0, 7, 6, 3, 9, 2, 8, 11,\n  13, 11, 7, 14, 12, 1, 3, 9, 5, 0, 15, 4, 8, 6, 2, 10,\n  6, 15, 14, 9, 11, 3, 0, 8, 12, 2, 13, 7, 1, 4, 10, 5,\n  10, 2, 8, 4, 7, 6, 1, 5, 15, 11, 9, 14, 3, 12, 13, 0,\n  0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15,\n  14, 10, 4, 8, 9, 15, 13, 6, 1, 12, 0, 2, 11, 7, 5, 3\n]\n\n// These are offsets into a uint64 buffer.\n// Multiply them all by 2 to make them offsets into a uint32 buffer,\n// because this is Javascript and we don't have uint64s\nvar SIGMA82 = new Uint8Array(SIGMA8.map(function (x) { return x * 2 }))\n\n// Compression function. 'last' flag indicates last block.\n// Note we're representing 16 uint64s as 32 uint32s\nvar v = new Uint32Array(32)\nvar m = new Uint32Array(32)\nfunction blake2bCompress (ctx, last) {\n  var i = 0\n\n  // init work variables\n  for (i = 0; i < 16; i++) {\n    v[i] = ctx.h[i]\n    v[i + 16] = BLAKE2B_IV32[i]\n  }\n\n  // low 64 bits of offset\n  v[24] = v[24] ^ ctx.t\n  v[25] = v[25] ^ (ctx.t / 0x100000000)\n  // high 64 bits not supported, offset may not be higher than 2**53-1\n\n  // last block flag set ?\n  if (last) {\n    v[28] = ~v[28]\n    v[29] = ~v[29]\n  }\n\n  // get little-endian words\n  for (i = 0; i < 32; i++) {\n    m[i] = B2B_GET32(ctx.b, 4 * i)\n  }\n\n  // twelve rounds of mixing\n  for (i = 0; i < 12; i++) {\n    B2B_G(0, 8, 16, 24, SIGMA82[i * 16 + 0], SIGMA82[i * 16 + 1])\n    B2B_G(2, 10, 18, 26, SIGMA82[i * 16 + 2], SIGMA82[i * 16 + 3])\n    B2B_G(4, 12, 20, 28, SIGMA82[i * 16 + 4], SIGMA82[i * 16 + 5])\n    B2B_G(6, 14, 22, 30, SIGMA82[i * 16 + 6], SIGMA82[i * 16 + 7])\n    B2B_G(0, 10, 20, 30, SIGMA82[i * 16 + 8], SIGMA82[i * 16 + 9])\n    B2B_G(2, 12, 22, 24, SIGMA82[i * 16 + 10], SIGMA82[i * 16 + 11])\n    B2B_G(4, 14, 16, 26, SIGMA82[i * 16 + 12], SIGMA82[i * 16 + 13])\n    B2B_G(6, 8, 18, 28, SIGMA82[i * 16 + 14], SIGMA82[i * 16 + 15])\n  }\n\n  for (i = 0; i < 16; i++) {\n    ctx.h[i] = ctx.h[i] ^ v[i] ^ v[i + 16]\n  }\n}\n\n// reusable parameter_block\nvar parameter_block = new Uint8Array([\n  0, 0, 0, 0,      //  0: outlen, keylen, fanout, depth\n  0, 0, 0, 0,      //  4: leaf length, sequential mode\n  0, 0, 0, 0,      //  8: node offset\n  0, 0, 0, 0,      // 12: node offset\n  0, 0, 0, 0,      // 16: node depth, inner length, rfu\n  0, 0, 0, 0,      // 20: rfu\n  0, 0, 0, 0,      // 24: rfu\n  0, 0, 0, 0,      // 28: rfu\n  0, 0, 0, 0,      // 32: salt\n  0, 0, 0, 0,      // 36: salt\n  0, 0, 0, 0,      // 40: salt\n  0, 0, 0, 0,      // 44: salt\n  0, 0, 0, 0,      // 48: personal\n  0, 0, 0, 0,      // 52: personal\n  0, 0, 0, 0,      // 56: personal\n  0, 0, 0, 0       // 60: personal\n])\n\n// Creates a BLAKE2b hashing context\n// Requires an output length between 1 and 64 bytes\n// Takes an optional Uint8Array key\nfunction Blake2b (outlen, key, salt, personal) {\n  // zero out parameter_block before usage\n  parameter_block.fill(0)\n  // state, 'param block'\n\n  this.b = new Uint8Array(128)\n  this.h = new Uint32Array(16)\n  this.t = 0 // input count\n  this.c = 0 // pointer within buffer\n  this.outlen = outlen // output length in bytes\n\n  parameter_block[0] = outlen\n  if (key) parameter_block[1] = key.length\n  parameter_block[2] = 1 // fanout\n  parameter_block[3] = 1 // depth\n\n  if (salt) parameter_block.set(salt, 32)\n  if (personal) parameter_block.set(personal, 48)\n\n  // initialize hash state\n  for (var i = 0; i < 16; i++) {\n    this.h[i] = BLAKE2B_IV32[i] ^ B2B_GET32(parameter_block, i * 4)\n  }\n\n  // key the hash, if applicable\n  if (key) {\n    blake2bUpdate(this, key)\n    // at the end\n    this.c = 128\n  }\n}\n\nBlake2b.prototype.update = function (input) {\n  assert(input instanceof Uint8Array, 'input must be Uint8Array or Buffer')\n  blake2bUpdate(this, input)\n  return this\n}\n\nBlake2b.prototype.digest = function (out) {\n  var buf = (!out || out === 'binary' || out === 'hex') ? new Uint8Array(this.outlen) : out\n  assert(buf instanceof Uint8Array, 'out must be \"binary\", \"hex\", Uint8Array, or Buffer')\n  assert(buf.length >= this.outlen, 'out must have at least outlen bytes of space')\n  blake2bFinal(this, buf)\n  if (out === 'hex') return hexSlice(buf)\n  return buf\n}\n\nBlake2b.prototype.final = Blake2b.prototype.digest\n\nBlake2b.ready = function (cb) {\n  b2wasm.ready(function () {\n    cb() // ignore the error\n  })\n}\n\n// Updates a BLAKE2b streaming hash\n// Requires hash context and Uint8Array (byte array)\nfunction blake2bUpdate (ctx, input) {\n  for (var i = 0; i < input.length; i++) {\n    if (ctx.c === 128) { // buffer full ?\n      ctx.t += ctx.c // add counters\n      blake2bCompress(ctx, false) // compress (not last)\n      ctx.c = 0 // counter to zero\n    }\n    ctx.b[ctx.c++] = input[i]\n  }\n}\n\n// Completes a BLAKE2b streaming hash\n// Returns a Uint8Array containing the message digest\nfunction blake2bFinal (ctx, out) {\n  ctx.t += ctx.c // mark last block offset\n\n  while (ctx.c < 128) { // fill up with zeros\n    ctx.b[ctx.c++] = 0\n  }\n  blake2bCompress(ctx, true) // final block flag = 1\n\n  for (var i = 0; i < ctx.outlen; i++) {\n    out[i] = ctx.h[i >> 2] >> (8 * (i & 3))\n  }\n  return out\n}\n\nfunction hexSlice (buf) {\n  var str = ''\n  for (var i = 0; i < buf.length; i++) str += toHex(buf[i])\n  return str\n}\n\nfunction toHex (n) {\n  if (n < 16) return '0' + n.toString(16)\n  return n.toString(16)\n}\n\nvar Proto = Blake2b\n\nmodule.exports = function createHash (outlen, key, salt, personal, noAssert) {\n  if (noAssert !== true) {\n    assert(outlen >= BYTES_MIN, 'outlen must be at least ' + BYTES_MIN + ', was given ' + outlen)\n    assert(outlen <= BYTES_MAX, 'outlen must be at most ' + BYTES_MAX + ', was given ' + outlen)\n    if (key != null) {\n      assert(key instanceof Uint8Array, 'key must be Uint8Array or Buffer')\n      assert(key.length >= KEYBYTES_MIN, 'key must be at least ' + KEYBYTES_MIN + ', was given ' + key.length)\n      assert(key.length <= KEYBYTES_MAX, 'key must be at most ' + KEYBYTES_MAX + ', was given ' + key.length)\n    }\n    if (salt != null) {\n      assert(salt instanceof Uint8Array, 'salt must be Uint8Array or Buffer')\n      assert(salt.length === SALTBYTES, 'salt must be exactly ' + SALTBYTES + ', was given ' + salt.length)\n    }\n    if (personal != null) {\n      assert(personal instanceof Uint8Array, 'personal must be Uint8Array or Buffer')\n      assert(personal.length === PERSONALBYTES, 'personal must be exactly ' + PERSONALBYTES + ', was given ' + personal.length)\n    }\n  }\n\n  return new Proto(outlen, key, salt, personal)\n}\n\nmodule.exports.ready = function (cb) {\n  b2wasm.ready(function () { // ignore errors\n    cb()\n  })\n}\n\nmodule.exports.WASM_SUPPORTED = b2wasm.SUPPORTED\nmodule.exports.WASM_LOADED = false\n\nvar BYTES_MIN = module.exports.BYTES_MIN = 16\nvar BYTES_MAX = module.exports.BYTES_MAX = 64\nvar BYTES = module.exports.BYTES = 32\nvar KEYBYTES_MIN = module.exports.KEYBYTES_MIN = 16\nvar KEYBYTES_MAX = module.exports.KEYBYTES_MAX = 64\nvar KEYBYTES = module.exports.KEYBYTES = 32\nvar SALTBYTES = module.exports.SALTBYTES = 16\nvar PERSONALBYTES = module.exports.PERSONALBYTES = 16\n\nb2wasm.ready(function (err) {\n  if (!err) {\n    module.exports.WASM_LOADED = true\n    module.exports = b2wasm\n  }\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/blake2b/index.js\n");

/***/ })

};
;