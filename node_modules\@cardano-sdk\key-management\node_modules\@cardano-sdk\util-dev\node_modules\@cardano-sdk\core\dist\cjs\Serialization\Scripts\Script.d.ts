import * as Crypto from '@cardano-sdk/crypto';
import { HexBlob } from '@cardano-sdk/util';
import { NativeScript } from './NativeScript';
import { PlutusV1Script, PlutusV2Script, PlutusV3Script } from './PlutusScript';
import { ScriptLanguage } from './ScriptLanguage';
import type * as Cardano from '../../Cardano';
export declare class Script {
    #private;
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): Script;
    toCore(): Cardano.Script;
    static fromCore(coreScript: Cardano.Script): Script;
    language(): ScriptLanguage;
    static newNativeScript(nativeScript: NativeScript): Script;
    static newPlutusV1Script(plutusV1Script: PlutusV1Script): Script;
    static newPlutusV2Script(plutusV2Script: PlutusV2Script): Script;
    static newPlutusV3Script(plutusV3Script: PlutusV3Script): Script;
    asNative(): NativeScript | undefined;
    asPlutusV1(): PlutusV1Script | undefined;
    asPlutusV2(): PlutusV2Script | undefined;
    asPlutusV3(): PlutusV3Script | undefined;
    hash(): Crypto.Hash28ByteBase16;
}
//# sourceMappingURL=Script.d.ts.map