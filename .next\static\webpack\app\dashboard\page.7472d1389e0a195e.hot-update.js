"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_providers_WalletProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/WalletProvider */ \"(app-pages-browser)/./src/components/providers/WalletProvider.tsx\");\n/* harmony import */ var _lib_blockchain__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/blockchain */ \"(app-pages-browser)/./src/lib/blockchain.ts\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Coins_ExternalLink_MapPin_Trophy_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Coins,ExternalLink,MapPin,Trophy,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.mjs\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Coins_ExternalLink_MapPin_Trophy_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Coins,ExternalLink,MapPin,Trophy,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.mjs\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Coins_ExternalLink_MapPin_Trophy_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Coins,ExternalLink,MapPin,Trophy,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coins.mjs\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Coins_ExternalLink_MapPin_Trophy_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Coins,ExternalLink,MapPin,Trophy,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Coins_ExternalLink_MapPin_Trophy_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Coins,ExternalLink,MapPin,Trophy,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.mjs\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Coins_ExternalLink_MapPin_Trophy_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Coins,ExternalLink,MapPin,Trophy,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction DashboardPage() {\n    _s();\n    const { connected, wallet, address } = (0,_components_providers_WalletProvider__WEBPACK_IMPORTED_MODULE_2__.useWallet)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [trekBalance, setTrekBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [nfts, setNfts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [adaBalance, setAdaBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('0');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            if (connected && wallet) {\n                loadDashboardData();\n            }\n        }\n    }[\"DashboardPage.useEffect\"], [\n        connected,\n        wallet\n    ]);\n    const loadDashboardData = async ()=>{\n        if (!wallet) return;\n        setLoading(true);\n        try {\n            _lib_blockchain__WEBPACK_IMPORTED_MODULE_3__.blockchainService.setWallet(wallet);\n            // Load wallet data\n            const [balance, trekTokens, trailNFTs] = await Promise.all([\n                _lib_blockchain__WEBPACK_IMPORTED_MODULE_3__.blockchainService.getWalletBalance(),\n                _lib_blockchain__WEBPACK_IMPORTED_MODULE_3__.blockchainService.getTrekTokenBalance(),\n                _lib_blockchain__WEBPACK_IMPORTED_MODULE_3__.blockchainService.getTrailNFTs()\n            ]);\n            setAdaBalance(balance);\n            setTrekBalance(trekTokens);\n            setNfts(trailNFTs);\n        } catch (error) {\n            console.error('Error loading dashboard data:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const formatAddress = (addr)=>{\n        return \"\".concat(addr.slice(0, 8), \"...\").concat(addr.slice(-8));\n    };\n    const formatAda = (lovelace)=>{\n        try {\n            const ada = parseInt(lovelace) / 1000000;\n            return \"\".concat(ada.toFixed(2), \" ADA\");\n        } catch (e) {\n            return '0.00 ADA';\n        }\n    };\n    if (!connected) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Coins_ExternalLink_MapPin_Trophy_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                        children: \"Connect Your Wallet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: \"Please connect your Cardano wallet to view your dashboard.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.location.href = '/',\n                        className: \"bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors\",\n                        children: \"Go to Home\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 67,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-green-50 to-blue-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>window.location.href = '/',\n                                    className: \"flex items-center space-x-2 hover:opacity-80 transition-opacity\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Coins_ExternalLink_MapPin_Trophy_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-8 w-8 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: \"VinTrek\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"hidden md:flex space-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/\",\n                                        className: \"text-gray-700 hover:text-green-600 transition-colors\",\n                                        children: \"Home\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/trails\",\n                                        className: \"text-gray-700 hover:text-green-600 transition-colors\",\n                                        children: \"Trails\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/dashboard\",\n                                        className: \"text-green-600 font-medium\",\n                                        children: \"Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/rewards\",\n                                        className: \"text-gray-700 hover:text-green-600 transition-colors\",\n                                        children: \"Rewards\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Coins_ExternalLink_MapPin_Trophy_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-4 w-4 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-700\",\n                                        children: address ? formatAddress(address) : 'Connected'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                children: \"Your Adventure Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Track your hiking achievements, NFT collection, and TREK token rewards.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this),\n                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow-md p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"ADA Balance\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 130,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: formatAda(adaBalance)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 131,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-100 p-3 rounded-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Coins_ExternalLink_MapPin_Trophy_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"h-6 w-6 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow-md p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"TREK Tokens\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 143,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: trekBalance.toLocaleString()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-yellow-100 p-3 rounded-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Coins_ExternalLink_MapPin_Trophy_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-6 w-6 text-yellow-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow-md p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Trail NFTs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: nfts.length\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 157,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-green-100 p-3 rounded-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Coins_ExternalLink_MapPin_Trophy_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-6 w-6 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-md p-6 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold text-gray-900 mb-4\",\n                                        children: \"Your Trail NFT Collection\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this),\n                                    nfts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Coins_ExternalLink_MapPin_Trophy_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-16 w-16 text-gray-300 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                children: \"No Trail NFTs Yet\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"Complete your first trail to mint your first NFT certificate!\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>window.location.href = '/trails',\n                                                className: \"bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors\",\n                                                children: \"Explore Trails\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                        children: nfts.map((nft, index)=>{\n                                            var _nft_metadata, _nft_metadata1;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border rounded-lg p-4 hover:shadow-md transition-shadow\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"aspect-square bg-gradient-to-br from-green-100 to-blue-100 rounded-lg mb-4 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Coins_ExternalLink_MapPin_Trophy_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-12 w-12 text-green-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 187,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-gray-900 mb-2\",\n                                                        children: ((_nft_metadata = nft.metadata) === null || _nft_metadata === void 0 ? void 0 : _nft_metadata.name) || \"Trail NFT #\".concat(index + 1)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    ((_nft_metadata1 = nft.metadata) === null || _nft_metadata1 === void 0 ? void 0 : _nft_metadata1.attributes) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1 text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Coins_ExternalLink_MapPin_Trophy_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 197,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: nft.metadata.attributes.location\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 198,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 196,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Coins_ExternalLink_MapPin_Trophy_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 201,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: new Date(nft.metadata.attributes.completion_date).toLocaleDateString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 202,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 200,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 rounded-full text-xs \".concat(nft.metadata.attributes.difficulty === 'Easy' ? 'bg-green-100 text-green-800' : nft.metadata.attributes.difficulty === 'Moderate' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'),\n                                                                    children: nft.metadata.attributes.difficulty\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 205,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>window.open(\"https://cardanoscan.io/token/\".concat(nft.unit), '_blank'),\n                                                        className: \"mt-3 w-full flex items-center justify-center space-x-2 text-blue-600 hover:text-blue-700 transition-colors\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Coins_ExternalLink_MapPin_Trophy_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 220,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"View on Explorer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 221,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, nft.unit, true, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-md p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold text-gray-900 mb-4\",\n                                        children: \"Recent Activity\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Coins_ExternalLink_MapPin_Trophy_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-12 w-12 text-gray-300 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Activity tracking coming soon!\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500 mt-2\",\n                                                children: \"We're working on bringing you detailed activity logs and achievement tracking.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"S3WExWI6/CJGQCS4DQqrJ6fYVYA=\", false, function() {\n    return [\n        _components_providers_WalletProvider__WEBPACK_IMPORTED_MODULE_2__.useWallet\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/blockchain.ts":
/*!*******************************!*\
  !*** ./src/lib/blockchain.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BLOCKCHAIN_CONFIG: () => (/* binding */ BLOCKCHAIN_CONFIG),\n/* harmony export */   BlockchainService: () => (/* binding */ BlockchainService),\n/* harmony export */   TREK_TOKEN: () => (/* binding */ TREK_TOKEN),\n/* harmony export */   blockchainService: () => (/* binding */ blockchainService),\n/* harmony export */   formatAdaToLovelace: () => (/* binding */ formatAdaToLovelace),\n/* harmony export */   formatLovelaceToAda: () => (/* binding */ formatLovelaceToAda),\n/* harmony export */   generateAssetName: () => (/* binding */ generateAssetName)\n/* harmony export */ });\n// Dynamic imports for client-side only\nlet BrowserWallet = null;\nlet Transaction = null;\nlet AssetMetadata = null;\nif (true) {\n    __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_meshsdk_core_dist_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @meshsdk/core */ \"(app-pages-browser)/./node_modules/@meshsdk/core/dist/index.js\")).then((module)=>{\n        BrowserWallet = module.BrowserWallet;\n        Transaction = module.Transaction;\n        AssetMetadata = module.AssetMetadata;\n    });\n}\n// Blockchain configuration\nconst BLOCKCHAIN_CONFIG = {\n    network: \"testnet\" || 0,\n    blockfrostApiKey: \"testnetYourProjectIdHere\" || 0,\n    blockfrostUrl: \"https://cardano-testnet.blockfrost.io/api/v0\" || 0,\n    nftPolicyId: \"placeholder_nft_policy_id\" || 0,\n    tokenPolicyId: \"placeholder_token_policy_id\" || 0,\n    scriptAddress: \"addr_test1placeholder_script_address\" || 0\n};\n// TREK Token configuration\nconst TREK_TOKEN = {\n    symbol: 'TREK',\n    decimals: 6,\n    policyId: BLOCKCHAIN_CONFIG.tokenPolicyId,\n    assetName: '54524b'\n};\n// Blockchain service class\nclass BlockchainService {\n    setWallet(wallet) {\n        this.wallet = wallet;\n    }\n    // Get wallet balance in ADA\n    async getWalletBalance() {\n        if (!this.wallet) throw new Error('Wallet not connected');\n        try {\n            const balance = await this.wallet.getBalance();\n            return balance;\n        } catch (error) {\n            console.error('Error fetching wallet balance:', error);\n            throw error;\n        }\n    }\n    // Get wallet assets (NFTs and tokens)\n    async getWalletAssets() {\n        if (!this.wallet) throw new Error('Wallet not connected');\n        try {\n            const assets = await this.wallet.getAssets();\n            return assets;\n        } catch (error) {\n            console.error('Error fetching wallet assets:', error);\n            throw error;\n        }\n    }\n    // Get TREK token balance\n    async getTrekTokenBalance() {\n        try {\n            const assets = await this.getWalletAssets();\n            const trekAsset = assets.find((asset)=>asset.unit.startsWith(TREK_TOKEN.policyId));\n            if (trekAsset) {\n                return parseInt(trekAsset.quantity) / Math.pow(10, TREK_TOKEN.decimals);\n            }\n            return 0;\n        } catch (error) {\n            console.error('Error fetching TREK token balance:', error);\n            return 0;\n        }\n    }\n    // Get trail NFTs owned by wallet\n    async getTrailNFTs() {\n        try {\n            const assets = await this.getWalletAssets();\n            const nfts = assets.filter((asset)=>asset.unit.startsWith(BLOCKCHAIN_CONFIG.nftPolicyId) && asset.quantity === '1');\n            // Fetch metadata for each NFT\n            const nftsWithMetadata = await Promise.all(nfts.map(async (nft)=>{\n                try {\n                    const metadata = await this.fetchNFTMetadata(nft.unit);\n                    return {\n                        ...nft,\n                        metadata\n                    };\n                } catch (error) {\n                    console.error(\"Error fetching metadata for NFT \".concat(nft.unit, \":\"), error);\n                    return {\n                        ...nft,\n                        metadata: null\n                    };\n                }\n            }));\n            return nftsWithMetadata;\n        } catch (error) {\n            console.error('Error fetching trail NFTs:', error);\n            return [];\n        }\n    }\n    // Fetch NFT metadata from blockchain\n    async fetchNFTMetadata(assetId) {\n        try {\n            const response = await fetch(\"\".concat(BLOCKCHAIN_CONFIG.blockfrostUrl, \"/assets/\").concat(assetId), {\n                headers: {\n                    'project_id': BLOCKCHAIN_CONFIG.blockfrostApiKey\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const assetData = await response.json();\n            if (assetData.onchain_metadata) {\n                return assetData.onchain_metadata;\n            }\n            return null;\n        } catch (error) {\n            console.error('Error fetching NFT metadata:', error);\n            return null;\n        }\n    }\n    // Create booking transaction\n    async createBookingTransaction(trailId, amount, date) {\n        if (!this.wallet) throw new Error('Wallet not connected');\n        try {\n            const walletAddress = await this.wallet.getChangeAddress();\n            // Create booking metadata\n            const bookingMetadata = {\n                trail_id: trailId,\n                booking_date: date,\n                amount: amount,\n                timestamp: new Date().toISOString(),\n                hiker_address: walletAddress\n            };\n            // Build transaction (simplified - in production, this would interact with smart contracts)\n            const tx = new Transaction({\n                initiator: this.wallet\n            });\n            // Add metadata to transaction\n            tx.setMetadata(674, bookingMetadata);\n            // Send payment to script address (or trail operator)\n            tx.sendLovelace(BLOCKCHAIN_CONFIG.scriptAddress, (amount * 1000000).toString() // Convert ADA to Lovelace\n            );\n            const unsignedTx = await tx.build();\n            const signedTx = await this.wallet.signTx(unsignedTx);\n            const txHash = await this.wallet.submitTx(signedTx);\n            return txHash;\n        } catch (error) {\n            console.error('Error creating booking transaction:', error);\n            throw error;\n        }\n    }\n    // Mint trail completion NFT\n    async mintTrailNFT(trailData) {\n        if (!this.wallet) throw new Error('Wallet not connected');\n        try {\n            const walletAddress = await this.wallet.getChangeAddress();\n            const completionDate = new Date().toISOString();\n            // Create NFT metadata\n            const metadata = {\n                name: \"\".concat(trailData.trailName, \" Completion Certificate\"),\n                description: \"Proof of completion for \".concat(trailData.trailName, \" trail in \").concat(trailData.location),\n                image: \"ipfs://QmTrailNFTImage\".concat(Date.now()),\n                attributes: {\n                    trail_name: trailData.trailName,\n                    location: trailData.location,\n                    difficulty: trailData.difficulty,\n                    completion_date: completionDate,\n                    coordinates: trailData.coordinates,\n                    hiker_address: walletAddress\n                }\n            };\n            // Generate unique asset name\n            const assetName = \"VinTrekNFT\".concat(Date.now());\n            // Build minting transaction (simplified)\n            const tx = new Transaction({\n                initiator: this.wallet\n            });\n            // Add minting logic here (requires smart contract integration)\n            // This is a placeholder - actual implementation would use Mesh SDK's minting functions\n            const unsignedTx = await tx.build();\n            const signedTx = await this.wallet.signTx(unsignedTx);\n            const txHash = await this.wallet.submitTx(signedTx);\n            return txHash;\n        } catch (error) {\n            console.error('Error minting trail NFT:', error);\n            throw error;\n        }\n    }\n    // Mint TREK tokens as rewards\n    async mintTrekTokens(amount, reason) {\n        if (!this.wallet) throw new Error('Wallet not connected');\n        try {\n            const walletAddress = await this.wallet.getChangeAddress();\n            // Create reward metadata\n            const rewardMetadata = {\n                recipient: walletAddress,\n                amount: amount,\n                reason: reason,\n                timestamp: new Date().toISOString()\n            };\n            // Build token minting transaction (simplified)\n            const tx = new Transaction({\n                initiator: this.wallet\n            });\n            // Add metadata\n            tx.setMetadata(674, rewardMetadata);\n            // Add token minting logic here (requires smart contract integration)\n            const unsignedTx = await tx.build();\n            const signedTx = await this.wallet.signTx(unsignedTx);\n            const txHash = await this.wallet.submitTx(signedTx);\n            return txHash;\n        } catch (error) {\n            console.error('Error minting TREK tokens:', error);\n            throw error;\n        }\n    }\n    // Verify transaction on blockchain\n    async verifyTransaction(txHash) {\n        try {\n            const response = await fetch(\"\".concat(BLOCKCHAIN_CONFIG.blockfrostUrl, \"/txs/\").concat(txHash), {\n                headers: {\n                    'project_id': BLOCKCHAIN_CONFIG.blockfrostApiKey\n                }\n            });\n            if (response.ok) {\n                const txData = await response.json();\n                return txData.block !== null // Transaction is confirmed if it's in a block\n                ;\n            }\n            return false;\n        } catch (error) {\n            console.error('Error verifying transaction:', error);\n            return false;\n        }\n    }\n    constructor(wallet = null){\n        this.wallet = null;\n        this.wallet = wallet;\n    }\n}\n// Utility functions\nconst formatLovelaceToAda = (lovelace)=>{\n    return parseInt(lovelace) / 1000000;\n};\nconst formatAdaToLovelace = (ada)=>{\n    return (ada * 1000000).toString();\n};\nconst generateAssetName = (prefix)=>{\n    const timestamp = Date.now();\n    const random = Math.random().toString(36).substr(2, 9);\n    return \"\".concat(prefix).concat(timestamp).concat(random);\n};\n// Export singleton instance\nconst blockchainService = new BlockchainService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/blockchain.ts\n"));

/***/ })

});