import { HexBlob } from '@cardano-sdk/util';
import type * as Cardano from '../../Cardano/index.js';
export declare class Value {
    #private;
    constructor(coin: Cardano.Lovelace, multiasset?: Cardano.TokenMap);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): Value;
    toCore(): Cardano.Value;
    static fromCore(coreValue: Cardano.Value): Value;
    coin(): Cardano.Lovelace;
    setCoin(coin: Cardano.Lovelace): void;
    multiasset(): Cardano.TokenMap | undefined;
    setMultiasset(multiasset: Cardano.TokenMap): void;
}
//# sourceMappingURL=Value.d.ts.map