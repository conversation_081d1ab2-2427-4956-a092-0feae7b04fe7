"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/serialize-error";
exports.ids = ["vendor-chunks/serialize-error"];
exports.modules = {

/***/ "(ssr)/./node_modules/serialize-error/index.js":
/*!***********************************************!*\
  !*** ./node_modules/serialize-error/index.js ***!
  \***********************************************/
/***/ ((module) => {

eval("\n\nclass NonError extends Error {\n\tconstructor(message) {\n\t\tsuper(NonError._prepareSuperMessage(message));\n\t\tObject.defineProperty(this, 'name', {\n\t\t\tvalue: 'NonError',\n\t\t\tconfigurable: true,\n\t\t\twritable: true\n\t\t});\n\n\t\tif (Error.captureStackTrace) {\n\t\t\tError.captureStackTrace(this, NonError);\n\t\t}\n\t}\n\n\tstatic _prepareSuperMessage(message) {\n\t\ttry {\n\t\t\treturn JSON.stringify(message);\n\t\t} catch {\n\t\t\treturn String(message);\n\t\t}\n\t}\n}\n\nconst commonProperties = [\n\t{property: 'name', enumerable: false},\n\t{property: 'message', enumerable: false},\n\t{property: 'stack', enumerable: false},\n\t{property: 'code', enumerable: true}\n];\n\nconst isCalled = Symbol('.toJSON called');\n\nconst toJSON = from => {\n\tfrom[isCalled] = true;\n\tconst json = from.toJSON();\n\tdelete from[isCalled];\n\treturn json;\n};\n\nconst destroyCircular = ({\n\tfrom,\n\tseen,\n\tto_,\n\tforceEnumerable,\n\tmaxDepth,\n\tdepth\n}) => {\n\tconst to = to_ || (Array.isArray(from) ? [] : {});\n\n\tseen.push(from);\n\n\tif (depth >= maxDepth) {\n\t\treturn to;\n\t}\n\n\tif (typeof from.toJSON === 'function' && from[isCalled] !== true) {\n\t\treturn toJSON(from);\n\t}\n\n\tfor (const [key, value] of Object.entries(from)) {\n\t\tif (typeof Buffer === 'function' && Buffer.isBuffer(value)) {\n\t\t\tto[key] = '[object Buffer]';\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (typeof value === 'function') {\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (!value || typeof value !== 'object') {\n\t\t\tto[key] = value;\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (!seen.includes(from[key])) {\n\t\t\tdepth++;\n\n\t\t\tto[key] = destroyCircular({\n\t\t\t\tfrom: from[key],\n\t\t\t\tseen: seen.slice(),\n\t\t\t\tforceEnumerable,\n\t\t\t\tmaxDepth,\n\t\t\t\tdepth\n\t\t\t});\n\t\t\tcontinue;\n\t\t}\n\n\t\tto[key] = '[Circular]';\n\t}\n\n\tfor (const {property, enumerable} of commonProperties) {\n\t\tif (typeof from[property] === 'string') {\n\t\t\tObject.defineProperty(to, property, {\n\t\t\t\tvalue: from[property],\n\t\t\t\tenumerable: forceEnumerable ? true : enumerable,\n\t\t\t\tconfigurable: true,\n\t\t\t\twritable: true\n\t\t\t});\n\t\t}\n\t}\n\n\treturn to;\n};\n\nconst serializeError = (value, options = {}) => {\n\tconst {maxDepth = Number.POSITIVE_INFINITY} = options;\n\n\tif (typeof value === 'object' && value !== null) {\n\t\treturn destroyCircular({\n\t\t\tfrom: value,\n\t\t\tseen: [],\n\t\t\tforceEnumerable: true,\n\t\t\tmaxDepth,\n\t\t\tdepth: 0\n\t\t});\n\t}\n\n\t// People sometimes throw things besides Error objects…\n\tif (typeof value === 'function') {\n\t\t// `JSON.stringify()` discards functions. We do too, unless a function is thrown directly.\n\t\treturn `[Function: ${(value.name || 'anonymous')}]`;\n\t}\n\n\treturn value;\n};\n\nconst deserializeError = (value, options = {}) => {\n\tconst {maxDepth = Number.POSITIVE_INFINITY} = options;\n\n\tif (value instanceof Error) {\n\t\treturn value;\n\t}\n\n\tif (typeof value === 'object' && value !== null && !Array.isArray(value)) {\n\t\tconst newError = new Error(); // eslint-disable-line unicorn/error-message\n\t\tdestroyCircular({\n\t\t\tfrom: value,\n\t\t\tseen: [],\n\t\t\tto_: newError,\n\t\t\tmaxDepth,\n\t\t\tdepth: 0\n\t\t});\n\t\treturn newError;\n\t}\n\n\treturn new NonError(value);\n};\n\nmodule.exports = {\n\tserializeError,\n\tdeserializeError\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/serialize-error/index.js\n");

/***/ })

};
;