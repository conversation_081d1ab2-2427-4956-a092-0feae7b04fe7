import { CborAdditionalInfo } from './CborAdditionalInfo';
import { CborReaderState } from './CborReaderState';
import { CborSimpleValue } from './CborSimpleValue';
import { CborTag } from './CborTag';
import { HexBlob } from '@cardano-sdk/util';
export declare class CborReader {
    #private;
    constructor(data: HexBlob);
    peekState(): CborReaderState;
    getBytesRemaining(): number;
    skipValue(): void;
    readEncodedValue(): Uint8Array;
    readStartArray(): number | null;
    readEndArray(): void;
    readInt(): bigint;
    readUInt(): bigint;
    readDouble(): number;
    readSimpleValue(): CborSimpleValue;
    readCborNegativeIntegerRepresentation(): bigint;
    readStartMap(): number | null;
    readEndMap(): void;
    readBoolean(): boolean;
    readNull(): void;
    readStartIndefiniteLengthByteString(): void;
    readEndIndefiniteLengthByteString(): void;
    readByteString(): Uint8Array;
    readDefiniteLengthByteString(): Uint8Array;
    readStartIndefiniteLengthTextString(): void;
    readEndIndefiniteLengthTextString(): void;
    readTextString(): string;
    readDefiniteLengthTextString(): string;
    readTag(): CborTag;
    peekTag(): CborTag;
    static ensureReadCapacityInArray(data: Uint8Array, bytesToRead: number): void;
    static mapSimpleValueDataToReaderState(value: CborAdditionalInfo): CborReaderState;
}
//# sourceMappingURL=CborReader.d.ts.map