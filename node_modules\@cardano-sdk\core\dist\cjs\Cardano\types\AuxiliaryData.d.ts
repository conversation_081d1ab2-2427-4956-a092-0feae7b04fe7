import { Hash32ByteBase16 } from '@cardano-sdk/crypto';
import { <PERSON>ript } from './Script';
export declare type MetadatumMap = Map<Metadatum, Metadatum>;
export declare type Metadatum = bigint | MetadatumMap | string | Uint8Array | Metadatum[];
export declare type TxMetadata = Map<bigint, Metadatum>;
export interface AuxiliaryData {
    blob?: TxMetadata;
    scripts?: Script[];
}
export declare const computeAuxiliaryDataHash: (data: AuxiliaryData | undefined) => Hash32ByteBase16 | undefined;
//# sourceMappingURL=AuxiliaryData.d.ts.map