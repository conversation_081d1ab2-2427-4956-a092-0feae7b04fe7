/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/page"],{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/createLucideIcon.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ createLucideIcon$1),\n/* harmony export */   toKebabCase: () => (/* binding */ toKebabCase)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _defaultAttributes_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./defaultAttributes.mjs */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/defaultAttributes.mjs\");\n/**\n * lucide-react v0.0.1 - ISC\n */ \n\nconst toKebabCase = (string)=>string.replace(/([a-z0-9])([A-Z])/g, \"$1-$2\").toLowerCase();\nconst createLucideIcon = (iconName, iconNode)=>{\n    const Component = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((param, ref)=>{\n        let { color = \"currentColor\", size = 24, strokeWidth = 2, absoluteStrokeWidth, children, ...rest } = param;\n        return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(\"svg\", {\n            ref,\n            ..._defaultAttributes_mjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n            width: size,\n            height: size,\n            stroke: color,\n            strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n            className: \"lucide lucide-\".concat(toKebabCase(iconName)),\n            ...rest\n        }, [\n            ...iconNode.map((param)=>{\n                let [tag, attrs] = param;\n                return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(tag, attrs);\n            }),\n            ...(Array.isArray(children) ? children : [\n                children\n            ]) || []\n        ]);\n    });\n    Component.displayName = \"\".concat(iconName);\n    return Component;\n};\nvar createLucideIcon$1 = createLucideIcon;\n //# sourceMappingURL=createLucideIcon.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vY3JlYXRlTHVjaWRlSWNvbi5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFzQmEsb0JBQWMsQ0FBQyxNQUFtQixVQUFPLFFBQVEsb0JBQXNCLFNBQU8sRUFBRSxXQUFZO0FBRXpHLE1BQU0saUJBQW1CLEdBQUMsVUFBa0IsUUFBbUM7SUFDN0UsTUFBTSxDQUFZLDJFQUNoQixRQUFpRyxDQUMvRjtZQURELENBQUUsTUFBUSxtQkFBZ0IsQ0FBTyxVQUFJLENBQWMsZUFBRyxvQkFBcUIsZUFBYSxJQUFLOzZCQUM1RixzREFDRSxNQUNBO1lBQ0U7WUFDQSxHQUFHO1lBQ0gsS0FBTztZQUNQLE1BQVE7WUFDUixNQUFRO1lBQ1IsWUFBYSxxQkFBc0IsUUFBTyxFQUFXLFlBQUksSUFBSyxVQUFPLENBQUksSUFBSTtZQUM3RSxVQUFXLENBQWlCLGlCQUFvQixPQUFwQixZQUFZLFFBQVE7WUFDaEQsR0FBRztRQUNMLEdBQ0E7ZUFDSyxDQUFTLFlBQUk7b0JBQUMsQ0FBQyxDQUFLLEtBQUssS0FBTTtxQ0FBQSxxREFBYyxHQUFLLE9BQUssQ0FBQzs7ZUFDM0QsQ0FDRyxNQUFNLE9BQVEsU0FBUSxJQUFJLFFBQVc7Z0JBQUMsQ0FBUTthQUFBLEtBQU0sQ0FBQztTQUUxRDs7SUFJTixVQUFVLGNBQWMsQ0FBRztJQUVwQjtBQUNUO0FBRUEseUJBQWUiLCJzb3VyY2VzIjpbIkQ6XFxoYWNrb3Rob25cXHNyY1xcY3JlYXRlTHVjaWRlSWNvbi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBmb3J3YXJkUmVmLCBjcmVhdGVFbGVtZW50LCBSZWFjdFNWRywgU1ZHUHJvcHMsIEZvcndhcmRSZWZFeG90aWNDb21wb25lbnQsIFJlZkF0dHJpYnV0ZXMgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgZGVmYXVsdEF0dHJpYnV0ZXMgZnJvbSAnLi9kZWZhdWx0QXR0cmlidXRlcyc7XG5cbmV4cG9ydCB0eXBlIEljb25Ob2RlID0gW2VsZW1lbnROYW1lOiBrZXlvZiBSZWFjdFNWRywgYXR0cnM6IFJlY29yZDxzdHJpbmcsIHN0cmluZz5dW11cblxuZXhwb3J0IHR5cGUgU1ZHQXR0cmlidXRlcyA9IFBhcnRpYWw8U1ZHUHJvcHM8U1ZHU1ZHRWxlbWVudD4+XG50eXBlIENvbXBvbmVudEF0dHJpYnV0ZXMgPSBSZWZBdHRyaWJ1dGVzPFNWR1NWR0VsZW1lbnQ+ICYgU1ZHQXR0cmlidXRlc1xuXG5leHBvcnQgaW50ZXJmYWNlIEx1Y2lkZVByb3BzIGV4dGVuZHMgQ29tcG9uZW50QXR0cmlidXRlcyB7XG4gIHNpemU/OiBzdHJpbmcgfCBudW1iZXJcbiAgYWJzb2x1dGVTdHJva2VXaWR0aD86IGJvb2xlYW5cbn1cblxuZXhwb3J0IHR5cGUgTHVjaWRlSWNvbiA9IEZvcndhcmRSZWZFeG90aWNDb21wb25lbnQ8THVjaWRlUHJvcHM+O1xuLyoqXG4gKiBDb252ZXJ0cyBzdHJpbmcgdG8gS2ViYWJDYXNlXG4gKiBDb3BpZWQgZnJvbSBzY3JpcHRzL2hlbHBlci4gSWYgYW55b25lIGtub3dzIGhvdyB0byBwcm9wZXJseSBpbXBvcnQgaXQgaGVyZVxuICogdGhlbiBwbGVhc2UgZml4IGl0LlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSBzdHJpbmdcbiAqIEByZXR1cm5zIHtzdHJpbmd9IEEga2ViYWJpemVkIHN0cmluZ1xuICovXG5leHBvcnQgY29uc3QgdG9LZWJhYkNhc2UgPSAoc3RyaW5nOiBzdHJpbmcpID0+IHN0cmluZy5yZXBsYWNlKC8oW2EtejAtOV0pKFtBLVpdKS9nLCAnJDEtJDInKS50b0xvd2VyQ2FzZSgpO1xuXG5jb25zdCBjcmVhdGVMdWNpZGVJY29uID0gKGljb25OYW1lOiBzdHJpbmcsIGljb25Ob2RlOiBJY29uTm9kZSk6IEx1Y2lkZUljb24gPT4ge1xuICBjb25zdCBDb21wb25lbnQgPSBmb3J3YXJkUmVmPFNWR1NWR0VsZW1lbnQsIEx1Y2lkZVByb3BzPihcbiAgICAoeyBjb2xvciA9ICdjdXJyZW50Q29sb3InLCBzaXplID0gMjQsIHN0cm9rZVdpZHRoID0gMiwgYWJzb2x1dGVTdHJva2VXaWR0aCwgY2hpbGRyZW4sIC4uLnJlc3QgfSwgcmVmKSA9PlxuICAgICAgY3JlYXRlRWxlbWVudChcbiAgICAgICAgJ3N2ZycsXG4gICAgICAgIHtcbiAgICAgICAgICByZWYsXG4gICAgICAgICAgLi4uZGVmYXVsdEF0dHJpYnV0ZXMsXG4gICAgICAgICAgd2lkdGg6IHNpemUsXG4gICAgICAgICAgaGVpZ2h0OiBzaXplLFxuICAgICAgICAgIHN0cm9rZTogY29sb3IsXG4gICAgICAgICAgc3Ryb2tlV2lkdGg6IGFic29sdXRlU3Ryb2tlV2lkdGggPyBOdW1iZXIoc3Ryb2tlV2lkdGgpICogMjQgLyBOdW1iZXIoc2l6ZSkgOiBzdHJva2VXaWR0aCxcbiAgICAgICAgICBjbGFzc05hbWU6IGBsdWNpZGUgbHVjaWRlLSR7dG9LZWJhYkNhc2UoaWNvbk5hbWUpfWAsXG4gICAgICAgICAgLi4ucmVzdCxcbiAgICAgICAgfSxcbiAgICAgICAgW1xuICAgICAgICAgIC4uLmljb25Ob2RlLm1hcCgoW3RhZywgYXR0cnNdKSA9PiBjcmVhdGVFbGVtZW50KHRhZywgYXR0cnMpKSxcbiAgICAgICAgICAuLi4oXG4gICAgICAgICAgICAoQXJyYXkuaXNBcnJheShjaGlsZHJlbikgPyBjaGlsZHJlbiA6IFtjaGlsZHJlbl0pIHx8IFtdXG4gICAgICAgICAgKVxuICAgICAgICBdLFxuICAgICAgKSxcbiAgKTtcblxuICBDb21wb25lbnQuZGlzcGxheU5hbWUgPSBgJHtpY29uTmFtZX1gO1xuXG4gIHJldHVybiBDb21wb25lbnQ7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBjcmVhdGVMdWNpZGVJY29uXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/defaultAttributes.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/defaultAttributes.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ defaultAttributes)\n/* harmony export */ });\n/**\n * lucide-react v0.0.1 - ISC\n */ var defaultAttributes = {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: 24,\n    height: 24,\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeWidth: 2,\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n};\n //# sourceMappingURL=defaultAttributes.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vZGVmYXVsdEF0dHJpYnV0ZXMubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztJQUFBLENBQWU7SUFDYixLQUFPO0lBQ1AsS0FBTztJQUNQLE1BQVE7SUFDUixPQUFTO0lBQ1QsSUFBTTtJQUNOLE1BQVE7SUFDUixXQUFhO0lBQ2IsYUFBZTtJQUNmLGNBQWdCO0FBQ2xCIiwic291cmNlcyI6WyJEOlxcaGFja290aG9uXFxzcmNcXGRlZmF1bHRBdHRyaWJ1dGVzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcbiAgeG1sbnM6ICdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZycsXG4gIHdpZHRoOiAyNCxcbiAgaGVpZ2h0OiAyNCxcbiAgdmlld0JveDogJzAgMCAyNCAyNCcsXG4gIGZpbGw6ICdub25lJyxcbiAgc3Ryb2tlOiAnY3VycmVudENvbG9yJyxcbiAgc3Ryb2tlV2lkdGg6IDIsXG4gIHN0cm9rZUxpbmVjYXA6ICdyb3VuZCcsXG4gIHN0cm9rZUxpbmVqb2luOiAncm91bmQnLFxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/defaultAttributes.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/alert-circle.mjs ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AlertCircle)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs\");\n/**\n * lucide-react v0.0.1 - ISC\n */ \nconst AlertCircle = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"AlertCircle\", [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"12\",\n            x2: \"12\",\n            y1: \"8\",\n            y2: \"12\",\n            key: \"1pkeuh\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"12\",\n            x2: \"12.01\",\n            y1: \"16\",\n            y2: \"16\",\n            key: \"4dfq90\"\n        }\n    ]\n]);\n //# sourceMappingURL=alert-circle.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/calendar.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Calendar)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs\");\n/**\n * lucide-react v0.0.1 - ISC\n */ \nconst Calendar = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Calendar\", [\n    [\n        \"rect\",\n        {\n            width: \"18\",\n            height: \"18\",\n            x: \"3\",\n            y: \"4\",\n            rx: \"2\",\n            ry: \"2\",\n            key: \"eu3xkr\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"16\",\n            x2: \"16\",\n            y1: \"2\",\n            y2: \"6\",\n            key: \"m3sa8f\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"8\",\n            x2: \"8\",\n            y1: \"2\",\n            y2: \"6\",\n            key: \"18kwsl\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"3\",\n            x2: \"21\",\n            y1: \"10\",\n            y2: \"10\",\n            key: \"xt86sb\"\n        }\n    ]\n]);\n //# sourceMappingURL=calendar.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/chevron-down.mjs ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChevronDown)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs\");\n/**\n * lucide-react v0.0.1 - ISC\n */ \nconst ChevronDown = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"ChevronDown\", [\n    [\n        \"path\",\n        {\n            d: \"m6 9 6 6 6-6\",\n            key: \"qrunsl\"\n        }\n    ]\n]);\n //# sourceMappingURL=chevron-down.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2hldnJvbi1kb3duLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQWFNLG9CQUFjLGlFQUFnQixDQUFDLGFBQWU7SUFDbEQ7UUFBQyxNQUFRO1FBQUE7WUFBRSxHQUFHLENBQWdCO1lBQUEsS0FBSztRQUFBLENBQVU7S0FBQTtDQUM5QyIsInNvdXJjZXMiOlsiRDpcXHNyY1xcaWNvbnNcXGNoZXZyb24tZG93bi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIENoZXZyb25Eb3duXG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThjR0YwYUNCa1BTSnROaUE1SURZZ05pQTJMVFlpSUM4K0Nqd3ZjM1puUGdvPSkgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvY2hldnJvbi1kb3duXG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgQ2hldnJvbkRvd24gPSBjcmVhdGVMdWNpZGVJY29uKCdDaGV2cm9uRG93bicsIFtcbiAgWydwYXRoJywgeyBkOiAnbTYgOSA2IDYgNi02Jywga2V5OiAncXJ1bnNsJyB9XSxcbl0pO1xuXG5leHBvcnQgZGVmYXVsdCBDaGV2cm9uRG93bjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.mjs":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/clock.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Clock)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs\");\n/**\n * lucide-react v0.0.1 - ISC\n */ \nconst Clock = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Clock\", [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"polyline\",\n        {\n            points: \"12 6 12 12 16 14\",\n            key: \"68esgv\"\n        }\n    ]\n]);\n //# sourceMappingURL=clock.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2xvY2subWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBYU0sY0FBUSxpRUFBZ0IsQ0FBQyxPQUFTO0lBQ3RDO1FBQUMsUUFBVTtRQUFBO1lBQUUsRUFBSTtZQUFNLENBQUksUUFBTTtZQUFBLENBQUc7WUFBTSxHQUFLO1FBQUEsQ0FBVTtLQUFBO0lBQ3pEO1FBQUMsVUFBWTtRQUFBO1lBQUUsUUFBUSxDQUFvQjtZQUFBLEtBQUs7UUFBQSxDQUFVO0tBQUE7Q0FDM0QiLCJzb3VyY2VzIjpbIkQ6XFxzcmNcXGljb25zXFxjbG9jay50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIENsb2NrXG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThZMmx5WTJ4bElHTjRQU0l4TWlJZ1kzazlJakV5SWlCeVBTSXhNQ0lnTHo0S0lDQThjRzlzZVd4cGJtVWdjRzlwYm5SelBTSXhNaUEySURFeUlERXlJREUySURFMElpQXZQZ284TDNOMlp6NEspIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL2Nsb2NrXG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgQ2xvY2sgPSBjcmVhdGVMdWNpZGVJY29uKCdDbG9jaycsIFtcbiAgWydjaXJjbGUnLCB7IGN4OiAnMTInLCBjeTogJzEyJywgcjogJzEwJywga2V5OiAnMW1nbGF5JyB9XSxcbiAgWydwb2x5bGluZScsIHsgcG9pbnRzOiAnMTIgNiAxMiAxMiAxNiAxNCcsIGtleTogJzY4ZXNndicgfV0sXG5dKTtcblxuZXhwb3J0IGRlZmF1bHQgQ2xvY2s7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coins.mjs":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/coins.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Coins)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs\");\n/**\n * lucide-react v0.0.1 - ISC\n */ \nconst Coins = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Coins\", [\n    [\n        \"circle\",\n        {\n            cx: \"8\",\n            cy: \"8\",\n            r: \"6\",\n            key: \"3yglwk\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M18.09 10.37A6 6 0 1 1 10.34 18\",\n            key: \"t5s6rm\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M7 6h1v4\",\n            key: \"1obek4\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m16.71 13.88.7.71-2.82 2.82\",\n            key: \"1rbuyh\"\n        }\n    ]\n]);\n //# sourceMappingURL=coins.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coins.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/copy.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Copy)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs\");\n/**\n * lucide-react v0.0.1 - ISC\n */ \nconst Copy = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Copy\", [\n    [\n        \"rect\",\n        {\n            width: \"14\",\n            height: \"14\",\n            x: \"8\",\n            y: \"8\",\n            rx: \"2\",\n            ry: \"2\",\n            key: \"17jyea\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2\",\n            key: \"zix9uf\"\n        }\n    ]\n]);\n //# sourceMappingURL=copy.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.mjs":
/*!********************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/external-link.mjs ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ExternalLink)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs\");\n/**\n * lucide-react v0.0.1 - ISC\n */ \nconst ExternalLink = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"ExternalLink\", [\n    [\n        \"path\",\n        {\n            d: \"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\",\n            key: \"a6xqqp\"\n        }\n    ],\n    [\n        \"polyline\",\n        {\n            points: \"15 3 21 3 21 9\",\n            key: \"mznyad\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"10\",\n            x2: \"21\",\n            y1: \"14\",\n            y2: \"3\",\n            key: \"18c3s4\"\n        }\n    ]\n]);\n //# sourceMappingURL=external-link.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/log-out.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LogOut)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs\");\n/**\n * lucide-react v0.0.1 - ISC\n */ \nconst LogOut = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"LogOut\", [\n    [\n        \"path\",\n        {\n            d: \"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4\",\n            key: \"1uf3rs\"\n        }\n    ],\n    [\n        \"polyline\",\n        {\n            points: \"16 17 21 12 16 7\",\n            key: \"1gabdz\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"21\",\n            x2: \"9\",\n            y1: \"12\",\n            y2: \"12\",\n            key: \"1uyos4\"\n        }\n    ]\n]);\n //# sourceMappingURL=log-out.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/map-pin.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MapPin)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs\");\n/**\n * lucide-react v0.0.1 - ISC\n */ \nconst MapPin = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"MapPin\", [\n    [\n        \"path\",\n        {\n            d: \"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z\",\n            key: \"2oe9fu\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"10\",\n            r: \"3\",\n            key: \"ilqhr7\"\n        }\n    ]\n]);\n //# sourceMappingURL=map-pin.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvbWFwLXBpbi5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFhTSxlQUFTLGlFQUFnQixDQUFDLFFBQVU7SUFDeEM7UUFDRTtRQUNBO1lBQUUsRUFBRyxpREFBa0Q7WUFBQSxLQUFLLFFBQVM7UUFBQTtLQUN2RTtJQUNBO1FBQUMsUUFBVTtRQUFBO1lBQUUsRUFBSTtZQUFNLENBQUksUUFBTTtZQUFBLENBQUc7WUFBSyxHQUFLO1FBQUEsQ0FBVTtLQUFBO0NBQ3pEIiwic291cmNlcyI6WyJEOlxcc3JjXFxpY29uc1xcbWFwLXBpbi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIE1hcFBpblxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0dGMGFDQmtQU0pOTWpBZ01UQmpNQ0EyTFRnZ01USXRPQ0F4TW5NdE9DMDJMVGd0TVRKaE9DQTRJREFnTUNBeElERTJJREJhSWlBdlBnb2dJRHhqYVhKamJHVWdZM2c5SWpFeUlpQmplVDBpTVRBaUlISTlJak1pSUM4K0Nqd3ZjM1puUGdvPSkgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvbWFwLXBpblxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IE1hcFBpbiA9IGNyZWF0ZUx1Y2lkZUljb24oJ01hcFBpbicsIFtcbiAgW1xuICAgICdwYXRoJyxcbiAgICB7IGQ6ICdNMjAgMTBjMCA2LTggMTItOCAxMnMtOC02LTgtMTJhOCA4IDAgMCAxIDE2IDBaJywga2V5OiAnMm9lOWZ1JyB9LFxuICBdLFxuICBbJ2NpcmNsZScsIHsgY3g6ICcxMicsIGN5OiAnMTAnLCByOiAnMycsIGtleTogJ2lscWhyNycgfV0sXG5dKTtcblxuZXhwb3J0IGRlZmF1bHQgTWFwUGluO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mountain.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/mountain.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Mountain)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs\");\n/**\n * lucide-react v0.0.1 - ISC\n */ \nconst Mountain = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Mountain\", [\n    [\n        \"path\",\n        {\n            d: \"m8 3 4 8 5-5 5 15H2L8 3z\",\n            key: \"otkl63\"\n        }\n    ]\n]);\n //# sourceMappingURL=mountain.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvbW91bnRhaW4ubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBYU0saUJBQVcsaUVBQWdCLENBQUMsVUFBWTtJQUM1QztRQUFDLE1BQVE7UUFBQTtZQUFFLEdBQUcsQ0FBNEI7WUFBQSxLQUFLO1FBQUEsQ0FBVTtLQUFBO0NBQzFEIiwic291cmNlcyI6WyJEOlxcc3JjXFxpY29uc1xcbW91bnRhaW4udHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBNb3VudGFpblxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0dGMGFDQmtQU0p0T0NBeklEUWdPQ0ExTFRVZ05TQXhOVWd5VERnZ00zb2lJQzgrQ2p3dmMzWm5QZ289KSAtIGh0dHBzOi8vbHVjaWRlLmRldi9pY29ucy9tb3VudGFpblxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IE1vdW50YWluID0gY3JlYXRlTHVjaWRlSWNvbignTW91bnRhaW4nLCBbXG4gIFsncGF0aCcsIHsgZDogJ204IDMgNCA4IDUtNSA1IDE1SDJMOCAzeicsIGtleTogJ290a2w2MycgfV0sXG5dKTtcblxuZXhwb3J0IGRlZmF1bHQgTW91bnRhaW47XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mountain.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/trending-up.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TrendingUp)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs\");\n/**\n * lucide-react v0.0.1 - ISC\n */ \nconst TrendingUp = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"TrendingUp\", [\n    [\n        \"polyline\",\n        {\n            points: \"22 7 13.5 15.5 8.5 10.5 2 17\",\n            key: \"126l90\"\n        }\n    ],\n    [\n        \"polyline\",\n        {\n            points: \"16 7 22 7 22 13\",\n            key: \"kwv8wd\"\n        }\n    ]\n]);\n //# sourceMappingURL=trending-up.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/trophy.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Trophy)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs\");\n/**\n * lucide-react v0.0.1 - ISC\n */ \nconst Trophy = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Trophy\", [\n    [\n        \"path\",\n        {\n            d: \"M6 9H4.5a2.5 2.5 0 0 1 0-5H6\",\n            key: \"17hqa7\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M18 9h1.5a2.5 2.5 0 0 0 0-5H18\",\n            key: \"lmptdp\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M4 22h16\",\n            key: \"57wxv0\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22\",\n            key: \"1nw9bq\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22\",\n            key: \"1np0yb\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M18 2H6v7a6 6 0 0 0 12 0V2Z\",\n            key: \"u46fv3\"\n        }\n    ]\n]);\n //# sourceMappingURL=trophy.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.mjs":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/users.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Users)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs\");\n/**\n * lucide-react v0.0.1 - ISC\n */ \nconst Users = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Users\", [\n    [\n        \"path\",\n        {\n            d: \"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\",\n            key: \"1yyitq\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"9\",\n            cy: \"7\",\n            r: \"4\",\n            key: \"nufk8\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M22 21v-2a4 4 0 0 0-3-3.87\",\n            key: \"kshegd\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M16 3.13a4 4 0 0 1 0 7.75\",\n            key: \"1da9ce\"\n        }\n    ]\n]);\n //# sourceMappingURL=users.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/wallet.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Wallet)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs\");\n/**\n * lucide-react v0.0.1 - ISC\n */ \nconst Wallet = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Wallet\", [\n    [\n        \"path\",\n        {\n            d: \"M21 12V7H5a2 2 0 0 1 0-4h14v4\",\n            key: \"195gfw\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M3 5v14a2 2 0 0 0 2 2h16v-5\",\n            key: \"195n9w\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M18 12a2 2 0 0 0 0 4h4v-4Z\",\n            key: \"vllfpd\"\n        }\n    ]\n]);\n //# sourceMappingURL=wallet.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.mjs":
/*!********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/x.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ X)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.mjs */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.mjs\");\n/**\n * lucide-react v0.0.1 - ISC\n */ \nconst X = (0,_createLucideIcon_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"X\", [\n    [\n        \"path\",\n        {\n            d: \"M18 6 6 18\",\n            key: \"1bl5f8\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m6 6 12 12\",\n            key: \"d8bk6v\"\n        }\n    ]\n]);\n //# sourceMappingURL=x.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMveC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFhTSxVQUFJLGlFQUFnQixDQUFDLEdBQUs7SUFDOUI7UUFBQyxNQUFRO1FBQUE7WUFBRSxHQUFHLENBQWM7WUFBQSxLQUFLO1FBQUEsQ0FBVTtLQUFBO0lBQzNDO1FBQUMsTUFBUTtRQUFBO1lBQUUsR0FBRyxDQUFjO1lBQUEsS0FBSztRQUFBLENBQVU7S0FBQTtDQUM1QyIsInNvdXJjZXMiOlsiRDpcXHNyY1xcaWNvbnNcXHgudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBYXG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThjR0YwYUNCa1BTSk5NVGdnTmlBMklERTRJaUF2UGdvZ0lEeHdZWFJvSUdROUltMDJJRFlnTVRJZ01USWlJQzgrQ2p3dmMzWm5QZ289KSAtIGh0dHBzOi8vbHVjaWRlLmRldi9pY29ucy94XG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgWCA9IGNyZWF0ZUx1Y2lkZUljb24oJ1gnLCBbXG4gIFsncGF0aCcsIHsgZDogJ00xOCA2IDYgMTgnLCBrZXk6ICcxYmw1ZjgnIH1dLFxuICBbJ3BhdGgnLCB7IGQ6ICdtNiA2IDEyIDEyJywga2V5OiAnZDhiazZ2JyB9XSxcbl0pO1xuXG5leHBvcnQgZGVmYXVsdCBYO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(app-pages-browser)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q2hhY2tvdGhvbiU1QyU1Q0Jsb2NrY2hhaW4lNUMlNUNWaW50cmVrJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSw4SkFBMkYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXGhhY2tvdGhvblxcXFxCbG9ja2NoYWluXFxcXFZpbnRyZWtcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRWE7QUFDYixLQUFxQztBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWCwrQ0FBK0MsNkJBQTZCO0FBQzVFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsZ0JBQWdCLGdEQUFnRDtBQUNoRSxnQkFBZ0IsYUFBYTtBQUM3QjtBQUNBO0FBQ0EsZ0NBQWdDLGtDQUFrQyxPQUFPO0FBQ3pFO0FBQ0EsZ0dBQWdHLFNBQVMsVUFBVSxzRkFBc0YsYUFBYSxVQUFVLFVBQVU7QUFDMU87QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsbUJBQU8sQ0FBQyxzR0FBMEI7QUFDbEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLGdCQUFnQjtBQUNwQixJQUFJLGNBQWM7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHIiwic291cmNlcyI6WyJEOlxcaGFja290aG9uXFxCbG9ja2NoYWluXFxWaW50cmVrXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGNvbXBpbGVkXFxyZWFjdFxcY2pzXFxyZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZSBSZWFjdFxuICogcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzXG4gKlxuICogQ29weXJpZ2h0IChjKSBNZXRhIFBsYXRmb3JtcywgSW5jLiBhbmQgYWZmaWxpYXRlcy5cbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZSBmb3VuZCBpbiB0aGVcbiAqIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqL1xuXG5cInVzZSBzdHJpY3RcIjtcblwicHJvZHVjdGlvblwiICE9PSBwcm9jZXNzLmVudi5OT0RFX0VOViAmJlxuICAoZnVuY3Rpb24gKCkge1xuICAgIGZ1bmN0aW9uIGdldENvbXBvbmVudE5hbWVGcm9tVHlwZSh0eXBlKSB7XG4gICAgICBpZiAobnVsbCA9PSB0eXBlKSByZXR1cm4gbnVsbDtcbiAgICAgIGlmIChcImZ1bmN0aW9uXCIgPT09IHR5cGVvZiB0eXBlKVxuICAgICAgICByZXR1cm4gdHlwZS4kJHR5cGVvZiA9PT0gUkVBQ1RfQ0xJRU5UX1JFRkVSRU5DRVxuICAgICAgICAgID8gbnVsbFxuICAgICAgICAgIDogdHlwZS5kaXNwbGF5TmFtZSB8fCB0eXBlLm5hbWUgfHwgbnVsbDtcbiAgICAgIGlmIChcInN0cmluZ1wiID09PSB0eXBlb2YgdHlwZSkgcmV0dXJuIHR5cGU7XG4gICAgICBzd2l0Y2ggKHR5cGUpIHtcbiAgICAgICAgY2FzZSBSRUFDVF9GUkFHTUVOVF9UWVBFOlxuICAgICAgICAgIHJldHVybiBcIkZyYWdtZW50XCI7XG4gICAgICAgIGNhc2UgUkVBQ1RfUFJPRklMRVJfVFlQRTpcbiAgICAgICAgICByZXR1cm4gXCJQcm9maWxlclwiO1xuICAgICAgICBjYXNlIFJFQUNUX1NUUklDVF9NT0RFX1RZUEU6XG4gICAgICAgICAgcmV0dXJuIFwiU3RyaWN0TW9kZVwiO1xuICAgICAgICBjYXNlIFJFQUNUX1NVU1BFTlNFX1RZUEU6XG4gICAgICAgICAgcmV0dXJuIFwiU3VzcGVuc2VcIjtcbiAgICAgICAgY2FzZSBSRUFDVF9TVVNQRU5TRV9MSVNUX1RZUEU6XG4gICAgICAgICAgcmV0dXJuIFwiU3VzcGVuc2VMaXN0XCI7XG4gICAgICAgIGNhc2UgUkVBQ1RfQUNUSVZJVFlfVFlQRTpcbiAgICAgICAgICByZXR1cm4gXCJBY3Rpdml0eVwiO1xuICAgICAgfVxuICAgICAgaWYgKFwib2JqZWN0XCIgPT09IHR5cGVvZiB0eXBlKVxuICAgICAgICBzd2l0Y2ggKFxuICAgICAgICAgIChcIm51bWJlclwiID09PSB0eXBlb2YgdHlwZS50YWcgJiZcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXG4gICAgICAgICAgICAgIFwiUmVjZWl2ZWQgYW4gdW5leHBlY3RlZCBvYmplY3QgaW4gZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKCkuIFRoaXMgaXMgbGlrZWx5IGEgYnVnIGluIFJlYWN0LiBQbGVhc2UgZmlsZSBhbiBpc3N1ZS5cIlxuICAgICAgICAgICAgKSxcbiAgICAgICAgICB0eXBlLiQkdHlwZW9mKVxuICAgICAgICApIHtcbiAgICAgICAgICBjYXNlIFJFQUNUX1BPUlRBTF9UWVBFOlxuICAgICAgICAgICAgcmV0dXJuIFwiUG9ydGFsXCI7XG4gICAgICAgICAgY2FzZSBSRUFDVF9DT05URVhUX1RZUEU6XG4gICAgICAgICAgICByZXR1cm4gKHR5cGUuZGlzcGxheU5hbWUgfHwgXCJDb250ZXh0XCIpICsgXCIuUHJvdmlkZXJcIjtcbiAgICAgICAgICBjYXNlIFJFQUNUX0NPTlNVTUVSX1RZUEU6XG4gICAgICAgICAgICByZXR1cm4gKHR5cGUuX2NvbnRleHQuZGlzcGxheU5hbWUgfHwgXCJDb250ZXh0XCIpICsgXCIuQ29uc3VtZXJcIjtcbiAgICAgICAgICBjYXNlIFJFQUNUX0ZPUldBUkRfUkVGX1RZUEU6XG4gICAgICAgICAgICB2YXIgaW5uZXJUeXBlID0gdHlwZS5yZW5kZXI7XG4gICAgICAgICAgICB0eXBlID0gdHlwZS5kaXNwbGF5TmFtZTtcbiAgICAgICAgICAgIHR5cGUgfHxcbiAgICAgICAgICAgICAgKCh0eXBlID0gaW5uZXJUeXBlLmRpc3BsYXlOYW1lIHx8IGlubmVyVHlwZS5uYW1lIHx8IFwiXCIpLFxuICAgICAgICAgICAgICAodHlwZSA9IFwiXCIgIT09IHR5cGUgPyBcIkZvcndhcmRSZWYoXCIgKyB0eXBlICsgXCIpXCIgOiBcIkZvcndhcmRSZWZcIikpO1xuICAgICAgICAgICAgcmV0dXJuIHR5cGU7XG4gICAgICAgICAgY2FzZSBSRUFDVF9NRU1PX1RZUEU6XG4gICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAoaW5uZXJUeXBlID0gdHlwZS5kaXNwbGF5TmFtZSB8fCBudWxsKSxcbiAgICAgICAgICAgICAgbnVsbCAhPT0gaW5uZXJUeXBlXG4gICAgICAgICAgICAgICAgPyBpbm5lclR5cGVcbiAgICAgICAgICAgICAgICA6IGdldENvbXBvbmVudE5hbWVGcm9tVHlwZSh0eXBlLnR5cGUpIHx8IFwiTWVtb1wiXG4gICAgICAgICAgICApO1xuICAgICAgICAgIGNhc2UgUkVBQ1RfTEFaWV9UWVBFOlxuICAgICAgICAgICAgaW5uZXJUeXBlID0gdHlwZS5fcGF5bG9hZDtcbiAgICAgICAgICAgIHR5cGUgPSB0eXBlLl9pbml0O1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgcmV0dXJuIGdldENvbXBvbmVudE5hbWVGcm9tVHlwZSh0eXBlKGlubmVyVHlwZSkpO1xuICAgICAgICAgICAgfSBjYXRjaCAoeCkge31cbiAgICAgICAgfVxuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuICAgIGZ1bmN0aW9uIHRlc3RTdHJpbmdDb2VyY2lvbih2YWx1ZSkge1xuICAgICAgcmV0dXJuIFwiXCIgKyB2YWx1ZTtcbiAgICB9XG4gICAgZnVuY3Rpb24gY2hlY2tLZXlTdHJpbmdDb2VyY2lvbih2YWx1ZSkge1xuICAgICAgdHJ5IHtcbiAgICAgICAgdGVzdFN0cmluZ0NvZXJjaW9uKHZhbHVlKTtcbiAgICAgICAgdmFyIEpTQ29tcGlsZXJfaW5saW5lX3Jlc3VsdCA9ICExO1xuICAgICAgfSBjYXRjaCAoZSkge1xuICAgICAgICBKU0NvbXBpbGVyX2lubGluZV9yZXN1bHQgPSAhMDtcbiAgICAgIH1cbiAgICAgIGlmIChKU0NvbXBpbGVyX2lubGluZV9yZXN1bHQpIHtcbiAgICAgICAgSlNDb21waWxlcl9pbmxpbmVfcmVzdWx0ID0gY29uc29sZTtcbiAgICAgICAgdmFyIEpTQ29tcGlsZXJfdGVtcF9jb25zdCA9IEpTQ29tcGlsZXJfaW5saW5lX3Jlc3VsdC5lcnJvcjtcbiAgICAgICAgdmFyIEpTQ29tcGlsZXJfaW5saW5lX3Jlc3VsdCRqc2NvbXAkMCA9XG4gICAgICAgICAgKFwiZnVuY3Rpb25cIiA9PT0gdHlwZW9mIFN5bWJvbCAmJlxuICAgICAgICAgICAgU3ltYm9sLnRvU3RyaW5nVGFnICYmXG4gICAgICAgICAgICB2YWx1ZVtTeW1ib2wudG9TdHJpbmdUYWddKSB8fFxuICAgICAgICAgIHZhbHVlLmNvbnN0cnVjdG9yLm5hbWUgfHxcbiAgICAgICAgICBcIk9iamVjdFwiO1xuICAgICAgICBKU0NvbXBpbGVyX3RlbXBfY29uc3QuY2FsbChcbiAgICAgICAgICBKU0NvbXBpbGVyX2lubGluZV9yZXN1bHQsXG4gICAgICAgICAgXCJUaGUgcHJvdmlkZWQga2V5IGlzIGFuIHVuc3VwcG9ydGVkIHR5cGUgJXMuIFRoaXMgdmFsdWUgbXVzdCBiZSBjb2VyY2VkIHRvIGEgc3RyaW5nIGJlZm9yZSB1c2luZyBpdCBoZXJlLlwiLFxuICAgICAgICAgIEpTQ29tcGlsZXJfaW5saW5lX3Jlc3VsdCRqc2NvbXAkMFxuICAgICAgICApO1xuICAgICAgICByZXR1cm4gdGVzdFN0cmluZ0NvZXJjaW9uKHZhbHVlKTtcbiAgICAgIH1cbiAgICB9XG4gICAgZnVuY3Rpb24gZ2V0VGFza05hbWUodHlwZSkge1xuICAgICAgaWYgKHR5cGUgPT09IFJFQUNUX0ZSQUdNRU5UX1RZUEUpIHJldHVybiBcIjw+XCI7XG4gICAgICBpZiAoXG4gICAgICAgIFwib2JqZWN0XCIgPT09IHR5cGVvZiB0eXBlICYmXG4gICAgICAgIG51bGwgIT09IHR5cGUgJiZcbiAgICAgICAgdHlwZS4kJHR5cGVvZiA9PT0gUkVBQ1RfTEFaWV9UWVBFXG4gICAgICApXG4gICAgICAgIHJldHVybiBcIjwuLi4+XCI7XG4gICAgICB0cnkge1xuICAgICAgICB2YXIgbmFtZSA9IGdldENvbXBvbmVudE5hbWVGcm9tVHlwZSh0eXBlKTtcbiAgICAgICAgcmV0dXJuIG5hbWUgPyBcIjxcIiArIG5hbWUgKyBcIj5cIiA6IFwiPC4uLj5cIjtcbiAgICAgIH0gY2F0Y2ggKHgpIHtcbiAgICAgICAgcmV0dXJuIFwiPC4uLj5cIjtcbiAgICAgIH1cbiAgICB9XG4gICAgZnVuY3Rpb24gZ2V0T3duZXIoKSB7XG4gICAgICB2YXIgZGlzcGF0Y2hlciA9IFJlYWN0U2hhcmVkSW50ZXJuYWxzLkE7XG4gICAgICByZXR1cm4gbnVsbCA9PT0gZGlzcGF0Y2hlciA/IG51bGwgOiBkaXNwYXRjaGVyLmdldE93bmVyKCk7XG4gICAgfVxuICAgIGZ1bmN0aW9uIFVua25vd25Pd25lcigpIHtcbiAgICAgIHJldHVybiBFcnJvcihcInJlYWN0LXN0YWNrLXRvcC1mcmFtZVwiKTtcbiAgICB9XG4gICAgZnVuY3Rpb24gaGFzVmFsaWRLZXkoY29uZmlnKSB7XG4gICAgICBpZiAoaGFzT3duUHJvcGVydHkuY2FsbChjb25maWcsIFwia2V5XCIpKSB7XG4gICAgICAgIHZhciBnZXR0ZXIgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yKGNvbmZpZywgXCJrZXlcIikuZ2V0O1xuICAgICAgICBpZiAoZ2V0dGVyICYmIGdldHRlci5pc1JlYWN0V2FybmluZykgcmV0dXJuICExO1xuICAgICAgfVxuICAgICAgcmV0dXJuIHZvaWQgMCAhPT0gY29uZmlnLmtleTtcbiAgICB9XG4gICAgZnVuY3Rpb24gZGVmaW5lS2V5UHJvcFdhcm5pbmdHZXR0ZXIocHJvcHMsIGRpc3BsYXlOYW1lKSB7XG4gICAgICBmdW5jdGlvbiB3YXJuQWJvdXRBY2Nlc3NpbmdLZXkoKSB7XG4gICAgICAgIHNwZWNpYWxQcm9wS2V5V2FybmluZ1Nob3duIHx8XG4gICAgICAgICAgKChzcGVjaWFsUHJvcEtleVdhcm5pbmdTaG93biA9ICEwKSxcbiAgICAgICAgICBjb25zb2xlLmVycm9yKFxuICAgICAgICAgICAgXCIlczogYGtleWAgaXMgbm90IGEgcHJvcC4gVHJ5aW5nIHRvIGFjY2VzcyBpdCB3aWxsIHJlc3VsdCBpbiBgdW5kZWZpbmVkYCBiZWluZyByZXR1cm5lZC4gSWYgeW91IG5lZWQgdG8gYWNjZXNzIHRoZSBzYW1lIHZhbHVlIHdpdGhpbiB0aGUgY2hpbGQgY29tcG9uZW50LCB5b3Ugc2hvdWxkIHBhc3MgaXQgYXMgYSBkaWZmZXJlbnQgcHJvcC4gKGh0dHBzOi8vcmVhY3QuZGV2L2xpbmsvc3BlY2lhbC1wcm9wcylcIixcbiAgICAgICAgICAgIGRpc3BsYXlOYW1lXG4gICAgICAgICAgKSk7XG4gICAgICB9XG4gICAgICB3YXJuQWJvdXRBY2Nlc3NpbmdLZXkuaXNSZWFjdFdhcm5pbmcgPSAhMDtcbiAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShwcm9wcywgXCJrZXlcIiwge1xuICAgICAgICBnZXQ6IHdhcm5BYm91dEFjY2Vzc2luZ0tleSxcbiAgICAgICAgY29uZmlndXJhYmxlOiAhMFxuICAgICAgfSk7XG4gICAgfVxuICAgIGZ1bmN0aW9uIGVsZW1lbnRSZWZHZXR0ZXJXaXRoRGVwcmVjYXRpb25XYXJuaW5nKCkge1xuICAgICAgdmFyIGNvbXBvbmVudE5hbWUgPSBnZXRDb21wb25lbnROYW1lRnJvbVR5cGUodGhpcy50eXBlKTtcbiAgICAgIGRpZFdhcm5BYm91dEVsZW1lbnRSZWZbY29tcG9uZW50TmFtZV0gfHxcbiAgICAgICAgKChkaWRXYXJuQWJvdXRFbGVtZW50UmVmW2NvbXBvbmVudE5hbWVdID0gITApLFxuICAgICAgICBjb25zb2xlLmVycm9yKFxuICAgICAgICAgIFwiQWNjZXNzaW5nIGVsZW1lbnQucmVmIHdhcyByZW1vdmVkIGluIFJlYWN0IDE5LiByZWYgaXMgbm93IGEgcmVndWxhciBwcm9wLiBJdCB3aWxsIGJlIHJlbW92ZWQgZnJvbSB0aGUgSlNYIEVsZW1lbnQgdHlwZSBpbiBhIGZ1dHVyZSByZWxlYXNlLlwiXG4gICAgICAgICkpO1xuICAgICAgY29tcG9uZW50TmFtZSA9IHRoaXMucHJvcHMucmVmO1xuICAgICAgcmV0dXJuIHZvaWQgMCAhPT0gY29tcG9uZW50TmFtZSA/IGNvbXBvbmVudE5hbWUgOiBudWxsO1xuICAgIH1cbiAgICBmdW5jdGlvbiBSZWFjdEVsZW1lbnQoXG4gICAgICB0eXBlLFxuICAgICAga2V5LFxuICAgICAgc2VsZixcbiAgICAgIHNvdXJjZSxcbiAgICAgIG93bmVyLFxuICAgICAgcHJvcHMsXG4gICAgICBkZWJ1Z1N0YWNrLFxuICAgICAgZGVidWdUYXNrXG4gICAgKSB7XG4gICAgICBzZWxmID0gcHJvcHMucmVmO1xuICAgICAgdHlwZSA9IHtcbiAgICAgICAgJCR0eXBlb2Y6IFJFQUNUX0VMRU1FTlRfVFlQRSxcbiAgICAgICAgdHlwZTogdHlwZSxcbiAgICAgICAga2V5OiBrZXksXG4gICAgICAgIHByb3BzOiBwcm9wcyxcbiAgICAgICAgX293bmVyOiBvd25lclxuICAgICAgfTtcbiAgICAgIG51bGwgIT09ICh2b2lkIDAgIT09IHNlbGYgPyBzZWxmIDogbnVsbClcbiAgICAgICAgPyBPYmplY3QuZGVmaW5lUHJvcGVydHkodHlwZSwgXCJyZWZcIiwge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogITEsXG4gICAgICAgICAgICBnZXQ6IGVsZW1lbnRSZWZHZXR0ZXJXaXRoRGVwcmVjYXRpb25XYXJuaW5nXG4gICAgICAgICAgfSlcbiAgICAgICAgOiBPYmplY3QuZGVmaW5lUHJvcGVydHkodHlwZSwgXCJyZWZcIiwgeyBlbnVtZXJhYmxlOiAhMSwgdmFsdWU6IG51bGwgfSk7XG4gICAgICB0eXBlLl9zdG9yZSA9IHt9O1xuICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHR5cGUuX3N0b3JlLCBcInZhbGlkYXRlZFwiLCB7XG4gICAgICAgIGNvbmZpZ3VyYWJsZTogITEsXG4gICAgICAgIGVudW1lcmFibGU6ICExLFxuICAgICAgICB3cml0YWJsZTogITAsXG4gICAgICAgIHZhbHVlOiAwXG4gICAgICB9KTtcbiAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0eXBlLCBcIl9kZWJ1Z0luZm9cIiwge1xuICAgICAgICBjb25maWd1cmFibGU6ICExLFxuICAgICAgICBlbnVtZXJhYmxlOiAhMSxcbiAgICAgICAgd3JpdGFibGU6ICEwLFxuICAgICAgICB2YWx1ZTogbnVsbFxuICAgICAgfSk7XG4gICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodHlwZSwgXCJfZGVidWdTdGFja1wiLCB7XG4gICAgICAgIGNvbmZpZ3VyYWJsZTogITEsXG4gICAgICAgIGVudW1lcmFibGU6ICExLFxuICAgICAgICB3cml0YWJsZTogITAsXG4gICAgICAgIHZhbHVlOiBkZWJ1Z1N0YWNrXG4gICAgICB9KTtcbiAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0eXBlLCBcIl9kZWJ1Z1Rhc2tcIiwge1xuICAgICAgICBjb25maWd1cmFibGU6ICExLFxuICAgICAgICBlbnVtZXJhYmxlOiAhMSxcbiAgICAgICAgd3JpdGFibGU6ICEwLFxuICAgICAgICB2YWx1ZTogZGVidWdUYXNrXG4gICAgICB9KTtcbiAgICAgIE9iamVjdC5mcmVlemUgJiYgKE9iamVjdC5mcmVlemUodHlwZS5wcm9wcyksIE9iamVjdC5mcmVlemUodHlwZSkpO1xuICAgICAgcmV0dXJuIHR5cGU7XG4gICAgfVxuICAgIGZ1bmN0aW9uIGpzeERFVkltcGwoXG4gICAgICB0eXBlLFxuICAgICAgY29uZmlnLFxuICAgICAgbWF5YmVLZXksXG4gICAgICBpc1N0YXRpY0NoaWxkcmVuLFxuICAgICAgc291cmNlLFxuICAgICAgc2VsZixcbiAgICAgIGRlYnVnU3RhY2ssXG4gICAgICBkZWJ1Z1Rhc2tcbiAgICApIHtcbiAgICAgIHZhciBjaGlsZHJlbiA9IGNvbmZpZy5jaGlsZHJlbjtcbiAgICAgIGlmICh2b2lkIDAgIT09IGNoaWxkcmVuKVxuICAgICAgICBpZiAoaXNTdGF0aWNDaGlsZHJlbilcbiAgICAgICAgICBpZiAoaXNBcnJheUltcGwoY2hpbGRyZW4pKSB7XG4gICAgICAgICAgICBmb3IgKFxuICAgICAgICAgICAgICBpc1N0YXRpY0NoaWxkcmVuID0gMDtcbiAgICAgICAgICAgICAgaXNTdGF0aWNDaGlsZHJlbiA8IGNoaWxkcmVuLmxlbmd0aDtcbiAgICAgICAgICAgICAgaXNTdGF0aWNDaGlsZHJlbisrXG4gICAgICAgICAgICApXG4gICAgICAgICAgICAgIHZhbGlkYXRlQ2hpbGRLZXlzKGNoaWxkcmVuW2lzU3RhdGljQ2hpbGRyZW5dKTtcbiAgICAgICAgICAgIE9iamVjdC5mcmVlemUgJiYgT2JqZWN0LmZyZWV6ZShjaGlsZHJlbik7XG4gICAgICAgICAgfSBlbHNlXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKFxuICAgICAgICAgICAgICBcIlJlYWN0LmpzeDogU3RhdGljIGNoaWxkcmVuIHNob3VsZCBhbHdheXMgYmUgYW4gYXJyYXkuIFlvdSBhcmUgbGlrZWx5IGV4cGxpY2l0bHkgY2FsbGluZyBSZWFjdC5qc3hzIG9yIFJlYWN0LmpzeERFVi4gVXNlIHRoZSBCYWJlbCB0cmFuc2Zvcm0gaW5zdGVhZC5cIlxuICAgICAgICAgICAgKTtcbiAgICAgICAgZWxzZSB2YWxpZGF0ZUNoaWxkS2V5cyhjaGlsZHJlbik7XG4gICAgICBpZiAoaGFzT3duUHJvcGVydHkuY2FsbChjb25maWcsIFwia2V5XCIpKSB7XG4gICAgICAgIGNoaWxkcmVuID0gZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKHR5cGUpO1xuICAgICAgICB2YXIga2V5cyA9IE9iamVjdC5rZXlzKGNvbmZpZykuZmlsdGVyKGZ1bmN0aW9uIChrKSB7XG4gICAgICAgICAgcmV0dXJuIFwia2V5XCIgIT09IGs7XG4gICAgICAgIH0pO1xuICAgICAgICBpc1N0YXRpY0NoaWxkcmVuID1cbiAgICAgICAgICAwIDwga2V5cy5sZW5ndGhcbiAgICAgICAgICAgID8gXCJ7a2V5OiBzb21lS2V5LCBcIiArIGtleXMuam9pbihcIjogLi4uLCBcIikgKyBcIjogLi4ufVwiXG4gICAgICAgICAgICA6IFwie2tleTogc29tZUtleX1cIjtcbiAgICAgICAgZGlkV2FybkFib3V0S2V5U3ByZWFkW2NoaWxkcmVuICsgaXNTdGF0aWNDaGlsZHJlbl0gfHxcbiAgICAgICAgICAoKGtleXMgPVxuICAgICAgICAgICAgMCA8IGtleXMubGVuZ3RoID8gXCJ7XCIgKyBrZXlzLmpvaW4oXCI6IC4uLiwgXCIpICsgXCI6IC4uLn1cIiA6IFwie31cIiksXG4gICAgICAgICAgY29uc29sZS5lcnJvcihcbiAgICAgICAgICAgICdBIHByb3BzIG9iamVjdCBjb250YWluaW5nIGEgXCJrZXlcIiBwcm9wIGlzIGJlaW5nIHNwcmVhZCBpbnRvIEpTWDpcXG4gIGxldCBwcm9wcyA9ICVzO1xcbiAgPCVzIHsuLi5wcm9wc30gLz5cXG5SZWFjdCBrZXlzIG11c3QgYmUgcGFzc2VkIGRpcmVjdGx5IHRvIEpTWCB3aXRob3V0IHVzaW5nIHNwcmVhZDpcXG4gIGxldCBwcm9wcyA9ICVzO1xcbiAgPCVzIGtleT17c29tZUtleX0gey4uLnByb3BzfSAvPicsXG4gICAgICAgICAgICBpc1N0YXRpY0NoaWxkcmVuLFxuICAgICAgICAgICAgY2hpbGRyZW4sXG4gICAgICAgICAgICBrZXlzLFxuICAgICAgICAgICAgY2hpbGRyZW5cbiAgICAgICAgICApLFxuICAgICAgICAgIChkaWRXYXJuQWJvdXRLZXlTcHJlYWRbY2hpbGRyZW4gKyBpc1N0YXRpY0NoaWxkcmVuXSA9ICEwKSk7XG4gICAgICB9XG4gICAgICBjaGlsZHJlbiA9IG51bGw7XG4gICAgICB2b2lkIDAgIT09IG1heWJlS2V5ICYmXG4gICAgICAgIChjaGVja0tleVN0cmluZ0NvZXJjaW9uKG1heWJlS2V5KSwgKGNoaWxkcmVuID0gXCJcIiArIG1heWJlS2V5KSk7XG4gICAgICBoYXNWYWxpZEtleShjb25maWcpICYmXG4gICAgICAgIChjaGVja0tleVN0cmluZ0NvZXJjaW9uKGNvbmZpZy5rZXkpLCAoY2hpbGRyZW4gPSBcIlwiICsgY29uZmlnLmtleSkpO1xuICAgICAgaWYgKFwia2V5XCIgaW4gY29uZmlnKSB7XG4gICAgICAgIG1heWJlS2V5ID0ge307XG4gICAgICAgIGZvciAodmFyIHByb3BOYW1lIGluIGNvbmZpZylcbiAgICAgICAgICBcImtleVwiICE9PSBwcm9wTmFtZSAmJiAobWF5YmVLZXlbcHJvcE5hbWVdID0gY29uZmlnW3Byb3BOYW1lXSk7XG4gICAgICB9IGVsc2UgbWF5YmVLZXkgPSBjb25maWc7XG4gICAgICBjaGlsZHJlbiAmJlxuICAgICAgICBkZWZpbmVLZXlQcm9wV2FybmluZ0dldHRlcihcbiAgICAgICAgICBtYXliZUtleSxcbiAgICAgICAgICBcImZ1bmN0aW9uXCIgPT09IHR5cGVvZiB0eXBlXG4gICAgICAgICAgICA/IHR5cGUuZGlzcGxheU5hbWUgfHwgdHlwZS5uYW1lIHx8IFwiVW5rbm93blwiXG4gICAgICAgICAgICA6IHR5cGVcbiAgICAgICAgKTtcbiAgICAgIHJldHVybiBSZWFjdEVsZW1lbnQoXG4gICAgICAgIHR5cGUsXG4gICAgICAgIGNoaWxkcmVuLFxuICAgICAgICBzZWxmLFxuICAgICAgICBzb3VyY2UsXG4gICAgICAgIGdldE93bmVyKCksXG4gICAgICAgIG1heWJlS2V5LFxuICAgICAgICBkZWJ1Z1N0YWNrLFxuICAgICAgICBkZWJ1Z1Rhc2tcbiAgICAgICk7XG4gICAgfVxuICAgIGZ1bmN0aW9uIHZhbGlkYXRlQ2hpbGRLZXlzKG5vZGUpIHtcbiAgICAgIFwib2JqZWN0XCIgPT09IHR5cGVvZiBub2RlICYmXG4gICAgICAgIG51bGwgIT09IG5vZGUgJiZcbiAgICAgICAgbm9kZS4kJHR5cGVvZiA9PT0gUkVBQ1RfRUxFTUVOVF9UWVBFICYmXG4gICAgICAgIG5vZGUuX3N0b3JlICYmXG4gICAgICAgIChub2RlLl9zdG9yZS52YWxpZGF0ZWQgPSAxKTtcbiAgICB9XG4gICAgdmFyIFJlYWN0ID0gcmVxdWlyZShcIm5leHQvZGlzdC9jb21waWxlZC9yZWFjdFwiKSxcbiAgICAgIFJFQUNUX0VMRU1FTlRfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC50cmFuc2l0aW9uYWwuZWxlbWVudFwiKSxcbiAgICAgIFJFQUNUX1BPUlRBTF9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LnBvcnRhbFwiKSxcbiAgICAgIFJFQUNUX0ZSQUdNRU5UX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3QuZnJhZ21lbnRcIiksXG4gICAgICBSRUFDVF9TVFJJQ1RfTU9ERV9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LnN0cmljdF9tb2RlXCIpLFxuICAgICAgUkVBQ1RfUFJPRklMRVJfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5wcm9maWxlclwiKTtcbiAgICBTeW1ib2wuZm9yKFwicmVhY3QucHJvdmlkZXJcIik7XG4gICAgdmFyIFJFQUNUX0NPTlNVTUVSX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3QuY29uc3VtZXJcIiksXG4gICAgICBSRUFDVF9DT05URVhUX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3QuY29udGV4dFwiKSxcbiAgICAgIFJFQUNUX0ZPUldBUkRfUkVGX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3QuZm9yd2FyZF9yZWZcIiksXG4gICAgICBSRUFDVF9TVVNQRU5TRV9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LnN1c3BlbnNlXCIpLFxuICAgICAgUkVBQ1RfU1VTUEVOU0VfTElTVF9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LnN1c3BlbnNlX2xpc3RcIiksXG4gICAgICBSRUFDVF9NRU1PX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3QubWVtb1wiKSxcbiAgICAgIFJFQUNUX0xBWllfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5sYXp5XCIpLFxuICAgICAgUkVBQ1RfQUNUSVZJVFlfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5hY3Rpdml0eVwiKSxcbiAgICAgIFJFQUNUX0NMSUVOVF9SRUZFUkVOQ0UgPSBTeW1ib2wuZm9yKFwicmVhY3QuY2xpZW50LnJlZmVyZW5jZVwiKSxcbiAgICAgIFJlYWN0U2hhcmVkSW50ZXJuYWxzID1cbiAgICAgICAgUmVhY3QuX19DTElFTlRfSU5URVJOQUxTX0RPX05PVF9VU0VfT1JfV0FSTl9VU0VSU19USEVZX0NBTk5PVF9VUEdSQURFLFxuICAgICAgaGFzT3duUHJvcGVydHkgPSBPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LFxuICAgICAgaXNBcnJheUltcGwgPSBBcnJheS5pc0FycmF5LFxuICAgICAgY3JlYXRlVGFzayA9IGNvbnNvbGUuY3JlYXRlVGFza1xuICAgICAgICA/IGNvbnNvbGUuY3JlYXRlVGFza1xuICAgICAgICA6IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICAgIH07XG4gICAgUmVhY3QgPSB7XG4gICAgICBcInJlYWN0LXN0YWNrLWJvdHRvbS1mcmFtZVwiOiBmdW5jdGlvbiAoY2FsbFN0YWNrRm9yRXJyb3IpIHtcbiAgICAgICAgcmV0dXJuIGNhbGxTdGFja0ZvckVycm9yKCk7XG4gICAgICB9XG4gICAgfTtcbiAgICB2YXIgc3BlY2lhbFByb3BLZXlXYXJuaW5nU2hvd247XG4gICAgdmFyIGRpZFdhcm5BYm91dEVsZW1lbnRSZWYgPSB7fTtcbiAgICB2YXIgdW5rbm93bk93bmVyRGVidWdTdGFjayA9IFJlYWN0W1wicmVhY3Qtc3RhY2stYm90dG9tLWZyYW1lXCJdLmJpbmQoXG4gICAgICBSZWFjdCxcbiAgICAgIFVua25vd25Pd25lclxuICAgICkoKTtcbiAgICB2YXIgdW5rbm93bk93bmVyRGVidWdUYXNrID0gY3JlYXRlVGFzayhnZXRUYXNrTmFtZShVbmtub3duT3duZXIpKTtcbiAgICB2YXIgZGlkV2FybkFib3V0S2V5U3ByZWFkID0ge307XG4gICAgZXhwb3J0cy5GcmFnbWVudCA9IFJFQUNUX0ZSQUdNRU5UX1RZUEU7XG4gICAgZXhwb3J0cy5qc3hERVYgPSBmdW5jdGlvbiAoXG4gICAgICB0eXBlLFxuICAgICAgY29uZmlnLFxuICAgICAgbWF5YmVLZXksXG4gICAgICBpc1N0YXRpY0NoaWxkcmVuLFxuICAgICAgc291cmNlLFxuICAgICAgc2VsZlxuICAgICkge1xuICAgICAgdmFyIHRyYWNrQWN0dWFsT3duZXIgPVxuICAgICAgICAxZTQgPiBSZWFjdFNoYXJlZEludGVybmFscy5yZWNlbnRseUNyZWF0ZWRPd25lclN0YWNrcysrO1xuICAgICAgcmV0dXJuIGpzeERFVkltcGwoXG4gICAgICAgIHR5cGUsXG4gICAgICAgIGNvbmZpZyxcbiAgICAgICAgbWF5YmVLZXksXG4gICAgICAgIGlzU3RhdGljQ2hpbGRyZW4sXG4gICAgICAgIHNvdXJjZSxcbiAgICAgICAgc2VsZixcbiAgICAgICAgdHJhY2tBY3R1YWxPd25lclxuICAgICAgICAgID8gRXJyb3IoXCJyZWFjdC1zdGFjay10b3AtZnJhbWVcIilcbiAgICAgICAgICA6IHVua25vd25Pd25lckRlYnVnU3RhY2ssXG4gICAgICAgIHRyYWNrQWN0dWFsT3duZXIgPyBjcmVhdGVUYXNrKGdldFRhc2tOYW1lKHR5cGUpKSA6IHVua25vd25Pd25lckRlYnVnVGFza1xuICAgICAgKTtcbiAgICB9O1xuICB9KSgpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkQ6XFxoYWNrb3Rob25cXEJsb2NrY2hhaW5cXFZpbnRyZWtcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcY29tcGlsZWRcXHJlYWN0XFxqc3gtZGV2LXJ1bnRpbWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5wcm9kdWN0aW9uLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Coins_MapPin_Mountain_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Coins,MapPin,Mountain,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mountain.mjs\");\n/* harmony import */ var _barrel_optimize_names_Coins_MapPin_Mountain_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Coins,MapPin,Mountain,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.mjs\");\n/* harmony import */ var _barrel_optimize_names_Coins_MapPin_Mountain_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Coins,MapPin,Mountain,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Coins_MapPin_Mountain_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Coins,MapPin,Mountain,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coins.mjs\");\n/* harmony import */ var _barrel_optimize_names_Coins_MapPin_Mountain_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Coins,MapPin,Mountain,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.mjs\");\n/* harmony import */ var _components_wallet_WalletConnect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/wallet/WalletConnect */ \"(app-pages-browser)/./src/components/wallet/WalletConnect.tsx\");\n/* harmony import */ var _components_trails_TrailCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/trails/TrailCard */ \"(app-pages-browser)/./src/components/trails/TrailCard.tsx\");\n/* harmony import */ var _components_ui_StatsCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/StatsCard */ \"(app-pages-browser)/./src/components/ui/StatsCard.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n// Mock data for demonstration\nconst featuredTrails = [\n    {\n        id: '1',\n        name: 'Ella Rock Trail',\n        location: 'Ella, Sri Lanka',\n        difficulty: 'Moderate',\n        distance: '8.2 km',\n        duration: '4-5 hours',\n        image: '/api/placeholder/400/300',\n        description: 'A scenic hike offering panoramic views of the hill country.',\n        price: 2500,\n        available: true\n    },\n    {\n        id: '2',\n        name: 'Adam\\'s Peak',\n        location: 'Ratnapura, Sri Lanka',\n        difficulty: 'Hard',\n        distance: '11.5 km',\n        duration: '6-8 hours',\n        image: '/api/placeholder/400/300',\n        description: 'Sacred mountain with breathtaking sunrise views.',\n        price: 3500,\n        available: true\n    },\n    {\n        id: '3',\n        name: 'Horton Plains',\n        location: 'Nuwara Eliya, Sri Lanka',\n        difficulty: 'Easy',\n        distance: '9.5 km',\n        duration: '3-4 hours',\n        image: '/api/placeholder/400/300',\n        description: 'UNESCO World Heritage site with World\\'s End viewpoint.',\n        price: 4000,\n        available: false\n    }\n];\nconst stats = [\n    {\n        icon: _barrel_optimize_names_Coins_MapPin_Mountain_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        label: 'Trails Available',\n        value: '150+'\n    },\n    {\n        icon: _barrel_optimize_names_Coins_MapPin_Mountain_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        label: 'Active Hikers',\n        value: '2.5K+'\n    },\n    {\n        icon: _barrel_optimize_names_Coins_MapPin_Mountain_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        label: 'NFTs Minted',\n        value: '1.2K+'\n    },\n    {\n        icon: _barrel_optimize_names_Coins_MapPin_Mountain_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        label: 'TREK Tokens Earned',\n        value: '50K+'\n    }\n];\nfunction HomePage() {\n    _s();\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-green-50 to-blue-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_MapPin_Mountain_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-8 w-8 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"VinTrek\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"hidden md:flex space-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/trails\",\n                                        className: \"text-gray-700 hover:text-green-600 transition-colors\",\n                                        children: \"Trails\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/dashboard\",\n                                        className: \"text-gray-700 hover:text-green-600 transition-colors\",\n                                        children: \"Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/rewards\",\n                                        className: \"text-gray-700 hover:text-green-600 transition-colors\",\n                                        children: \"Rewards\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"text-gray-700 hover:text-green-600 transition-colors\",\n                                        children: \"Community\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_wallet_WalletConnect__WEBPACK_IMPORTED_MODULE_2__.WalletConnect, {\n                                onConnectionChange: setIsConnected\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative py-20 px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl md:text-6xl font-bold text-gray-900 mb-6\",\n                            children: [\n                                \"Explore Sri Lanka's Trails,\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-green-600 block\",\n                                    children: \"Earn Blockchain Rewards\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 mb-8 max-w-3xl mx-auto\",\n                            children: \"Discover breathtaking hiking trails, mint unique NFTs upon completion, and earn TREK tokens for your eco-tourism adventures.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>window.location.href = '/trails',\n                                    className: \"bg-green-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors\",\n                                    children: \"Explore Trails\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>{\n                                        var _document_getElementById;\n                                        return window.scrollTo({\n                                            top: ((_document_getElementById = document.getElementById('how-it-works')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.offsetTop) || 0,\n                                            behavior: 'smooth'\n                                        });\n                                    },\n                                    className: \"border border-green-600 text-green-600 px-8 py-3 rounded-lg font-semibold hover:bg-green-50 transition-colors\",\n                                    children: \"Learn More\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 px-4 sm:px-6 lg:px-8 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-8\",\n                        children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_StatsCard__WEBPACK_IMPORTED_MODULE_4__.StatsCard, {\n                                ...stat\n                            }, index, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                    children: \"Featured Trails\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 max-w-2xl mx-auto\",\n                                    children: \"Discover our most popular hiking destinations and start your blockchain-powered adventure today.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: featuredTrails.map((trail)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_trails_TrailCard__WEBPACK_IMPORTED_MODULE_3__.TrailCard, {\n                                    trail: trail\n                                }, trail.id, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"how-it-works\",\n                className: \"py-16 px-4 sm:px-6 lg:px-8 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                    children: \"How VinTrek Works\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 max-w-2xl mx-auto\",\n                                    children: \"Experience the future of eco-tourism with blockchain technology\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_MapPin_Mountain_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-8 w-8 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-2\",\n                                            children: \"1. Discover & Book\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Browse trails, check availability, and book your adventure with your Cardano wallet.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_MapPin_Mountain_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-8 w-8 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-2\",\n                                            children: \"2. Complete Trail\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Hike the trail, enjoy nature, and verify completion via GPS or manual confirmation.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_MapPin_Mountain_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-8 w-8 text-yellow-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-2\",\n                                            children: \"3. Earn Rewards\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Mint unique trail NFTs and earn TREK tokens for completing adventures and activities.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-gray-900 text-white py-12 px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-4 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coins_MapPin_Mountain_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-6 w-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl font-bold\",\n                                                    children: \"VinTrek\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400\",\n                                            children: \"Blockchain-powered eco-tourism platform for Sri Lanka's beautiful trails.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold mb-4\",\n                                            children: \"Platform\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-white transition-colors\",\n                                                        children: \"Trails\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-white transition-colors\",\n                                                        children: \"Dashboard\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-white transition-colors\",\n                                                        children: \"Rewards\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-white transition-colors\",\n                                                        children: \"Community\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold mb-4\",\n                                            children: \"Blockchain\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-white transition-colors\",\n                                                        children: \"NFT Collection\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-white transition-colors\",\n                                                        children: \"TREK Token\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-white transition-colors\",\n                                                        children: \"Wallet Guide\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-white transition-colors\",\n                                                        children: \"Smart Contracts\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold mb-4\",\n                                            children: \"Support\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-white transition-colors\",\n                                                        children: \"Help Center\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-white transition-colors\",\n                                                        children: \"Contact Us\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-white transition-colors\",\n                                                        children: \"Terms of Service\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"hover:text-white transition-colors\",\n                                                        children: \"Privacy Policy\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"\\xa9 2024 VinTrek. All rights reserved. Built on Cardano blockchain.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 174,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"TapwvE5zBt+nEzwDvBvyo5vtnMM=\");\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/providers/WalletProvider.tsx":
/*!*****************************************************!*\
  !*** ./src/components/providers/WalletProvider.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalletProvider: () => (/* binding */ WalletProvider),\n/* harmony export */   useWallet: () => (/* binding */ useWallet)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useWallet,WalletProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n// Dynamically import BrowserWallet to avoid SSR issues\nlet BrowserWallet = null;\nif (true) {\n    __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_meshsdk_core_dist_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @meshsdk/core */ \"(app-pages-browser)/./node_modules/@meshsdk/core/dist/index.js\")).then((module)=>{\n        BrowserWallet = module.BrowserWallet;\n    });\n}\nconst WalletContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useWallet() {\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(WalletContext);\n    if (context === undefined) {\n        throw new Error('useWallet must be used within a WalletProvider');\n    }\n    return context;\n}\n_s(useWallet, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nfunction WalletProvider(param) {\n    let { children } = param;\n    _s1();\n    const [wallet, setWallet] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [connected, setConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [connecting, setConnecting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [address, setAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [balance, setBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const getAvailableWallets = ()=>{\n        var _window_cardano, _window_cardano1, _window_cardano2, _window_cardano3;\n        if (false) {}\n        const wallets = [];\n        if ((_window_cardano = window.cardano) === null || _window_cardano === void 0 ? void 0 : _window_cardano.lace) wallets.push('lace');\n        if ((_window_cardano1 = window.cardano) === null || _window_cardano1 === void 0 ? void 0 : _window_cardano1.eternl) wallets.push('eternl');\n        if ((_window_cardano2 = window.cardano) === null || _window_cardano2 === void 0 ? void 0 : _window_cardano2.nami) wallets.push('nami');\n        if ((_window_cardano3 = window.cardano) === null || _window_cardano3 === void 0 ? void 0 : _window_cardano3.flint) wallets.push('flint');\n        return wallets;\n    };\n    const connect = async (walletName)=>{\n        if ( false || !BrowserWallet) return;\n        setConnecting(true);\n        try {\n            const browserWallet = await BrowserWallet.enable(walletName);\n            setWallet(browserWallet);\n            // Get wallet address\n            const addresses = await browserWallet.getUsedAddresses();\n            if (addresses.length > 0) {\n                setAddress(addresses[0]);\n            }\n            // Get wallet balance\n            try {\n                const balance = await browserWallet.getBalance();\n                setBalance(balance);\n            } catch (error) {\n                console.warn('Could not fetch balance:', error);\n                setBalance('0');\n            }\n            setConnected(true);\n            // Store connection state\n            localStorage.setItem('vintrek_wallet', walletName);\n        } catch (error) {\n            console.error('Failed to connect wallet:', error);\n            throw error;\n        } finally{\n            setConnecting(false);\n        }\n    };\n    const disconnect = ()=>{\n        setWallet(null);\n        setConnected(false);\n        setAddress(null);\n        setBalance(null);\n        localStorage.removeItem('vintrek_wallet');\n    };\n    // Auto-reconnect on page load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WalletProvider.useEffect\": ()=>{\n            const savedWallet = localStorage.getItem('vintrek_wallet');\n            if (savedWallet && getAvailableWallets().includes(savedWallet)) {\n                connect(savedWallet).catch(console.error);\n            }\n        }\n    }[\"WalletProvider.useEffect\"], []);\n    const value = {\n        wallet,\n        connected,\n        connecting,\n        address,\n        balance,\n        connect,\n        disconnect,\n        getAvailableWallets\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WalletContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\providers\\\\WalletProvider.tsx\",\n        lineNumber: 121,\n        columnNumber: 5\n    }, this);\n}\n_s1(WalletProvider, \"dSBI9X6BQq4oMvol2/Gn4R+d8Wg=\");\n_c = WalletProvider;\nvar _c;\n$RefreshReg$(_c, \"WalletProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/providers/WalletProvider.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/trails/BookingModal.tsx":
/*!************************************************!*\
  !*** ./src/components/trails/BookingModal.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BookingModal: () => (/* binding */ BookingModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_MapPin_TrendingUp_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,MapPin,TrendingUp,Users,Wallet,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_MapPin_TrendingUp_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,MapPin,TrendingUp,Users,Wallet,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_MapPin_TrendingUp_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,MapPin,TrendingUp,Users,Wallet,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_MapPin_TrendingUp_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,MapPin,TrendingUp,Users,Wallet,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_MapPin_TrendingUp_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,MapPin,TrendingUp,Users,Wallet,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_MapPin_TrendingUp_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,MapPin,TrendingUp,Users,Wallet,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_MapPin_TrendingUp_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,MapPin,TrendingUp,Users,Wallet,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Clock_MapPin_TrendingUp_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Clock,MapPin,TrendingUp,Users,Wallet,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.mjs\");\n/* harmony import */ var _components_providers_WalletProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/WalletProvider */ \"(app-pages-browser)/./src/components/providers/WalletProvider.tsx\");\n/* harmony import */ var _lib_blockchain__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/blockchain */ \"(app-pages-browser)/./src/lib/blockchain.ts\");\n/* __next_internal_client_entry_do_not_use__ BookingModal auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction BookingModal(param) {\n    let { trail, isOpen, onClose } = param;\n    _s();\n    const { connected, wallet } = (0,_components_providers_WalletProvider__WEBPACK_IMPORTED_MODULE_2__.useWallet)();\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [participants, setParticipants] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [isBooking, setIsBooking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [bookingError, setBookingError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    if (!isOpen) return null;\n    const totalPrice = trail.price * participants;\n    const availableSpots = trail.maxCapacity - trail.currentBookings;\n    const handleBooking = async ()=>{\n        if (!connected || !wallet) {\n            setBookingError('Please connect your wallet to book this trail.');\n            return;\n        }\n        if (!selectedDate) {\n            setBookingError('Please select a date for your adventure.');\n            return;\n        }\n        if (participants > availableSpots) {\n            setBookingError(\"Only \".concat(availableSpots, \" spots available.\"));\n            return;\n        }\n        setIsBooking(true);\n        setBookingError('');\n        try {\n            _lib_blockchain__WEBPACK_IMPORTED_MODULE_3__.blockchainService.setWallet(wallet);\n            // Create booking transaction\n            const txHash = await _lib_blockchain__WEBPACK_IMPORTED_MODULE_3__.blockchainService.createBookingTransaction(trail.id, totalPrice / 1000, selectedDate);\n            alert(\"Booking successful! Transaction hash: \".concat(txHash));\n            onClose();\n        } catch (error) {\n            console.error('Booking failed:', error);\n            setBookingError('Booking failed. Please try again or check your wallet balance.');\n        } finally{\n            setIsBooking(false);\n        }\n    };\n    const getTomorrowDate = ()=>{\n        const tomorrow = new Date();\n        tomorrow.setDate(tomorrow.getDate() + 1);\n        return tomorrow.toISOString().split('T')[0];\n    };\n    const getMaxDate = ()=>{\n        const maxDate = new Date();\n        maxDate.setDate(maxDate.getDate() + 90) // 3 months ahead\n        ;\n        return maxDate.toISOString().split('T')[0];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-6 border-b\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: \"Book Your Adventure\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_MapPin_TrendingUp_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-b\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-2\",\n                            children: trail.name\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-4 text-sm text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_MapPin_TrendingUp_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2 text-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: trail.location\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_MapPin_TrendingUp_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2 text-green-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: trail.distance\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_MapPin_TrendingUp_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2 text-purple-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: trail.duration\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 rounded-full text-xs \".concat(trail.difficulty === 'Easy' ? 'bg-green-100 text-green-800' : trail.difficulty === 'Moderate' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'),\n                                        children: trail.difficulty\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-3\",\n                            children: trail.description\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Select Date\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_MapPin_TrendingUp_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"date\",\n                                                    value: selectedDate,\n                                                    onChange: (e)=>setSelectedDate(e.target.value),\n                                                    min: getTomorrowDate(),\n                                                    max: getMaxDate(),\n                                                    className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Participants\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_MapPin_TrendingUp_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: participants,\n                                                    onChange: (e)=>setParticipants(parseInt(e.target.value)),\n                                                    className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent\",\n                                                    children: Array.from({\n                                                        length: Math.min(availableSpots, 10)\n                                                    }, (_, i)=>i + 1).map((num)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: num,\n                                                            children: [\n                                                                num,\n                                                                \" \",\n                                                                num === 1 ? 'person' : 'people'\n                                                            ]\n                                                        }, num, true, {\n                                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mt-1\",\n                                            children: [\n                                                availableSpots,\n                                                \" spots available\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 p-4 bg-gray-50 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Price per person:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: [\n                                                \"₨\",\n                                                trail.price.toLocaleString()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Participants:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: participants\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t pt-2 flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: \"Total:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg font-bold text-green-600\",\n                                            children: [\n                                                \"₨\",\n                                                totalPrice.toLocaleString()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 p-4 bg-green-50 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium text-green-800 mb-2\",\n                                    children: \"Blockchain Rewards\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-green-700 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"✓ Earn 50 TREK tokens per person upon completion\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"✓ Mint unique trail completion NFT\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"✓ On-chain booking confirmation\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this),\n                        bookingError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 p-4 bg-red-50 rounded-lg flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_MapPin_TrendingUp_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-5 w-5 text-red-500 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-red-700\",\n                                    children: bookingError\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 13\n                        }, this),\n                        !connected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 p-4 bg-yellow-50 rounded-lg flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Clock_MapPin_TrendingUp_Users_Wallet_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-5 w-5 text-yellow-500 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-yellow-700\",\n                                    children: \"Connect your Cardano wallet to proceed with booking\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 flex space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onClose,\n                                    className: \"flex-1 py-3 px-4 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleBooking,\n                                    disabled: !connected || isBooking || !selectedDate || !trail.available,\n                                    className: \"flex-1 py-3 px-4 rounded-lg font-medium transition-colors \".concat(connected && !isBooking && selectedDate && trail.available ? 'bg-green-600 text-white hover:bg-green-700' : 'bg-gray-300 text-gray-500 cursor-not-allowed'),\n                                    children: isBooking ? 'Processing...' : \"Book for ₨\".concat(totalPrice.toLocaleString())\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-4 text-xs text-gray-500 text-center\",\n                            children: \"By booking, you agree to our terms and conditions. Cancellations must be made 24 hours in advance.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n            lineNumber: 80,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\BookingModal.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, this);\n}\n_s(BookingModal, \"rHoMy80eQGHCgA32qI/p+xep85s=\", false, function() {\n    return [\n        _components_providers_WalletProvider__WEBPACK_IMPORTED_MODULE_2__.useWallet\n    ];\n});\n_c = BookingModal;\nvar _c;\n$RefreshReg$(_c, \"BookingModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/trails/BookingModal.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/trails/TrailCard.tsx":
/*!*********************************************!*\
  !*** ./src/components/trails/TrailCard.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TrailCard: () => (/* binding */ TrailCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_MapPin_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,MapPin,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.mjs\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_MapPin_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,MapPin,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.mjs\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_MapPin_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,MapPin,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.mjs\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_MapPin_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,MapPin,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.mjs\");\n/* harmony import */ var _BookingModal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./BookingModal */ \"(app-pages-browser)/./src/components/trails/BookingModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ TrailCard auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction TrailCard(param) {\n    let { trail } = param;\n    _s();\n    const [showBookingModal, setShowBookingModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const getDifficultyColor = (difficulty)=>{\n        switch(difficulty.toLowerCase()){\n            case 'easy':\n                return 'bg-green-100 text-green-800';\n            case 'moderate':\n                return 'bg-yellow-100 text-yellow-800';\n            case 'hard':\n                return 'bg-red-100 text-red-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const handleBookTrail = ()=>{\n        if (!trail.available) return;\n        setShowBookingModal(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                onClick: ()=>window.location.href = \"/trails/\".concat(trail.id),\n                className: \"cursor-pointer\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative h-48 bg-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-4 left-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(getDifficultyColor(trail.difficulty)),\n                                children: trail.difficulty\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-4 right-4\",\n                            children: trail.available ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium\",\n                                children: \"Available\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium\",\n                                children: \"Booked\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-full flex items-center justify-center text-gray-400\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_MapPin_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"h-12 w-12\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-2\",\n                                children: trail.name\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center text-gray-600 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_MapPin_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-4 w-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: trail.location\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 text-sm\",\n                                children: trail.description\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-4 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_MapPin_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: trail.distance\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_MapPin_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 text-green-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: trail.duration\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t pt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-lg font-semibold text-gray-900\",\n                                                children: [\n                                                    \"₨\",\n                                                    trail.price.toLocaleString()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"per person\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium text-green-600\",\n                                                children: \"Earn Rewards\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: \"50 TREK + NFT\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: handleBookTrail,\n                                        disabled: !trail.available,\n                                        className: \"flex-1 py-2 px-4 rounded-lg font-medium transition-colors \".concat(trail.available ? 'bg-green-600 text-white hover:bg-green-700' : 'bg-gray-300 text-gray-500 cursor-not-allowed'),\n                                        children: trail.available ? 'Book Trail' : 'Unavailable'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        title: \"View availability calendar\",\n                                        className: \"px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors\",\n                                        onClick: ()=>setShowBookingModal(true),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_MapPin_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-3 bg-blue-50 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs font-medium text-blue-800 mb-1\",\n                                children: \"Blockchain Features\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between text-xs text-blue-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"✓ On-chain booking\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"✓ NFT certificate\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"✓ Token rewards\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BookingModal__WEBPACK_IMPORTED_MODULE_2__.BookingModal, {\n                trail: trail,\n                isOpen: showBookingModal,\n                onClose: ()=>setShowBookingModal(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\trails\\\\TrailCard.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n_s(TrailCard, \"9KLO1rbu0X8wOiK4f81pnOwuofI=\");\n_c = TrailCard;\nvar _c;\n$RefreshReg$(_c, \"TrailCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/trails/TrailCard.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/StatsCard.tsx":
/*!*****************************************!*\
  !*** ./src/components/ui/StatsCard.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StatsCard: () => (/* binding */ StatsCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ StatsCard auto */ \nfunction StatsCard(param) {\n    let { icon: Icon, label, value } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                    className: \"h-6 w-6 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\StatsCard.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\StatsCard.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-2xl font-bold text-gray-900 mb-1\",\n                children: value\n            }, void 0, false, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\StatsCard.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-gray-600 text-sm\",\n                children: label\n            }, void 0, false, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\StatsCard.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\ui\\\\StatsCard.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n_c = StatsCard;\nvar _c;\n$RefreshReg$(_c, \"StatsCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL1N0YXRzQ2FyZC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBVU8sU0FBU0EsVUFBVSxLQUE0QztRQUE1QyxFQUFFQyxNQUFNQyxJQUFJLEVBQUVDLEtBQUssRUFBRUMsS0FBSyxFQUFrQixHQUE1QztJQUN4QixxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNEO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDSjtvQkFBS0ksV0FBVTs7Ozs7Ozs7Ozs7MEJBRWxCLDhEQUFDRDtnQkFBSUMsV0FBVTswQkFBeUNGOzs7Ozs7MEJBQ3hELDhEQUFDQztnQkFBSUMsV0FBVTswQkFBeUJIOzs7Ozs7Ozs7Ozs7QUFHOUM7S0FWZ0JIIiwic291cmNlcyI6WyJEOlxcaGFja290aG9uXFxCbG9ja2NoYWluXFxWaW50cmVrXFxzcmNcXGNvbXBvbmVudHNcXHVpXFxTdGF0c0NhcmQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyBMdWNpZGVJY29uIH0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuXG5pbnRlcmZhY2UgU3RhdHNDYXJkUHJvcHMge1xuICBpY29uOiBMdWNpZGVJY29uXG4gIGxhYmVsOiBzdHJpbmdcbiAgdmFsdWU6IHN0cmluZ1xufVxuXG5leHBvcnQgZnVuY3Rpb24gU3RhdHNDYXJkKHsgaWNvbjogSWNvbiwgbGFiZWwsIHZhbHVlIH06IFN0YXRzQ2FyZFByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmVlbi0xMDAgdy0xMiBoLTEyIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBteC1hdXRvIG1iLTNcIj5cbiAgICAgICAgPEljb24gY2xhc3NOYW1lPVwiaC02IHctNiB0ZXh0LWdyZWVuLTYwMFwiIC8+XG4gICAgICA8L2Rpdj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItMVwiPnt2YWx1ZX08L2Rpdj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCB0ZXh0LXNtXCI+e2xhYmVsfTwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiU3RhdHNDYXJkIiwiaWNvbiIsIkljb24iLCJsYWJlbCIsInZhbHVlIiwiZGl2IiwiY2xhc3NOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/StatsCard.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/wallet/WalletConnect.tsx":
/*!*************************************************!*\
  !*** ./src/components/wallet/WalletConnect.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalletConnect: () => (/* binding */ WalletConnect)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Copy,ExternalLink,LogOut,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Copy,ExternalLink,LogOut,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Copy,ExternalLink,LogOut,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Copy,ExternalLink,LogOut,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Copy,ExternalLink,LogOut,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.mjs\");\n/* harmony import */ var _components_providers_WalletProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/WalletProvider */ \"(app-pages-browser)/./src/components/providers/WalletProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ WalletConnect auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction WalletConnect(param) {\n    let { onConnectionChange } = param;\n    _s();\n    const { connected, connecting, address, balance, connect, disconnect, getAvailableWallets } = (0,_components_providers_WalletProvider__WEBPACK_IMPORTED_MODULE_2__.useWallet)();\n    const [showWalletModal, setShowWalletModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAccountModal, setShowAccountModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const availableWallets = getAvailableWallets();\n    const handleConnect = async (walletName)=>{\n        try {\n            await connect(walletName);\n            setShowWalletModal(false);\n            onConnectionChange === null || onConnectionChange === void 0 ? void 0 : onConnectionChange(true);\n        } catch (error) {\n            console.error('Connection failed:', error);\n            alert('Failed to connect wallet. Please try again.');\n        }\n    };\n    const handleDisconnect = ()=>{\n        disconnect();\n        setShowAccountModal(false);\n        onConnectionChange === null || onConnectionChange === void 0 ? void 0 : onConnectionChange(false);\n    };\n    const copyAddress = ()=>{\n        if (address) {\n            navigator.clipboard.writeText(address);\n            alert('Address copied to clipboard!');\n        }\n    };\n    const formatAddress = (addr)=>{\n        return \"\".concat(addr.slice(0, 8), \"...\").concat(addr.slice(-8));\n    };\n    const formatBalance = (bal)=>{\n        try {\n            const lovelace = parseInt(bal);\n            const ada = lovelace / 1000000;\n            return \"\".concat(ada.toFixed(2), \" ADA\");\n        } catch (e) {\n            return '0.00 ADA';\n        }\n    };\n    if (connected && address) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>setShowAccountModal(!showAccountModal),\n                    className: \"flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"hidden sm:inline\",\n                            children: formatAddress(address)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this),\n                showAccountModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border z-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-b\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"Connected Wallet\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowAccountModal(false),\n                                            className: \"text-gray-400 hover:text-gray-600\",\n                                            children: \"\\xd7\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: balance ? formatBalance(balance) : 'Loading...'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-b\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Address\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-mono text-sm\",\n                                                children: formatAddress(address)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: copyAddress,\n                                                className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                                title: \"Copy address\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>window.open(\"https://cardanoscan.io/address/\".concat(address), '_blank'),\n                                                className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                                title: \"View on explorer\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleDisconnect,\n                                className: \"w-full flex items-center justify-center space-x-2 text-red-600 hover:bg-red-50 py-2 px-4 rounded-lg transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Disconnect\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setShowWalletModal(true),\n                disabled: connecting,\n                className: \"flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: connecting ? 'Connecting...' : 'Connect Wallet'\n                    }, void 0, false, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            showWalletModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"Connect Wallet\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowWalletModal(false),\n                                    className: \"text-gray-400 hover:text-gray-600\",\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, this),\n                        availableWallets.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-12 w-12 text-gray-300 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                    children: \"No Wallets Found\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: \"Please install a Cardano wallet to continue.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"https://www.lace.io/\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"block w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors\",\n                                            children: \"Install Lace Wallet\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"https://eternl.io/\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"block w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors\",\n                                            children: \"Install Eternl Wallet\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: \"Choose a wallet to connect to VinTrek:\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 17\n                                }, this),\n                                availableWallets.map((walletName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleConnect(walletName),\n                                        disabled: connecting,\n                                        className: \"w-full flex items-center space-x-3 p-4 border rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Copy_ExternalLink_LogOut_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"h-4 w-4 text-gray-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 text-left\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium capitalize\",\n                                                        children: walletName\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: [\n                                                            walletName === 'lace' && 'Lace Wallet',\n                                                            walletName === 'eternl' && 'Eternl Wallet',\n                                                            walletName === 'nami' && 'Nami Wallet',\n                                                            walletName === 'flint' && 'Flint Wallet'\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, walletName, true, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 19\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n                lineNumber: 146,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\wallet\\\\WalletConnect.tsx\",\n        lineNumber: 135,\n        columnNumber: 5\n    }, this);\n}\n_s(WalletConnect, \"3BB4weGfZX/q4DYQeyNMdzoJGDg=\", false, function() {\n    return [\n        _components_providers_WalletProvider__WEBPACK_IMPORTED_MODULE_2__.useWallet\n    ];\n});\n_c = WalletConnect;\nvar _c;\n$RefreshReg$(_c, \"WalletConnect\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/wallet/WalletConnect.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/blockchain.ts":
/*!*******************************!*\
  !*** ./src/lib/blockchain.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BLOCKCHAIN_CONFIG: () => (/* binding */ BLOCKCHAIN_CONFIG),\n/* harmony export */   BlockchainService: () => (/* binding */ BlockchainService),\n/* harmony export */   TREK_TOKEN: () => (/* binding */ TREK_TOKEN),\n/* harmony export */   blockchainService: () => (/* binding */ blockchainService),\n/* harmony export */   formatAdaToLovelace: () => (/* binding */ formatAdaToLovelace),\n/* harmony export */   formatLovelaceToAda: () => (/* binding */ formatLovelaceToAda),\n/* harmony export */   generateAssetName: () => (/* binding */ generateAssetName)\n/* harmony export */ });\n// Dynamic imports for client-side only\nlet BrowserWallet = null;\nlet Transaction = null;\nlet AssetMetadata = null;\nif (true) {\n    __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_meshsdk_core_dist_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @meshsdk/core */ \"(app-pages-browser)/./node_modules/@meshsdk/core/dist/index.js\")).then((module)=>{\n        BrowserWallet = module.BrowserWallet;\n        Transaction = module.Transaction;\n        AssetMetadata = module.AssetMetadata;\n    });\n}\n// Blockchain configuration\nconst BLOCKCHAIN_CONFIG = {\n    network: \"testnet\" || 0,\n    blockfrostApiKey: \"testnetYourProjectIdHere\" || 0,\n    blockfrostUrl: \"https://cardano-testnet.blockfrost.io/api/v0\" || 0,\n    nftPolicyId: \"placeholder_nft_policy_id\" || 0,\n    tokenPolicyId: \"placeholder_token_policy_id\" || 0,\n    scriptAddress: \"addr_test1placeholder_script_address\" || 0\n};\n// TREK Token configuration\nconst TREK_TOKEN = {\n    symbol: 'TREK',\n    decimals: 6,\n    policyId: BLOCKCHAIN_CONFIG.tokenPolicyId,\n    assetName: '54524b'\n};\n// Blockchain service class\nclass BlockchainService {\n    setWallet(wallet) {\n        this.wallet = wallet;\n    }\n    // Get wallet balance in ADA\n    async getWalletBalance() {\n        if (!this.wallet || \"object\" === 'undefined') throw new Error('Wallet not connected');\n        try {\n            const balance = await this.wallet.getBalance();\n            return balance;\n        } catch (error) {\n            console.error('Error fetching wallet balance:', error);\n            throw error;\n        }\n    }\n    // Get wallet assets (NFTs and tokens)\n    async getWalletAssets() {\n        if (!this.wallet) throw new Error('Wallet not connected');\n        try {\n            const assets = await this.wallet.getAssets();\n            return assets;\n        } catch (error) {\n            console.error('Error fetching wallet assets:', error);\n            throw error;\n        }\n    }\n    // Get TREK token balance\n    async getTrekTokenBalance() {\n        try {\n            const assets = await this.getWalletAssets();\n            const trekAsset = assets.find((asset)=>asset.unit.startsWith(TREK_TOKEN.policyId));\n            if (trekAsset) {\n                return parseInt(trekAsset.quantity) / Math.pow(10, TREK_TOKEN.decimals);\n            }\n            return 0;\n        } catch (error) {\n            console.error('Error fetching TREK token balance:', error);\n            return 0;\n        }\n    }\n    // Get trail NFTs owned by wallet\n    async getTrailNFTs() {\n        try {\n            const assets = await this.getWalletAssets();\n            const nfts = assets.filter((asset)=>asset.unit.startsWith(BLOCKCHAIN_CONFIG.nftPolicyId) && asset.quantity === '1');\n            // Fetch metadata for each NFT\n            const nftsWithMetadata = await Promise.all(nfts.map(async (nft)=>{\n                try {\n                    const metadata = await this.fetchNFTMetadata(nft.unit);\n                    return {\n                        ...nft,\n                        metadata\n                    };\n                } catch (error) {\n                    console.error(\"Error fetching metadata for NFT \".concat(nft.unit, \":\"), error);\n                    return {\n                        ...nft,\n                        metadata: null\n                    };\n                }\n            }));\n            return nftsWithMetadata;\n        } catch (error) {\n            console.error('Error fetching trail NFTs:', error);\n            return [];\n        }\n    }\n    // Fetch NFT metadata from blockchain\n    async fetchNFTMetadata(assetId) {\n        try {\n            const response = await fetch(\"\".concat(BLOCKCHAIN_CONFIG.blockfrostUrl, \"/assets/\").concat(assetId), {\n                headers: {\n                    'project_id': BLOCKCHAIN_CONFIG.blockfrostApiKey\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const assetData = await response.json();\n            if (assetData.onchain_metadata) {\n                return assetData.onchain_metadata;\n            }\n            return null;\n        } catch (error) {\n            console.error('Error fetching NFT metadata:', error);\n            return null;\n        }\n    }\n    // Create booking transaction\n    async createBookingTransaction(trailId, amount, date) {\n        if (!this.wallet || \"object\" === 'undefined' || !Transaction) throw new Error('Wallet not connected or Transaction not available');\n        try {\n            const walletAddress = await this.wallet.getChangeAddress();\n            // Create booking metadata\n            const bookingMetadata = {\n                trail_id: trailId,\n                booking_date: date,\n                amount: amount,\n                timestamp: new Date().toISOString(),\n                hiker_address: walletAddress\n            };\n            // Build transaction (simplified - in production, this would interact with smart contracts)\n            const tx = new Transaction({\n                initiator: this.wallet\n            });\n            // Add metadata to transaction\n            tx.setMetadata(674, bookingMetadata);\n            // Send payment to script address (or trail operator)\n            tx.sendLovelace(BLOCKCHAIN_CONFIG.scriptAddress, (amount * 1000000).toString() // Convert ADA to Lovelace\n            );\n            const unsignedTx = await tx.build();\n            const signedTx = await this.wallet.signTx(unsignedTx);\n            const txHash = await this.wallet.submitTx(signedTx);\n            return txHash;\n        } catch (error) {\n            console.error('Error creating booking transaction:', error);\n            throw error;\n        }\n    }\n    // Mint trail completion NFT\n    async mintTrailNFT(trailData) {\n        if (!this.wallet || \"object\" === 'undefined' || !Transaction) throw new Error('Wallet not connected or Transaction not available');\n        try {\n            const walletAddress = await this.wallet.getChangeAddress();\n            const completionDate = new Date().toISOString();\n            // Create NFT metadata\n            const metadata = {\n                name: \"\".concat(trailData.trailName, \" Completion Certificate\"),\n                description: \"Proof of completion for \".concat(trailData.trailName, \" trail in \").concat(trailData.location),\n                image: \"ipfs://QmTrailNFTImage\".concat(Date.now()),\n                attributes: {\n                    trail_name: trailData.trailName,\n                    location: trailData.location,\n                    difficulty: trailData.difficulty,\n                    completion_date: completionDate,\n                    coordinates: trailData.coordinates,\n                    hiker_address: walletAddress\n                }\n            };\n            // Generate unique asset name\n            const assetName = \"VinTrekNFT\".concat(Date.now());\n            // Build minting transaction (simplified)\n            const tx = new Transaction({\n                initiator: this.wallet\n            });\n            // Add minting logic here (requires smart contract integration)\n            // This is a placeholder - actual implementation would use Mesh SDK's minting functions\n            const unsignedTx = await tx.build();\n            const signedTx = await this.wallet.signTx(unsignedTx);\n            const txHash = await this.wallet.submitTx(signedTx);\n            return txHash;\n        } catch (error) {\n            console.error('Error minting trail NFT:', error);\n            throw error;\n        }\n    }\n    // Mint TREK tokens as rewards\n    async mintTrekTokens(amount, reason) {\n        if (!this.wallet || \"object\" === 'undefined' || !Transaction) throw new Error('Wallet not connected or Transaction not available');\n        try {\n            const walletAddress = await this.wallet.getChangeAddress();\n            // Create reward metadata\n            const rewardMetadata = {\n                recipient: walletAddress,\n                amount: amount,\n                reason: reason,\n                timestamp: new Date().toISOString()\n            };\n            // Build token minting transaction (simplified)\n            const tx = new Transaction({\n                initiator: this.wallet\n            });\n            // Add metadata\n            tx.setMetadata(674, rewardMetadata);\n            // Add token minting logic here (requires smart contract integration)\n            const unsignedTx = await tx.build();\n            const signedTx = await this.wallet.signTx(unsignedTx);\n            const txHash = await this.wallet.submitTx(signedTx);\n            return txHash;\n        } catch (error) {\n            console.error('Error minting TREK tokens:', error);\n            throw error;\n        }\n    }\n    // Verify transaction on blockchain\n    async verifyTransaction(txHash) {\n        try {\n            const response = await fetch(\"\".concat(BLOCKCHAIN_CONFIG.blockfrostUrl, \"/txs/\").concat(txHash), {\n                headers: {\n                    'project_id': BLOCKCHAIN_CONFIG.blockfrostApiKey\n                }\n            });\n            if (response.ok) {\n                const txData = await response.json();\n                return txData.block !== null // Transaction is confirmed if it's in a block\n                ;\n            }\n            return false;\n        } catch (error) {\n            console.error('Error verifying transaction:', error);\n            return false;\n        }\n    }\n    constructor(wallet = null){\n        this.wallet = null;\n        this.wallet = wallet;\n    }\n}\n// Utility functions\nconst formatLovelaceToAda = (lovelace)=>{\n    return parseInt(lovelace) / 1000000;\n};\nconst formatAdaToLovelace = (ada)=>{\n    return (ada * 1000000).toString();\n};\nconst generateAssetName = (prefix)=>{\n    const timestamp = Date.now();\n    const random = Math.random().toString(36).substr(2, 9);\n    return \"\".concat(prefix).concat(timestamp).concat(random);\n};\n// Export singleton instance\nconst blockchainService = new BlockchainService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/blockchain.ts\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Chackothon%5C%5CBlockchain%5C%5CVintrek%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);