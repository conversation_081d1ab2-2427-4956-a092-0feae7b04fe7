import { HexBlob } from '@cardano-sdk/util';
import type * as Cardano from '../../../Cardano';
export declare class TimelockExpiry {
    #private;
    constructor(slot: Cardano.Slot);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): TimelockExpiry;
    toCore(): Cardano.RequireTimeBeforeScript;
    static fromCore(script: Cardano.RequireTimeBeforeScript): TimelockExpiry;
    slot(): Cardano.Slot;
    setSlot(slot: Cardano.Slot): void;
}
//# sourceMappingURL=TimelockExpiry.d.ts.map