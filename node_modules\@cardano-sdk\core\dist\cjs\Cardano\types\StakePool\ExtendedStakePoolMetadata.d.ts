import { PoolIdHex } from './primitives';
export declare type SerialNumber = number;
export declare type DeclaredPoolLocation = string;
export declare enum ExtendedPoolStatus {
    Active = "active",
    Retired = "retired",
    Offline = "offline",
    Experimental = "experimental",
    Private = "private"
}
export declare type PrimaryContactPreference = string;
export declare type EmailAddress = string;
export declare type FacebookAccount = string;
export declare type GithubAccount = string;
export declare type RSSFeed = string;
export declare type TelegramAccount = string;
export declare type TwitterAccount = string;
export declare type YoutubeAccount = string;
export declare type DiscordAccount = string;
export declare type TwitchAccount = string;
export declare type PoolIconInPNGFileFormat64X64Px = string;
export declare type PoolLogoInPNGFileFormat = string;
export declare type PoolLogoInSVGFileFormat = string;
export declare type PoolPrimaryColor = string;
export declare type PoolSecondaryColor = string;
export declare type TheITNPoolOwnerPublicKey = string;
export declare type TheSecretKeyGeneratedWitness = string;
export declare type MeAboutInfo = string;
export declare type ServerAboutInfo = string;
export declare type CompanyAboutInfo = string;
export declare type CompanyName = string;
export declare type CompanyAddress = string;
export declare type CompanyCity = string;
export declare type CompanyCountry = string;
export declare type CompanyId = string;
export declare type CompanyVatId = string;
export interface PoolContactData {
    primary?: PrimaryContactPreference;
    email?: EmailAddress;
    facebook?: FacebookAccount;
    github?: GithubAccount;
    feed?: RSSFeed;
    telegram?: TelegramAccount;
    twitter?: TwitterAccount;
    twitch?: TwitchAccount;
    youtube?: YoutubeAccount;
    discord?: DiscordAccount;
    [k: string]: unknown;
}
export interface ThePoolsMediaAssets {
    icon_png_64x64?: PoolIconInPNGFileFormat64X64Px;
    logo_png?: PoolLogoInPNGFileFormat;
    logo_svg?: PoolLogoInSVGFileFormat;
    color_fg?: PoolPrimaryColor;
    color_bg?: PoolSecondaryColor;
    [k: string]: unknown;
}
export interface ITNVerification {
    owner?: TheITNPoolOwnerPublicKey;
    witness?: TheSecretKeyGeneratedWitness;
    [k: string]: unknown;
}
export interface PoolCompanyInfo {
    name?: CompanyName;
    addr?: CompanyAddress;
    city?: CompanyCity;
    country?: CompanyCountry;
    company_id?: CompanyId;
    vat_id?: CompanyVatId;
    [k: string]: unknown;
}
export interface PoolAboutInfo {
    me?: MeAboutInfo;
    server?: ServerAboutInfo;
    company?: CompanyAboutInfo;
    [k: string]: unknown;
}
export interface ExtendedStakePoolMetadataFields {
    id?: PoolIdHex;
    location?: DeclaredPoolLocation;
    status?: ExtendedPoolStatus;
    contact?: PoolContactData;
    media_assets?: ThePoolsMediaAssets;
    itn?: ITNVerification;
    company?: PoolCompanyInfo;
    about?: PoolAboutInfo;
    [k: string]: unknown;
}
export interface ExtendedStakePoolMetadata {
    serial?: SerialNumber;
    pool: ExtendedStakePoolMetadataFields;
    [k: string]: unknown;
}
//# sourceMappingURL=ExtendedStakePoolMetadata.d.ts.map