import { ExtendedStakePoolMetadata } from './ExtendedStakePoolMetadata';
import { Fraction } from '../';
import { Hash32ByteBase16 } from '@cardano-sdk/crypto';
import { Lovelace } from '../Value';
import { OpaqueString } from '@cardano-sdk/util';
import { PoolId, VrfVkHex } from './primitives';
import { Relay } from './Relay';
import { RewardAccount } from '../../Address';
export interface PoolMetadataJson {
    hash: Hash32ByteBase16;
    url: string;
}
export declare type PoolMdVk = OpaqueString<'PoolMdVk'>;
export declare const PoolMdVk: (target: string) => PoolMdVk;
export interface Cip6MetadataFields {
    extDataUrl?: string;
    extSigUrl?: string;
    extVkey?: PoolMdVk;
}
export interface APMetadataFields {
    extended?: string;
}
export interface StakePoolMainMetadataFields {
    ticker: string;
    name: string;
    description: string;
    homepage: string;
}
export interface StakePoolExtendedMetadataFields {
    ext?: ExtendedStakePoolMetadata | null | undefined;
}
export declare type StakePoolMetadata = Cip6MetadataFields & APMetadataFields & StakePoolMainMetadataFields & StakePoolExtendedMetadataFields;
export interface PoolParameters {
    id: PoolId;
    rewardAccount: RewardAccount;
    pledge: Lovelace;
    cost: Lovelace;
    margin: Fraction;
    metadataJson?: PoolMetadataJson;
    metadata?: StakePoolMetadata;
    relays: Relay[];
    owners: RewardAccount[];
    vrf: VrfVkHex;
}
//# sourceMappingURL=PoolParameters.d.ts.map