'use client'

import { <PERSON>actNode, useEffect, useState } from 'react'
import { WalletProvider } from './WalletProvider'

interface ClientWalletProviderProps {
  children: ReactNode
}

export function ClientWalletProvider({ children }: ClientWalletProviderProps) {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return <>{children}</>
  }

  return <WalletProvider>{children}</WalletProvider>
}
