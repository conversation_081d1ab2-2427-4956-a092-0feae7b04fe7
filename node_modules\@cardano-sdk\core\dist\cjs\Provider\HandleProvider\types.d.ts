import { Asset, Cardano, HttpProviderConfigPaths, Point, Provider } from '../..';
export declare type Handle = string;
export interface HandleResolution {
    policyId: Cardano.PolicyId;
    handle: Handle;
    cardanoAddress: Cardano.PaymentAddress;
    hasDatum: boolean;
    defaultForStakeCredential?: Handle;
    defaultForPaymentCredential?: Handle;
    image?: Asset.Uri;
    backgroundImage?: Asset.Uri;
    profilePic?: Asset.Uri;
    resolvedAt?: Point;
    parentHandle?: Handle;
}
export interface ResolveHandlesArgs {
    handles: Handle[];
}
export interface HandleProvider extends Provider {
    resolveHandles(args: ResolveHandlesArgs): Promise<Array<HandleResolution | null>>;
    getPolicyIds(): Promise<Cardano.PolicyId[]>;
}
export declare const handleProviderPaths: HttpProviderConfigPaths<HandleProvider>;
//# sourceMappingURL=types.d.ts.map