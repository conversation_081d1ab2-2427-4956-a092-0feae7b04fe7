import { DRepID } from './DRepID';
import { OpaqueString } from '@cardano-sdk/util';
import { HydratedTx, HydratedTxIn, Tx, TxIn, TxOut } from '../types';
import { NetworkId } from '../ChainId';
import { RewardAccount } from './RewardAccount';
export declare type PaymentAddress = OpaqueString<'PaymentAddress'>;
export declare const PaymentAddress: (value: string) => PaymentAddress;
export declare const isAddressWithin: (addresses: PaymentAddress[]) => ({ address }: {
    address: PaymentAddress;
}) => boolean;
export declare const inputsWithAddresses: (tx: HydratedTx, ownAddresses: PaymentAddress[]) => HydratedTxIn[];
export declare type ResolveOptions = {
    hints: Tx[];
};
export declare type ResolveInput = (txIn: TxIn, options?: ResolveOptions) => Promise<TxOut | null>;
export interface InputResolver {
    resolveInput: ResolveInput;
}
export declare const addressNetworkId: (address: RewardAccount | PaymentAddress | DRepID) => NetworkId;
//# sourceMappingURL=PaymentAddress.d.ts.map