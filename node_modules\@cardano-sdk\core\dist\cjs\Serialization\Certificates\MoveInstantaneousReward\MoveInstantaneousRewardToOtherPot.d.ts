import * as Cardano from '../../../Cardano';
import { HexBlob } from '@cardano-sdk/util';
export declare class MoveInstantaneousRewardToOtherPot {
    #private;
    constructor(pot: Cardano.MirCertificatePot, amount: Cardano.Lovelace);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): MoveInstantaneousRewardToOtherPot;
    toCore(): Cardano.MirCertificate;
    static fromCore(cert: Cardano.MirCertificate): MoveInstantaneousRewardToOtherPot;
    pot(): Cardano.MirCertificatePot;
    setPot(pot: Cardano.MirCertificatePot): void;
    getAmount(): Cardano.Lovelace;
    setAmount(amount: Cardano.Lovelace): void;
}
//# sourceMappingURL=MoveInstantaneousRewardToOtherPot.d.ts.map