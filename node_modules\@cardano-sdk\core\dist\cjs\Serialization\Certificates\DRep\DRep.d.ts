import * as Cardano from '../../../Cardano';
import * as Crypto from '@cardano-sdk/crypto';
import { DRepKind } from './DRepKind';
import { HexBlob } from '@cardano-sdk/util';
export declare class DRep {
    #private;
    constructor(kind: DRepKind, credential?: Cardano.Credential);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): DRep;
    toCore(): Cardano.DelegateRepresentative;
    static fromCore(deleg: Cardano.DelegateRepresentative): DRep;
    static newKeyHash(keyHash: Crypto.Ed25519KeyHashHex): DRep;
    static newScriptHash(scriptHash: Crypto.Hash28ByteBase16): DRep;
    static newAlwaysAbstain(): DRep;
    static newAlwaysNoConfidence(): DRep;
    kind(): DRepKind;
    toKeyHash(): Crypto.Ed25519KeyHashHex | undefined;
    toScriptHash(): Crypto.Hash28ByteBase16 | undefined;
}
//# sourceMappingURL=DRep.d.ts.map