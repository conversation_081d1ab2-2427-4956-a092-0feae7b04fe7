import { Asset, Cardano, Provider } from '../../index.js';
export interface AssetsExtraData {
    nftMetadata?: boolean;
    tokenMetadata?: boolean;
}
export interface GetAssetArgs {
    assetId: Cardano.AssetId;
    extraData?: AssetsExtraData;
}
export interface GetAssetsArgs {
    assetIds: Cardano.AssetId[];
    extraData?: AssetsExtraData;
}
export interface AssetProvider extends Provider {
    getAsset: (args: GetAssetArgs) => Promise<Asset.AssetInfo>;
    getAssets: (args: GetAssetsArgs) => Promise<Asset.AssetInfo[]>;
}
//# sourceMappingURL=types.d.ts.map