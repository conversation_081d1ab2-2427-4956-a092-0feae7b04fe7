import * as Crypto from '@cardano-sdk/crypto';
import { CborSet, Hash } from '../Common';
import { Certificate } from '../Certificates';
import { HexBlob } from '@cardano-sdk/util';
import { ProposalProcedure } from './ProposalProcedure';
import { TransactionId } from '../../Cardano/types/Transaction';
import { TransactionInput } from './TransactionInput';
import { TransactionOutput } from './TransactionOutput';
import { Update } from '../Update';
import { VotingProcedures } from './VotingProcedures';
import type * as Cardano from '../../Cardano';
declare type TransactionInputSet = CborSet<ReturnType<TransactionInput['toCore']>, TransactionInput>;
export declare class TransactionBody {
    #private;
    constructor(inputs: TransactionInputSet, outputs: Array<TransactionOutput>, fee: Cardano.Lovelace, ttl?: Cardano.Slot);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): TransactionBody;
    toCore(): Cardano.TxBody;
    static fromCore(coreTransactionBody: Cardano.TxBody): TransactionBody;
    setInputs(inputs: TransactionInputSet): void;
    inputs(): TransactionInputSet;
    setOutputs(outputs: Array<TransactionOutput>): void;
    outputs(): Array<TransactionOutput>;
    setFee(fee: Cardano.Lovelace): void;
    fee(): Cardano.Lovelace;
    setTtl(ttl: Cardano.Slot): void;
    ttl(): Cardano.Slot | undefined;
    setCerts(certs: CborSet<ReturnType<Certificate['toCore']>, Certificate>): void;
    certs(): CborSet<Cardano.Certificate, Certificate> | undefined;
    setWithdrawals(withdrawals: Map<Cardano.RewardAccount, Cardano.Lovelace>): void;
    withdrawals(): Map<Cardano.RewardAccount, Cardano.Lovelace> | undefined;
    setUpdate(update: Update): void;
    update(): Update | undefined;
    setAuxiliaryDataHash(auxiliaryDataHash: Crypto.Hash32ByteBase16): void;
    auxiliaryDataHash(): Crypto.Hash32ByteBase16 | undefined;
    setValidityStartInterval(validityStartInterval: Cardano.Slot): void;
    validityStartInterval(): Cardano.Slot | undefined;
    setMint(mint: Cardano.TokenMap): void;
    mint(): Cardano.TokenMap | undefined;
    setScriptDataHash(scriptDataHash: Crypto.Hash32ByteBase16): void;
    scriptDataHash(): Crypto.Hash32ByteBase16 | undefined;
    setCollateral(collateral: TransactionInputSet): void;
    collateral(): TransactionInputSet | undefined;
    setRequiredSigners(requiredSigners: CborSet<Crypto.Ed25519KeyHashHex, Hash<Crypto.Ed25519KeyHashHex>>): void;
    requiredSigners(): CborSet<Crypto.Ed25519KeyHashHex, Hash<Crypto.Ed25519KeyHashHex>> | undefined;
    setNetworkId(networkId: Cardano.NetworkId): void;
    networkId(): Cardano.NetworkId | undefined;
    setCollateralReturn(collateralReturn: TransactionOutput): void;
    collateralReturn(): TransactionOutput | undefined;
    setTotalCollateral(totalCollateral: Cardano.Lovelace): void;
    totalCollateral(): Cardano.Lovelace | undefined;
    setReferenceInputs(referenceInputs: TransactionInputSet): void;
    referenceInputs(): TransactionInputSet | undefined;
    setVotingProcedures(votingProcedures: VotingProcedures): void;
    votingProcedures(): VotingProcedures | undefined;
    setProposalProcedures(proposalProcedure: CborSet<ReturnType<ProposalProcedure['toCore']>, ProposalProcedure>): void;
    proposalProcedures(): CborSet<Cardano.ProposalProcedure, ProposalProcedure> | undefined;
    setCurrentTreasuryValue(currentTreasuryValue: Cardano.Lovelace): void;
    currentTreasuryValue(): Cardano.Lovelace | undefined;
    setDonation(donation: Cardano.Lovelace): void;
    donation(): Cardano.Lovelace | undefined;
    hash(): TransactionId;
    hasTaggedSets(): boolean;
}
export {};
//# sourceMappingURL=TransactionBody.d.ts.map