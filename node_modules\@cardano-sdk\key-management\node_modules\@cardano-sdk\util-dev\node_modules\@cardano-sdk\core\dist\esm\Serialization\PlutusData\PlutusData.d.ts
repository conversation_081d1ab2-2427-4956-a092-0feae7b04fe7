import * as Crypto from '@cardano-sdk/crypto';
import { ConstrPlutusData } from './ConstrPlutusData.js';
import { HexBlob } from '@cardano-sdk/util';
import { PlutusDataKind } from './PlutusDataKind.js';
import { PlutusList } from './PlutusList.js';
import { PlutusMap } from './PlutusMap.js';
import type * as Cardano from '../../Cardano/index.js';
export declare class PlutusData {
    #private;
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): PlutusData;
    toCore(): Cardano.PlutusData;
    hash(): Crypto.Hash32ByteBase16;
    static fromCore(data: Cardano.PlutusData): PlutusData;
    static newConstrPlutusData(constrPlutusData: ConstrPlutusData): PlutusData;
    static newMap(map: PlutusMap): PlutusData;
    static newList(list: PlutusList): PlutusData;
    static newInteger(integer: bigint): PlutusData;
    static newBytes(bytes: Uint8Array): PlutusData;
    getKind(): PlutusDataKind;
    asConstrPlutusData(): ConstrPlutusData | undefined;
    asMap(): PlutusMap | undefined;
    asList(): PlutusList | undefined;
    asInteger(): bigint | undefined;
    asBoundedBytes(): Uint8Array | undefined;
    equals(other: PlutusData): boolean;
    private static mapToPlutusList;
    private static mapToCorePlutusList;
    private static bufferToBigint;
}
//# sourceMappingURL=PlutusData.d.ts.map