import { HexBlob } from '@cardano-sdk/util';
import type * as Cardano from '../../Cardano/index.js';
export declare class Unregistration {
    #private;
    constructor(credential: Cardano.Credential, deposit: Cardano.Lovelace);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): Unregistration;
    toCore(): Cardano.NewStakeAddressCertificate;
    static fromCore(cert: Cardano.NewStakeAddressCertificate): Unregistration;
    stakeCredential(): Cardano.Credential;
    setStakeCredential(credential: Cardano.Credential): void;
    deposit(): Cardano.Lovelace;
    setDeposit(deposit: Cardano.Lovelace): void;
}
//# sourceMappingURL=Unregistration.d.ts.map