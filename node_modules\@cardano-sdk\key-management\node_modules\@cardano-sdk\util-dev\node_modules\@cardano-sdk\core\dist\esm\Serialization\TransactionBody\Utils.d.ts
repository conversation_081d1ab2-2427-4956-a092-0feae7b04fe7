import * as Crypto from '@cardano-sdk/crypto';
import type * as Cardano from '../../Cardano/index.js';
export declare const sortCanonically: (lhs: [string, unknown], rhs: [string, unknown]) => 1 | -1;
export declare const tokenMapToMultiAsset: (tokenMap: Cardano.TokenMap) => Map<Crypto.Hash28ByteBase16, Map<Cardano.AssetName, bigint>>;
export declare const multiAssetsToTokenMap: (multiassets: Map<Crypto.Hash28ByteBase16, Map<Cardano.AssetName, bigint>>) => Cardano.TokenMap;
//# sourceMappingURL=Utils.d.ts.map