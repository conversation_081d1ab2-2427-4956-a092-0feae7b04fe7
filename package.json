{"name": "vintrek", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@meshsdk/core": "^1.6.8", "@meshsdk/react": "^1.1.8", "@meshsdk/wallet": "^1.6.8", "autoprefixer": "^10.4.21", "clsx": "^2.0.0", "lucide-react": "^0.263.1", "next": "15.3.4", "react": "^18", "react-dom": "^18", "tailwind-merge": "^1.14.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "15.3.4", "postcss": "^8.5.6", "tailwindcss": "^3.4.1", "typescript": "^5"}}