import { HexBlob } from '@cardano-sdk/util';
import { PoolParams } from './PoolParams/index.js';
import type * as Cardano from '../../Cardano/index.js';
export declare class PoolRegistration {
    #private;
    constructor(params: PoolParams);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): PoolRegistration;
    toCore(): Cardano.PoolRegistrationCertificate;
    static fromCore(cert: Cardano.PoolRegistrationCertificate): PoolRegistration;
    poolParameters(): PoolParams;
    setPoolParameters(parameters: PoolParams): void;
}
//# sourceMappingURL=PoolRegistration.d.ts.map