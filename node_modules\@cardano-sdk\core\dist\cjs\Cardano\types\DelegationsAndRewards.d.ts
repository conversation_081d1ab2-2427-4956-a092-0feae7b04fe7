import { Lovelace } from './Value';
import { Metadatum } from './AuxiliaryData';
import { PoolId, PoolIdHex, StakePool } from './StakePool';
import { RewardAccount } from '../Address';
export interface DelegationsAndRewards {
    delegate?: PoolId;
    rewards: Lovelace;
}
export interface Delegatee {
    currentEpoch?: StakePool;
    nextEpoch?: StakePool;
    nextNextEpoch?: StakePool;
}
export declare enum StakeCredentialStatus {
    Registering = "REGISTERING",
    Registered = "REGISTERED",
    Unregistering = "UNREGISTERING",
    Unregistered = "UNREGISTERED"
}
export interface RewardAccountInfo {
    address: RewardAccount;
    credentialStatus: StakeCredentialStatus;
    delegatee?: Delegatee;
    rewardBalance: Lovelace;
    deposit?: Lovelace;
}
export interface Cip17Pool {
    id: PoolIdHex;
    weight: number;
    name?: string;
    ticker?: string;
}
export interface Cip17DelegationPortfolio {
    name: string;
    pools: Cip17Pool[];
    description?: string;
    author?: string;
}
export declare const DelegationMetadataLabel = 6862n;
export declare type DelegationPortfolioMetadata = Exclude<Cip17DelegationPortfolio, 'pools'> & {
    pools: Pick<Cip17Pool, 'id' | 'weight'>[];
};
export declare const portfolioMetadataFromCip17: (cip17: Cip17DelegationPortfolio) => DelegationPortfolioMetadata;
export declare const cip17FromMetadatum: (portfolio: Metadatum) => Cip17DelegationPortfolio;
//# sourceMappingURL=DelegationsAndRewards.d.ts.map