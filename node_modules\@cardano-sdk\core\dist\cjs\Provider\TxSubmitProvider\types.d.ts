import Cardano, { HandleResolution, Provider } from '../..';
declare type SerializedTransaction = Cardano.util.HexBlob;
export interface SubmitTxArgs {
    signedTransaction: SerializedTransaction;
    context?: {
        handleResolutions: HandleResolution[];
    };
}
export interface TxSubmitProvider extends Provider {
    submitTx: (args: SubmitTxArgs) => Promise<void>;
}
export {};
//# sourceMappingURL=types.d.ts.map