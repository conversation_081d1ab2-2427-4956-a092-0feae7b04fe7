import { Datum } from '../Common/Datum.js';
import { HexBlob } from '@cardano-sdk/util';
import { Script } from '../Scripts/index.js';
import { Value } from './Value.js';
import type * as Cardano from '../../Cardano/index.js';
export declare const REQUIRED_FIELDS_COUNT = 2;
export declare class TransactionOutput {
    #private;
    constructor(address: Cardano.Address, amount: Value);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): TransactionOutput;
    toCore(): Cardano.TxOut;
    static fromCore(coreTransactionOutput: Cardano.TxOut): TransactionOutput;
    address(): Cardano.Address;
    amount(): Value;
    datum(): Datum | undefined;
    setDatum(data: Datum): void;
    scriptRef(): Script | undefined;
    setScriptRef(script: Script): void;
}
//# sourceMappingURL=TransactionOutput.d.ts.map