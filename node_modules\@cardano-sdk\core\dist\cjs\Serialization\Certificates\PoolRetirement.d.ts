import * as Cardano from '../../Cardano';
import * as Crypto from '@cardano-sdk/crypto';
import { HexBlob } from '@cardano-sdk/util';
export declare class PoolRetirement {
    #private;
    constructor(poolKeyHash: Crypto.Ed25519KeyHashHex, epoch: Cardano.EpochNo);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): PoolRetirement;
    toCore(): Cardano.PoolRetirementCertificate;
    static fromCore(cert: Cardano.PoolRetirementCertificate): PoolRetirement;
    poolKeyHash(): Crypto.Ed25519KeyHashHex;
    setPoolKeyHash(poolKeyHash: Crypto.Ed25519KeyHashHex): void;
    epoch(): Cardano.EpochNo;
    setEpoch(epoch: Cardano.EpochNo): void;
}
//# sourceMappingURL=PoolRetirement.d.ts.map