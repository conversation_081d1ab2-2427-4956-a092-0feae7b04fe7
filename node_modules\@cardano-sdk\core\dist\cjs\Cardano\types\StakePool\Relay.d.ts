export interface RelayByAddress {
    __typename: '<PERSON>layByAddress';
    ipv4?: string;
    ipv6?: string;
    port?: number;
}
export interface RelayByName {
    __typename: 'RelayByName';
    hostname: string;
    port?: number;
}
export interface RelayByNameMultihost {
    __typename: 'RelayByNameMultihost';
    dnsName: string;
}
export declare type Relay = RelayByAddress | RelayByName | RelayByNameMultihost;
//# sourceMappingURL=Relay.d.ts.map