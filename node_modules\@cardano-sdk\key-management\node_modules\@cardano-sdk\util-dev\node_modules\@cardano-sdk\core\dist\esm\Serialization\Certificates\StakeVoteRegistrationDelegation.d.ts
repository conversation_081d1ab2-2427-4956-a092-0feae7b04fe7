import * as Crypto from '@cardano-sdk/crypto';
import { DRep } from './DRep/index.js';
import { HexBlob } from '@cardano-sdk/util';
import type * as Cardano from '../../Cardano/index.js';
export declare class StakeVoteRegistrationDelegation {
    #private;
    constructor(stakeCredential: Cardano.Credential, deposit: Cardano.Lovelace, dRep: DRep, poolKeyHash: Crypto.Ed25519KeyHashHex);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): StakeVoteRegistrationDelegation;
    toCore(): Cardano.StakeVoteRegistrationDelegationCertificate;
    static fromCore(deleg: Cardano.StakeVoteRegistrationDelegationCertificate): StakeVoteRegistrationDelegation;
    stakeCredential(): Cardano.Credential;
    deposit(): Cardano.Lovelace;
    dRep(): DRep;
    poolKeyHash(): Crypto.Ed25519KeyHashHex;
}
//# sourceMappingURL=StakeVoteRegistrationDelegation.d.ts.map