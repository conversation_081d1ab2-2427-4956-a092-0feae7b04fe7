import * as Crypto from '@cardano-sdk/crypto';
import { HexBlob } from '@cardano-sdk/util';
import type * as Cardano from '../../Cardano';
export declare class StakeDelegation {
    #private;
    constructor(credential: Cardano.Credential, poolKeyHash: Crypto.Ed25519KeyHashHex);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): StakeDelegation;
    toCore(): Cardano.StakeDelegationCertificate;
    static fromCore(cert: Cardano.StakeDelegationCertificate): StakeDelegation;
    stakeCredential(): Cardano.Credential;
    setStakeCredential(credential: Cardano.Credential): void;
    poolKeyHash(): Crypto.Ed25519KeyHashHex;
    setPoolKeyHash(poolKeyHash: Crypto.Ed25519KeyHashHex): void;
}
//# sourceMappingURL=StakeDelegation.d.ts.map