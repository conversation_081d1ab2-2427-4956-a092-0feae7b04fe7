import * as Crypto from '@cardano-sdk/crypto';
import { Anchor, DelegateRepresentative } from './Governance';
import { Credential, RewardAccount } from '../Address';
import { EpochNo } from './Block';
import { Lovelace } from './Value';
import { PoolId, PoolParameters } from './StakePool';
export declare enum CertificateType {
    StakeRegistration = "StakeRegistrationCertificate",
    StakeDeregistration = "StakeDeregistrationCertificate",
    PoolRegistration = "PoolRegistrationCertificate",
    PoolRetirement = "PoolRetirementCertificate",
    StakeDelegation = "StakeDelegationCertificate",
    MIR = "MirCertificate",
    GenesisKeyDelegation = "GenesisKeyDelegationCertificate",
    Registration = "RegistrationCertificate",
    Unregistration = "UnRegistrationCertificate",
    VoteDelegation = "VoteDelegationCertificate",
    StakeVoteDelegation = "StakeVoteDelegationCertificate",
    StakeRegistrationDelegation = "StakeRegistrationDelegateCertificate",
    VoteRegistrationDelegation = "VoteRegistrationDelegateCertificate",
    StakeVoteRegistrationDelegation = "StakeVoteRegistrationDelegateCertificate",
    AuthorizeCommitteeHot = "AuthorizeCommitteeHotCertificate",
    ResignCommitteeCold = "ResignCommitteeColdCertificate",
    RegisterDelegateRepresentative = "RegisterDelegateRepresentativeCertificate",
    UnregisterDelegateRepresentative = "UnregisterDelegateRepresentativeCertificate",
    UpdateDelegateRepresentative = "UpdateDelegateRepresentativeCertificate"
}
export interface NewStakeAddressCertificate {
    __typename: CertificateType.Registration | CertificateType.Unregistration;
    stakeCredential: Credential;
    deposit: Lovelace;
}
export interface VoteDelegationCertificate {
    __typename: CertificateType.VoteDelegation;
    stakeCredential: Credential;
    dRep: DelegateRepresentative;
}
export interface StakeVoteDelegationCertificate {
    __typename: CertificateType.StakeVoteDelegation;
    stakeCredential: Credential;
    poolId: PoolId;
    dRep: DelegateRepresentative;
}
export interface StakeRegistrationDelegationCertificate {
    __typename: CertificateType.StakeRegistrationDelegation;
    stakeCredential: Credential;
    poolId: PoolId;
    deposit: Lovelace;
}
export interface VoteRegistrationDelegationCertificate {
    __typename: CertificateType.VoteRegistrationDelegation;
    stakeCredential: Credential;
    dRep: DelegateRepresentative;
    deposit: Lovelace;
}
export interface StakeVoteRegistrationDelegationCertificate {
    __typename: CertificateType.StakeVoteRegistrationDelegation;
    stakeCredential: Credential;
    poolId: PoolId;
    dRep: DelegateRepresentative;
    deposit: Lovelace;
}
export interface AuthorizeCommitteeHotCertificate {
    __typename: CertificateType.AuthorizeCommitteeHot;
    coldCredential: Credential;
    hotCredential: Credential;
}
export interface ResignCommitteeColdCertificate {
    __typename: CertificateType.ResignCommitteeCold;
    coldCredential: Credential;
    anchor: Anchor | null;
}
export interface RegisterDelegateRepresentativeCertificate {
    __typename: CertificateType.RegisterDelegateRepresentative;
    dRepCredential: Credential;
    deposit: Lovelace;
    anchor: Anchor | null;
}
export interface UnRegisterDelegateRepresentativeCertificate {
    __typename: CertificateType.UnregisterDelegateRepresentative;
    dRepCredential: Credential;
    deposit: Lovelace;
}
export interface UpdateDelegateRepresentativeCertificate {
    __typename: CertificateType.UpdateDelegateRepresentative;
    dRepCredential: Credential;
    anchor: Anchor | null;
}
export interface StakeAddressCertificate {
    __typename: CertificateType.StakeRegistration | CertificateType.StakeDeregistration;
    stakeCredential: Credential;
}
export interface PoolRegistrationCertificate {
    __typename: CertificateType.PoolRegistration;
    poolParameters: PoolParameters;
}
export interface PoolRetirementCertificate {
    __typename: CertificateType.PoolRetirement;
    poolId: PoolId;
    epoch: EpochNo;
}
export interface StakeDelegationCertificate {
    __typename: CertificateType.StakeDelegation;
    stakeCredential: Credential;
    poolId: PoolId;
}
export declare enum MirCertificatePot {
    Reserves = "reserve",
    Treasury = "treasury"
}
export declare enum MirCertificateKind {
    ToOtherPot = "toOtherPot",
    ToStakeCreds = "ToStakeCreds"
}
export interface MirCertificate {
    __typename: CertificateType.MIR;
    kind: MirCertificateKind;
    stakeCredential?: Credential;
    quantity: Lovelace;
    pot: MirCertificatePot;
}
export interface GenesisKeyDelegationCertificate {
    __typename: CertificateType.GenesisKeyDelegation;
    genesisHash: Crypto.Hash28ByteBase16;
    genesisDelegateHash: Crypto.Hash28ByteBase16;
    vrfKeyHash: Crypto.Hash32ByteBase16;
}
export declare type Certificate = StakeAddressCertificate | PoolRegistrationCertificate | PoolRetirementCertificate | StakeDelegationCertificate | MirCertificate | GenesisKeyDelegationCertificate | NewStakeAddressCertificate | VoteDelegationCertificate | StakeVoteDelegationCertificate | StakeRegistrationDelegationCertificate | VoteRegistrationDelegationCertificate | StakeVoteRegistrationDelegationCertificate | AuthorizeCommitteeHotCertificate | ResignCommitteeColdCertificate | RegisterDelegateRepresentativeCertificate | UnRegisterDelegateRepresentativeCertificate | UpdateDelegateRepresentativeCertificate;
export declare const PostConwayStakeRegistrationCertificateTypes: readonly [CertificateType.Registration, CertificateType.VoteRegistrationDelegation, CertificateType.StakeRegistrationDelegation, CertificateType.StakeVoteRegistrationDelegation];
export declare const StakeRegistrationCertificateTypes: readonly [CertificateType.StakeRegistration, CertificateType.Registration, CertificateType.VoteRegistrationDelegation, CertificateType.StakeRegistrationDelegation, CertificateType.StakeVoteRegistrationDelegation];
export declare type StakeRegistrationCertificateTypes = typeof StakeRegistrationCertificateTypes[number];
export declare const StakeDelegationCertificateTypes: readonly [CertificateType.StakeDelegation, CertificateType.StakeVoteDelegation, CertificateType.StakeRegistrationDelegation, CertificateType.StakeVoteRegistrationDelegation];
export declare type StakeDelegationCertificateTypes = typeof StakeDelegationCertificateTypes[number];
export declare const RegAndDeregCertificateTypes: readonly [CertificateType.StakeRegistration, CertificateType.Registration, CertificateType.VoteRegistrationDelegation, CertificateType.StakeRegistrationDelegation, CertificateType.StakeVoteRegistrationDelegation, CertificateType.Unregistration, CertificateType.StakeDeregistration];
export declare type RegAndDeregCertificateTypes = typeof RegAndDeregCertificateTypes[number];
export declare const StakeCredentialCertificateTypes: readonly [CertificateType.StakeRegistration, CertificateType.Registration, CertificateType.VoteRegistrationDelegation, CertificateType.StakeRegistrationDelegation, CertificateType.StakeVoteRegistrationDelegation, CertificateType.Unregistration, CertificateType.StakeDeregistration, CertificateType.StakeDelegation, CertificateType.StakeVoteDelegation, CertificateType.StakeRegistrationDelegation, CertificateType.StakeVoteRegistrationDelegation, CertificateType.VoteDelegation];
declare type CertificateTypeMap = {
    [CertificateType.AuthorizeCommitteeHot]: AuthorizeCommitteeHotCertificate;
    [CertificateType.GenesisKeyDelegation]: GenesisKeyDelegationCertificate;
    [CertificateType.MIR]: MirCertificate;
    [CertificateType.PoolRegistration]: PoolRegistrationCertificate;
    [CertificateType.PoolRetirement]: PoolRetirementCertificate;
    [CertificateType.RegisterDelegateRepresentative]: RegisterDelegateRepresentativeCertificate;
    [CertificateType.Registration]: NewStakeAddressCertificate;
    [CertificateType.ResignCommitteeCold]: ResignCommitteeColdCertificate;
    [CertificateType.StakeDelegation]: StakeDelegationCertificate;
    [CertificateType.StakeDeregistration]: StakeAddressCertificate;
    [CertificateType.StakeRegistration]: StakeAddressCertificate;
    [CertificateType.StakeRegistrationDelegation]: StakeRegistrationDelegationCertificate;
    [CertificateType.StakeVoteDelegation]: StakeVoteDelegationCertificate;
    [CertificateType.StakeVoteRegistrationDelegation]: StakeVoteRegistrationDelegationCertificate;
    [CertificateType.UnregisterDelegateRepresentative]: UnRegisterDelegateRepresentativeCertificate;
    [CertificateType.Unregistration]: NewStakeAddressCertificate;
    [CertificateType.UpdateDelegateRepresentative]: UpdateDelegateRepresentativeCertificate;
    [CertificateType.VoteDelegation]: VoteDelegationCertificate;
    [CertificateType.VoteRegistrationDelegation]: VoteRegistrationDelegationCertificate;
};
export declare const isCertType: <K extends keyof CertificateTypeMap>(certificate: Certificate, certificateTypes: readonly K[]) => certificate is CertificateTypeMap[K];
export declare const createStakeRegistrationCert: (rewardAccount: RewardAccount) => Certificate;
export declare const createStakeDeregistrationCert: (rewardAccount: RewardAccount, deposit?: Lovelace) => Certificate;
export declare const createDelegationCert: (rewardAccount: RewardAccount, poolId: PoolId) => Certificate;
export declare const stakeKeyCertificates: (certificates?: Certificate[]) => (StakeAddressCertificate | NewStakeAddressCertificate | StakeRegistrationDelegationCertificate | VoteRegistrationDelegationCertificate | StakeVoteRegistrationDelegationCertificate)[];
export declare const includesAnyCertificate: (haystack: Certificate[], needle: readonly CertificateType[]) => boolean;
export {};
//# sourceMappingURL=Certificate.d.ts.map