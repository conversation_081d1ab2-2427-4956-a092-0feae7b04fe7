## [2.6.10](https://github.com/webtorrent/webtorrent/compare/v2.6.9...v2.6.10) (2025-06-28)


### Bug Fixes

* **deps:** update dependency bittorrent-protocol to ^4.1.18 ([#2965](https://github.com/webtorrent/webtorrent/issues/2965)) ([0122af4](https://github.com/webtorrent/webtorrent/commit/0122af4c0e53fc70a7b9c8a5a712590f835a7e80))

## [2.6.9](https://github.com/webtorrent/webtorrent/compare/v2.6.8...v2.6.9) (2025-06-28)


### Bug Fixes

* **deps:** update dependency streamx to v2.22.1 ([#2960](https://github.com/webtorrent/webtorrent/issues/2960)) ([a0dc518](https://github.com/webtorrent/webtorrent/commit/a0dc5188a9426e287af5b12aa037e7b7e248cbb9))

## [2.6.8](https://github.com/webtorrent/webtorrent/compare/v2.6.7...v2.6.8) (2025-05-23)


### Bug Fixes

* bad file iterator code ([#2953](https://github.com/webtorrent/webtorrent/issues/2953)) ([b119706](https://github.com/webtorrent/webtorrent/commit/b119706f2a521b728e4e1ddea13c2802507968d9))

## [2.6.7](https://github.com/webtorrent/webtorrent/compare/v2.6.6...v2.6.7) (2025-05-16)


### Bug Fixes

* removing stream selections ([#2952](https://github.com/webtorrent/webtorrent/issues/2952)) ([a5cbad0](https://github.com/webtorrent/webtorrent/commit/a5cbad0bfdf1bf8ebdc3c4f4e1500e183a8cd51a))

## [2.6.6](https://github.com/webtorrent/webtorrent/compare/v2.6.5...v2.6.6) (2025-05-15)


### Bug Fixes

* **deps:** update webtorrent ([#2951](https://github.com/webtorrent/webtorrent/issues/2951)) ([e9eb53c](https://github.com/webtorrent/webtorrent/commit/e9eb53ce5db33f212bc44702d0e157b665040eb4))

## [2.6.5](https://github.com/webtorrent/webtorrent/compare/v2.6.4...v2.6.5) (2025-05-14)


### Bug Fixes

* **deps:** update dependency bittorrent-dht to ^11.0.10 ([#2950](https://github.com/webtorrent/webtorrent/issues/2950)) ([effaf4e](https://github.com/webtorrent/webtorrent/commit/effaf4e55e30d91362b924a774ba243bea86d09c))

## [2.6.4](https://github.com/webtorrent/webtorrent/compare/v2.6.3...v2.6.4) (2025-05-14)


### Bug Fixes

* **deps:** update dependency debug to ^4.4.1 ([#2949](https://github.com/webtorrent/webtorrent/issues/2949)) ([2954871](https://github.com/webtorrent/webtorrent/commit/2954871756160e6071d873f489703c1c245dc202))

## [2.6.3](https://github.com/webtorrent/webtorrent/compare/v2.6.2...v2.6.3) (2025-04-18)


### Bug Fixes

* **deps:** update dependency torrent-discovery to ^11.0.16 ([#2943](https://github.com/webtorrent/webtorrent/issues/2943)) ([6c0154e](https://github.com/webtorrent/webtorrent/commit/6c0154e5255f9c6590dcb62c466ed22c043d5ee0))

## [2.6.2](https://github.com/webtorrent/webtorrent/compare/v2.6.1...v2.6.2) (2025-04-18)


### Bug Fixes

* null body on OPTIONS ([#2942](https://github.com/webtorrent/webtorrent/issues/2942)) ([76b508a](https://github.com/webtorrent/webtorrent/commit/76b508aa3482697dfad11d42d7d34de52484e65b))

## [2.6.1](https://github.com/webtorrent/webtorrent/compare/v2.6.0...v2.6.1) (2025-04-18)


### Bug Fixes

* **deps:** update dependency @silentbot1/nat-api to ^0.4.8 ([#2941](https://github.com/webtorrent/webtorrent/issues/2941)) ([134362e](https://github.com/webtorrent/webtorrent/commit/134362e51fa4c5f1996f3aaf38dcf1ca21e42580))

# [2.6.0](https://github.com/webtorrent/webtorrent/compare/v2.5.19...v2.6.0) (2025-04-04)


### Features

* add an option to store the bitfield in the file system ([#2878](https://github.com/webtorrent/webtorrent/issues/2878)) ([e29a474](https://github.com/webtorrent/webtorrent/commit/e29a4740b45995719652a1886a42f816e38fa456))

## [2.5.19](https://github.com/webtorrent/webtorrent/compare/v2.5.18...v2.5.19) (2025-02-05)


### Bug Fixes

* **deps:** update dependency streamx to v2.22.0 ([#2921](https://github.com/webtorrent/webtorrent/issues/2921)) ([f06ac81](https://github.com/webtorrent/webtorrent/commit/f06ac81887c4738067f92246949c1e5fcdf60d09))

## [2.5.18](https://github.com/webtorrent/webtorrent/compare/v2.5.17...v2.5.18) (2025-02-02)

## [2.5.17](https://github.com/webtorrent/webtorrent/compare/v2.5.16...v2.5.17) (2025-01-30)


### Bug Fixes

* **deps:** update dependency torrent-piece to ^3.0.1 ([#2920](https://github.com/webtorrent/webtorrent/issues/2920)) ([86d7980](https://github.com/webtorrent/webtorrent/commit/86d7980ff64161449ace94dba9f66520d038fad9))

## [2.5.16](https://github.com/webtorrent/webtorrent/compare/v2.5.15...v2.5.16) (2025-01-29)


### Bug Fixes

* **deps:** update dependency streamx to v2.21.1 ([#2898](https://github.com/webtorrent/webtorrent/issues/2898)) ([7727845](https://github.com/webtorrent/webtorrent/commit/772784501d7e2aaa170344c79eb3c490634c64cd))

## [2.5.15](https://github.com/webtorrent/webtorrent/compare/v2.5.14...v2.5.15) (2025-01-29)


### Bug Fixes

* bad done and select behavior and documentation ([#2916](https://github.com/webtorrent/webtorrent/issues/2916)) ([8237d5f](https://github.com/webtorrent/webtorrent/commit/8237d5fa23c9f8f46a0d1ad7bc820ff661ee5ba6))

## [2.5.14](https://github.com/webtorrent/webtorrent/compare/v2.5.13...v2.5.14) (2025-01-21)


### Bug Fixes

* noPeers not working correctly ([#2915](https://github.com/webtorrent/webtorrent/issues/2915)) ([62b4118](https://github.com/webtorrent/webtorrent/commit/62b41182ac71acee82cb53ed490f35ad1710ca1f))

## [2.5.13](https://github.com/webtorrent/webtorrent/compare/v2.5.12...v2.5.13) (2025-01-20)


### Bug Fixes

* **deps:** update webtorrent ([#2914](https://github.com/webtorrent/webtorrent/issues/2914)) ([e4f237d](https://github.com/webtorrent/webtorrent/commit/e4f237d9b0118146e7dced65fd5a8690b5771682))

## [2.5.12](https://github.com/webtorrent/webtorrent/compare/v2.5.11...v2.5.12) (2025-01-04)


### Bug Fixes

* **deps:** update webtorrent ([#2910](https://github.com/webtorrent/webtorrent/issues/2910)) ([53049b6](https://github.com/webtorrent/webtorrent/commit/53049b6121d1ea4116d41ebec79b49cd8146ac3d))

## [2.5.11](https://github.com/webtorrent/webtorrent/compare/v2.5.10...v2.5.11) (2024-12-28)


### Bug Fixes

* **deps:** update dependency torrent-discovery to ^11.0.14 ([#2908](https://github.com/webtorrent/webtorrent/issues/2908)) ([85e1d8a](https://github.com/webtorrent/webtorrent/commit/85e1d8add7bc0e2c426f1ec1bba54f77275dbfde))

## [2.5.10](https://github.com/webtorrent/webtorrent/compare/v2.5.9...v2.5.10) (2024-12-07)


### Bug Fixes

* **deps:** update dependency torrent-discovery to ^11.0.13 ([#2901](https://github.com/webtorrent/webtorrent/issues/2901)) ([03f7021](https://github.com/webtorrent/webtorrent/commit/03f7021b89cc18be9becd1284b21b27b09790153))

## [2.5.9](https://github.com/webtorrent/webtorrent/compare/v2.5.8...v2.5.9) (2024-12-07)


### Bug Fixes

* **deps:** update webtorrent ([#2900](https://github.com/webtorrent/webtorrent/issues/2900)) ([2b45a14](https://github.com/webtorrent/webtorrent/commit/2b45a142382fdcf2b48a6618b71930e119e55d70))

## [2.5.8](https://github.com/webtorrent/webtorrent/compare/v2.5.7...v2.5.8) (2024-12-07)


### Bug Fixes

* **deps:** update dependency debug to ^4.4.0 ([#2899](https://github.com/webtorrent/webtorrent/issues/2899)) ([1dd3c1a](https://github.com/webtorrent/webtorrent/commit/1dd3c1a45bca399d9800932318525881224527a4))

## [2.5.7](https://github.com/webtorrent/webtorrent/compare/v2.5.6...v2.5.7) (2024-11-23)


### Bug Fixes

* **deps:** update dependency streamx to v2.20.2 ([#2888](https://github.com/webtorrent/webtorrent/issues/2888)) ([4b00a0e](https://github.com/webtorrent/webtorrent/commit/4b00a0e1e70b8f71f264e254f70f2bfe80b16992))

## [2.5.6](https://github.com/webtorrent/webtorrent/compare/v2.5.5...v2.5.6) (2024-11-17)


### Bug Fixes

* uTP error on setup ([#2892](https://github.com/webtorrent/webtorrent/issues/2892)) ([283fd76](https://github.com/webtorrent/webtorrent/commit/283fd76b8ed0589c397711510d1efddf18297967))

## [2.5.5](https://github.com/webtorrent/webtorrent/compare/v2.5.4...v2.5.5) (2024-11-06)


### Bug Fixes

* **deps:** update dependency streamx to v2.20.1 ([#2855](https://github.com/webtorrent/webtorrent/issues/2855)) ([6618cee](https://github.com/webtorrent/webtorrent/commit/6618cee538264639f92d436ee253e321a284777e))

## [2.5.4](https://github.com/webtorrent/webtorrent/compare/v2.5.3...v2.5.4) (2024-11-06)


### Bug Fixes

* **deps:** update dependency pump to ^3.0.2 ([#2864](https://github.com/webtorrent/webtorrent/issues/2864)) ([019f115](https://github.com/webtorrent/webtorrent/commit/019f115031003a6cb119d13527bd6faaf5bfad8c))

## [2.5.3](https://github.com/webtorrent/webtorrent/compare/v2.5.2...v2.5.3) (2024-11-05)


### Bug Fixes

* **deps:** update webtorrent ([c5eed32](https://github.com/webtorrent/webtorrent/commit/c5eed329cf06dc2638ddae4193f6c1af83d557d0))

## [2.5.2](https://github.com/webtorrent/webtorrent/compare/v2.5.1...v2.5.2) (2024-11-05)


### Bug Fixes

* parse host even if this.client.blocked is false, to allow line 868 to pass correctly and default to utp. ([#2805](https://github.com/webtorrent/webtorrent/issues/2805)) ([beef270](https://github.com/webtorrent/webtorrent/commit/beef270d62e749c406637da8d82e2e5f85a03020))

## [2.5.1](https://github.com/webtorrent/webtorrent/compare/v2.5.0...v2.5.1) (2024-09-07)


### Bug Fixes

* **deps:** update dependency debug to ^4.3.7 ([69c1441](https://github.com/webtorrent/webtorrent/commit/69c14418af5fe724dc294ca5469d7c3188542b88))

# [2.5.0](https://github.com/webtorrent/webtorrent/compare/v2.4.15...v2.5.0) (2024-08-04)


### Features

* drop IDB ([#2851](https://github.com/webtorrent/webtorrent/issues/2851)) ([dc26aa6](https://github.com/webtorrent/webtorrent/commit/dc26aa6363d6004518de4f7ffc1d6a3968543a26))

## [2.4.15](https://github.com/webtorrent/webtorrent/compare/v2.4.14...v2.4.15) (2024-08-03)


### Bug Fixes

* custom length file buffer ([#2843](https://github.com/webtorrent/webtorrent/issues/2843)) ([5d40ad1](https://github.com/webtorrent/webtorrent/commit/5d40ad166ea531e1be9d07511c6245135ea9d87e))

## [2.4.14](https://github.com/webtorrent/webtorrent/compare/v2.4.13...v2.4.14) (2024-07-28)


### Bug Fixes

* **deps:** update dependency debug to ^4.3.6 ([c6dde2c](https://github.com/webtorrent/webtorrent/commit/c6dde2c95fb06cf6b5668225d3a1bdfa3a59c053))

## [2.4.13](https://github.com/webtorrent/webtorrent/compare/v2.4.12...v2.4.13) (2024-07-28)


### Bug Fixes

* **deps:** update webtorrent ([9eb656f](https://github.com/webtorrent/webtorrent/commit/9eb656f81016dffcc178ece9f15402ef63bc6a99))

## [2.4.12](https://github.com/webtorrent/webtorrent/compare/v2.4.11...v2.4.12) (2024-07-17)


### Bug Fixes

* **deps:** update dependency @thaunknown/simple-peer to ^10.0.10 ([09d7ea0](https://github.com/webtorrent/webtorrent/commit/09d7ea08cc461902aab6c9fc699f7962b1d977bd))

## [2.4.11](https://github.com/webtorrent/webtorrent/compare/v2.4.10...v2.4.11) (2024-06-29)


### Bug Fixes

* revert "fix: drop IDB chunk store ([#2553](https://github.com/webtorrent/webtorrent/issues/2553))" ([#2828](https://github.com/webtorrent/webtorrent/issues/2828)) ([348fba5](https://github.com/webtorrent/webtorrent/commit/348fba52e02670b506461f401cd067a759963b58))

## [2.4.10](https://github.com/webtorrent/webtorrent/compare/v2.4.9...v2.4.10) (2024-06-29)


### Bug Fixes

* force streamx version ([#2826](https://github.com/webtorrent/webtorrent/issues/2826)) ([1f96952](https://github.com/webtorrent/webtorrent/commit/1f96952dbac120a52c27d5a65cb4384d28f748ab))

## [2.4.9](https://github.com/webtorrent/webtorrent/compare/v2.4.8...v2.4.9) (2024-06-29)


### Bug Fixes

* **deps:** update dependency @thaunknown/simple-peer to ^10.0.9 ([fc54e8e](https://github.com/webtorrent/webtorrent/commit/fc54e8e3c1fba34ca87b2312a91f72ecdfc26907))

## [2.4.8](https://github.com/webtorrent/webtorrent/compare/v2.4.7...v2.4.8) (2024-06-29)


### Bug Fixes

* drop IDB chunk store ([#2553](https://github.com/webtorrent/webtorrent/issues/2553)) ([18a4683](https://github.com/webtorrent/webtorrent/commit/18a468304c3826da42d6e4a4bc7e074ea9297187))

## [2.4.7](https://github.com/webtorrent/webtorrent/compare/v2.4.6...v2.4.7) (2024-06-29)


### Bug Fixes

* **deps:** update dependency parse-torrent to ^11.0.17 ([72f4abc](https://github.com/webtorrent/webtorrent/commit/72f4abc2963b985faa905cbea2da77f84600c706))

## [2.4.6](https://github.com/webtorrent/webtorrent/compare/v2.4.5...v2.4.6) (2024-06-29)


### Bug Fixes

* revert streamx to 2.17 ([#2819](https://github.com/webtorrent/webtorrent/issues/2819)) ([f4465d0](https://github.com/webtorrent/webtorrent/commit/f4465d043f301452682a8db562d90123b94cd58e))

## [2.4.5](https://github.com/webtorrent/webtorrent/compare/v2.4.4...v2.4.5) (2024-06-29)


### Bug Fixes

* **deps:** update dependency bittorrent-protocol to ^4.1.13 ([d596da6](https://github.com/webtorrent/webtorrent/commit/d596da6da8ea3f6f2f65947ab9e15fcebbb2ba9c))

## [2.4.4](https://github.com/webtorrent/webtorrent/compare/v2.4.3...v2.4.4) (2024-06-28)


### Bug Fixes

* **deps:** update dependency streamx to ^2.18.0 ([#2798](https://github.com/webtorrent/webtorrent/issues/2798)) ([20e18b3](https://github.com/webtorrent/webtorrent/commit/20e18b3dbc9be6c2e9abfadaf041e986b99fa690))

## [2.4.3](https://github.com/webtorrent/webtorrent/compare/v2.4.2...v2.4.3) (2024-06-28)


### Bug Fixes

* **deps:** update dependency @thaunknown/simple-peer to ^10.0.8 ([27e34e5](https://github.com/webtorrent/webtorrent/commit/27e34e5059281af5a9d0de4231d3304fd71c5bda))
* **deps:** update dependency hybrid-chunk-store to ^1.2.6 ([#2818](https://github.com/webtorrent/webtorrent/issues/2818)) ([478c691](https://github.com/webtorrent/webtorrent/commit/478c691bd89ab54f9e76dcddce7eb0f56d8bca68))

## [2.4.2](https://github.com/webtorrent/webtorrent/compare/v2.4.1...v2.4.2) (2024-06-22)


### Bug Fixes

* allow client level default trackers to be set ([#2815](https://github.com/webtorrent/webtorrent/issues/2815)) ([a0e056d](https://github.com/webtorrent/webtorrent/commit/a0e056dc01c08c78e8b5ab33b3f50b7e96b37825))

## [2.4.1](https://github.com/webtorrent/webtorrent/compare/v2.4.0...v2.4.1) (2024-06-13)

# [2.4.0](https://github.com/webtorrent/webtorrent/compare/v2.3.6...v2.4.0) (2024-06-06)


### Features

* add `opts.seedOutgoingConnections` to allow controlling outbound connections for seeding. ([#2803](https://github.com/webtorrent/webtorrent/issues/2803)) ([d40616f](https://github.com/webtorrent/webtorrent/commit/d40616f27d1b584b1a8db66e75638298388505c9))

## [2.3.6](https://github.com/webtorrent/webtorrent/compare/v2.3.5...v2.3.6) (2024-06-02)


### Bug Fixes

* **deps:** update webtorrent to ^11.0.6 ([63061c6](https://github.com/webtorrent/webtorrent/commit/63061c68dcd070db982e824eaccee755429f3549))

## [2.3.5](https://github.com/webtorrent/webtorrent/compare/v2.3.4...v2.3.5) (2024-06-01)


### Bug Fixes

* **deps:** update dependency torrent-discovery to ^11.0.5 ([4742c01](https://github.com/webtorrent/webtorrent/commit/4742c01560e9f28d2d5bd194f6b84f3b5889ecd1))

## [2.3.4](https://github.com/webtorrent/webtorrent/compare/v2.3.3...v2.3.4) (2024-06-01)


### Bug Fixes

* **deps:** update webtorrent ([6ff995f](https://github.com/webtorrent/webtorrent/commit/6ff995fafffb59138f31705fdca5944b0eafbb0c))

## [2.3.3](https://github.com/webtorrent/webtorrent/compare/v2.3.2...v2.3.3) (2024-06-01)


### Bug Fixes

* **deps:** update dependency debug to ^4.3.5 ([4a47201](https://github.com/webtorrent/webtorrent/commit/4a472018dfc5c6ca218d5a1b7ba4683aa8e312a7))

## [2.3.2](https://github.com/webtorrent/webtorrent/compare/v2.3.1...v2.3.2) (2024-05-25)


### Bug Fixes

* **deps:** update dependency bitfield to ^4.2.0 ([#2730](https://github.com/webtorrent/webtorrent/issues/2730)) ([867e4f2](https://github.com/webtorrent/webtorrent/commit/867e4f2cabcc4087fc55ef3cc0bc1e5f2d75fb21))

## [2.3.1](https://github.com/webtorrent/webtorrent/compare/v2.3.0...v2.3.1) (2024-05-23)


### Bug Fixes

* **deps:** update dependency uint8-util to ^2.2.5 ([#2784](https://github.com/webtorrent/webtorrent/issues/2784)) ([3c3993f](https://github.com/webtorrent/webtorrent/commit/3c3993fb8c49c99214f7dce514b8751f37f4a277))

# [2.3.0](https://github.com/webtorrent/webtorrent/compare/v2.2.2...v2.3.0) (2024-05-23)


### Features

* Built-in-webrtc ([#2786](https://github.com/webtorrent/webtorrent/issues/2786)) ([c693f9e](https://github.com/webtorrent/webtorrent/commit/c693f9ea91c32b0f8e81d2d57476405fda86c7a7))

## [2.2.2](https://github.com/webtorrent/webtorrent/compare/v2.2.1...v2.2.2) (2024-05-23)


### Bug Fixes

* **deps:** update dependency torrent-discovery to ^11.0.4 ([9841d3d](https://github.com/webtorrent/webtorrent/commit/9841d3d99e7329a57abb40f9ef80dc914db6699e))

## [2.2.1](https://github.com/webtorrent/webtorrent/compare/v2.2.0...v2.2.1) (2024-03-26)


### Bug Fixes

* **deps:** update dependency hybrid-chunk-store to ^1.2.4 ([#2764](https://github.com/webtorrent/webtorrent/issues/2764)) ([9ce6188](https://github.com/webtorrent/webtorrent/commit/9ce61882717a197aa9a05defe011c2135b44d235))

# [2.2.0](https://github.com/webtorrent/webtorrent/compare/v2.1.37...v2.2.0) (2024-03-26)


### Features

* Refactor selections with non-overlapping data structure ([#2757](https://github.com/webtorrent/webtorrent/issues/2757)) ([467f30c](https://github.com/webtorrent/webtorrent/commit/467f30ccb48d318e7cf9e801d40ec3158fa220f6))

## [2.1.37](https://github.com/webtorrent/webtorrent/compare/v2.1.36...v2.1.37) (2024-03-04)


### Bug Fixes

* **deps:** update dependency streamx to ^2.16.1 ([#2752](https://github.com/webtorrent/webtorrent/issues/2752)) ([33e87e2](https://github.com/webtorrent/webtorrent/commit/33e87e281c02d9cb9f3be7468271722d7d0c3a76))

## [2.1.36](https://github.com/webtorrent/webtorrent/compare/v2.1.35...v2.1.36) (2024-02-09)


### Bug Fixes

* **deps:** update dependency create-torrent to ^6.0.17 ([54f6add](https://github.com/webtorrent/webtorrent/commit/54f6addef3f4e275b51f8c17f7bbd6e99beaf3f3))

## [2.1.35](https://github.com/webtorrent/webtorrent/compare/v2.1.34...v2.1.35) (2024-02-04)


### Bug Fixes

* **deps:** update dependency streamx to ^2.15.7 ([#2748](https://github.com/webtorrent/webtorrent/issues/2748)) ([13b135f](https://github.com/webtorrent/webtorrent/commit/13b135f42e985b6af8b676dda24bca1aa75a7a5a))

## [2.1.34](https://github.com/webtorrent/webtorrent/compare/v2.1.33...v2.1.34) (2024-01-16)


### Bug Fixes

* **deps:** update webtorrent ([0585322](https://github.com/webtorrent/webtorrent/commit/058532277f6e3a80cf9a772e3d8ca45ec7a41762))

## [2.1.33](https://github.com/webtorrent/webtorrent/compare/v2.1.32...v2.1.33) (2024-01-12)


### Bug Fixes

* malformed debug ID ([#2733](https://github.com/webtorrent/webtorrent/issues/2733)) ([2453316](https://github.com/webtorrent/webtorrent/commit/24533162fada3d93404bfe104f5a0b9861350d0d))

## [2.1.32](https://github.com/webtorrent/webtorrent/compare/v2.1.31...v2.1.32) (2023-12-12)


### Bug Fixes

* **deps:** update dependency create-torrent to ^6.0.16 ([97c0af1](https://github.com/webtorrent/webtorrent/commit/97c0af1e165f77f912e133fdf896d8cea3a5476a))

## [2.1.31](https://github.com/webtorrent/webtorrent/compare/v2.1.30...v2.1.31) (2023-12-11)


### Bug Fixes

* **deps:** update dependency streamx to ^2.15.6 ([#2715](https://github.com/webtorrent/webtorrent/issues/2715)) ([3f4a80f](https://github.com/webtorrent/webtorrent/commit/3f4a80f8cea979d1d18329aec097bcdb22a9f2bc))

## [2.1.30](https://github.com/webtorrent/webtorrent/compare/v2.1.29...v2.1.30) (2023-11-30)


### Bug Fixes

* **deps:** update dependency streamx to ^2.15.5 ([#2695](https://github.com/webtorrent/webtorrent/issues/2695)) ([262e1e5](https://github.com/webtorrent/webtorrent/commit/262e1e5c66815a3a3f5ebd1d408c1dd5e7cd60da))

## [2.1.29](https://github.com/webtorrent/webtorrent/compare/v2.1.28...v2.1.29) (2023-11-12)


### Bug Fixes

* chromeapp build ([#2704](https://github.com/webtorrent/webtorrent/issues/2704)) ([ed9f368](https://github.com/webtorrent/webtorrent/commit/ed9f368fbbcb076f963e26a627ef6bc36b91a4b2))

## [2.1.28](https://github.com/webtorrent/webtorrent/compare/v2.1.27...v2.1.28) (2023-11-09)


### Bug Fixes

* **deps:** update dependency torrent-discovery to v11 ([#2702](https://github.com/webtorrent/webtorrent/issues/2702)) ([62ff1bf](https://github.com/webtorrent/webtorrent/commit/62ff1bf6559474eb114f131c30eed18ff485afc0))

## [2.1.27](https://github.com/webtorrent/webtorrent/compare/v2.1.26...v2.1.27) (2023-10-03)


### Bug Fixes

* **deps:** update dependency uint8-util to ^2.2.4 ([#2653](https://github.com/webtorrent/webtorrent/issues/2653)) ([26ab5d3](https://github.com/webtorrent/webtorrent/commit/26ab5d3e2196cc92edde3d89c1b8877a1385700a))

## [2.1.26](https://github.com/webtorrent/webtorrent/compare/v2.1.25...v2.1.26) (2023-10-02)


### Bug Fixes

* incorrect File.stream() object this reference ([#2672](https://github.com/webtorrent/webtorrent/issues/2672)) ([8577a47](https://github.com/webtorrent/webtorrent/commit/8577a4719e32ece57f8e9d30df4c7e819e1b0f70))

## [2.1.25](https://github.com/webtorrent/webtorrent/compare/v2.1.24...v2.1.25) (2023-08-11)


### Bug Fixes

* **deps:** update webtorrent ([1639c0e](https://github.com/webtorrent/webtorrent/commit/1639c0ea095942fdddaaee63b05e30498c2984d2))

## [2.1.24](https://github.com/webtorrent/webtorrent/compare/v2.1.23...v2.1.24) (2023-08-11)


### Bug Fixes

* **deps:** update dependency bittorrent-protocol to ^4.1.11 ([701280f](https://github.com/webtorrent/webtorrent/commit/701280f635e375b0166c902925dadb6fa31c424c))

## [2.1.23](https://github.com/webtorrent/webtorrent/compare/v2.1.22...v2.1.23) (2023-08-10)


### Bug Fixes

* **deps:** update dependency create-torrent to ^6.0.14 ([d7e81ea](https://github.com/webtorrent/webtorrent/commit/d7e81ea886c272efc0cad5944082aeab9fd66cf8))

## [2.1.22](https://github.com/webtorrent/webtorrent/compare/v2.1.21...v2.1.22) (2023-08-10)


### Bug Fixes

* **deps:** update webtorrent ([d447f8c](https://github.com/webtorrent/webtorrent/commit/d447f8c1718c22df4c81844197119d17b5200e00))

## [2.1.21](https://github.com/webtorrent/webtorrent/compare/v2.1.20...v2.1.21) (2023-08-10)


### Bug Fixes

* **deps:** update dependency bittorrent-protocol to ^4.1.10 ([0c60f65](https://github.com/webtorrent/webtorrent/commit/0c60f651ec5c4ab562d5cd155e887971fa78e5bf))

## [2.1.20](https://github.com/webtorrent/webtorrent/compare/v2.1.19...v2.1.20) (2023-08-10)


### Bug Fixes

* **deps:** update dependency ut_metadata to ^4.0.3 ([c62044a](https://github.com/webtorrent/webtorrent/commit/c62044ae9fe5399526a896cb1bce1f16ff79b87f))

## [2.1.19](https://github.com/webtorrent/webtorrent/compare/v2.1.18...v2.1.19) (2023-08-10)


### Bug Fixes

* **deps:** update webtorrent ([4a60108](https://github.com/webtorrent/webtorrent/commit/4a601081b74876cd0368155becffacee944c32b0))

## [2.1.18](https://github.com/webtorrent/webtorrent/compare/v2.1.17...v2.1.18) (2023-08-09)


### Bug Fixes

* **deps:** update dependency streamx to ^2.15.1 ([#2593](https://github.com/webtorrent/webtorrent/issues/2593)) ([260fca8](https://github.com/webtorrent/webtorrent/commit/260fca8bac9efa2d102f70010815d3e6d91f7bbd))

## [2.1.17](https://github.com/webtorrent/webtorrent/compare/v2.1.16...v2.1.17) (2023-08-06)


### Bug Fixes

* **deps:** update webtorrent ([9ad7e9b](https://github.com/webtorrent/webtorrent/commit/9ad7e9bf51fc88944a6d06888a2cbeccccb09612))

## [2.1.16](https://github.com/webtorrent/webtorrent/compare/v2.1.15...v2.1.16) (2023-07-30)


### Bug Fixes

* **deps:** update dependency uint8-util to ^2.2.2 ([#2633](https://github.com/webtorrent/webtorrent/issues/2633)) ([8478be4](https://github.com/webtorrent/webtorrent/commit/8478be42b694eaaf7370ca5f8370973683bca0f8))

## [2.1.15](https://github.com/webtorrent/webtorrent/compare/v2.1.14...v2.1.15) (2023-07-24)


### Bug Fixes

* **deps:** update webtorrent ([f9b34d6](https://github.com/webtorrent/webtorrent/commit/f9b34d615baec11a597e22894bf5384671af24c1))

## [2.1.14](https://github.com/webtorrent/webtorrent/compare/v2.1.13...v2.1.14) (2023-07-23)


### Bug Fixes

* **deps:** update dependency torrent-piece to v3 ([#2623](https://github.com/webtorrent/webtorrent/issues/2623)) ([7fb7afa](https://github.com/webtorrent/webtorrent/commit/7fb7afab083551c9d6e60998eeb1837f80b45b6c))

## [2.1.13](https://github.com/webtorrent/webtorrent/compare/v2.1.12...v2.1.13) (2023-07-19)


### Bug Fixes

* **deps:** update dependency @silentbot1/nat-api to ^0.4.7 ([#2619](https://github.com/webtorrent/webtorrent/issues/2619)) ([a44286c](https://github.com/webtorrent/webtorrent/commit/a44286c4f7ea69c79a238651c95ef42d93e70581))

## [2.1.12](https://github.com/webtorrent/webtorrent/compare/v2.1.11...v2.1.12) (2023-07-05)


### Bug Fixes

* perf: faster stream resolution ([#2607](https://github.com/webtorrent/webtorrent/issues/2607)) ([e692270](https://github.com/webtorrent/webtorrent/commit/e692270cf9ba7d793b4b35b509ac0572f07477ce))

## [2.1.11](https://github.com/webtorrent/webtorrent/compare/v2.1.10...v2.1.11) (2023-06-17)


### Performance Improvements

* drop buffer ([#2596](https://github.com/webtorrent/webtorrent/issues/2596)) ([7679994](https://github.com/webtorrent/webtorrent/commit/76799949239b784f98ccfc45210f68c7233e74ac))

## [2.1.10](https://github.com/webtorrent/webtorrent/compare/v2.1.9...v2.1.10) (2023-06-17)


### Bug Fixes

* **deps:** update dependency hybrid-chunk-store to ^1.2.2 ([#2595](https://github.com/webtorrent/webtorrent/issues/2595)) ([b04cc7a](https://github.com/webtorrent/webtorrent/commit/b04cc7a8ca3d555ebaecff0c3b90f91bc31d1508))

## [2.1.9](https://github.com/webtorrent/webtorrent/compare/v2.1.8...v2.1.9) (2023-06-17)


### Bug Fixes

* **deps:** update webtorrent ([#2592](https://github.com/webtorrent/webtorrent/issues/2592)) ([0c62c36](https://github.com/webtorrent/webtorrent/commit/0c62c366b0310161c2d98a658515dac3f0d16502))

## [2.1.8](https://github.com/webtorrent/webtorrent/compare/v2.1.7...v2.1.8) (2023-06-16)


### Performance Improvements

* use peer/lite ([#2591](https://github.com/webtorrent/webtorrent/issues/2591)) ([4e853f0](https://github.com/webtorrent/webtorrent/commit/4e853f0e965bbca21033c3912f8cc990b35ac2bf))

## [2.1.7](https://github.com/webtorrent/webtorrent/compare/v2.1.6...v2.1.7) (2023-06-16)


### Bug Fixes

* **deps:** update webtorrent ([#2590](https://github.com/webtorrent/webtorrent/issues/2590)) ([01865c0](https://github.com/webtorrent/webtorrent/commit/01865c09dd84527dcf0d3caf3cbcb408bf009e2f))

## [2.1.6](https://github.com/webtorrent/webtorrent/compare/v2.1.5...v2.1.6) (2023-06-16)


### Performance Improvements

* use path-esm as polyfill for path ([#2587](https://github.com/webtorrent/webtorrent/issues/2587)) ([6e08b00](https://github.com/webtorrent/webtorrent/commit/6e08b0069f1c7d7357ac56fcff9a81e0e07ff452))

## [2.1.5](https://github.com/webtorrent/webtorrent/compare/v2.1.4...v2.1.5) (2023-06-16)


### Bug Fixes

* **deps:** update dependency @thaunknown/simple-peer to ^9.12.1 ([#2588](https://github.com/webtorrent/webtorrent/issues/2588)) ([9ea487b](https://github.com/webtorrent/webtorrent/commit/9ea487b4f2470ba6d3afb4bcdbc4a19504a31a25))

## [2.1.4](https://github.com/webtorrent/webtorrent/compare/v2.1.3...v2.1.4) (2023-06-16)


### Bug Fixes

* **deps:** update dependency hybrid-chunk-store to ^1.2.1 ([#2589](https://github.com/webtorrent/webtorrent/issues/2589)) ([f482419](https://github.com/webtorrent/webtorrent/commit/f482419d5ad94e205f33c2aa4d67e8ba4ad9ddfb))

## [2.1.3](https://github.com/webtorrent/webtorrent/compare/v2.1.2...v2.1.3) (2023-06-16)


### Bug Fixes

* **deps:** update dependency streamx to ^2.14.3 ([#2583](https://github.com/webtorrent/webtorrent/issues/2583)) ([d68ea94](https://github.com/webtorrent/webtorrent/commit/d68ea941d53d6dfdf39046796c215c332fd338ab))

## [2.1.2](https://github.com/webtorrent/webtorrent/compare/v2.1.1...v2.1.2) (2023-06-16)


### Bug Fixes

* **deps:** update dependency fs-chunk-store to ^4.1.0 ([c9103bf](https://github.com/webtorrent/webtorrent/commit/c9103bf410ad81d9afd3683a9c856bcbe3bf5079))

## [2.1.1](https://github.com/webtorrent/webtorrent/compare/v2.1.0...v2.1.1) (2023-06-15)


### Bug Fixes

* Revert "fix: correctly destroy piped streams in server" ([#2585](https://github.com/webtorrent/webtorrent/issues/2585)) ([e3c9269](https://github.com/webtorrent/webtorrent/commit/e3c9269b9f9deba824559054ea6ba2e369357b60)), closes [#2565](https://github.com/webtorrent/webtorrent/issues/2565)

# [2.1.0](https://github.com/webtorrent/webtorrent/compare/v2.0.37...v2.1.0) (2023-06-13)


### Features

* update dependency streamx to ^2.14.1 ([#2580](https://github.com/webtorrent/webtorrent/issues/2580)) ([92df3a9](https://github.com/webtorrent/webtorrent/commit/92df3a963293d5bee4b22a01421f794907966e19))

## [2.0.37](https://github.com/webtorrent/webtorrent/compare/v2.0.36...v2.0.37) (2023-06-08)


### Bug Fixes

* dont encode url path ([#2573](https://github.com/webtorrent/webtorrent/issues/2573)) ([13ad0fb](https://github.com/webtorrent/webtorrent/commit/13ad0fb4f20d6035889569a55c89d4ef813a246d))

## [2.0.36](https://github.com/webtorrent/webtorrent/compare/v2.0.35...v2.0.36) (2023-06-08)


### Bug Fixes

* **deps:** update dependency torrent-discovery to ^10.0.11 ([9415322](https://github.com/webtorrent/webtorrent/commit/94153227df2e0371af11dedf24cb1bd429dc2760))

## [2.0.35](https://github.com/webtorrent/webtorrent/compare/v2.0.34...v2.0.35) (2023-06-07)


### Bug Fixes

* **deps:** update dependency semantic-release to v21 ([#2510](https://github.com/webtorrent/webtorrent/issues/2510)) ([e74b843](https://github.com/webtorrent/webtorrent/commit/e74b843f2f102d590d68db1db15ea7ac840fc485))

## [2.0.34](https://github.com/webtorrent/webtorrent/compare/v2.0.33...v2.0.34) (2023-06-05)


### Bug Fixes

* **deps:** update dependency torrent-discovery to ^10.0.10 ([#2569](https://github.com/webtorrent/webtorrent/issues/2569)) ([31796f8](https://github.com/webtorrent/webtorrent/commit/31796f82f24ed4da2f634eec84203b49688b589c))

## [2.0.33](https://github.com/webtorrent/webtorrent/compare/v2.0.32...v2.0.33) (2023-06-05)


### Bug Fixes

* **deps:** update dependency streamx to ^2.14.0 ([#2568](https://github.com/webtorrent/webtorrent/issues/2568)) ([29f412a](https://github.com/webtorrent/webtorrent/commit/29f412a665b95bdb35d10c5129271705edc1e184))

## [2.0.32](https://github.com/webtorrent/webtorrent/compare/v2.0.31...v2.0.32) (2023-06-05)


### Bug Fixes

* **deps:** update dependency streamx to ^2.13.3 ([#2562](https://github.com/webtorrent/webtorrent/issues/2562)) ([11f86db](https://github.com/webtorrent/webtorrent/commit/11f86dbf495f2e16952054f3f0cea76d6a812fce))

## [2.0.31](https://github.com/webtorrent/webtorrent/compare/v2.0.30...v2.0.31) (2023-06-05)


### Bug Fixes

* correctly destroy piped streams in server ([#2565](https://github.com/webtorrent/webtorrent/issues/2565)) ([86eda0a](https://github.com/webtorrent/webtorrent/commit/86eda0a470c4a5418a15ad55c31648ee043270c3))

## [2.0.30](https://github.com/webtorrent/webtorrent/compare/v2.0.29...v2.0.30) (2023-06-05)


### Bug Fixes

* utp support bundlers and preprocessors ([#2564](https://github.com/webtorrent/webtorrent/issues/2564)) ([8b13937](https://github.com/webtorrent/webtorrent/commit/8b13937289629eeceb59a8dc8a9d4336d8f91ae7))

## [2.0.29](https://github.com/webtorrent/webtorrent/compare/v2.0.28...v2.0.29) (2023-06-04)


### Bug Fixes

* utp ([#2561](https://github.com/webtorrent/webtorrent/issues/2561)) ([e4b2e34](https://github.com/webtorrent/webtorrent/commit/e4b2e34a69b6a09197b1cd8406f19fe2099c76a5))

## [2.0.28](https://github.com/webtorrent/webtorrent/compare/v2.0.27...v2.0.28) (2023-06-03)


### Bug Fixes

* attempt to fix saucelabs CI ([#2557](https://github.com/webtorrent/webtorrent/issues/2557)) ([c89c27d](https://github.com/webtorrent/webtorrent/commit/c89c27d546bb89cfbe897f144afe48559452816f))

## [2.0.27](https://github.com/webtorrent/webtorrent/compare/v2.0.26...v2.0.27) (2023-05-31)


### Bug Fixes

* **deps:** update dependency lt_donthave to v2 ([#2555](https://github.com/webtorrent/webtorrent/issues/2555)) ([21e5007](https://github.com/webtorrent/webtorrent/commit/21e5007e111de492ce46c6d858c7b4a6b05523af))

## [2.0.26](https://github.com/webtorrent/webtorrent/compare/v2.0.25...v2.0.26) (2023-05-31)


### Bug Fixes

* **deps:** update dependency parse-torrent to ^11.0.12 ([2766f6f](https://github.com/webtorrent/webtorrent/commit/2766f6fa396bc2f82595a83d7ede3effd0d53a42))

## [2.0.25](https://github.com/webtorrent/webtorrent/compare/v2.0.24...v2.0.25) (2023-05-30)


### Bug Fixes

* drop simple-concat ([#2552](https://github.com/webtorrent/webtorrent/issues/2552)) ([1243a76](https://github.com/webtorrent/webtorrent/commit/1243a76e51228e469568a52c94dc9bf666ee93be))

## [2.0.24](https://github.com/webtorrent/webtorrent/compare/v2.0.23...v2.0.24) (2023-05-30)


### Bug Fixes

* **deps:** update dependency bittorrent-protocol to ^4.1.7 ([#2551](https://github.com/webtorrent/webtorrent/issues/2551)) ([da87d38](https://github.com/webtorrent/webtorrent/commit/da87d3827fef1e16405328c16c4f21d4e943c4e2))

## [2.0.23](https://github.com/webtorrent/webtorrent/compare/v2.0.22...v2.0.23) (2023-05-28)


### Bug Fixes

* drop randombytes ([#2546](https://github.com/webtorrent/webtorrent/issues/2546)) ([06b6548](https://github.com/webtorrent/webtorrent/commit/06b6548fab240f425704abe044dea1be5c5662fe))

## [2.0.22](https://github.com/webtorrent/webtorrent/compare/v2.0.21...v2.0.22) (2023-05-27)


### Bug Fixes

* **deps:** update dependency torrent-discovery to ^10.0.9 ([#2545](https://github.com/webtorrent/webtorrent/issues/2545)) ([e28a7ba](https://github.com/webtorrent/webtorrent/commit/e28a7ba784c970bb33a7218a21c7d0b497be23a2))

## [2.0.21](https://github.com/webtorrent/webtorrent/compare/v2.0.20...v2.0.21) (2023-05-27)


### Performance Improvements

* reduce the impact of request idle callback ([#2542](https://github.com/webtorrent/webtorrent/issues/2542)) ([36a3e36](https://github.com/webtorrent/webtorrent/commit/36a3e3617a03a39b1fbceadf265ab297ae647500))

## [2.0.20](https://github.com/webtorrent/webtorrent/compare/v2.0.19...v2.0.20) (2023-05-27)


### Bug Fixes

* replace simple-peer with maintained one ([#2540](https://github.com/webtorrent/webtorrent/issues/2540)) ([2994641](https://github.com/webtorrent/webtorrent/commit/29946414dbc53a23adf61e91d302fa1ea82f543f))

## [2.0.19](https://github.com/webtorrent/webtorrent/compare/v2.0.18...v2.0.19) (2023-05-27)


### Bug Fixes

* **deps:** update dependency torrent-discovery to ^10.0.8 ([714535b](https://github.com/webtorrent/webtorrent/commit/714535bc30466c5bf84a9c544747b9a16b57a21d))

## [2.0.18](https://github.com/webtorrent/webtorrent/compare/v2.0.17...v2.0.18) (2023-05-07)


### Bug Fixes

* **deps:** update dependency @webtorrent/semantic-release-config to v1.0.9 ([#2528](https://github.com/webtorrent/webtorrent/issues/2528)) ([19d26f0](https://github.com/webtorrent/webtorrent/commit/19d26f00b7a0a0617ca4c0d6d94806a5256353ed))

## [2.0.17](https://github.com/webtorrent/webtorrent/compare/v2.0.16...v2.0.17) (2023-05-04)


### Bug Fixes

* http rejections handling ([#2525](https://github.com/webtorrent/webtorrent/issues/2525)) ([6232ea0](https://github.com/webtorrent/webtorrent/commit/6232ea0ab10b29a7c92697d9376c8065eaf29e6e))

## [2.0.16](https://github.com/webtorrent/webtorrent/compare/v2.0.15...v2.0.16) (2023-04-03)


### Bug Fixes

* **deps:** update webtorrent ([7a7838c](https://github.com/webtorrent/webtorrent/commit/7a7838c158ca35ad9587e5316521a776b4a3a8f5))

## [2.0.15](https://github.com/webtorrent/webtorrent/compare/v2.0.14...v2.0.15) (2023-03-20)


### Bug Fixes

* Replace setting blob 'mimeType' property with 'type' ([#2502](https://github.com/webtorrent/webtorrent/issues/2502)) ([869bdcd](https://github.com/webtorrent/webtorrent/commit/869bdcd438052ac6a23dd3ac73c77146cd853c83))

## [2.0.14](https://github.com/webtorrent/webtorrent/compare/v2.0.13...v2.0.14) (2023-02-22)


### Bug Fixes

* **deps:** update dependency create-torrent to ^6.0.10 ([d9e6784](https://github.com/webtorrent/webtorrent/commit/d9e678471124638c1ffa4b305d4fa6bc1165aa4f))

## [2.0.13](https://github.com/webtorrent/webtorrent/compare/v2.0.12...v2.0.13) (2023-02-13)


### Bug Fixes

* **deps:** update dependency hybrid-chunk-store to ^1.2.0 ([#2482](https://github.com/webtorrent/webtorrent/issues/2482)) ([792876b](https://github.com/webtorrent/webtorrent/commit/792876b10bcd5d6a16f98bb93f95ab691447df79))

## [2.0.12](https://github.com/webtorrent/webtorrent/compare/v2.0.11...v2.0.12) (2023-02-06)


### Bug Fixes

* **deps:** update dependency uint8-util to ^2.1.9 ([#2480](https://github.com/webtorrent/webtorrent/issues/2480)) ([d9efb5d](https://github.com/webtorrent/webtorrent/commit/d9efb5d329a0404bda21430994c112e0534339ff))

## [2.0.11](https://github.com/webtorrent/webtorrent/compare/v2.0.10...v2.0.11) (2023-02-01)


### Bug Fixes

* **deps:** update dependency cross-fetch-ponyfill to ^1.0.3 ([#2477](https://github.com/webtorrent/webtorrent/issues/2477)) ([970d937](https://github.com/webtorrent/webtorrent/commit/970d937b9086d5a72f0ec4608e232deb951236a8))

## [2.0.10](https://github.com/webtorrent/webtorrent/compare/v2.0.9...v2.0.10) (2023-02-01)


### Bug Fixes

* **deps:** update dependency cross-fetch-ponyfill to ^1.0.3 ([#2475](https://github.com/webtorrent/webtorrent/issues/2475)) ([63eeed1](https://github.com/webtorrent/webtorrent/commit/63eeed1090202d47c6dab9a3743cb0e5be1a64c0))

## [2.0.9](https://github.com/webtorrent/webtorrent/compare/v2.0.8...v2.0.9) (2023-02-01)


### Bug Fixes

* **deps:** update webtorrent ([#2474](https://github.com/webtorrent/webtorrent/issues/2474)) ([bfbf36b](https://github.com/webtorrent/webtorrent/commit/bfbf36be6fe3ddda7112333a13c256a4bf2d190b)), closes [#2476](https://github.com/webtorrent/webtorrent/issues/2476)

## [2.0.8](https://github.com/webtorrent/webtorrent/compare/v2.0.7...v2.0.8) (2023-01-31)


### Bug Fixes

* **deps:** update dependency uint8-util to ^2.1.7 ([#2473](https://github.com/webtorrent/webtorrent/issues/2473)) ([9a6edff](https://github.com/webtorrent/webtorrent/commit/9a6edff4ae25dfcef1dd168b1ef5066855eccbb0))

## [2.0.7](https://github.com/webtorrent/webtorrent/compare/v2.0.6...v2.0.7) (2023-01-31)


### Bug Fixes

* **deps:** update dependency parse-torrent to ^11.0.5 ([#2470](https://github.com/webtorrent/webtorrent/issues/2470)) ([9447cd6](https://github.com/webtorrent/webtorrent/commit/9447cd65d522cd84d2abbe36bf9bc950631e6b7c))

## [2.0.6](https://github.com/webtorrent/webtorrent/compare/v2.0.5...v2.0.6) (2023-01-30)


### Performance Improvements

* drop simple-get ([#2448](https://github.com/webtorrent/webtorrent/issues/2448)) ([b3c8376](https://github.com/webtorrent/webtorrent/commit/b3c83763abc964b705d66272ec7d76300ca0e4d2))

## [2.0.5](https://github.com/webtorrent/webtorrent/compare/v2.0.4...v2.0.5) (2023-01-30)


### Bug Fixes

* **deps:** update dependency parse-torrent to v11 ([#2459](https://github.com/webtorrent/webtorrent/issues/2459)) ([4cb909d](https://github.com/webtorrent/webtorrent/commit/4cb909df7f368d4aeec1345f10c90ab4c44d8308)), closes [#2461](https://github.com/webtorrent/webtorrent/issues/2461)

## [2.0.4](https://github.com/webtorrent/webtorrent/compare/v2.0.3...v2.0.4) (2023-01-28)


### Bug Fixes

* make server index pages return links with absolute urls ([#2455](https://github.com/webtorrent/webtorrent/issues/2455)) ([f2687e2](https://github.com/webtorrent/webtorrent/commit/f2687e28ea370c8e9e2ec4a8ed15c398c01f645a)), closes [#2454](https://github.com/webtorrent/webtorrent/issues/2454)

## [2.0.3](https://github.com/webtorrent/webtorrent/compare/v2.0.2...v2.0.3) (2023-01-28)


### Bug Fixes

* **deps:** update dependency fs-chunk-store to ^4.0.1 ([#2463](https://github.com/webtorrent/webtorrent/issues/2463)) ([bb7c640](https://github.com/webtorrent/webtorrent/commit/bb7c6405b264223d7af13e21c654e575c2499b79))

## [2.0.2](https://github.com/webtorrent/webtorrent/compare/v2.0.1...v2.0.2) (2023-01-28)


### Performance Improvements

* drop streamx ([#2462](https://github.com/webtorrent/webtorrent/issues/2462)) ([cde79ed](https://github.com/webtorrent/webtorrent/commit/cde79edf540932f10c9af26ccc5c56489e362a9f))

## [2.0.1](https://github.com/webtorrent/webtorrent/compare/v2.0.0...v2.0.1) (2023-01-25)


### Bug Fixes

* **deps:** update dependency hybrid-chunk-store to ^1.1.3 ([2b7c435](https://github.com/webtorrent/webtorrent/commit/2b7c43576645baf1208c4352ce1e4dcc96554b3c))

# [2.0.0](https://github.com/webtorrent/webtorrent/compare/v1.9.7...v2.0.0) (2023-01-23)


### Bug Fixes

* dependencies ([9bd7933](https://github.com/webtorrent/webtorrent/commit/9bd7933e4a1858dac20a668d1a12c26903cf77e5))
* deprecate render-media ([#2180](https://github.com/webtorrent/webtorrent/issues/2180)) ([8b5ecea](https://github.com/webtorrent/webtorrent/commit/8b5ecea9a12be44fb258b2da31c3e1e35e9b725e))
* **deps:** update dependency streamx to ^2.13.1 ([#2430](https://github.com/webtorrent/webtorrent/issues/2430)) ([fb5f5a6](https://github.com/webtorrent/webtorrent/commit/fb5f5a65f373a61b41d83b7ec5a5c0ca85eb7343))
* **deps:** update dependency streamx to ^2.13.2 ([#2438](https://github.com/webtorrent/webtorrent/issues/2438)) ([4a177d7](https://github.com/webtorrent/webtorrent/commit/4a177d737a123aec2362b8be6c75a6dbabb991ec))
* documentation, unity ([9ae5f17](https://github.com/webtorrent/webtorrent/commit/9ae5f177de6b7ba40440740b885098c58229e445))
* ESM browser tests ([975c463](https://github.com/webtorrent/webtorrent/commit/975c46399fbda71a0baac1c81798156789fd83ca))
* remove dead code ([2839c7a](https://github.com/webtorrent/webtorrent/commit/2839c7a55222d5b74feb2ce423b3cc2be61ee4e8))
* remove tap-spec where it's not necessary ([5188eca](https://github.com/webtorrent/webtorrent/commit/5188ecac01b30115b20e87bfc4fec46348ea7f8d))
* revert bittorent-protocol ver ([28df830](https://github.com/webtorrent/webtorrent/commit/28df830b2273200cf499fb3327259cd4c9c49eb9))
* standard [whitespace] ([a4bc0c3](https://github.com/webtorrent/webtorrent/commit/a4bc0c3c5167405d0b93f8f061d9bad3229414fd))
* standard linting ([a64a719](https://github.com/webtorrent/webtorrent/commit/a64a719e6a854f8d22d5b37781ee268441ca967d))


### Continuous Integration

* fix release node 18 ([25eb995](https://github.com/webtorrent/webtorrent/commit/25eb995c9804a0a4e6022e0be3b0b7e46ce60656))


* Merge pull request #2260 from webtorrent/v2 ([f8c545a](https://github.com/webtorrent/webtorrent/commit/f8c545a7da0f0fb9a35c4377d249ee1e946540c4)), closes [#2260](https://github.com/webtorrent/webtorrent/issues/2260)
* feat, perf: w3c-like File, file-iterator (#2414) ([69d85a8](https://github.com/webtorrent/webtorrent/commit/69d85a8b2d05f09702df5df40894c4e56c22d52d)), closes [#2414](https://github.com/webtorrent/webtorrent/issues/2414)


### Features

* esm ([98353d9](https://github.com/webtorrent/webtorrent/commit/98353d910a11e5cc90b4ed86bca558586cb787d5))
* rescan of torrent should get all (in)valid events ([#1903](https://github.com/webtorrent/webtorrent/issues/1903)) ([4745739](https://github.com/webtorrent/webtorrent/commit/474573930a0f3c1cac242fb3b9616db881462d00))
* sw-renderer tests ([73aff7f](https://github.com/webtorrent/webtorrent/commit/73aff7f1c22d340470777e462f9465736d0ed46d))
* unify HTTP server and SW renderer ([7aeea17](https://github.com/webtorrent/webtorrent/commit/7aeea1757000741a04409dadeaf9fab3966b399d))
* use storage (FSA+IDB) instead of memory in browser ([00e1c9f](https://github.com/webtorrent/webtorrent/commit/00e1c9f16d5d82bf1bfe3f889d29d8ce450fc787))


### Performance Improvements

* drop browserify ([c73d28e](https://github.com/webtorrent/webtorrent/commit/c73d28e7c17ebeb1921871275adb7a7899dada91))
* drop chunk store stream ([cf4d593](https://github.com/webtorrent/webtorrent/commit/cf4d5938084f01f3004884f26abaea65fc3cb365))
* drop rusha, drop Buffer ([#2390](https://github.com/webtorrent/webtorrent/issues/2390)) ([9ac1dfa](https://github.com/webtorrent/webtorrent/commit/9ac1dfacb25af85bfe2360fdc1872e41034e9efb))
* use mime-lite ([c83734c](https://github.com/webtorrent/webtorrent/commit/c83734c5a2e0ba0fe789dda81f5f610581b4c973))


### BREAKING CHANGES

* v2
* ESM only, drop node 12 and ndoe 14
* deprecate getBuffer

* fix: reading when destroyed

## [1.9.7](https://github.com/webtorrent/webtorrent/compare/v1.9.6...v1.9.7) (2023-01-12)


### Bug Fixes

* **deps:** update webtorrent ([#2424](https://github.com/webtorrent/webtorrent/issues/2424)) ([1b002db](https://github.com/webtorrent/webtorrent/commit/1b002db3ec5395c3bccec3619480e3ad9af30e25))

## [1.9.6](https://github.com/webtorrent/webtorrent/compare/v1.9.5...v1.9.6) (2022-12-03)


### Bug Fixes

* fix: fix: error loop ([14fe83f](https://github.com/webtorrent/webtorrent/commit/14fe83fe0088123b7caab4372217bc151094f9a3))

## [1.9.5](https://github.com/webtorrent/webtorrent/compare/v1.9.4...v1.9.5) (2022-12-03)


### Bug Fixes

* infinite error loop, connecting after destroy ([79dbf69](https://github.com/webtorrent/webtorrent/commit/79dbf697cb50dc64e42139581603ac228003e1f4))

## [1.9.4](https://github.com/webtorrent/webtorrent/compare/v1.9.3...v1.9.4) (2022-11-21)


### Bug Fixes

* typo in `removePeer` ([#2408](https://github.com/webtorrent/webtorrent/issues/2408)) ([83d5f72](https://github.com/webtorrent/webtorrent/commit/83d5f728b261bae5ff05160898738434f7512abb))

## [1.9.3](https://github.com/webtorrent/webtorrent/compare/v1.9.2...v1.9.3) (2022-11-18)


### Bug Fixes

* removePeer error after destroy ([36a64a1](https://github.com/webtorrent/webtorrent/commit/36a64a1b5680929841e91cee5aef07b8b1359b7c))

## [1.9.2](https://github.com/webtorrent/webtorrent/compare/v1.9.1...v1.9.2) (2022-11-11)


### Bug Fixes

* **deps:** update dependency create-torrent to ^5.0.9 ([#2392](https://github.com/webtorrent/webtorrent/issues/2392)) ([d5139ed](https://github.com/webtorrent/webtorrent/commit/d5139edf6dc91b04dd729b5d6aa08c111502b25a))

## [1.9.1](https://github.com/webtorrent/webtorrent/compare/v1.9.0...v1.9.1) (2022-10-28)


### Bug Fixes

* **deps:** update dependency streamx to ^2.12.5 ([#2366](https://github.com/webtorrent/webtorrent/issues/2366)) ([457a2d3](https://github.com/webtorrent/webtorrent/commit/457a2d3d5def6bb75d2551f34ab9fa350ec4a4e5))

# [1.9.0](https://github.com/webtorrent/webtorrent/compare/v1.8.32...v1.9.0) (2022-10-28)


### Bug Fixes

* **deps:** update dependency fs-chunk-store to v3 ([#2380](https://github.com/webtorrent/webtorrent/issues/2380)) ([9abd966](https://github.com/webtorrent/webtorrent/commit/9abd96691ab73ebc8fb10aa79a67b5db0c92ec72))


### Features

* add chitchatter link ([#2388](https://github.com/webtorrent/webtorrent/issues/2388)) ([7ef22f7](https://github.com/webtorrent/webtorrent/commit/7ef22f726bed2dfaa6f29ad633955f59db6a9022))

## [1.8.32](https://github.com/webtorrent/webtorrent/compare/v1.8.31...v1.8.32) (2022-10-09)


### Bug Fixes

* **deps:** update dependency torrent-discovery to ^9.4.14 ([8fadd4f](https://github.com/webtorrent/webtorrent/commit/8fadd4f437a9e60da4cbc49b3b6d6e65db38373f))

## [1.8.31](https://github.com/webtorrent/webtorrent/compare/v1.8.30...v1.8.31) (2022-10-08)


### Bug Fixes

* **deps:** update dependency bittorrent-dht to ^10.0.6 ([#2384](https://github.com/webtorrent/webtorrent/issues/2384)) ([34089f8](https://github.com/webtorrent/webtorrent/commit/34089f836bcb24df275f83b3026af548e32d02e9))

## [1.8.30](https://github.com/webtorrent/webtorrent/compare/v1.8.29...v1.8.30) (2022-09-12)


### Bug Fixes

* return duplicate torrent on Webtorrent.add() ([#2372](https://github.com/webtorrent/webtorrent/issues/2372)) ([05d27bf](https://github.com/webtorrent/webtorrent/commit/05d27bfd449aa705cefefe74d4a9eef327f63b4a))

## [1.8.29](https://github.com/webtorrent/webtorrent/compare/v1.8.28...v1.8.29) (2022-09-03)


### Bug Fixes

* **deps:** update dependency create-torrent to ^5.0.6 ([03a0f50](https://github.com/webtorrent/webtorrent/commit/03a0f500013172a5070dd5e8e67063bea6eaf190))

## [1.8.28](https://github.com/webtorrent/webtorrent/compare/v1.8.27...v1.8.28) (2022-09-02)


### Bug Fixes

* **deps:** update dependency fast-blob-stream to ^1.1.1 ([a441dea](https://github.com/webtorrent/webtorrent/commit/a441dea5d1a20982e9ad3fbe5237bbb02fb55898))
* **deps:** update dependency join-async-iterator to ^1.1.1 ([09b9958](https://github.com/webtorrent/webtorrent/commit/09b995814a2b5192cd9d9dd9a620d2b3dbdb5ab7))

## [1.8.27](https://github.com/webtorrent/webtorrent/compare/v1.8.26...v1.8.27) (2022-09-02)


### Bug Fixes

* dedupe packages ([9ea33c2](https://github.com/webtorrent/webtorrent/commit/9ea33c2c1cd3ea9fcfa66569a3fb94148afd9869))
* drop multi-stream ([5d87d1b](https://github.com/webtorrent/webtorrent/commit/5d87d1b32356f6b75ed4f9aefe91f3ddf1ecebdb))
* null opts causing error ([3a8f901](https://github.com/webtorrent/webtorrent/commit/3a8f901a48503a5c767b6174904e2c062d403a6a))
* use streamx instead of stream ([8b97ee8](https://github.com/webtorrent/webtorrent/commit/8b97ee8cc18b05e0d20135ea8f1651e97bb65c6f))

## [1.8.26](https://github.com/webtorrent/webtorrent/compare/v1.8.25...v1.8.26) (2022-07-04)


### Bug Fixes

* **deps:** update dependency create-torrent to ^5.0.4 ([3728336](https://github.com/webtorrent/webtorrent/commit/37283369f3476d32ac9ca85c2c2da4bd4fee273c))

## [1.8.25](https://github.com/webtorrent/webtorrent/compare/v1.8.24...v1.8.25) (2022-07-03)


### Bug Fixes

* **deps:** update dependency create-torrent to ^5.0.3 ([5009d10](https://github.com/webtorrent/webtorrent/commit/5009d1018bdec96afd4bf15e5f7143951623fd48))

## [1.8.24](https://github.com/webtorrent/webtorrent/compare/v1.8.23...v1.8.24) (2022-06-23)


### Bug Fixes

* support stream cancelling ([#2335](https://github.com/webtorrent/webtorrent/issues/2335)) ([2e4f91f](https://github.com/webtorrent/webtorrent/commit/2e4f91f668ea867d768291e9efd3e1c1eb825b97))

## [1.8.23](https://github.com/webtorrent/webtorrent/compare/v1.8.22...v1.8.23) (2022-06-23)


### Bug Fixes

* **deps:** update dependency torrent-discovery to ^9.4.13 ([1e3373a](https://github.com/webtorrent/webtorrent/commit/1e3373ab9e6dd16e5b10a2e32988386615f6cf28))

## [1.8.22](https://github.com/webtorrent/webtorrent/compare/v1.8.21...v1.8.22) (2022-05-24)


### Bug Fixes

* **deps:** update dependency bittorrent-dht to ^10.0.4 ([327d723](https://github.com/webtorrent/webtorrent/commit/327d7234a85f38bf8d397e41875a5603a8d175d1))

## [1.8.21](https://github.com/webtorrent/webtorrent/compare/v1.8.20...v1.8.21) (2022-05-23)


### Bug Fixes

* **deps:** update dependency throughput to ^1.0.1 ([#2321](https://github.com/webtorrent/webtorrent/issues/2321)) ([d53d95e](https://github.com/webtorrent/webtorrent/commit/d53d95e1ed75a288cea706d65fb660f2be3a02a0))

## [1.8.20](https://github.com/webtorrent/webtorrent/compare/v1.8.19...v1.8.20) (2022-05-14)


### Bug Fixes

* **deps:** update dependency bittorrent-dht to ^10.0.3 ([#2320](https://github.com/webtorrent/webtorrent/issues/2320)) ([2ebbd8e](https://github.com/webtorrent/webtorrent/commit/2ebbd8eacfd0d7778357ca0f44ead331ba439a26))

## [1.8.19](https://github.com/webtorrent/webtorrent/compare/v1.8.18...v1.8.19) (2022-05-11)


### Bug Fixes

* **deps:** update dependency torrent-discovery to ^9.4.12 ([36e931a](https://github.com/webtorrent/webtorrent/commit/36e931aff5e44fc5aa298a6166bfe7b4e1560499))

## [1.8.18](https://github.com/webtorrent/webtorrent/compare/v1.8.17...v1.8.18) (2022-05-11)


### Bug Fixes

* **deps:** update dependency bittorrent-tracker to v9.18.6 ([#2315](https://github.com/webtorrent/webtorrent/issues/2315)) ([b63d652](https://github.com/webtorrent/webtorrent/commit/b63d652229c4cf55a4eebbe95c98a62716bcc377))

## [1.8.17](https://github.com/webtorrent/webtorrent/compare/v1.8.16...v1.8.17) (2022-05-11)


### Bug Fixes

* measure transfer rates without using timeouts ([#2314](https://github.com/webtorrent/webtorrent/issues/2314)) ([522ee4c](https://github.com/webtorrent/webtorrent/commit/522ee4cd14d3b41e92c3978e9816cb66e00233f0))

## [1.8.16](https://github.com/webtorrent/webtorrent/compare/v1.8.15...v1.8.16) (2022-04-28)


### Bug Fixes

* **deps:** update dependency bittorrent-protocol to ^3.5.5 ([3522080](https://github.com/webtorrent/webtorrent/commit/35220804054c5369b5e88a39411125f0273b2adc))

## [1.8.15](https://github.com/webtorrent/webtorrent/compare/v1.8.14...v1.8.15) (2022-04-26)

## [1.8.14](https://github.com/webtorrent/webtorrent/compare/v1.8.13...v1.8.14) (2022-04-22)


### Bug Fixes

* **deps:** update dependency bitfield to ^4.1.0 ([#2303](https://github.com/webtorrent/webtorrent/issues/2303)) ([a778522](https://github.com/webtorrent/webtorrent/commit/a7785227d88fbac22a1b627460694b3523833fde))
* **deps:** update dependency bittorrent-protocol to ^3.5.3 ([#2302](https://github.com/webtorrent/webtorrent/issues/2302)) ([11f9426](https://github.com/webtorrent/webtorrent/commit/11f9426ee9ef3b800ac978afa5082809eb87c545))

## [1.8.13](https://github.com/webtorrent/webtorrent/compare/v1.8.12...v1.8.13) (2022-03-30)


### Bug Fixes

* **deps:** update dependency fs-chunk-store to ^2.0.5 ([2eef418](https://github.com/webtorrent/webtorrent/commit/2eef41884ffbee8f723ea2846e58756066983a0b))

## [1.8.12](https://github.com/webtorrent/webtorrent/compare/v1.8.11...v1.8.12) (2022-03-28)


### Bug Fixes

* **deps:** update dependency bittorrent-protocol to ^3.5.2 ([13fb0d6](https://github.com/webtorrent/webtorrent/commit/13fb0d60df963edb83945c8b040159c560e67368))

## [1.8.11](https://github.com/webtorrent/webtorrent/compare/v1.8.10...v1.8.11) (2022-03-27)

## [1.8.10](https://github.com/webtorrent/webtorrent/compare/v1.8.9...v1.8.10) (2022-03-27)


### Bug Fixes

* web seed request URLs ([#2267](https://github.com/webtorrent/webtorrent/issues/2267)) ([5b4880a](https://github.com/webtorrent/webtorrent/commit/5b4880aee559b4b8d294503039d07af98b09418c))

## [1.8.9](https://github.com/webtorrent/webtorrent/compare/v1.8.8...v1.8.9) (2022-03-26)


### Bug Fixes

* **deps:** update dependency parse-torrent to ^9.1.5 ([650a8c9](https://github.com/webtorrent/webtorrent/commit/650a8c93a68852da8beefd12c28a23ec4e1dc2a4))

## [1.8.8](https://github.com/webtorrent/webtorrent/compare/v1.8.7...v1.8.8) (2022-03-25)


### Bug Fixes

* **deps:** update dependency debug to ^4.3.4 ([017c488](https://github.com/webtorrent/webtorrent/commit/017c4889ff62d9b74a457fe8cf5d1699686a4754))

## [1.8.7](https://github.com/webtorrent/webtorrent/compare/v1.8.6...v1.8.7) (2022-03-24)

## [1.8.6](https://github.com/webtorrent/webtorrent/compare/v1.8.5...v1.8.6) (2022-03-11)


### Bug Fixes

* **deps:** update dependency create-torrent to ^5.0.2 ([#2276](https://github.com/webtorrent/webtorrent/issues/2276)) ([f389f87](https://github.com/webtorrent/webtorrent/commit/f389f8755de9ef8725adba8163347f7fd65fb069))

## [1.8.5](https://github.com/webtorrent/webtorrent/compare/v1.8.4...v1.8.5) (2022-03-06)


### Bug Fixes

* **deps:** update dependency torrent-discovery to ^9.4.9 ([5a08647](https://github.com/webtorrent/webtorrent/commit/5a086471a8499ee41e16b4db20200baa0458d849))

## [1.8.4](https://github.com/webtorrent/webtorrent/compare/v1.8.3...v1.8.4) (2022-03-03)


### Bug Fixes

* **deps:** update dependency torrent-discovery to ^9.4.8 ([#2272](https://github.com/webtorrent/webtorrent/issues/2272)) ([aa2fa59](https://github.com/webtorrent/webtorrent/commit/aa2fa59a4c7c166a52ecd9060191b6e695d3c85f))

## [1.8.3](https://github.com/webtorrent/webtorrent/compare/v1.8.2...v1.8.3) (2022-02-21)

## [1.8.2](https://github.com/webtorrent/webtorrent/compare/v1.8.1...v1.8.2) (2022-02-17)


### Bug Fixes

* **deps:** update dependency simple-peer to ^9.11.1 ([34df41a](https://github.com/webtorrent/webtorrent/commit/34df41a05438bcb765077187e04ac2d95d74ea74))

## [1.8.1](https://github.com/webtorrent/webtorrent/compare/v1.8.0...v1.8.1) (2022-02-03)


### Bug Fixes

* going for 1.8.1 ([e39057f](https://github.com/webtorrent/webtorrent/commit/e39057f634a66ce813482f87d2bfa862ebce5737))

# [1.8.0](https://github.com/webtorrent/webtorrent/compare/v1.7.4...v1.8.0) (2022-02-03)


### Features

* 1.8.0 fixing ([cac563b](https://github.com/webtorrent/webtorrent/commit/cac563b37d754fc4fa6993717bb6d1077b2bfd62))

## [1.7.4](https://github.com/webtorrent/webtorrent/compare/v1.7.3...v1.7.4) (2022-02-03)


### Bug Fixes

* getStreamURL docs ([#2257](https://github.com/webtorrent/webtorrent/issues/2257)) ([69bdd93](https://github.com/webtorrent/webtorrent/commit/69bdd93bdbe64ab9cc2c22e700560ecea02645e2))

## [1.7.3](https://github.com/webtorrent/webtorrent/compare/v1.7.2...v1.7.3) (2022-02-03)

## [1.7.2](https://github.com/webtorrent/webtorrent/compare/v1.7.1...v1.7.2) (2022-01-27)


### Bug Fixes

* **deps:** update webtorrent ([#2247](https://github.com/webtorrent/webtorrent/issues/2247)) ([6345ebe](https://github.com/webtorrent/webtorrent/commit/6345ebe4bae018a01bab3a25202a1864fb2230d5))

## [1.7.1](https://github.com/webtorrent/webtorrent/compare/v1.7.0...v1.7.1) (2022-01-20)

# [1.7.0](https://github.com/webtorrent/webtorrent/compare/v1.6.0...v1.7.0) (2022-01-20)


### Features

* add reqq field support ([#2246](https://github.com/webtorrent/webtorrent/issues/2246)) ([8de2a13](https://github.com/webtorrent/webtorrent/commit/8de2a136d68366d298e3ec01b5e4b17a4c4e074c))

# [1.6.0](https://github.com/webtorrent/webtorrent/compare/v1.5.11...v1.6.0) (2022-01-17)


### Features

* add BEP6 Fast Extension support ([#2243](https://github.com/webtorrent/webtorrent/issues/2243)) ([4f02de3](https://github.com/webtorrent/webtorrent/commit/4f02de3a445f3a9eb46c49a8964c9660bdf6e5d7))

## [1.5.11](https://github.com/webtorrent/webtorrent/compare/v1.5.10...v1.5.11) (2022-01-14)


### Bug Fixes

* **deps:** update dependency debug to ^4.3.3 ([#2228](https://github.com/webtorrent/webtorrent/issues/2228)) ([e268096](https://github.com/webtorrent/webtorrent/commit/e268096c201bc0b9e28d9c94cd7b9287c9f0f2e8))
* **deps:** update dependency mime to v3 ([#2216](https://github.com/webtorrent/webtorrent/issues/2216)) ([77da8cb](https://github.com/webtorrent/webtorrent/commit/77da8cb629078d55870684e56e0e0b2091c7e723))

## [1.5.10](https://github.com/webtorrent/webtorrent/compare/v1.5.9...v1.5.10) (2022-01-14)


### Bug Fixes

* use @webtorrent/http-node ([699d747](https://github.com/webtorrent/webtorrent/commit/699d747c477a26a5c8d4e6f1537e3e67b6c6ad94))

## [1.5.9](https://github.com/webtorrent/webtorrent/compare/v1.5.8...v1.5.9) (2022-01-12)

## [1.5.8](https://github.com/webtorrent/webtorrent/compare/v1.5.7...v1.5.8) (2021-10-26)


### Bug Fixes

* Prep for esm ([#2205](https://github.com/webtorrent/webtorrent/issues/2205)) ([ba6b799](https://github.com/webtorrent/webtorrent/commit/ba6b799ff614fedf26a29448620604aae3d2afec))

## [1.5.7](https://github.com/webtorrent/webtorrent/compare/v1.5.6...v1.5.7) (2021-10-25)


### Bug Fixes

* add storeOpts, specify store path, align with docs ([#2121](https://github.com/webtorrent/webtorrent/issues/2121)) ([6cd9b5f](https://github.com/webtorrent/webtorrent/commit/6cd9b5f74d7cd676259cd11daa2a568a3c5666d9))

## [1.5.6](https://github.com/webtorrent/webtorrent/compare/v1.5.5...v1.5.6) (2021-10-05)


### Bug Fixes

* http-node git location ([67ddac0](https://github.com/webtorrent/webtorrent/commit/67ddac03bc02e2923ec30af7054745a395d0280c))

## [1.5.5](https://github.com/webtorrent/webtorrent/compare/v1.5.4...v1.5.5) (2021-09-02)


### Bug Fixes

* DHT cleanup after torrent removal ([#2185](https://github.com/webtorrent/webtorrent/issues/2185)) ([320541f](https://github.com/webtorrent/webtorrent/commit/320541f271fdbd7303f544b6e99ab6b0a450fd8c)), closes [#1289](https://github.com/webtorrent/webtorrent/issues/1289)

## [1.5.4](https://github.com/webtorrent/webtorrent/compare/v1.5.3...v1.5.4) (2021-08-25)


### Bug Fixes

* handle done event when new files selected ([#2183](https://github.com/webtorrent/webtorrent/issues/2183)) ([c543788](https://github.com/webtorrent/webtorrent/commit/c5437886086f455c61f0ff71bbfecd3f9e6b2609))

## [1.5.3](https://github.com/webtorrent/webtorrent/compare/v1.5.2...v1.5.3) (2021-08-20)


### Bug Fixes

* **deps:** update dependency @webtorrent/semantic-release-config to v1.0.7 ([#2175](https://github.com/webtorrent/webtorrent/issues/2175)) ([404de99](https://github.com/webtorrent/webtorrent/commit/404de999f7dd83e7884c23511e92a3f5b28d784d))

## [1.5.2](https://github.com/webtorrent/webtorrent/compare/v1.5.1...v1.5.2) (2021-08-20)

## [1.5.1](https://github.com/webtorrent/webtorrent/compare/v1.5.0...v1.5.1) (2021-08-20)


### Bug Fixes

* **deps:** update dependency @webtorrent/semantic-release-config to v1.0.6 ([#2173](https://github.com/webtorrent/webtorrent/issues/2173)) ([210bda9](https://github.com/webtorrent/webtorrent/commit/210bda96533239c5a85eff4020c4684c29a3b758))

# [1.5.0](https://github.com/webtorrent/webtorrent/compare/v1.4.0...v1.5.0) (2021-08-19)


### Features

* add service worker server as an alternative to renderMedia ([#2098](https://github.com/webtorrent/webtorrent/issues/2098)) ([604943e](https://github.com/webtorrent/webtorrent/commit/604943e325c68721251a71c29d94e6a07ce0b31c))

# [1.4.0](https://github.com/webtorrent/webtorrent/compare/v1.3.10...v1.4.0) (2021-08-17)


### Features

* Add PE/MSE support ([#1820](https://github.com/webtorrent/webtorrent/issues/1820)) ([9938c94](https://github.com/webtorrent/webtorrent/commit/9938c949eee9c69c6774767e885e40f0a73898d9)), closes [#1384](https://github.com/webtorrent/webtorrent/issues/1384)

## [1.3.10](https://github.com/webtorrent/webtorrent/compare/v1.3.9...v1.3.10) (2021-08-16)

## [1.3.9](https://github.com/webtorrent/webtorrent/compare/v1.3.8...v1.3.9) (2021-08-06)


### Bug Fixes

* **deps:** update dependency create-torrent to ^5.0.1 ([#2167](https://github.com/webtorrent/webtorrent/issues/2167)) ([283cbf8](https://github.com/webtorrent/webtorrent/commit/283cbf84bffd4fbd0b267796dc45f00491bcdc92))

## [1.3.8](https://github.com/webtorrent/webtorrent/compare/v1.3.7...v1.3.8) (2021-08-06)


### Bug Fixes

* **deps:** update dependency create-torrent to v5 ([#2165](https://github.com/webtorrent/webtorrent/issues/2165)) ([501fd9e](https://github.com/webtorrent/webtorrent/commit/501fd9ed79d1127d74a29c47e230697ed372ba5e))

## [1.3.7](https://github.com/webtorrent/webtorrent/compare/v1.3.6...v1.3.7) (2021-08-05)


### Bug Fixes

* **deps:** update dependency torrent-discovery to ^9.4.4 ([#2164](https://github.com/webtorrent/webtorrent/issues/2164)) ([fb59617](https://github.com/webtorrent/webtorrent/commit/fb59617c016d5e64a86b3d20032664ba751019a9))

## [1.3.6](https://github.com/webtorrent/webtorrent/compare/v1.3.5...v1.3.6) (2021-08-05)


### Bug Fixes

* **deps:** update dependency bittorrent-dht to ^10.0.2 ([#2163](https://github.com/webtorrent/webtorrent/issues/2163)) ([fc89c78](https://github.com/webtorrent/webtorrent/commit/fc89c7822eecdece2b80bb2841e5131722f8bac3))

## [1.3.5](https://github.com/webtorrent/webtorrent/compare/v1.3.4...v1.3.5) (2021-08-04)


### Bug Fixes

* **deps:** update webtorrent ([#2162](https://github.com/webtorrent/webtorrent/issues/2162)) ([a00688b](https://github.com/webtorrent/webtorrent/commit/a00688b881bdec5109eeb45b6c7186d771ce5788))

## [1.3.4](https://github.com/webtorrent/webtorrent/compare/v1.3.3...v1.3.4) (2021-07-30)


### Bug Fixes

* **deps:** update webtorrent ([#2149](https://github.com/webtorrent/webtorrent/issues/2149)) ([d03203d](https://github.com/webtorrent/webtorrent/commit/d03203d01fe7a98b6d8631ebd0ceba2ea4b5ab22))

## [1.3.3](https://github.com/webtorrent/webtorrent/compare/v1.3.2...v1.3.3) (2021-07-25)


### Bug Fixes

* log error when failing to load blocklist ip set ([c0a07fb](https://github.com/webtorrent/webtorrent/commit/c0a07fbbbc3fb39dfeddf76383f22710b8ee1d54))

## [1.3.2](https://github.com/webtorrent/webtorrent/compare/v1.3.1...v1.3.2) (2021-07-24)


### Bug Fixes

* **deps:** update dependency speed-limiter to ^1.0.2 ([#2153](https://github.com/webtorrent/webtorrent/issues/2153)) ([17fb0e2](https://github.com/webtorrent/webtorrent/commit/17fb0e240fafc396bfd124ae667238c5c21d02e3))

## [1.3.1](https://github.com/webtorrent/webtorrent/compare/v1.3.0...v1.3.1) (2021-07-24)


### Bug Fixes

* speed limit for zero ([#2155](https://github.com/webtorrent/webtorrent/issues/2155)) ([88cca71](https://github.com/webtorrent/webtorrent/commit/88cca71fb46c1b9a65085ea674f5af5be4407a17))

# [1.3.0](https://github.com/webtorrent/webtorrent/compare/v1.2.6...v1.3.0) (2021-07-23)


### Features

* add speed limit to client ([#2062](https://github.com/webtorrent/webtorrent/issues/2062)) ([39bb33c](https://github.com/webtorrent/webtorrent/commit/39bb33c3cf694cdee45378ea4b30c66c93576d2a))

## [1.2.6](https://github.com/webtorrent/webtorrent/compare/v1.2.5...v1.2.6) (2021-07-23)


### Bug Fixes

* **deps:** update dependency create-torrent to ^4.7.1 ([#2145](https://github.com/webtorrent/webtorrent/issues/2145)) ([a75f1aa](https://github.com/webtorrent/webtorrent/commit/a75f1aa4b6173a0958d3c367070e27f0ef4e5e54))

## [1.2.5](https://github.com/webtorrent/webtorrent/compare/v1.2.4...v1.2.5) (2021-07-19)


### Bug Fixes

* **deps:** update dependency bittorrent-dht to ^10.0.1 ([#2139](https://github.com/webtorrent/webtorrent/issues/2139)) ([8476d9c](https://github.com/webtorrent/webtorrent/commit/8476d9cd8b218b74bc3544de007680bcc1db9d31))

## [1.2.4](https://github.com/webtorrent/webtorrent/compare/v1.2.3...v1.2.4) (2021-07-13)


### Bug Fixes

* call public FileStream.destroy method so cb is defined ([#2135](https://github.com/webtorrent/webtorrent/issues/2135)) ([b035cbe](https://github.com/webtorrent/webtorrent/commit/b035cbe9ea59d3fcfc6467dcf4f2f49da2f7c3d8))

## [1.2.3](https://github.com/webtorrent/webtorrent/compare/v1.2.2...v1.2.3) (2021-07-13)


### Bug Fixes

* store.put is async and might fail ([#2006](https://github.com/webtorrent/webtorrent/issues/2006)) ([3b3f65a](https://github.com/webtorrent/webtorrent/commit/3b3f65afaecb88dcdbb1f342a3643343eaf22c80))

## [1.2.2](https://github.com/webtorrent/webtorrent/compare/v1.2.1...v1.2.2) (2021-07-11)


### Bug Fixes

* **deps:** update dependency debug to ^4.3.2 ([#2127](https://github.com/webtorrent/webtorrent/issues/2127)) ([33f813d](https://github.com/webtorrent/webtorrent/commit/33f813d0a882d9d8727f5875a48ae40aab780656))

## [1.2.1](https://github.com/webtorrent/webtorrent/compare/v1.2.0...v1.2.1) (2021-07-11)


### Bug Fixes

* modernize code ([#2134](https://github.com/webtorrent/webtorrent/issues/2134)) ([46033ae](https://github.com/webtorrent/webtorrent/commit/46033ae52eca6e22301bb8ed9566c498d3494711))

# [1.2.0](https://github.com/webtorrent/webtorrent/compare/v1.1.6...v1.2.0) (2021-07-09)


### Features

* support adding paused torrents. ([#2004](https://github.com/webtorrent/webtorrent/issues/2004)) ([5c79c0a](https://github.com/webtorrent/webtorrent/commit/5c79c0a01424087e4c37776d86ef745191504df4))

## [1.1.6](https://github.com/webtorrent/webtorrent/compare/v1.1.5...v1.1.6) (2021-07-08)


### Bug Fixes

* **deps:** update dependency bittorrent-protocol to ^3.4.2 ([#2132](https://github.com/webtorrent/webtorrent/issues/2132)) ([7223cbf](https://github.com/webtorrent/webtorrent/commit/7223cbf7918e543542acf9d53da4bec0753a5e00))

## [1.1.5](https://github.com/webtorrent/webtorrent/compare/v1.1.4...v1.1.5) (2021-07-06)


### Bug Fixes

* ensure uTP peer address is IPv4 ([#2125](https://github.com/webtorrent/webtorrent/issues/2125)) ([100a2ae](https://github.com/webtorrent/webtorrent/commit/100a2aebe23420dd70842b3948896f8fecfee235))

## [1.1.4](https://github.com/webtorrent/webtorrent/compare/v1.1.3...v1.1.4) (2021-07-03)


### Bug Fixes

* **deps:** update webtorrent ([#2126](https://github.com/webtorrent/webtorrent/issues/2126)) ([87c69be](https://github.com/webtorrent/webtorrent/commit/87c69bea112b6a33175962fefac14c825a690312))

## [1.1.3](https://github.com/webtorrent/webtorrent/compare/v1.1.2...v1.1.3) (2021-07-02)


### Bug Fixes

* add preversion ([55fe206](https://github.com/webtorrent/webtorrent/commit/55fe206e3bd6b48e29018fb7f7bdf6e8055248a2))

## [1.1.2](https://github.com/webtorrent/webtorrent/compare/v1.1.1...v1.1.2) (2021-07-02)

## [1.1.1](https://github.com/webtorrent/webtorrent/compare/v1.1.0...v1.1.1) (2021-07-02)


### Bug Fixes

* Cleanup duplicated deselect() code ([#2113](https://github.com/webtorrent/webtorrent/issues/2113)) ([b94d713](https://github.com/webtorrent/webtorrent/commit/b94d71314bd7ae122c6150b6e92b3f2bd5da504a))

# [1.1.0](https://github.com/webtorrent/webtorrent/compare/v1.0.4...v1.1.0) (2021-06-30)


### Features

* Use a cache on the chunk store ([#2095](https://github.com/webtorrent/webtorrent/issues/2095)) ([d540058](https://github.com/webtorrent/webtorrent/commit/d540058ebd7f32e613d26c33e8a99b16d39a13d8))

## [1.0.4](https://github.com/webtorrent/webtorrent/compare/v1.0.3...v1.0.4) (2021-06-30)

## [1.0.3](https://github.com/webtorrent/webtorrent/compare/v1.0.2...v1.0.3) (2021-06-30)


### Bug Fixes

* remove deprecated functionality ([#2118](https://github.com/webtorrent/webtorrent/issues/2118)) ([2bf6cf4](https://github.com/webtorrent/webtorrent/commit/2bf6cf42e09c448cab0dddcd74ea9a49dc3f18a0))

## [1.0.2](https://github.com/webtorrent/webtorrent/compare/v1.0.1...v1.0.2) (2021-06-23)


### Bug Fixes

* enable UTP by default if there's support ([0df9eb6](https://github.com/webtorrent/webtorrent/commit/0df9eb60171ff18cec052e8f31a515b341bdd03a))

## [1.0.1](https://github.com/webtorrent/webtorrent/compare/v1.0.0...v1.0.1) (2021-06-17)


### Bug Fixes

* make utp-native optional ([#1966](https://github.com/webtorrent/webtorrent/issues/1966)) ([73c941c](https://github.com/webtorrent/webtorrent/commit/73c941c6eb3b539efbbbb499ab3d033531347b19))

# [1.0.0](https://github.com/webtorrent/webtorrent/compare/v0.118.0...v1.0.0) (2021-05-21)


### Bug Fixes

* bring back release config ([d78055b](https://github.com/webtorrent/webtorrent/commit/d78055b2fd6275f9ba18474f601c0a4d3284231c))
* getAnnounceOpts ([#2075](https://github.com/webtorrent/webtorrent/issues/2075)) ([633b922](https://github.com/webtorrent/webtorrent/commit/633b9224b7c7176599a5e53775de1a48d8e864b5))
* install config ([6ba44c4](https://github.com/webtorrent/webtorrent/commit/6ba44c444f6af6f070c3059ad00ca2d10868058d))
* **deps:** update webtorrent ([18a8962](https://github.com/webtorrent/webtorrent/commit/18a8962328fb42e1ebc56ed5dbe73b97f096fbd1))
* ci ([134721c](https://github.com/webtorrent/webtorrent/commit/134721c16d3338270cdcef300bb164720b1d3ae7))
* github ci secrets ([fc7ec9f](https://github.com/webtorrent/webtorrent/commit/fc7ec9f223079a3c1a2a8b54a4cca022aef4c440))


### chore

* add release ([#2077](https://github.com/webtorrent/webtorrent/issues/2077)) ([db9de2d](https://github.com/webtorrent/webtorrent/commit/db9de2d99a260d68f2719396835a09b2d0742e9f))


### Reverts

* version strategy gh actions ([1cba675](https://github.com/webtorrent/webtorrent/commit/1cba6753d449ae46f287e9104ed1f0330d640911))


### BREAKING CHANGES

* chore: add release
* add semantic release config
* Update release.yml

# WebTorrent Version History

## v0.112.0 - 2020-11-05

- Ensure that `appendTo` callback is called once video tag is added to DOM, not after play (#1967)

## v0.111.0 - 2020-11-05

- Add Local Service Discovery (BEP14)
- bitfield@4

## v0.110.1 - 2020-11-03

- Fix BEP53 implementation

## v0.110.0 - 2020-11-03

- Support Implement the peer address property (x.pe) from BEP09

## v0.109.2 - 2020-10-27

- Fix "Cannot read property 'utp' of null"

## v0.109.1 - 2020-10-23

- Peer reconnect timeout throwing error after torrent is destroyed

## v0.109.0 - 2020-10-22

- refactor torrent._rechoke()
- simple-get@4
- electron@9
- deps
- Add stale bot config
- Create no-response.yml
- Create config.yml
- Update no-response.yml
- Add uTP support (BEP29)
- check if torrent is destroyed before emitting download/upload event
- ut_pex 2.0.1
- browserify@17
- electron@10

## v0.108.6 - 2020-05-29

- update deps

## v0.108.5 - 2020-05-29

- bump deps

## v0.108.4 - 2020-05-28

- add test for downloading from a manually added peer
- fix: not setting initial wire interest
- update interest when a peer's bitfield changes

## v0.108.3 - 2020-05-15

- Create `webtorrent.chromeapp.js`
- update bittorrent-dht to version 10.0.0
- Change parseRange.parse to parseRange

## v0.108.2 - 2020-05-10

- implement store destruction option
- Fix drag-drop.min.js link
- update parse-numeric-range to version 1.2.0
- browsers: add tests for safari, edge, android, iphone

## v0.108.1 - 2020-04-01

- fix ratio calculation

## v0.108.0 - 2020-04-01

- Check if client is set when debug logging
- downgrade end-of-stream to v1.4.1
- `private` option overrides default, only if it's defined
- use native Set instead of uniq library
- Improve code readability

## v0.107.17 - 2019-11-12

- Unbreak built file

## v0.107.16 - 2019-09-10

- fix git commit reference to `http-node` package

## v0.107.15 - 2019-09-10

- Return server from server.listen for method chaining to work

## v0.107.14 - 2019-09-10

- Update .gitignore

## v0.107.13 - 2019-09-10

- Added tests to check the order of torrent events

## v0.107.12 - 2019-09-08

- Fixed how first piece's irrelevant bytes are calculated

## v0.107.11 - 2019-09-07

- Added timeout option for `requestIdlecallback` to prevent longer delays in download

## v0.107.10 - 2019-09-07

- Server now uses relative urls

## v0.107.9 - 2019-09-07

- Added a check in case user destroys torrent in response to `metadata` event

## v0.107.8 - 2019-09-07

- Fixed the torrent event emission order; now `metadata` is emitted before `ready` and `done`

## v0.107.7 - 2019-09-06

- Updated to simple-sha1@3
- Updated jsdelivr urls to use latest Webtorrent

## v0.107.6 - 2019-08-28

- Fixed XSS vulnerability in the http Server ([issue](https://github.com/brave/brave-browser/issues/5821))

## v0.107.5 - 2019-08-22

- No meaningful changes

## v0.107.4 - 2019-08-19

- Added api documentation for some torrent properties
- Bug fix: trackers now recieve 0 while seeding file instead of the file size
- Updated org-wide security policies and contributing guidelines

## v0.107.3 - 2019-08-10

- No meaningful changes

## v0.107.2 - 2019-08-09

- Scripts are now more verbose

## v0.107.1 - 2019-08-09

- Updated to stream-to-bolob-url@3
- Added `chromeapp` field to package.json for specifying Chrome App dependency substitutions

## v0.107.0 - 2019-08-07

- Smaller build with tinify
- Added size-disc script to visualize bundle

## v0.106.0 - 2019-08-05

- Updated to electron@6
- Dropped support for node versions < 10

## v0.105.3 - 2019-08-02

- Now uses 'application/octet-stream' mimetype as fallback instead of null

## v0.105.2 - 2019-07-31

- Fixed server `hostname` option to mitigate DNS rebinding attack ([issue](https://github.com/webtorrent/webtorrent/pull/1678))

## v0.105.1 - 2019-07-24

- Bug fixed: Video streaming is now fixed in Brave nightly and chromium nightly ([issue](https://github.com/brave/brave-browser/issues/5358))

## v0.105.0 - 2019-07-06

- Updated to parse-torrent@7
- Added manual verification for torrent files

## v0.104.0 - 2019-06-29

- Updated to chunk-store-stream@4
- Updated to multistream@3
- Updated to create-torrent@4
- Dropped support for node versions < 8

## v0.103.4 - 2019-06-19

- No meaningful changes

## v0.103.3 - 2019-06-19

- Updated to electron@5

## v0.103.2 - 2019-06-12

- Added the ability to close and restore streaming server

## v0.103.1 - 2019-03-11

- Updated to electron@4
- Bug fixed: File progress is no longer shown in negative

## v0.103.0 - 2018-12-11

- No longer verifies file hashes passed to seed
- No longer calls torrent.load() when seeding FS filepath
- Reduced download impact on slower computers: now download chunks at a lower priority ([rationale](https://github.com/webtorrent/webtorrent/pull/1513))

## v0.102.4 - 2018-08-31

- No meaningful changes

## v0.102.3 - 2018-08-31

- Removed xtend
- Removed the concurrency limit in browser
- Reduced installtion size by removing zero-fill
- Updated to bittorrent-dht@9

## v0.102.2 - 2018-08-28

- Update some webtorrent packages to ES6 ([webtorrent/#1443](https://github.com/webtorrent/webtorrent/issues/1443))

## v0.102.1 - 2018-08-10

- No meaningful changes

## v0.102.0 - 2018-08-04

- Updated to chunk-store-stream@3
- Updated to immediate-chunk-store@2

## v0.101.2 - 2018-07-27

- Updated to torrent-discovery@9.0.2

## v0.101.1 - 2018-07-27

- Updated to bittorrent-protocol@3
- Optimized peers:  peers now start as uninterested and only move to interested if/once they have a piece that we need  ([webtorrent/#1059](https://github.com/webtorrent/webtorrent/issues/1059))

## v0.101.0 - 2018-07-19

- No meaningful changes

## v0.100.0 - 2018-05-23

- Implemented BEP53 to alow file selection using `select only` parameter in MagnetURIs ([webtorrent/#1395](https://github.com/webtorrent/webtorrent-hybrid/issues/1395))

## v0.99.4 - 2018-05-03

- Use updated `babel-minify` minifier instead of deprecated `babili`

## v0.99.3 - 2018-04-26

- Add extra check to prevent invalid `peer.conn.remotePort` from being used ([webtorrent-hybrid/#76](https://github.com/webtorrent/webtorrent-hybrid/issues/76))

## v0.99.2 - 2018-04-24

- Use `.npmignore` to prevent unneeded files from being included in the published package

## v0.99.1 - 2018-04-24

- Expose `WebTorrent.VERSION` (#1358)
- Update to simple-get@3
- Update to parse-torrent@6

## v0.99.0 - 2018-04-19

- `renderTo()`/`appendTo()` does not autoplay by default anymore ([rationale](https://github.com/webtorrent/webtorrent/commit/fbbffbbb445096a909c851cdc4ca15204b9952b9))
  - Pass `{autoplay: true}` to `renderTo()`/`appendTo()` to get the old behavior.
- `renderTo()`/`appendTo()` has a new `muted` option to mute the video by default.

## v0.98.24 - 2018-03-02

- Add hostname option to mitigate DNS rebinding (#1260)
- Update to simple-peer@9
- Browser testing: switch from `zuul` to `airtap`

## v0.98.23 - 2018-02-20

- Update to bitfield@2

## v0.98.22 - 2018-02-17

- Update to browserify@16
- Update to bittorrent-dht@8
- Update to pump@3

## v0.98.21 - 2018-01-26

- Update to pump@2
- Update to mime@2
- Update to cross-spawn@6
- Update to browserify@15

## v0.98.20 - 2017-10-17

- Fix `file.downloaded` for last piece
- Fix destroyed torrent debug
- Update to mime@2
- Update to debug@3
- Update to electron@1

## v0.98.19 - 2017-06-25

- Add `origin` option for torrent.createServer() (#1096)
- Add `file.progress` property (#1140)
- Switch to ES6-compatible minifier

## v0.98.18 - 2017-04-14

- Transfer webtorrent from @feross to @webtorrent organization.

## v0.98.17 - 2017-04-13

- Fix uncaught exception (#1103)

## v0.98.16 - 2017-04-07

- Update to simple-peer@8

## v0.98.15 - 2017-03-30

- No meaningful changes

## v0.98.14 - 2017-03-17

- Add filename to URLs on server index page (#1078)

## v0.98.13 - 2017-03-16

- No meaningful changes

## v0.98.12 - 2017-03-13

- Fix files under 16Kb are not downloaded correctly (#1077)

## v0.98.11 - 2017-03-13

- Fix detection of seeding peers (#1076)

## v0.98.10 - 2017-03-06

- Update to bittorrent-tracker@9

## v0.98.9 - 2017-03-01

- Update to finalhandler@1
- Update to simple-peer@7

## v0.98.8 - 2017-02-13

- wait to notify() or updateInterest() at end of GC (#1044)
- Update to cross-spawn@5

## v0.98.7 - 2017-02-11

- Change os.tmpDir() to os.tmpdir() (#1043)

## v0.98.6 - 2017-02-09

- Refactor http server; support content-disposition (#1039)

## v0.98.5 - 2017-02-02

- Don't print debug log after torrent is destroyed

## v0.98.4 - 2017-02-02

- Be more defensive: prevent code from running after destroy
- Fix "Cannot read property 'complete' of null" (#1022)
- Include infoHash in torrent.js debug logs
- Update to browserify@14

## v0.98.3 - 2017-01-19

- Emit more warnings (#1021)
- Set user-agent header for http tracker requests (#1019)

## v0.98.2 - 2017-01-18

- Don't send 'completed' event to tracker on client.seed (#991)
- Set user-agent header for http tracker requests (#962)

## v0.98.1 - 2017-01-13

- Don't emit 'completed' on client.seed
- Do not choke on web seeds (#972)

## v0.98.0 - 2016-11-23

- Add property for downloaded bytes per file (`file.downloaded`) (#974)
- Cross-origin HTTP redirect workaround for web seeds (#909)

## v0.97.2 - 2016-09-26

- Creating a WebTorrent client with the `{tracker: false}` to disable communication with trackers should not affect creating a torrent with `.seed()`. The resulting torrent file should still contain the normal `announce` field. (#928)
- Add more peer ID entropy

## v0.97.1 - 2016-09-17

- Handle invalid range handers instead of throwing (#921)

## v0.97.0 - 2016-09-17

- Add option to disable BEP19 web seeds (`webSeeds` option to the `WebTorrent` constructor)

## v0.96.5 - 2016-09-13

- Fix exceptions in `server.close()` and `server.destroy()`

## v0.96.4 - 2016-08-23

- Warn when WebTorrent is installed on Node.js older than v4.0.0.

## v0.96.3 - 2016-08-22

- Better docs for .renderTo()

## v0.96.2 - 2016-08-20

- Replace 'hat' with 'randombytes'
- Better debug logs

## v0.96.1 - 2016-08-18

- Prevent possible stack overflow

## v0.96.0 - 2016-08-03

- Add options to disable autoplay/hide controls with `appendTo()` and `renderTo()`

## v0.95.6 - 2016-07-28

- Allow deselecting the entire torrent with `deselect()` to happen earlier

## v0.95.5 - 2016-07-26

- Fix support for FileList input to client.seed()

## v0.95.4 - 2016-07-26

- Skip blocklist logic when opts.blocklist is not set

## v0.95.3 - 2016-07-26

- Fix WebTorrent version string

## v0.95.2 - 2016-06-22

- HEAD requests to HTTP server should not send entire body
- WebTorrent, LLC is now the steward of the project

## v0.95.1 - 2016-06-15

- Emit 'seed' event on the torrent object

## v0.95.0 - 2016-06-15

- API: Add `file.getBlob()` method
- Fix rare exception in `lib/tcp-pool.js`

## v0.94.4 - 2016-06-10

- Support torrent with a single 0 byte file
- Use `<` since it handles `NaN` in a predictable way, i.e. `false`

## v0.94.3 - 2016-05-30

- Use `safe-buffer` for improved buffer safety

## v0.94.2 - 2016-05-28

- Fix rare exception in `lib/file.js`

## v0.94.1 - 2016-05-26

- Make WebTorrent user agent string consistent across whole codebase

## v0.94.0 - 2016-05-19

- Support exact source (xs) paramter of magnet URIs, for retreiving metadata

## v0.93.4 - 2016-05-17

- Fix rare exception caused by race condition in `lib/peer.js`

## v0.93.3 - 2016-05-13

- Don't unset `{tracker: {wrtc: false}}` option to `WebTorrent` constructor.

## v0.93.2 - 2016-05-12

- When a duplicate torrent is added, don't emit the 'infoHash' event after 'error'. The 'error' event should be the last event.

## v0.93.1 - 2016-05-08

- Remove `path-exists` dependency.

## v0.93.0 - 2016-05-08

- Move tracker options (`rtcConfig` and `wrtc`) into `opts.tracker`.

  Before:

  ```js
  var client = new WebTorrent({ rtcConfig: {}, wrtc: {} })
  ```

  After:

  ```js
  var client = new WebTorrent({ tracker: { rtcConfig: {}, wrtc: {} } })
  ```

## v0.92.0 - 2016-05-05

- Add new event: `torrent.on('noPeers', function (announceType) {})`

  Emitted whenever a DHT or tracker announce occurs, but no peers have been found.  `announceType` is either `'tracker'` or `'dht'` depending on which announce occurred to trigger this event.  Note that if you're attempting to discover peers from both a tracker and a DHT, you'll see this event separately for each.

## v0.91.4 - 2016-05-05

- Fix exception: "peer.\_destroy is not a function" when calling `torrent.pause()`

## v0.91.3 - 2016-05-04

- Fix `torrent.swarm` from causing an infinite recursion.

## v0.91.2 - 2016-04-28

- Test node v6

## v0.91.1 - 2016-04-24

- Emit 'done' event *after* sending the `'complete'` message to the tracker.

## v0.91.0 - 2016-04-21

### Added

- `client.listening` property to signal whether TCP server is listening for incoming
  connections.

- `client.dhtPort` property reflects the actual DHT port when user doesn't specify one
  (this is parallel to `client.torrentPort` for the TCP torrent listening server)

### Changed

- Merged `Swarm` class into `Torrent` object. Properties on `torrent.swarm` (like
  `torrent.swarm.wires`) now exist on `torrent` (e.g. `torrent.wires`).

- Deprecate: Do not use `torrent.swarm` anymore. Use `torrent` instead.

- `torrent.addPeer` can no longer be called before the `infoHash` event has been
  emitted.

- Remove `torrent.on('listening')` event. Use `client.on('listening')` instead.

- Remove support from `TCPPool` for listening on multiple ports. This was not used by
  WebTorrent and just added complexity. There is now a single `TCPPool` instance for the
  whole WebTorrent client.

- Deprecate: Do not use `client.download()` anymore. Use `client.add()` instead.

- Only pass `torrent.infoHash` to the Chunk Store constructor, instead of the `Torrent`
  instance itself, to prevent accidental memory leaks of the `Torrent` object by the
  store. (Open an issue if you were using other properties. They can be re-added.)

- Non-fatal errors with a single torrent will be emitted at `torrent.on('error')`. You
  should listen to this event. Previously, all torrent errors were also emitted on
  `client.on('error')` and handling `torrent.on('error')` was optional. This design is
  better since now it is possible to distinguish between fatal client errors
  (`client.on('error')`) when the whole client becomes unusable versus recoverable errors
  where only a single torrent fails (`torrent.on('error')`) but the client can continue to
  be used. However, if there is no `torrent.on('error')` event, then the error will be
  forwarded to `client.on('error')`. This prevents crashing the client when the user
  only has a listener on the client, but it makes it impossible for them to determine
  a client error versus a torrent error.

- Removed `torrent.numBlockedPeers` property. Use the `blockedPeer` event to track this
  yourself.

### Fixed

- If `client.get` is passed a `Torrent` instance, it now only returns it if it is present
  in the client.

- Errors creating a torrent with `client.seed` are now emitted on the returned `torrent`
  object instead of the client (unless there is no event listeners on
  `torrent.on('error')` as previously discussed). The torrent object is now also destroyed
  automatically for the user, as was probably expected.

- Do not return existing torrent object when duplicate torrent is added. Fire an
  `'error'` event instead.

- Memory leaks of `Torrent` object caused by many internal subclasses of WebTorrent,
  including `RarityMap`, `TCPPool`, `WebConn`, `Server`, `File`.

- `client.ratio` and `torrent.ratio` are now calculated as `uploaded / received` instead
  of `uploaded / downloaded`.

## Previous versions

We did not maintain a changelog for versions prior to v0.91.0. The initial release of WebTorrent was on Dec 4, 2013.
