import { HexBlob } from '@cardano-sdk/util';
import type * as Cardano from '../../Cardano/index.js';
export declare class ProtocolVersion {
    #private;
    constructor(major: number, minor: number);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): ProtocolVersion;
    toCore(): Cardano.ProtocolVersion;
    static fromCore(version: Cardano.ProtocolVersion): ProtocolVersion;
    major(): number;
    setMajor(major: number): void;
    minor(): number;
    setMinor(minor: number): void;
}
//# sourceMappingURL=ProtocolVersion.d.ts.map