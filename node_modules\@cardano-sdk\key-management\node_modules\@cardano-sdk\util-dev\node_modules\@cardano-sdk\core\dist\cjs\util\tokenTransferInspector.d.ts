import { Inspector } from './txInspector';
import type * as Cardano from '../Cardano';
import type { AssetInfo } from '../Asset';
import type { AssetProvider } from '../Provider';
import type { Logger } from 'ts-log';
import type { Milliseconds } from './time';
export declare type AssetInfoWithAmount = {
    amount: Cardano.Lovelace;
    assetInfo: AssetInfo;
};
export declare type TokenTransferValue = {
    assets: Map<Cardano.AssetId, AssetInfoWithAmount>;
    coins: Cardano.Lovelace;
};
export declare type TokenTransferInspection = {
    fromAddress: Map<Cardano.PaymentAddress, TokenTransferValue>;
    toAddress: Map<Cardano.PaymentAddress, TokenTransferValue>;
};
export interface TokenTransferInspectorArgs {
    inputResolver: Cardano.InputResolver;
    fromAddressAssetProvider: AssetProvider;
    toAddressAssetProvider: AssetProvider;
    timeout: Milliseconds;
    logger: Logger;
}
export declare type TokenTransferInspector = (args: TokenTransferInspectorArgs) => Inspector<TokenTransferInspection>;
export declare const tokenTransferInspector: TokenTransferInspector;
//# sourceMappingURL=tokenTransferInspector.d.ts.map