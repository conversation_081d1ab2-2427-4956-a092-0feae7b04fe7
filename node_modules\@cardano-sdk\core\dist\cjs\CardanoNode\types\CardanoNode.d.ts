import { <PERSON>lace, PoolId, VrfVkHex } from '../../Cardano';
import { HealthCheckResponse } from '../../Provider';
import { Milliseconds } from '../../util';
export interface EraSummary {
    parameters: {
        epochLength: number;
        slotLength: Milliseconds;
    };
    start: {
        slot: number;
        time: Date;
    };
}
export declare type StakeDistribution = Map<PoolId, {
    stake: {
        pool: Lovelace;
        supply: Lovelace;
    };
    vrf: VrfVkHex;
}>;
export interface CardanoNode {
    eraSummaries: () => Promise<EraSummary[]>;
    systemStart: () => Promise<Date>;
    stakeDistribution: () => Promise<StakeDistribution>;
    healthCheck(): Promise<HealthCheckResponse>;
}
//# sourceMappingURL=CardanoNode.d.ts.map