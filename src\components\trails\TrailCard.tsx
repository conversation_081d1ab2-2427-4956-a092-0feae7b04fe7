'use client'

import { useState } from 'react'
import { MapP<PERSON>, Clock, TrendingUp, Calendar } from 'lucide-react'
import { BookingModal } from './BookingModal'

interface Trail {
  id: string
  name: string
  location: string
  difficulty: string
  distance: string
  duration: string
  image: string
  description: string
  price: number
  available: boolean
}

interface TrailCardProps {
  trail: Trail
}

export function TrailCard({ trail }: TrailCardProps) {
  const [showBookingModal, setShowBookingModal] = useState(false)

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return 'bg-green-100 text-green-800'
      case 'moderate':
        return 'bg-yellow-100 text-yellow-800'
      case 'hard':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const handleBookTrail = () => {
    if (!trail.available) return
    setShowBookingModal(true)
  }

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
      {/* Trail Image */}
      <div className="relative h-48 bg-gray-200">
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
        <div className="absolute top-4 left-4">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(trail.difficulty)}`}>
            {trail.difficulty}
          </span>
        </div>
        <div className="absolute top-4 right-4">
          {trail.available ? (
            <span className="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium">
              Available
            </span>
          ) : (
            <span className="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium">
              Booked
            </span>
          )}
        </div>
        {/* Placeholder for trail image */}
        <div className="w-full h-full flex items-center justify-center text-gray-400">
          <MapPin className="h-12 w-12" />
        </div>
      </div>

      {/* Trail Info */}
      <div className="p-6">
        <div className="mb-4">
          <h3 className="text-xl font-semibold text-gray-900 mb-2">{trail.name}</h3>
          <div className="flex items-center text-gray-600 mb-2">
            <MapPin className="h-4 w-4 mr-1" />
            <span className="text-sm">{trail.location}</span>
          </div>
          <p className="text-gray-600 text-sm">{trail.description}</p>
        </div>

        {/* Trail Stats */}
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="flex items-center text-sm text-gray-600">
            <TrendingUp className="h-4 w-4 mr-2 text-blue-500" />
            <span>{trail.distance}</span>
          </div>
          <div className="flex items-center text-sm text-gray-600">
            <Clock className="h-4 w-4 mr-2 text-green-500" />
            <span>{trail.duration}</span>
          </div>
        </div>

        {/* Pricing and Rewards */}
        <div className="border-t pt-4">
          <div className="flex items-center justify-between mb-4">
            <div>
              <div className="text-lg font-semibold text-gray-900">
                ₨{trail.price.toLocaleString()}
              </div>
              <div className="text-sm text-gray-600">per person</div>
            </div>
            <div className="text-right">
              <div className="text-sm font-medium text-green-600">Earn Rewards</div>
              <div className="text-xs text-gray-500">50 TREK + NFT</div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-2">
            <button
              type="button"
              onClick={handleBookTrail}
              disabled={!trail.available}
              className={`flex-1 py-2 px-4 rounded-lg font-medium transition-colors ${
                trail.available
                  ? 'bg-green-600 text-white hover:bg-green-700'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }`}
            >
              {trail.available ? 'Book Trail' : 'Unavailable'}
            </button>
            <button
              type="button"
              title="View availability calendar"
              className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
              onClick={() => setShowBookingModal(true)}
            >
              <Calendar className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Blockchain Features */}
        <div className="mt-4 p-3 bg-blue-50 rounded-lg">
          <div className="text-xs font-medium text-blue-800 mb-1">Blockchain Features</div>
          <div className="flex items-center justify-between text-xs text-blue-600">
            <span>✓ On-chain booking</span>
            <span>✓ NFT certificate</span>
            <span>✓ Token rewards</span>
          </div>
        </div>
      </div>

      {/* Booking Modal */}
      <BookingModal
        trail={trail}
        isOpen={showBookingModal}
        onClose={() => setShowBookingModal(false)}
      />
    </div>
  )
}
