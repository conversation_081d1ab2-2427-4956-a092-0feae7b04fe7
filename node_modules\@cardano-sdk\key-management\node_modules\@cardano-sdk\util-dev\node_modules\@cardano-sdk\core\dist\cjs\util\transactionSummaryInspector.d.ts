import * as Crypto from '@cardano-sdk/crypto';
import { Inspector } from './txInspector';
import type * as Cardano from '../Cardano';
import type { AssetInfoWithAmount } from './tokenTransferInspector';
import type { AssetProvider } from '../Provider';
import type { Logger } from 'ts-log';
import type { Milliseconds } from './time';
interface TransactionSummaryInspectorArgs {
    addresses: Cardano.PaymentAddress[];
    rewardAccounts: Cardano.RewardAccount[];
    inputResolver: Cardano.InputResolver;
    protocolParameters: Cardano.ProtocolParameters;
    assetProvider: AssetProvider;
    dRepKeyHash?: Crypto.Ed25519KeyHashHex;
    timeout: Milliseconds;
    logger: Logger;
}
export declare type TransactionSummaryInspection = {
    assets: Map<Cardano.AssetId, AssetInfoWithAmount>;
    coins: Cardano.Lovelace;
    collateral: Cardano.Lovelace;
    deposit: Cardano.Lovelace;
    returnedDeposit: Cardano.Lovelace;
    fee: Cardano.Lovelace;
    unresolved: {
        inputs: Cardano.TxIn[];
        value: Cardano.Value;
    };
};
export declare type TransactionSummaryInspector = (args: TransactionSummaryInspectorArgs) => Inspector<TransactionSummaryInspection>;
export declare const getCollateral: (tx: Cardano.Tx, inputResolver: Cardano.InputResolver, addresses: Cardano.PaymentAddress[]) => Promise<Cardano.Lovelace>;
export declare const transactionSummaryInspector: TransactionSummaryInspector;
export {};
//# sourceMappingURL=transactionSummaryInspector.d.ts.map