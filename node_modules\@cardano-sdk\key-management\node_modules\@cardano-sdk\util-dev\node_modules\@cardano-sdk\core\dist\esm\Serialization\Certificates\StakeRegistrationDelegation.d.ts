import * as Crypto from '@cardano-sdk/crypto';
import { HexBlob } from '@cardano-sdk/util';
import type * as Cardano from '../../Cardano/index.js';
export declare class StakeRegistrationDelegation {
    #private;
    constructor(stakeCredential: Cardano.Credential, deposit: Cardano.Lovelace, poolKeyHash: Crypto.Ed25519KeyHashHex);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): StakeRegistrationDelegation;
    toCore(): Cardano.StakeRegistrationDelegationCertificate;
    static fromCore(deleg: Cardano.StakeRegistrationDelegationCertificate): StakeRegistrationDelegation;
    stakeCredential(): Cardano.Credential;
    deposit(): Cardano.Lovelace;
    poolKeyHash(): Crypto.Ed25519KeyHashHex;
}
//# sourceMappingURL=StakeRegistrationDelegation.d.ts.map