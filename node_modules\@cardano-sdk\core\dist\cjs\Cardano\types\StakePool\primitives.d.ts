import * as Crypto from '@cardano-sdk/crypto';
import { OpaqueString } from '@cardano-sdk/util';
export declare type PoolId = OpaqueString<'PoolId'>;
export declare const PoolId: {
    (value: string): PoolId;
    fromKeyHash(value: Crypto.Ed25519KeyHashHex): PoolId;
    toKeyHash(poolId: PoolId): Crypto.Ed25519KeyHashHex;
};
export declare type PoolIdHex = OpaqueString<'PoolIdHex'>;
export declare const PoolIdHex: (value: string) => PoolIdHex;
export declare type VrfVkHex = OpaqueString<'VrfVkHex'>;
export declare const VrfVkHex: (target: string) => VrfVkHex;
//# sourceMappingURL=primitives.d.ts.map