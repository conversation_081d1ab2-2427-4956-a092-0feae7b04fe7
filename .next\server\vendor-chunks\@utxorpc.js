"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@utxorpc";
exports.ids = ["vendor-chunks/@utxorpc"];
exports.modules = {

/***/ "(ssr)/./node_modules/@utxorpc/sdk/lib/node/index.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@utxorpc/sdk/lib/node/index.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CardanoQueryClient: () => (/* binding */ QueryClient),\n/* harmony export */   CardanoSubmitClient: () => (/* binding */ SubmitClient),\n/* harmony export */   CardanoSyncClient: () => (/* binding */ SyncClient)\n/* harmony export */ });\n/* harmony import */ var _connectrpc_connect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @connectrpc/connect */ \"(ssr)/./node_modules/@connectrpc/connect/dist/esm/promise-client.js\");\n/* harmony import */ var _connectrpc_connect_node__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @connectrpc/connect-node */ \"(ssr)/./node_modules/@connectrpc/connect-node/dist/esm/index.js\");\n/* harmony import */ var buffer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! buffer */ \"buffer\");\n/* harmony import */ var _utxorpc_spec__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @utxorpc/spec */ \"(ssr)/./node_modules/@utxorpc/spec/lib/index.mjs\");\n// src/cardano.ts\n\n\n// src/grpcTransport.node.ts\n\nvar createGrpcTransport = _connectrpc_connect_node__WEBPACK_IMPORTED_MODULE_0__.createGrpcTransport;\n\n// src/cardano.ts\n\n\n\n// src/common.ts\nfunction metadataInterceptor(options) {\n  return (next) => async (req) => {\n    if (!!(options == null ? void 0 : options.headers)) {\n      Object.entries(options.headers).forEach(\n        ([key, value]) => req.header.set(key, value)\n      );\n    }\n    return await next(req);\n  };\n}\n\n// src/cardano.ts\nfunction anyChainToBlock(msg) {\n  return msg.chain.case == \"cardano\" ? msg.chain.value : null;\n}\nfunction pointToBlockRef(p) {\n  return new _utxorpc_spec__WEBPACK_IMPORTED_MODULE_2__.sync.BlockRef({\n    index: BigInt(p.slot),\n    hash: new Uint8Array(buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(p.hash, \"hex\"))\n  });\n}\nfunction blockRefToPoint(r) {\n  return {\n    slot: r.index.toString(),\n    hash: buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(r.hash).toString(\"hex\")\n  };\n}\nfunction anyUtxoToChain(u) {\n  switch (u.parsedState.case) {\n    case \"cardano\":\n      return {\n        txoRef: u.txoRef,\n        parsedValued: u.parsedState.value,\n        nativeBytes: u.nativeBytes\n      };\n    default:\n      throw Error(\"source is not Cardano data\");\n  }\n}\nfunction anyParamsToChain(p) {\n  switch (p.params.case) {\n    case \"cardano\":\n      return p.params.value;\n    default:\n      throw Error(\"source is not Cardano data\");\n  }\n}\nvar SyncClient = class {\n  constructor(options) {\n    let headerInterceptor = metadataInterceptor(options);\n    const transport = createGrpcTransport({\n      httpVersion: \"2\",\n      baseUrl: options.uri,\n      interceptors: [headerInterceptor]\n    });\n    this.inner = (0,_connectrpc_connect__WEBPACK_IMPORTED_MODULE_3__.createPromiseClient)(_utxorpc_spec__WEBPACK_IMPORTED_MODULE_2__.syncConnect.SyncService, transport);\n  }\n  async *followTip(intersect) {\n    const req = new _utxorpc_spec__WEBPACK_IMPORTED_MODULE_2__.sync.FollowTipRequest({\n      intersect: intersect == null ? void 0 : intersect.map((p) => pointToBlockRef(p))\n    });\n    const res = this.inner.followTip(req);\n    for await (const any of res) {\n      switch (any.action.case) {\n        case \"apply\":\n          yield {\n            action: \"apply\",\n            block: anyChainToBlock(any.action.value)\n          };\n          break;\n        case \"undo\":\n          yield {\n            action: \"undo\",\n            block: anyChainToBlock(any.action.value)\n          };\n          break;\n        case \"reset\":\n          yield {\n            action: \"reset\",\n            point: blockRefToPoint(any.action.value)\n          };\n      }\n    }\n  }\n  async fetchBlock(p) {\n    const req = pointToBlockRef(p);\n    const res = await this.inner.fetchBlock({ ref: [req] });\n    return anyChainToBlock(res.block[0]);\n  }\n};\nvar QueryClient = class {\n  constructor(options) {\n    let headerInterceptor = metadataInterceptor(options);\n    const transport = createGrpcTransport({\n      httpVersion: \"2\",\n      baseUrl: options.uri,\n      interceptors: [headerInterceptor]\n    });\n    this.inner = (0,_connectrpc_connect__WEBPACK_IMPORTED_MODULE_3__.createPromiseClient)(_utxorpc_spec__WEBPACK_IMPORTED_MODULE_2__.queryConnect.QueryService, transport);\n  }\n  async readParams() {\n    const res = await this.inner.readParams({});\n    return anyParamsToChain(res.values);\n  }\n  async readUtxosByOutputRef(refs) {\n    const searchResponse = await this.inner.readUtxos({\n      keys: refs.map((ref) => {\n        return {\n          hash: ref.txHash,\n          index: ref.outputIndex\n        };\n      })\n    });\n    return searchResponse.items.map(anyUtxoToChain);\n  }\n  async searchUtxosByMatch(pattern) {\n    const searchResponse = await this.inner.searchUtxos({\n      predicate: {\n        match: { utxoPattern: { value: pattern, case: \"cardano\" } }\n      }\n    });\n    return searchResponse.items.map(anyUtxoToChain);\n  }\n  async searchUtxosByAddress(address) {\n    return this.searchUtxosByMatch({\n      address: {\n        exactAddress: address\n      }\n    });\n  }\n  async searchUtxosByPaymentPart(paymentPart) {\n    return this.searchUtxosByMatch({\n      address: {\n        paymentPart\n      }\n    });\n  }\n  async searchUtxosByDelegationPart(delegationPart) {\n    return this.searchUtxosByMatch({\n      address: {\n        delegationPart\n      }\n    });\n  }\n  async searchUtxosByAsset(policyId, name) {\n    return this.searchUtxosByMatch({\n      asset: policyId ? { policyId } : { assetName: name }\n    });\n  }\n  async searchUtxosByAddressWithAsset(address, policyId, name) {\n    return this.searchUtxosByMatch({\n      address: {\n        exactAddress: address\n      },\n      asset: policyId ? { policyId } : { assetName: name }\n    });\n  }\n};\nvar SubmitClient = class {\n  constructor(options) {\n    let headerInterceptor = metadataInterceptor(options);\n    const transport = createGrpcTransport({\n      httpVersion: \"2\",\n      baseUrl: options.uri,\n      interceptors: [headerInterceptor]\n    });\n    this.inner = (0,_connectrpc_connect__WEBPACK_IMPORTED_MODULE_3__.createPromiseClient)(_utxorpc_spec__WEBPACK_IMPORTED_MODULE_2__.submitConnect.SubmitService, transport);\n  }\n  async submitTx(tx) {\n    const res = await this.inner.submitTx({\n      tx: [tx].map((cbor) => ({ type: { case: \"raw\", value: cbor } }))\n    });\n    return res.ref[0];\n  }\n  async *waitForTx(txHash) {\n    const updates = this.inner.waitForTx({\n      ref: [txHash]\n    });\n    for await (const change of updates) {\n      yield change.stage;\n    }\n  }\n};\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHV0eG9ycGMvc2RrL2xpYi9ub2RlL2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQUE7QUFHNkI7O0FBRTdCO0FBQzBGO0FBQzFGLDBCQUEwQix5RUFBdUI7O0FBRWpEO0FBQ2dDO0FBTVQ7O0FBRXZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsK0NBQUk7QUFDakI7QUFDQSx5QkFBeUIsMENBQU07QUFDL0IsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVSwwQ0FBTTtBQUNoQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLGlCQUFpQix3RUFBbUIsQ0FBQyxzREFBVztBQUNoRDtBQUNBO0FBQ0Esb0JBQW9CLCtDQUFJO0FBQ3hCO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQThDLFlBQVk7QUFDMUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHdFQUFtQixDQUFDLHVEQUFZO0FBQ2pEO0FBQ0E7QUFDQSw4Q0FBOEM7QUFDOUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLGVBQWU7QUFDaEM7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsV0FBVyxJQUFJO0FBQ3pDLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQLDBCQUEwQixXQUFXLElBQUk7QUFDekMsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxpQkFBaUIsd0VBQW1CLENBQUMsd0RBQWE7QUFDbEQ7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDLFFBQVEsNEJBQTRCO0FBQ3BFLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFLRTtBQUNGIiwic291cmNlcyI6WyJEOlxcaGFja290aG9uXFxCbG9ja2NoYWluXFxWaW50cmVrXFxub2RlX21vZHVsZXNcXEB1dHhvcnBjXFxzZGtcXGxpYlxcbm9kZVxcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9jYXJkYW5vLnRzXG5pbXBvcnQge1xuICBjcmVhdGVQcm9taXNlQ2xpZW50XG59IGZyb20gXCJAY29ubmVjdHJwYy9jb25uZWN0XCI7XG5cbi8vIHNyYy9ncnBjVHJhbnNwb3J0Lm5vZGUudHNcbmltcG9ydCB7IGNyZWF0ZUdycGNUcmFuc3BvcnQgYXMgY3JlYXRlR3JwY05vZGVUcmFuc3BvcnQgfSBmcm9tIFwiQGNvbm5lY3RycGMvY29ubmVjdC1ub2RlXCI7XG52YXIgY3JlYXRlR3JwY1RyYW5zcG9ydCA9IGNyZWF0ZUdycGNOb2RlVHJhbnNwb3J0O1xuXG4vLyBzcmMvY2FyZGFuby50c1xuaW1wb3J0IHsgQnVmZmVyIH0gZnJvbSBcImJ1ZmZlclwiO1xuaW1wb3J0IHtcbiAgc3luYyxcbiAgc3luY0Nvbm5lY3QsXG4gIHF1ZXJ5Q29ubmVjdCxcbiAgc3VibWl0Q29ubmVjdFxufSBmcm9tIFwiQHV0eG9ycGMvc3BlY1wiO1xuXG4vLyBzcmMvY29tbW9uLnRzXG5mdW5jdGlvbiBtZXRhZGF0YUludGVyY2VwdG9yKG9wdGlvbnMpIHtcbiAgcmV0dXJuIChuZXh0KSA9PiBhc3luYyAocmVxKSA9PiB7XG4gICAgaWYgKCEhKG9wdGlvbnMgPT0gbnVsbCA/IHZvaWQgMCA6IG9wdGlvbnMuaGVhZGVycykpIHtcbiAgICAgIE9iamVjdC5lbnRyaWVzKG9wdGlvbnMuaGVhZGVycykuZm9yRWFjaChcbiAgICAgICAgKFtrZXksIHZhbHVlXSkgPT4gcmVxLmhlYWRlci5zZXQoa2V5LCB2YWx1ZSlcbiAgICAgICk7XG4gICAgfVxuICAgIHJldHVybiBhd2FpdCBuZXh0KHJlcSk7XG4gIH07XG59XG5cbi8vIHNyYy9jYXJkYW5vLnRzXG5mdW5jdGlvbiBhbnlDaGFpblRvQmxvY2sobXNnKSB7XG4gIHJldHVybiBtc2cuY2hhaW4uY2FzZSA9PSBcImNhcmRhbm9cIiA/IG1zZy5jaGFpbi52YWx1ZSA6IG51bGw7XG59XG5mdW5jdGlvbiBwb2ludFRvQmxvY2tSZWYocCkge1xuICByZXR1cm4gbmV3IHN5bmMuQmxvY2tSZWYoe1xuICAgIGluZGV4OiBCaWdJbnQocC5zbG90KSxcbiAgICBoYXNoOiBuZXcgVWludDhBcnJheShCdWZmZXIuZnJvbShwLmhhc2gsIFwiaGV4XCIpKVxuICB9KTtcbn1cbmZ1bmN0aW9uIGJsb2NrUmVmVG9Qb2ludChyKSB7XG4gIHJldHVybiB7XG4gICAgc2xvdDogci5pbmRleC50b1N0cmluZygpLFxuICAgIGhhc2g6IEJ1ZmZlci5mcm9tKHIuaGFzaCkudG9TdHJpbmcoXCJoZXhcIilcbiAgfTtcbn1cbmZ1bmN0aW9uIGFueVV0eG9Ub0NoYWluKHUpIHtcbiAgc3dpdGNoICh1LnBhcnNlZFN0YXRlLmNhc2UpIHtcbiAgICBjYXNlIFwiY2FyZGFub1wiOlxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgdHhvUmVmOiB1LnR4b1JlZixcbiAgICAgICAgcGFyc2VkVmFsdWVkOiB1LnBhcnNlZFN0YXRlLnZhbHVlLFxuICAgICAgICBuYXRpdmVCeXRlczogdS5uYXRpdmVCeXRlc1xuICAgICAgfTtcbiAgICBkZWZhdWx0OlxuICAgICAgdGhyb3cgRXJyb3IoXCJzb3VyY2UgaXMgbm90IENhcmRhbm8gZGF0YVwiKTtcbiAgfVxufVxuZnVuY3Rpb24gYW55UGFyYW1zVG9DaGFpbihwKSB7XG4gIHN3aXRjaCAocC5wYXJhbXMuY2FzZSkge1xuICAgIGNhc2UgXCJjYXJkYW5vXCI6XG4gICAgICByZXR1cm4gcC5wYXJhbXMudmFsdWU7XG4gICAgZGVmYXVsdDpcbiAgICAgIHRocm93IEVycm9yKFwic291cmNlIGlzIG5vdCBDYXJkYW5vIGRhdGFcIik7XG4gIH1cbn1cbnZhciBTeW5jQ2xpZW50ID0gY2xhc3Mge1xuICBjb25zdHJ1Y3RvcihvcHRpb25zKSB7XG4gICAgbGV0IGhlYWRlckludGVyY2VwdG9yID0gbWV0YWRhdGFJbnRlcmNlcHRvcihvcHRpb25zKTtcbiAgICBjb25zdCB0cmFuc3BvcnQgPSBjcmVhdGVHcnBjVHJhbnNwb3J0KHtcbiAgICAgIGh0dHBWZXJzaW9uOiBcIjJcIixcbiAgICAgIGJhc2VVcmw6IG9wdGlvbnMudXJpLFxuICAgICAgaW50ZXJjZXB0b3JzOiBbaGVhZGVySW50ZXJjZXB0b3JdXG4gICAgfSk7XG4gICAgdGhpcy5pbm5lciA9IGNyZWF0ZVByb21pc2VDbGllbnQoc3luY0Nvbm5lY3QuU3luY1NlcnZpY2UsIHRyYW5zcG9ydCk7XG4gIH1cbiAgYXN5bmMgKmZvbGxvd1RpcChpbnRlcnNlY3QpIHtcbiAgICBjb25zdCByZXEgPSBuZXcgc3luYy5Gb2xsb3dUaXBSZXF1ZXN0KHtcbiAgICAgIGludGVyc2VjdDogaW50ZXJzZWN0ID09IG51bGwgPyB2b2lkIDAgOiBpbnRlcnNlY3QubWFwKChwKSA9PiBwb2ludFRvQmxvY2tSZWYocCkpXG4gICAgfSk7XG4gICAgY29uc3QgcmVzID0gdGhpcy5pbm5lci5mb2xsb3dUaXAocmVxKTtcbiAgICBmb3IgYXdhaXQgKGNvbnN0IGFueSBvZiByZXMpIHtcbiAgICAgIHN3aXRjaCAoYW55LmFjdGlvbi5jYXNlKSB7XG4gICAgICAgIGNhc2UgXCJhcHBseVwiOlxuICAgICAgICAgIHlpZWxkIHtcbiAgICAgICAgICAgIGFjdGlvbjogXCJhcHBseVwiLFxuICAgICAgICAgICAgYmxvY2s6IGFueUNoYWluVG9CbG9jayhhbnkuYWN0aW9uLnZhbHVlKVxuICAgICAgICAgIH07XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgXCJ1bmRvXCI6XG4gICAgICAgICAgeWllbGQge1xuICAgICAgICAgICAgYWN0aW9uOiBcInVuZG9cIixcbiAgICAgICAgICAgIGJsb2NrOiBhbnlDaGFpblRvQmxvY2soYW55LmFjdGlvbi52YWx1ZSlcbiAgICAgICAgICB9O1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIFwicmVzZXRcIjpcbiAgICAgICAgICB5aWVsZCB7XG4gICAgICAgICAgICBhY3Rpb246IFwicmVzZXRcIixcbiAgICAgICAgICAgIHBvaW50OiBibG9ja1JlZlRvUG9pbnQoYW55LmFjdGlvbi52YWx1ZSlcbiAgICAgICAgICB9O1xuICAgICAgfVxuICAgIH1cbiAgfVxuICBhc3luYyBmZXRjaEJsb2NrKHApIHtcbiAgICBjb25zdCByZXEgPSBwb2ludFRvQmxvY2tSZWYocCk7XG4gICAgY29uc3QgcmVzID0gYXdhaXQgdGhpcy5pbm5lci5mZXRjaEJsb2NrKHsgcmVmOiBbcmVxXSB9KTtcbiAgICByZXR1cm4gYW55Q2hhaW5Ub0Jsb2NrKHJlcy5ibG9ja1swXSk7XG4gIH1cbn07XG52YXIgUXVlcnlDbGllbnQgPSBjbGFzcyB7XG4gIGNvbnN0cnVjdG9yKG9wdGlvbnMpIHtcbiAgICBsZXQgaGVhZGVySW50ZXJjZXB0b3IgPSBtZXRhZGF0YUludGVyY2VwdG9yKG9wdGlvbnMpO1xuICAgIGNvbnN0IHRyYW5zcG9ydCA9IGNyZWF0ZUdycGNUcmFuc3BvcnQoe1xuICAgICAgaHR0cFZlcnNpb246IFwiMlwiLFxuICAgICAgYmFzZVVybDogb3B0aW9ucy51cmksXG4gICAgICBpbnRlcmNlcHRvcnM6IFtoZWFkZXJJbnRlcmNlcHRvcl1cbiAgICB9KTtcbiAgICB0aGlzLmlubmVyID0gY3JlYXRlUHJvbWlzZUNsaWVudChxdWVyeUNvbm5lY3QuUXVlcnlTZXJ2aWNlLCB0cmFuc3BvcnQpO1xuICB9XG4gIGFzeW5jIHJlYWRQYXJhbXMoKSB7XG4gICAgY29uc3QgcmVzID0gYXdhaXQgdGhpcy5pbm5lci5yZWFkUGFyYW1zKHt9KTtcbiAgICByZXR1cm4gYW55UGFyYW1zVG9DaGFpbihyZXMudmFsdWVzKTtcbiAgfVxuICBhc3luYyByZWFkVXR4b3NCeU91dHB1dFJlZihyZWZzKSB7XG4gICAgY29uc3Qgc2VhcmNoUmVzcG9uc2UgPSBhd2FpdCB0aGlzLmlubmVyLnJlYWRVdHhvcyh7XG4gICAgICBrZXlzOiByZWZzLm1hcCgocmVmKSA9PiB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgaGFzaDogcmVmLnR4SGFzaCxcbiAgICAgICAgICBpbmRleDogcmVmLm91dHB1dEluZGV4XG4gICAgICAgIH07XG4gICAgICB9KVxuICAgIH0pO1xuICAgIHJldHVybiBzZWFyY2hSZXNwb25zZS5pdGVtcy5tYXAoYW55VXR4b1RvQ2hhaW4pO1xuICB9XG4gIGFzeW5jIHNlYXJjaFV0eG9zQnlNYXRjaChwYXR0ZXJuKSB7XG4gICAgY29uc3Qgc2VhcmNoUmVzcG9uc2UgPSBhd2FpdCB0aGlzLmlubmVyLnNlYXJjaFV0eG9zKHtcbiAgICAgIHByZWRpY2F0ZToge1xuICAgICAgICBtYXRjaDogeyB1dHhvUGF0dGVybjogeyB2YWx1ZTogcGF0dGVybiwgY2FzZTogXCJjYXJkYW5vXCIgfSB9XG4gICAgICB9XG4gICAgfSk7XG4gICAgcmV0dXJuIHNlYXJjaFJlc3BvbnNlLml0ZW1zLm1hcChhbnlVdHhvVG9DaGFpbik7XG4gIH1cbiAgYXN5bmMgc2VhcmNoVXR4b3NCeUFkZHJlc3MoYWRkcmVzcykge1xuICAgIHJldHVybiB0aGlzLnNlYXJjaFV0eG9zQnlNYXRjaCh7XG4gICAgICBhZGRyZXNzOiB7XG4gICAgICAgIGV4YWN0QWRkcmVzczogYWRkcmVzc1xuICAgICAgfVxuICAgIH0pO1xuICB9XG4gIGFzeW5jIHNlYXJjaFV0eG9zQnlQYXltZW50UGFydChwYXltZW50UGFydCkge1xuICAgIHJldHVybiB0aGlzLnNlYXJjaFV0eG9zQnlNYXRjaCh7XG4gICAgICBhZGRyZXNzOiB7XG4gICAgICAgIHBheW1lbnRQYXJ0XG4gICAgICB9XG4gICAgfSk7XG4gIH1cbiAgYXN5bmMgc2VhcmNoVXR4b3NCeURlbGVnYXRpb25QYXJ0KGRlbGVnYXRpb25QYXJ0KSB7XG4gICAgcmV0dXJuIHRoaXMuc2VhcmNoVXR4b3NCeU1hdGNoKHtcbiAgICAgIGFkZHJlc3M6IHtcbiAgICAgICAgZGVsZWdhdGlvblBhcnRcbiAgICAgIH1cbiAgICB9KTtcbiAgfVxuICBhc3luYyBzZWFyY2hVdHhvc0J5QXNzZXQocG9saWN5SWQsIG5hbWUpIHtcbiAgICByZXR1cm4gdGhpcy5zZWFyY2hVdHhvc0J5TWF0Y2goe1xuICAgICAgYXNzZXQ6IHBvbGljeUlkID8geyBwb2xpY3lJZCB9IDogeyBhc3NldE5hbWU6IG5hbWUgfVxuICAgIH0pO1xuICB9XG4gIGFzeW5jIHNlYXJjaFV0eG9zQnlBZGRyZXNzV2l0aEFzc2V0KGFkZHJlc3MsIHBvbGljeUlkLCBuYW1lKSB7XG4gICAgcmV0dXJuIHRoaXMuc2VhcmNoVXR4b3NCeU1hdGNoKHtcbiAgICAgIGFkZHJlc3M6IHtcbiAgICAgICAgZXhhY3RBZGRyZXNzOiBhZGRyZXNzXG4gICAgICB9LFxuICAgICAgYXNzZXQ6IHBvbGljeUlkID8geyBwb2xpY3lJZCB9IDogeyBhc3NldE5hbWU6IG5hbWUgfVxuICAgIH0pO1xuICB9XG59O1xudmFyIFN1Ym1pdENsaWVudCA9IGNsYXNzIHtcbiAgY29uc3RydWN0b3Iob3B0aW9ucykge1xuICAgIGxldCBoZWFkZXJJbnRlcmNlcHRvciA9IG1ldGFkYXRhSW50ZXJjZXB0b3Iob3B0aW9ucyk7XG4gICAgY29uc3QgdHJhbnNwb3J0ID0gY3JlYXRlR3JwY1RyYW5zcG9ydCh7XG4gICAgICBodHRwVmVyc2lvbjogXCIyXCIsXG4gICAgICBiYXNlVXJsOiBvcHRpb25zLnVyaSxcbiAgICAgIGludGVyY2VwdG9yczogW2hlYWRlckludGVyY2VwdG9yXVxuICAgIH0pO1xuICAgIHRoaXMuaW5uZXIgPSBjcmVhdGVQcm9taXNlQ2xpZW50KHN1Ym1pdENvbm5lY3QuU3VibWl0U2VydmljZSwgdHJhbnNwb3J0KTtcbiAgfVxuICBhc3luYyBzdWJtaXRUeCh0eCkge1xuICAgIGNvbnN0IHJlcyA9IGF3YWl0IHRoaXMuaW5uZXIuc3VibWl0VHgoe1xuICAgICAgdHg6IFt0eF0ubWFwKChjYm9yKSA9PiAoeyB0eXBlOiB7IGNhc2U6IFwicmF3XCIsIHZhbHVlOiBjYm9yIH0gfSkpXG4gICAgfSk7XG4gICAgcmV0dXJuIHJlcy5yZWZbMF07XG4gIH1cbiAgYXN5bmMgKndhaXRGb3JUeCh0eEhhc2gpIHtcbiAgICBjb25zdCB1cGRhdGVzID0gdGhpcy5pbm5lci53YWl0Rm9yVHgoe1xuICAgICAgcmVmOiBbdHhIYXNoXVxuICAgIH0pO1xuICAgIGZvciBhd2FpdCAoY29uc3QgY2hhbmdlIG9mIHVwZGF0ZXMpIHtcbiAgICAgIHlpZWxkIGNoYW5nZS5zdGFnZTtcbiAgICB9XG4gIH1cbn07XG5leHBvcnQge1xuICBRdWVyeUNsaWVudCBhcyBDYXJkYW5vUXVlcnlDbGllbnQsXG4gIFN1Ym1pdENsaWVudCBhcyBDYXJkYW5vU3VibWl0Q2xpZW50LFxuICBTeW5jQ2xpZW50IGFzIENhcmRhbm9TeW5jQ2xpZW50XG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@utxorpc/sdk/lib/node/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@utxorpc/spec/lib/index.mjs":
/*!**************************************************!*\
  !*** ./node_modules/@utxorpc/spec/lib/index.mjs ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cardano: () => (/* binding */ cardano_pb_exports),\n/* harmony export */   query: () => (/* binding */ query_pb_exports),\n/* harmony export */   queryConnect: () => (/* binding */ query_connect_exports),\n/* harmony export */   submit: () => (/* binding */ submit_pb_exports),\n/* harmony export */   submitConnect: () => (/* binding */ submit_connect_exports),\n/* harmony export */   sync: () => (/* binding */ sync_pb_exports),\n/* harmony export */   syncConnect: () => (/* binding */ sync_connect_exports),\n/* harmony export */   watch: () => (/* binding */ watch_pb_exports),\n/* harmony export */   watchConnect: () => (/* binding */ watch_connect_exports)\n/* harmony export */ });\n/* harmony import */ var _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @bufbuild/protobuf */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/proto3.js\");\n/* harmony import */ var _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @bufbuild/protobuf */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/message.js\");\n/* harmony import */ var _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @bufbuild/protobuf */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/proto-int64.js\");\n/* harmony import */ var _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @bufbuild/protobuf */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/google/protobuf/field_mask_pb.js\");\n/* harmony import */ var _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @bufbuild/protobuf */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/service-type.js\");\nvar __defProp = Object.defineProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\n\n// src/utxorpc/v1alpha/cardano/cardano_pb.ts\nvar cardano_pb_exports = {};\n__export(cardano_pb_exports, {\n  AddressPattern: () => AddressPattern,\n  Anchor: () => Anchor,\n  Asset: () => Asset,\n  AssetPattern: () => AssetPattern,\n  AuthCommitteeHotCert: () => AuthCommitteeHotCert,\n  AuxData: () => AuxData,\n  BigInt: () => BigInt,\n  Block: () => Block,\n  BlockBody: () => BlockBody,\n  BlockHeader: () => BlockHeader,\n  Certificate: () => Certificate,\n  Collateral: () => Collateral,\n  Constr: () => Constr,\n  CostModel: () => CostModel,\n  CostModels: () => CostModels,\n  DRep: () => DRep,\n  Datum: () => Datum,\n  EvalError: () => EvalError,\n  EvalTrace: () => EvalTrace,\n  ExPrices: () => ExPrices,\n  ExUnits: () => ExUnits,\n  GenesisKeyDelegationCert: () => GenesisKeyDelegationCert,\n  Metadata: () => Metadata,\n  Metadatum: () => Metadatum,\n  MetadatumArray: () => MetadatumArray,\n  MetadatumMap: () => MetadatumMap,\n  MetadatumPair: () => MetadatumPair,\n  MirCert: () => MirCert,\n  MirSource: () => MirSource,\n  MirTarget: () => MirTarget,\n  Multiasset: () => Multiasset,\n  NativeScript: () => NativeScript,\n  NativeScriptList: () => NativeScriptList,\n  PParams: () => PParams,\n  PlutusData: () => PlutusData,\n  PlutusDataArray: () => PlutusDataArray,\n  PlutusDataMap: () => PlutusDataMap,\n  PlutusDataPair: () => PlutusDataPair,\n  PoolMetadata: () => PoolMetadata,\n  PoolRegistrationCert: () => PoolRegistrationCert,\n  PoolRetirementCert: () => PoolRetirementCert,\n  ProtocolVersion: () => ProtocolVersion,\n  RationalNumber: () => RationalNumber,\n  Redeemer: () => Redeemer,\n  RedeemerPurpose: () => RedeemerPurpose,\n  RegCert: () => RegCert,\n  RegDRepCert: () => RegDRepCert,\n  Relay: () => Relay,\n  ResignCommitteeColdCert: () => ResignCommitteeColdCert,\n  Script: () => Script,\n  ScriptNOfK: () => ScriptNOfK,\n  StakeCredential: () => StakeCredential,\n  StakeDelegationCert: () => StakeDelegationCert,\n  StakeRegDelegCert: () => StakeRegDelegCert,\n  StakeVoteDelegCert: () => StakeVoteDelegCert,\n  StakeVoteRegDelegCert: () => StakeVoteRegDelegCert,\n  Tx: () => Tx,\n  TxEval: () => TxEval,\n  TxInput: () => TxInput,\n  TxOutput: () => TxOutput,\n  TxOutputPattern: () => TxOutputPattern,\n  TxPattern: () => TxPattern,\n  TxValidity: () => TxValidity,\n  UnRegCert: () => UnRegCert,\n  UnRegDRepCert: () => UnRegDRepCert,\n  UpdateDRepCert: () => UpdateDRepCert,\n  VKeyWitness: () => VKeyWitness,\n  VoteDelegCert: () => VoteDelegCert,\n  VoteRegDelegCert: () => VoteRegDelegCert,\n  Withdrawal: () => Withdrawal,\n  WitnessSet: () => WitnessSet\n});\n\nvar RedeemerPurpose = /* @__PURE__ */ ((RedeemerPurpose2) => {\n  RedeemerPurpose2[RedeemerPurpose2[\"UNSPECIFIED\"] = 0] = \"UNSPECIFIED\";\n  RedeemerPurpose2[RedeemerPurpose2[\"SPEND\"] = 1] = \"SPEND\";\n  RedeemerPurpose2[RedeemerPurpose2[\"MINT\"] = 2] = \"MINT\";\n  RedeemerPurpose2[RedeemerPurpose2[\"CERT\"] = 3] = \"CERT\";\n  RedeemerPurpose2[RedeemerPurpose2[\"REWARD\"] = 4] = \"REWARD\";\n  RedeemerPurpose2[RedeemerPurpose2[\"VOTE\"] = 5] = \"VOTE\";\n  RedeemerPurpose2[RedeemerPurpose2[\"PROPOSE\"] = 6] = \"PROPOSE\";\n  return RedeemerPurpose2;\n})(RedeemerPurpose || {});\n_bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.setEnumType(RedeemerPurpose, \"utxorpc.v1alpha.cardano.RedeemerPurpose\", [\n  { no: 0, name: \"REDEEMER_PURPOSE_UNSPECIFIED\" },\n  { no: 1, name: \"REDEEMER_PURPOSE_SPEND\" },\n  { no: 2, name: \"REDEEMER_PURPOSE_MINT\" },\n  { no: 3, name: \"REDEEMER_PURPOSE_CERT\" },\n  { no: 4, name: \"REDEEMER_PURPOSE_REWARD\" },\n  { no: 5, name: \"REDEEMER_PURPOSE_VOTE\" },\n  { no: 6, name: \"REDEEMER_PURPOSE_PROPOSE\" }\n]);\nvar MirSource = /* @__PURE__ */ ((MirSource2) => {\n  MirSource2[MirSource2[\"UNSPECIFIED\"] = 0] = \"UNSPECIFIED\";\n  MirSource2[MirSource2[\"RESERVES\"] = 1] = \"RESERVES\";\n  MirSource2[MirSource2[\"TREASURY\"] = 2] = \"TREASURY\";\n  return MirSource2;\n})(MirSource || {});\n_bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.setEnumType(MirSource, \"utxorpc.v1alpha.cardano.MirSource\", [\n  { no: 0, name: \"MIR_SOURCE_UNSPECIFIED\" },\n  { no: 1, name: \"MIR_SOURCE_RESERVES\" },\n  { no: 2, name: \"MIR_SOURCE_TREASURY\" }\n]);\nvar _Redeemer = class _Redeemer extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * Purpose of the redeemer.\n     *\n     * @generated from field: utxorpc.v1alpha.cardano.RedeemerPurpose purpose = 1;\n     */\n    this.purpose = 0 /* UNSPECIFIED */;\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _Redeemer().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _Redeemer().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _Redeemer().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_Redeemer, a, b);\n  }\n};\n_Redeemer.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_Redeemer.typeName = \"utxorpc.v1alpha.cardano.Redeemer\";\n_Redeemer.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"purpose\", kind: \"enum\", T: _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.getEnumType(RedeemerPurpose) },\n  { no: 2, name: \"payload\", kind: \"message\", T: PlutusData }\n]);\nvar Redeemer = _Redeemer;\nvar _TxInput = class _TxInput extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * Hash of the previous transaction.\n     *\n     * @generated from field: bytes tx_hash = 1;\n     */\n    this.txHash = new Uint8Array(0);\n    /**\n     * Index of the output in the previous transaction.\n     *\n     * @generated from field: uint32 output_index = 2;\n     */\n    this.outputIndex = 0;\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _TxInput().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _TxInput().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _TxInput().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_TxInput, a, b);\n  }\n};\n_TxInput.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_TxInput.typeName = \"utxorpc.v1alpha.cardano.TxInput\";\n_TxInput.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  {\n    no: 1,\n    name: \"tx_hash\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  },\n  {\n    no: 2,\n    name: \"output_index\",\n    kind: \"scalar\",\n    T: 13\n    /* ScalarType.UINT32 */\n  },\n  { no: 3, name: \"as_output\", kind: \"message\", T: TxOutput },\n  { no: 4, name: \"redeemer\", kind: \"message\", T: Redeemer }\n]);\nvar TxInput = _TxInput;\nvar _TxOutput = class _TxOutput extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * Address receiving the output.\n     *\n     * @generated from field: bytes address = 1;\n     */\n    this.address = new Uint8Array(0);\n    /**\n     * Amount of ADA in the output.\n     *\n     * @generated from field: uint64 coin = 2;\n     */\n    this.coin = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    /**\n     * Additional native (non-ADA) assets in the output.\n     *\n     * @generated from field: repeated utxorpc.v1alpha.cardano.Multiasset assets = 3;\n     */\n    this.assets = [];\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _TxOutput().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _TxOutput().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _TxOutput().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_TxOutput, a, b);\n  }\n};\n_TxOutput.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_TxOutput.typeName = \"utxorpc.v1alpha.cardano.TxOutput\";\n_TxOutput.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  {\n    no: 1,\n    name: \"address\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  },\n  {\n    no: 2,\n    name: \"coin\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  },\n  { no: 3, name: \"assets\", kind: \"message\", T: Multiasset, repeated: true },\n  { no: 4, name: \"datum\", kind: \"message\", T: Datum },\n  { no: 5, name: \"script\", kind: \"message\", T: Script }\n]);\nvar TxOutput = _TxOutput;\nvar _Datum = class _Datum extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * Hash of this datum as seen on-chain\n     *\n     * @generated from field: bytes hash = 1;\n     */\n    this.hash = new Uint8Array(0);\n    /**\n     * Original cbor-encoded data as seen on-chain\n     *\n     * @generated from field: bytes original_cbor = 3;\n     */\n    this.originalCbor = new Uint8Array(0);\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _Datum().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _Datum().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _Datum().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_Datum, a, b);\n  }\n};\n_Datum.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_Datum.typeName = \"utxorpc.v1alpha.cardano.Datum\";\n_Datum.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  {\n    no: 1,\n    name: \"hash\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  },\n  { no: 2, name: \"payload\", kind: \"message\", T: PlutusData },\n  {\n    no: 3,\n    name: \"original_cbor\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  }\n]);\nvar Datum = _Datum;\nvar _Asset = class _Asset extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * Name of the custom asset.\n     *\n     * @generated from field: bytes name = 1;\n     */\n    this.name = new Uint8Array(0);\n    /**\n     * Quantity of the custom asset in case of an output.\n     *\n     * @generated from field: uint64 output_coin = 2;\n     */\n    this.outputCoin = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    /**\n     * Quantity of the custom asset in case of a mint.\n     *\n     * @generated from field: int64 mint_coin = 3;\n     */\n    this.mintCoin = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _Asset().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _Asset().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _Asset().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_Asset, a, b);\n  }\n};\n_Asset.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_Asset.typeName = \"utxorpc.v1alpha.cardano.Asset\";\n_Asset.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  {\n    no: 1,\n    name: \"name\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  },\n  {\n    no: 2,\n    name: \"output_coin\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  },\n  {\n    no: 3,\n    name: \"mint_coin\",\n    kind: \"scalar\",\n    T: 3\n    /* ScalarType.INT64 */\n  }\n]);\nvar Asset = _Asset;\nvar _Multiasset = class _Multiasset extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * Policy ID governing the custom assets.\n     *\n     * @generated from field: bytes policy_id = 1;\n     */\n    this.policyId = new Uint8Array(0);\n    /**\n     * List of custom assets.\n     *\n     * @generated from field: repeated utxorpc.v1alpha.cardano.Asset assets = 2;\n     */\n    this.assets = [];\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _Multiasset().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _Multiasset().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _Multiasset().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_Multiasset, a, b);\n  }\n};\n_Multiasset.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_Multiasset.typeName = \"utxorpc.v1alpha.cardano.Multiasset\";\n_Multiasset.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  {\n    no: 1,\n    name: \"policy_id\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  },\n  { no: 2, name: \"assets\", kind: \"message\", T: Asset, repeated: true },\n  { no: 3, name: \"redeemer\", kind: \"message\", T: Redeemer }\n]);\nvar Multiasset = _Multiasset;\nvar _TxValidity = class _TxValidity extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * Start of the validity interval.\n     *\n     * @generated from field: uint64 start = 1;\n     */\n    this.start = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    /**\n     * End of the validity interval (TTL: Time to Live).\n     *\n     * @generated from field: uint64 ttl = 2;\n     */\n    this.ttl = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _TxValidity().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _TxValidity().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _TxValidity().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_TxValidity, a, b);\n  }\n};\n_TxValidity.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_TxValidity.typeName = \"utxorpc.v1alpha.cardano.TxValidity\";\n_TxValidity.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  {\n    no: 1,\n    name: \"start\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  },\n  {\n    no: 2,\n    name: \"ttl\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  }\n]);\nvar TxValidity = _TxValidity;\nvar _Collateral = class _Collateral extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * Collateral inputs for the transaction.\n     *\n     * @generated from field: repeated utxorpc.v1alpha.cardano.TxInput collateral = 1;\n     */\n    this.collateral = [];\n    /**\n     * Total amount of collateral.\n     *\n     * @generated from field: uint64 total_collateral = 3;\n     */\n    this.totalCollateral = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _Collateral().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _Collateral().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _Collateral().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_Collateral, a, b);\n  }\n};\n_Collateral.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_Collateral.typeName = \"utxorpc.v1alpha.cardano.Collateral\";\n_Collateral.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"collateral\", kind: \"message\", T: TxInput, repeated: true },\n  { no: 2, name: \"collateral_return\", kind: \"message\", T: TxOutput },\n  {\n    no: 3,\n    name: \"total_collateral\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  }\n]);\nvar Collateral = _Collateral;\nvar _Withdrawal = class _Withdrawal extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * Address of the reward account.\n     *\n     * @generated from field: bytes reward_account = 1;\n     */\n    this.rewardAccount = new Uint8Array(0);\n    /**\n     * Amount of ADA withdrawn.\n     *\n     * @generated from field: uint64 coin = 2;\n     */\n    this.coin = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _Withdrawal().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _Withdrawal().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _Withdrawal().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_Withdrawal, a, b);\n  }\n};\n_Withdrawal.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_Withdrawal.typeName = \"utxorpc.v1alpha.cardano.Withdrawal\";\n_Withdrawal.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  {\n    no: 1,\n    name: \"reward_account\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  },\n  {\n    no: 2,\n    name: \"coin\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  },\n  { no: 3, name: \"redeemer\", kind: \"message\", T: Redeemer }\n]);\nvar Withdrawal = _Withdrawal;\nvar _WitnessSet = class _WitnessSet extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * List of VKey witnesses.\n     *\n     * @generated from field: repeated utxorpc.v1alpha.cardano.VKeyWitness vkeywitness = 1;\n     */\n    this.vkeywitness = [];\n    /**\n     * List of scripts.\n     *\n     * @generated from field: repeated utxorpc.v1alpha.cardano.Script script = 2;\n     */\n    this.script = [];\n    /**\n     * List of Plutus data elements associated with the transaction.\n     *\n     * @generated from field: repeated utxorpc.v1alpha.cardano.PlutusData plutus_datums = 3;\n     */\n    this.plutusDatums = [];\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _WitnessSet().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _WitnessSet().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _WitnessSet().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_WitnessSet, a, b);\n  }\n};\n_WitnessSet.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_WitnessSet.typeName = \"utxorpc.v1alpha.cardano.WitnessSet\";\n_WitnessSet.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"vkeywitness\", kind: \"message\", T: VKeyWitness, repeated: true },\n  { no: 2, name: \"script\", kind: \"message\", T: Script, repeated: true },\n  { no: 3, name: \"plutus_datums\", kind: \"message\", T: PlutusData, repeated: true }\n]);\nvar WitnessSet = _WitnessSet;\nvar _AuxData = class _AuxData extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * List of auxiliary metadata elements.\n     *\n     * @generated from field: repeated utxorpc.v1alpha.cardano.Metadata metadata = 1;\n     */\n    this.metadata = [];\n    /**\n     * List of auxiliary scripts.\n     *\n     * @generated from field: repeated utxorpc.v1alpha.cardano.Script scripts = 2;\n     */\n    this.scripts = [];\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _AuxData().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _AuxData().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _AuxData().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_AuxData, a, b);\n  }\n};\n_AuxData.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_AuxData.typeName = \"utxorpc.v1alpha.cardano.AuxData\";\n_AuxData.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"metadata\", kind: \"message\", T: Metadata, repeated: true },\n  { no: 2, name: \"scripts\", kind: \"message\", T: Script, repeated: true }\n]);\nvar AuxData = _AuxData;\nvar _Tx = class _Tx extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * List of transaction inputs\n     *\n     * @generated from field: repeated utxorpc.v1alpha.cardano.TxInput inputs = 1;\n     */\n    this.inputs = [];\n    /**\n     * List of transaction outputs\n     *\n     * @generated from field: repeated utxorpc.v1alpha.cardano.TxOutput outputs = 2;\n     */\n    this.outputs = [];\n    /**\n     * List of certificates\n     *\n     * @generated from field: repeated utxorpc.v1alpha.cardano.Certificate certificates = 3;\n     */\n    this.certificates = [];\n    /**\n     * List of withdrawals\n     *\n     * @generated from field: repeated utxorpc.v1alpha.cardano.Withdrawal withdrawals = 4;\n     */\n    this.withdrawals = [];\n    /**\n     * List of minted custom assets\n     *\n     * @generated from field: repeated utxorpc.v1alpha.cardano.Multiasset mint = 5;\n     */\n    this.mint = [];\n    /**\n     * List of reference inputs\n     *\n     * @generated from field: repeated utxorpc.v1alpha.cardano.TxInput reference_inputs = 6;\n     */\n    this.referenceInputs = [];\n    /**\n     * Transaction fee in ADA\n     *\n     * @generated from field: uint64 fee = 9;\n     */\n    this.fee = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    /**\n     * Flag indicating whether the transaction was successful\n     *\n     * @generated from field: bool successful = 11;\n     */\n    this.successful = false;\n    /**\n     * Hash of the transaction that serves as main identifier\n     *\n     * @generated from field: bytes hash = 13;\n     */\n    this.hash = new Uint8Array(0);\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _Tx().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _Tx().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _Tx().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_Tx, a, b);\n  }\n};\n_Tx.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_Tx.typeName = \"utxorpc.v1alpha.cardano.Tx\";\n_Tx.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"inputs\", kind: \"message\", T: TxInput, repeated: true },\n  { no: 2, name: \"outputs\", kind: \"message\", T: TxOutput, repeated: true },\n  { no: 3, name: \"certificates\", kind: \"message\", T: Certificate, repeated: true },\n  { no: 4, name: \"withdrawals\", kind: \"message\", T: Withdrawal, repeated: true },\n  { no: 5, name: \"mint\", kind: \"message\", T: Multiasset, repeated: true },\n  { no: 6, name: \"reference_inputs\", kind: \"message\", T: TxInput, repeated: true },\n  { no: 7, name: \"witnesses\", kind: \"message\", T: WitnessSet },\n  { no: 8, name: \"collateral\", kind: \"message\", T: Collateral },\n  {\n    no: 9,\n    name: \"fee\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  },\n  { no: 10, name: \"validity\", kind: \"message\", T: TxValidity },\n  {\n    no: 11,\n    name: \"successful\",\n    kind: \"scalar\",\n    T: 8\n    /* ScalarType.BOOL */\n  },\n  { no: 12, name: \"auxiliary\", kind: \"message\", T: AuxData },\n  {\n    no: 13,\n    name: \"hash\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  }\n]);\nvar Tx = _Tx;\nvar _BlockHeader = class _BlockHeader extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * Slot number.\n     *\n     * @generated from field: uint64 slot = 1;\n     */\n    this.slot = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    /**\n     * Block hash.\n     *\n     * @generated from field: bytes hash = 2;\n     */\n    this.hash = new Uint8Array(0);\n    /**\n     * Block height.\n     *\n     * @generated from field: uint64 height = 3;\n     */\n    this.height = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _BlockHeader().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _BlockHeader().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _BlockHeader().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_BlockHeader, a, b);\n  }\n};\n_BlockHeader.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_BlockHeader.typeName = \"utxorpc.v1alpha.cardano.BlockHeader\";\n_BlockHeader.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  {\n    no: 1,\n    name: \"slot\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  },\n  {\n    no: 2,\n    name: \"hash\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  },\n  {\n    no: 3,\n    name: \"height\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  }\n]);\nvar BlockHeader = _BlockHeader;\nvar _BlockBody = class _BlockBody extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * List of transactions.\n     *\n     * @generated from field: repeated utxorpc.v1alpha.cardano.Tx tx = 1;\n     */\n    this.tx = [];\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _BlockBody().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _BlockBody().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _BlockBody().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_BlockBody, a, b);\n  }\n};\n_BlockBody.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_BlockBody.typeName = \"utxorpc.v1alpha.cardano.BlockBody\";\n_BlockBody.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"tx\", kind: \"message\", T: Tx, repeated: true }\n]);\nvar BlockBody = _BlockBody;\nvar _Block = class _Block extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _Block().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _Block().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _Block().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_Block, a, b);\n  }\n};\n_Block.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_Block.typeName = \"utxorpc.v1alpha.cardano.Block\";\n_Block.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"header\", kind: \"message\", T: BlockHeader },\n  { no: 2, name: \"body\", kind: \"message\", T: BlockBody }\n]);\nvar Block = _Block;\nvar _VKeyWitness = class _VKeyWitness extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * Verification key.\n     *\n     * @generated from field: bytes vkey = 1;\n     */\n    this.vkey = new Uint8Array(0);\n    /**\n     * Signature generated using the associated private key.\n     *\n     * @generated from field: bytes signature = 2;\n     */\n    this.signature = new Uint8Array(0);\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _VKeyWitness().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _VKeyWitness().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _VKeyWitness().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_VKeyWitness, a, b);\n  }\n};\n_VKeyWitness.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_VKeyWitness.typeName = \"utxorpc.v1alpha.cardano.VKeyWitness\";\n_VKeyWitness.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  {\n    no: 1,\n    name: \"vkey\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  },\n  {\n    no: 2,\n    name: \"signature\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  }\n]);\nvar VKeyWitness = _VKeyWitness;\nvar _NativeScript = class _NativeScript extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from oneof utxorpc.v1alpha.cardano.NativeScript.native_script\n     */\n    this.nativeScript = { case: void 0 };\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _NativeScript().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _NativeScript().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _NativeScript().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_NativeScript, a, b);\n  }\n};\n_NativeScript.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_NativeScript.typeName = \"utxorpc.v1alpha.cardano.NativeScript\";\n_NativeScript.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"script_pubkey\", kind: \"scalar\", T: 12, oneof: \"native_script\" },\n  { no: 2, name: \"script_all\", kind: \"message\", T: NativeScriptList, oneof: \"native_script\" },\n  { no: 3, name: \"script_any\", kind: \"message\", T: NativeScriptList, oneof: \"native_script\" },\n  { no: 4, name: \"script_n_of_k\", kind: \"message\", T: ScriptNOfK, oneof: \"native_script\" },\n  { no: 5, name: \"invalid_before\", kind: \"scalar\", T: 4, oneof: \"native_script\" },\n  { no: 6, name: \"invalid_hereafter\", kind: \"scalar\", T: 4, oneof: \"native_script\" }\n]);\nvar NativeScript = _NativeScript;\nvar _NativeScriptList = class _NativeScriptList extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * List of native scripts.\n     *\n     * @generated from field: repeated utxorpc.v1alpha.cardano.NativeScript items = 1;\n     */\n    this.items = [];\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _NativeScriptList().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _NativeScriptList().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _NativeScriptList().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_NativeScriptList, a, b);\n  }\n};\n_NativeScriptList.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_NativeScriptList.typeName = \"utxorpc.v1alpha.cardano.NativeScriptList\";\n_NativeScriptList.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"items\", kind: \"message\", T: NativeScript, repeated: true }\n]);\nvar NativeScriptList = _NativeScriptList;\nvar _ScriptNOfK = class _ScriptNOfK extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * The number of required satisfied scripts.\n     *\n     * @generated from field: uint32 k = 1;\n     */\n    this.k = 0;\n    /**\n     * List of native scripts.\n     *\n     * @generated from field: repeated utxorpc.v1alpha.cardano.NativeScript scripts = 2;\n     */\n    this.scripts = [];\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _ScriptNOfK().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _ScriptNOfK().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _ScriptNOfK().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_ScriptNOfK, a, b);\n  }\n};\n_ScriptNOfK.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_ScriptNOfK.typeName = \"utxorpc.v1alpha.cardano.ScriptNOfK\";\n_ScriptNOfK.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  {\n    no: 1,\n    name: \"k\",\n    kind: \"scalar\",\n    T: 13\n    /* ScalarType.UINT32 */\n  },\n  { no: 2, name: \"scripts\", kind: \"message\", T: NativeScript, repeated: true }\n]);\nvar ScriptNOfK = _ScriptNOfK;\nvar _Constr = class _Constr extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from field: uint32 tag = 1;\n     */\n    this.tag = 0;\n    /**\n     * @generated from field: uint64 any_constructor = 2;\n     */\n    this.anyConstructor = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    /**\n     * @generated from field: repeated utxorpc.v1alpha.cardano.PlutusData fields = 3;\n     */\n    this.fields = [];\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _Constr().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _Constr().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _Constr().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_Constr, a, b);\n  }\n};\n_Constr.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_Constr.typeName = \"utxorpc.v1alpha.cardano.Constr\";\n_Constr.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  {\n    no: 1,\n    name: \"tag\",\n    kind: \"scalar\",\n    T: 13\n    /* ScalarType.UINT32 */\n  },\n  {\n    no: 2,\n    name: \"any_constructor\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  },\n  { no: 3, name: \"fields\", kind: \"message\", T: PlutusData, repeated: true }\n]);\nvar Constr = _Constr;\nvar _BigInt = class _BigInt extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from oneof utxorpc.v1alpha.cardano.BigInt.big_int\n     */\n    this.bigInt = { case: void 0 };\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _BigInt().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _BigInt().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _BigInt().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_BigInt, a, b);\n  }\n};\n_BigInt.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_BigInt.typeName = \"utxorpc.v1alpha.cardano.BigInt\";\n_BigInt.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"int\", kind: \"scalar\", T: 3, oneof: \"big_int\" },\n  { no: 2, name: \"big_u_int\", kind: \"scalar\", T: 12, oneof: \"big_int\" },\n  { no: 3, name: \"big_n_int\", kind: \"scalar\", T: 12, oneof: \"big_int\" }\n]);\nvar BigInt = _BigInt;\nvar _PlutusDataPair = class _PlutusDataPair extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _PlutusDataPair().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _PlutusDataPair().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _PlutusDataPair().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_PlutusDataPair, a, b);\n  }\n};\n_PlutusDataPair.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_PlutusDataPair.typeName = \"utxorpc.v1alpha.cardano.PlutusDataPair\";\n_PlutusDataPair.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"key\", kind: \"message\", T: PlutusData },\n  { no: 2, name: \"value\", kind: \"message\", T: PlutusData }\n]);\nvar PlutusDataPair = _PlutusDataPair;\nvar _PlutusData = class _PlutusData extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from oneof utxorpc.v1alpha.cardano.PlutusData.plutus_data\n     */\n    this.plutusData = { case: void 0 };\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _PlutusData().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _PlutusData().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _PlutusData().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_PlutusData, a, b);\n  }\n};\n_PlutusData.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_PlutusData.typeName = \"utxorpc.v1alpha.cardano.PlutusData\";\n_PlutusData.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 2, name: \"constr\", kind: \"message\", T: Constr, oneof: \"plutus_data\" },\n  { no: 3, name: \"map\", kind: \"message\", T: PlutusDataMap, oneof: \"plutus_data\" },\n  { no: 4, name: \"big_int\", kind: \"message\", T: BigInt, oneof: \"plutus_data\" },\n  { no: 5, name: \"bounded_bytes\", kind: \"scalar\", T: 12, oneof: \"plutus_data\" },\n  { no: 6, name: \"array\", kind: \"message\", T: PlutusDataArray, oneof: \"plutus_data\" }\n]);\nvar PlutusData = _PlutusData;\nvar _PlutusDataMap = class _PlutusDataMap extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * List of key-value pairs.\n     *\n     * @generated from field: repeated utxorpc.v1alpha.cardano.PlutusDataPair pairs = 1;\n     */\n    this.pairs = [];\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _PlutusDataMap().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _PlutusDataMap().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _PlutusDataMap().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_PlutusDataMap, a, b);\n  }\n};\n_PlutusDataMap.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_PlutusDataMap.typeName = \"utxorpc.v1alpha.cardano.PlutusDataMap\";\n_PlutusDataMap.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"pairs\", kind: \"message\", T: PlutusDataPair, repeated: true }\n]);\nvar PlutusDataMap = _PlutusDataMap;\nvar _PlutusDataArray = class _PlutusDataArray extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * List of Plutus data items.\n     *\n     * @generated from field: repeated utxorpc.v1alpha.cardano.PlutusData items = 1;\n     */\n    this.items = [];\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _PlutusDataArray().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _PlutusDataArray().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _PlutusDataArray().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_PlutusDataArray, a, b);\n  }\n};\n_PlutusDataArray.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_PlutusDataArray.typeName = \"utxorpc.v1alpha.cardano.PlutusDataArray\";\n_PlutusDataArray.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"items\", kind: \"message\", T: PlutusData, repeated: true }\n]);\nvar PlutusDataArray = _PlutusDataArray;\nvar _Script = class _Script extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from oneof utxorpc.v1alpha.cardano.Script.script\n     */\n    this.script = { case: void 0 };\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _Script().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _Script().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _Script().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_Script, a, b);\n  }\n};\n_Script.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_Script.typeName = \"utxorpc.v1alpha.cardano.Script\";\n_Script.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"native\", kind: \"message\", T: NativeScript, oneof: \"script\" },\n  { no: 2, name: \"plutus_v1\", kind: \"scalar\", T: 12, oneof: \"script\" },\n  { no: 3, name: \"plutus_v2\", kind: \"scalar\", T: 12, oneof: \"script\" },\n  { no: 4, name: \"plutus_v3\", kind: \"scalar\", T: 12, oneof: \"script\" }\n]);\nvar Script = _Script;\nvar _Metadatum = class _Metadatum extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from oneof utxorpc.v1alpha.cardano.Metadatum.metadatum\n     */\n    this.metadatum = { case: void 0 };\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _Metadatum().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _Metadatum().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _Metadatum().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_Metadatum, a, b);\n  }\n};\n_Metadatum.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_Metadatum.typeName = \"utxorpc.v1alpha.cardano.Metadatum\";\n_Metadatum.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"int\", kind: \"scalar\", T: 3, oneof: \"metadatum\" },\n  { no: 2, name: \"bytes\", kind: \"scalar\", T: 12, oneof: \"metadatum\" },\n  { no: 3, name: \"text\", kind: \"scalar\", T: 9, oneof: \"metadatum\" },\n  { no: 4, name: \"array\", kind: \"message\", T: MetadatumArray, oneof: \"metadatum\" },\n  { no: 5, name: \"map\", kind: \"message\", T: MetadatumMap, oneof: \"metadatum\" }\n]);\nvar Metadatum = _Metadatum;\nvar _MetadatumArray = class _MetadatumArray extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from field: repeated utxorpc.v1alpha.cardano.Metadatum items = 1;\n     */\n    this.items = [];\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _MetadatumArray().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _MetadatumArray().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _MetadatumArray().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_MetadatumArray, a, b);\n  }\n};\n_MetadatumArray.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_MetadatumArray.typeName = \"utxorpc.v1alpha.cardano.MetadatumArray\";\n_MetadatumArray.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"items\", kind: \"message\", T: Metadatum, repeated: true }\n]);\nvar MetadatumArray = _MetadatumArray;\nvar _MetadatumMap = class _MetadatumMap extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from field: repeated utxorpc.v1alpha.cardano.MetadatumPair pairs = 1;\n     */\n    this.pairs = [];\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _MetadatumMap().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _MetadatumMap().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _MetadatumMap().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_MetadatumMap, a, b);\n  }\n};\n_MetadatumMap.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_MetadatumMap.typeName = \"utxorpc.v1alpha.cardano.MetadatumMap\";\n_MetadatumMap.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"pairs\", kind: \"message\", T: MetadatumPair, repeated: true }\n]);\nvar MetadatumMap = _MetadatumMap;\nvar _MetadatumPair = class _MetadatumPair extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _MetadatumPair().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _MetadatumPair().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _MetadatumPair().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_MetadatumPair, a, b);\n  }\n};\n_MetadatumPair.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_MetadatumPair.typeName = \"utxorpc.v1alpha.cardano.MetadatumPair\";\n_MetadatumPair.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"key\", kind: \"message\", T: Metadatum },\n  { no: 2, name: \"value\", kind: \"message\", T: Metadatum }\n]);\nvar MetadatumPair = _MetadatumPair;\nvar _Metadata = class _Metadata extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from field: uint64 label = 1;\n     */\n    this.label = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _Metadata().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _Metadata().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _Metadata().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_Metadata, a, b);\n  }\n};\n_Metadata.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_Metadata.typeName = \"utxorpc.v1alpha.cardano.Metadata\";\n_Metadata.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  {\n    no: 1,\n    name: \"label\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  },\n  { no: 2, name: \"value\", kind: \"message\", T: Metadatum }\n]);\nvar Metadata = _Metadata;\nvar _StakeCredential = class _StakeCredential extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from oneof utxorpc.v1alpha.cardano.StakeCredential.stake_credential\n     */\n    this.stakeCredential = { case: void 0 };\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _StakeCredential().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _StakeCredential().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _StakeCredential().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_StakeCredential, a, b);\n  }\n};\n_StakeCredential.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_StakeCredential.typeName = \"utxorpc.v1alpha.cardano.StakeCredential\";\n_StakeCredential.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"addr_key_hash\", kind: \"scalar\", T: 12, oneof: \"stake_credential\" },\n  { no: 2, name: \"script_hash\", kind: \"scalar\", T: 12, oneof: \"stake_credential\" }\n]);\nvar StakeCredential = _StakeCredential;\nvar _RationalNumber = class _RationalNumber extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from field: int32 numerator = 1;\n     */\n    this.numerator = 0;\n    /**\n     * @generated from field: uint32 denominator = 2;\n     */\n    this.denominator = 0;\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _RationalNumber().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _RationalNumber().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _RationalNumber().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_RationalNumber, a, b);\n  }\n};\n_RationalNumber.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_RationalNumber.typeName = \"utxorpc.v1alpha.cardano.RationalNumber\";\n_RationalNumber.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  {\n    no: 1,\n    name: \"numerator\",\n    kind: \"scalar\",\n    T: 5\n    /* ScalarType.INT32 */\n  },\n  {\n    no: 2,\n    name: \"denominator\",\n    kind: \"scalar\",\n    T: 13\n    /* ScalarType.UINT32 */\n  }\n]);\nvar RationalNumber = _RationalNumber;\nvar _Relay = class _Relay extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from field: bytes ip_v4 = 1;\n     */\n    this.ipV4 = new Uint8Array(0);\n    /**\n     * @generated from field: bytes ip_v6 = 2;\n     */\n    this.ipV6 = new Uint8Array(0);\n    /**\n     * @generated from field: string dns_name = 3;\n     */\n    this.dnsName = \"\";\n    /**\n     * @generated from field: uint32 port = 4;\n     */\n    this.port = 0;\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _Relay().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _Relay().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _Relay().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_Relay, a, b);\n  }\n};\n_Relay.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_Relay.typeName = \"utxorpc.v1alpha.cardano.Relay\";\n_Relay.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  {\n    no: 1,\n    name: \"ip_v4\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  },\n  {\n    no: 2,\n    name: \"ip_v6\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  },\n  {\n    no: 3,\n    name: \"dns_name\",\n    kind: \"scalar\",\n    T: 9\n    /* ScalarType.STRING */\n  },\n  {\n    no: 4,\n    name: \"port\",\n    kind: \"scalar\",\n    T: 13\n    /* ScalarType.UINT32 */\n  }\n]);\nvar Relay = _Relay;\nvar _PoolMetadata = class _PoolMetadata extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from field: string url = 1;\n     */\n    this.url = \"\";\n    /**\n     * @generated from field: bytes hash = 2;\n     */\n    this.hash = new Uint8Array(0);\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _PoolMetadata().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _PoolMetadata().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _PoolMetadata().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_PoolMetadata, a, b);\n  }\n};\n_PoolMetadata.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_PoolMetadata.typeName = \"utxorpc.v1alpha.cardano.PoolMetadata\";\n_PoolMetadata.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  {\n    no: 1,\n    name: \"url\",\n    kind: \"scalar\",\n    T: 9\n    /* ScalarType.STRING */\n  },\n  {\n    no: 2,\n    name: \"hash\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  }\n]);\nvar PoolMetadata = _PoolMetadata;\nvar _Certificate = class _Certificate extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from oneof utxorpc.v1alpha.cardano.Certificate.certificate\n     */\n    this.certificate = { case: void 0 };\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _Certificate().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _Certificate().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _Certificate().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_Certificate, a, b);\n  }\n};\n_Certificate.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_Certificate.typeName = \"utxorpc.v1alpha.cardano.Certificate\";\n_Certificate.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"stake_registration\", kind: \"message\", T: StakeCredential, oneof: \"certificate\" },\n  { no: 2, name: \"stake_deregistration\", kind: \"message\", T: StakeCredential, oneof: \"certificate\" },\n  { no: 3, name: \"stake_delegation\", kind: \"message\", T: StakeDelegationCert, oneof: \"certificate\" },\n  { no: 4, name: \"pool_registration\", kind: \"message\", T: PoolRegistrationCert, oneof: \"certificate\" },\n  { no: 5, name: \"pool_retirement\", kind: \"message\", T: PoolRetirementCert, oneof: \"certificate\" },\n  { no: 6, name: \"genesis_key_delegation\", kind: \"message\", T: GenesisKeyDelegationCert, oneof: \"certificate\" },\n  { no: 7, name: \"mir_cert\", kind: \"message\", T: MirCert, oneof: \"certificate\" },\n  { no: 8, name: \"reg_cert\", kind: \"message\", T: RegCert, oneof: \"certificate\" },\n  { no: 9, name: \"unreg_cert\", kind: \"message\", T: UnRegCert, oneof: \"certificate\" },\n  { no: 10, name: \"vote_deleg_cert\", kind: \"message\", T: VoteDelegCert, oneof: \"certificate\" },\n  { no: 11, name: \"stake_vote_deleg_cert\", kind: \"message\", T: StakeVoteDelegCert, oneof: \"certificate\" },\n  { no: 12, name: \"stake_reg_deleg_cert\", kind: \"message\", T: StakeRegDelegCert, oneof: \"certificate\" },\n  { no: 13, name: \"vote_reg_deleg_cert\", kind: \"message\", T: VoteRegDelegCert, oneof: \"certificate\" },\n  { no: 14, name: \"stake_vote_reg_deleg_cert\", kind: \"message\", T: StakeVoteRegDelegCert, oneof: \"certificate\" },\n  { no: 15, name: \"auth_committee_hot_cert\", kind: \"message\", T: AuthCommitteeHotCert, oneof: \"certificate\" },\n  { no: 16, name: \"resign_committee_cold_cert\", kind: \"message\", T: ResignCommitteeColdCert, oneof: \"certificate\" },\n  { no: 17, name: \"reg_drep_cert\", kind: \"message\", T: RegDRepCert, oneof: \"certificate\" },\n  { no: 18, name: \"unreg_drep_cert\", kind: \"message\", T: UnRegDRepCert, oneof: \"certificate\" },\n  { no: 19, name: \"update_drep_cert\", kind: \"message\", T: UpdateDRepCert, oneof: \"certificate\" },\n  { no: 100, name: \"redeemer\", kind: \"message\", T: Redeemer }\n]);\nvar Certificate = _Certificate;\nvar _StakeDelegationCert = class _StakeDelegationCert extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * Pool key hash.\n     *\n     * @generated from field: bytes pool_keyhash = 2;\n     */\n    this.poolKeyhash = new Uint8Array(0);\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _StakeDelegationCert().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _StakeDelegationCert().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _StakeDelegationCert().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_StakeDelegationCert, a, b);\n  }\n};\n_StakeDelegationCert.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_StakeDelegationCert.typeName = \"utxorpc.v1alpha.cardano.StakeDelegationCert\";\n_StakeDelegationCert.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"stake_credential\", kind: \"message\", T: StakeCredential },\n  {\n    no: 2,\n    name: \"pool_keyhash\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  }\n]);\nvar StakeDelegationCert = _StakeDelegationCert;\nvar _PoolRegistrationCert = class _PoolRegistrationCert extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * Operator key hash.\n     *\n     * @generated from field: bytes operator = 1;\n     */\n    this.operator = new Uint8Array(0);\n    /**\n     * VRF key hash.\n     *\n     * @generated from field: bytes vrf_keyhash = 2;\n     */\n    this.vrfKeyhash = new Uint8Array(0);\n    /**\n     * Pledge amount.\n     *\n     * @generated from field: uint64 pledge = 3;\n     */\n    this.pledge = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    /**\n     * Pool cost.\n     *\n     * @generated from field: uint64 cost = 4;\n     */\n    this.cost = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    /**\n     * Reward account.\n     *\n     * @generated from field: bytes reward_account = 6;\n     */\n    this.rewardAccount = new Uint8Array(0);\n    /**\n     * List of pool owner key hashes.\n     *\n     * @generated from field: repeated bytes pool_owners = 7;\n     */\n    this.poolOwners = [];\n    /**\n     * List of relays.\n     *\n     * @generated from field: repeated utxorpc.v1alpha.cardano.Relay relays = 8;\n     */\n    this.relays = [];\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _PoolRegistrationCert().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _PoolRegistrationCert().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _PoolRegistrationCert().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_PoolRegistrationCert, a, b);\n  }\n};\n_PoolRegistrationCert.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_PoolRegistrationCert.typeName = \"utxorpc.v1alpha.cardano.PoolRegistrationCert\";\n_PoolRegistrationCert.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  {\n    no: 1,\n    name: \"operator\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  },\n  {\n    no: 2,\n    name: \"vrf_keyhash\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  },\n  {\n    no: 3,\n    name: \"pledge\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  },\n  {\n    no: 4,\n    name: \"cost\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  },\n  { no: 5, name: \"margin\", kind: \"message\", T: RationalNumber },\n  {\n    no: 6,\n    name: \"reward_account\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  },\n  { no: 7, name: \"pool_owners\", kind: \"scalar\", T: 12, repeated: true },\n  { no: 8, name: \"relays\", kind: \"message\", T: Relay, repeated: true },\n  { no: 9, name: \"pool_metadata\", kind: \"message\", T: PoolMetadata }\n]);\nvar PoolRegistrationCert = _PoolRegistrationCert;\nvar _PoolRetirementCert = class _PoolRetirementCert extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * Pool key hash.\n     *\n     * @generated from field: bytes pool_keyhash = 1;\n     */\n    this.poolKeyhash = new Uint8Array(0);\n    /**\n     * Retirement epoch.\n     *\n     * @generated from field: uint64 epoch = 2;\n     */\n    this.epoch = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _PoolRetirementCert().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _PoolRetirementCert().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _PoolRetirementCert().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_PoolRetirementCert, a, b);\n  }\n};\n_PoolRetirementCert.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_PoolRetirementCert.typeName = \"utxorpc.v1alpha.cardano.PoolRetirementCert\";\n_PoolRetirementCert.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  {\n    no: 1,\n    name: \"pool_keyhash\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  },\n  {\n    no: 2,\n    name: \"epoch\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  }\n]);\nvar PoolRetirementCert = _PoolRetirementCert;\nvar _GenesisKeyDelegationCert = class _GenesisKeyDelegationCert extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * Genesis hash.\n     *\n     * @generated from field: bytes genesis_hash = 1;\n     */\n    this.genesisHash = new Uint8Array(0);\n    /**\n     * Genesis delegate hash.\n     *\n     * @generated from field: bytes genesis_delegate_hash = 2;\n     */\n    this.genesisDelegateHash = new Uint8Array(0);\n    /**\n     * VRF key hash.\n     *\n     * @generated from field: bytes vrf_keyhash = 3;\n     */\n    this.vrfKeyhash = new Uint8Array(0);\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _GenesisKeyDelegationCert().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _GenesisKeyDelegationCert().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _GenesisKeyDelegationCert().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_GenesisKeyDelegationCert, a, b);\n  }\n};\n_GenesisKeyDelegationCert.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_GenesisKeyDelegationCert.typeName = \"utxorpc.v1alpha.cardano.GenesisKeyDelegationCert\";\n_GenesisKeyDelegationCert.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  {\n    no: 1,\n    name: \"genesis_hash\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  },\n  {\n    no: 2,\n    name: \"genesis_delegate_hash\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  },\n  {\n    no: 3,\n    name: \"vrf_keyhash\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  }\n]);\nvar GenesisKeyDelegationCert = _GenesisKeyDelegationCert;\nvar _MirTarget = class _MirTarget extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from field: int64 delta_coin = 2;\n     */\n    this.deltaCoin = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _MirTarget().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _MirTarget().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _MirTarget().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_MirTarget, a, b);\n  }\n};\n_MirTarget.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_MirTarget.typeName = \"utxorpc.v1alpha.cardano.MirTarget\";\n_MirTarget.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"stake_credential\", kind: \"message\", T: StakeCredential },\n  {\n    no: 2,\n    name: \"delta_coin\",\n    kind: \"scalar\",\n    T: 3\n    /* ScalarType.INT64 */\n  }\n]);\nvar MirTarget = _MirTarget;\nvar _MirCert = class _MirCert extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from field: utxorpc.v1alpha.cardano.MirSource from = 1;\n     */\n    this.from = 0 /* UNSPECIFIED */;\n    /**\n     * @generated from field: repeated utxorpc.v1alpha.cardano.MirTarget to = 2;\n     */\n    this.to = [];\n    /**\n     * @generated from field: uint64 other_pot = 3;\n     */\n    this.otherPot = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _MirCert().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _MirCert().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _MirCert().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_MirCert, a, b);\n  }\n};\n_MirCert.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_MirCert.typeName = \"utxorpc.v1alpha.cardano.MirCert\";\n_MirCert.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"from\", kind: \"enum\", T: _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.getEnumType(MirSource) },\n  { no: 2, name: \"to\", kind: \"message\", T: MirTarget, repeated: true },\n  {\n    no: 3,\n    name: \"other_pot\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  }\n]);\nvar MirCert = _MirCert;\nvar _RegCert = class _RegCert extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from field: uint64 coin = 2;\n     */\n    this.coin = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _RegCert().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _RegCert().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _RegCert().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_RegCert, a, b);\n  }\n};\n_RegCert.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_RegCert.typeName = \"utxorpc.v1alpha.cardano.RegCert\";\n_RegCert.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"stake_credential\", kind: \"message\", T: StakeCredential },\n  {\n    no: 2,\n    name: \"coin\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  }\n]);\nvar RegCert = _RegCert;\nvar _UnRegCert = class _UnRegCert extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from field: uint64 coin = 2;\n     */\n    this.coin = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _UnRegCert().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _UnRegCert().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _UnRegCert().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_UnRegCert, a, b);\n  }\n};\n_UnRegCert.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_UnRegCert.typeName = \"utxorpc.v1alpha.cardano.UnRegCert\";\n_UnRegCert.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"stake_credential\", kind: \"message\", T: StakeCredential },\n  {\n    no: 2,\n    name: \"coin\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  }\n]);\nvar UnRegCert = _UnRegCert;\nvar _DRep = class _DRep extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from oneof utxorpc.v1alpha.cardano.DRep.drep\n     */\n    this.drep = { case: void 0 };\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _DRep().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _DRep().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _DRep().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_DRep, a, b);\n  }\n};\n_DRep.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_DRep.typeName = \"utxorpc.v1alpha.cardano.DRep\";\n_DRep.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"addr_key_hash\", kind: \"scalar\", T: 12, oneof: \"drep\" },\n  { no: 2, name: \"script_hash\", kind: \"scalar\", T: 12, oneof: \"drep\" },\n  { no: 3, name: \"abstain\", kind: \"scalar\", T: 8, oneof: \"drep\" },\n  { no: 4, name: \"no_confidence\", kind: \"scalar\", T: 8, oneof: \"drep\" }\n]);\nvar DRep = _DRep;\nvar _VoteDelegCert = class _VoteDelegCert extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _VoteDelegCert().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _VoteDelegCert().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _VoteDelegCert().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_VoteDelegCert, a, b);\n  }\n};\n_VoteDelegCert.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_VoteDelegCert.typeName = \"utxorpc.v1alpha.cardano.VoteDelegCert\";\n_VoteDelegCert.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"stake_credential\", kind: \"message\", T: StakeCredential },\n  { no: 2, name: \"drep\", kind: \"message\", T: DRep }\n]);\nvar VoteDelegCert = _VoteDelegCert;\nvar _StakeVoteDelegCert = class _StakeVoteDelegCert extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from field: bytes pool_keyhash = 2;\n     */\n    this.poolKeyhash = new Uint8Array(0);\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _StakeVoteDelegCert().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _StakeVoteDelegCert().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _StakeVoteDelegCert().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_StakeVoteDelegCert, a, b);\n  }\n};\n_StakeVoteDelegCert.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_StakeVoteDelegCert.typeName = \"utxorpc.v1alpha.cardano.StakeVoteDelegCert\";\n_StakeVoteDelegCert.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"stake_credential\", kind: \"message\", T: StakeCredential },\n  {\n    no: 2,\n    name: \"pool_keyhash\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  },\n  { no: 3, name: \"drep\", kind: \"message\", T: DRep }\n]);\nvar StakeVoteDelegCert = _StakeVoteDelegCert;\nvar _StakeRegDelegCert = class _StakeRegDelegCert extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from field: bytes pool_keyhash = 2;\n     */\n    this.poolKeyhash = new Uint8Array(0);\n    /**\n     * @generated from field: uint64 coin = 3;\n     */\n    this.coin = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _StakeRegDelegCert().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _StakeRegDelegCert().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _StakeRegDelegCert().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_StakeRegDelegCert, a, b);\n  }\n};\n_StakeRegDelegCert.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_StakeRegDelegCert.typeName = \"utxorpc.v1alpha.cardano.StakeRegDelegCert\";\n_StakeRegDelegCert.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"stake_credential\", kind: \"message\", T: StakeCredential },\n  {\n    no: 2,\n    name: \"pool_keyhash\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  },\n  {\n    no: 3,\n    name: \"coin\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  }\n]);\nvar StakeRegDelegCert = _StakeRegDelegCert;\nvar _VoteRegDelegCert = class _VoteRegDelegCert extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from field: uint64 coin = 3;\n     */\n    this.coin = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _VoteRegDelegCert().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _VoteRegDelegCert().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _VoteRegDelegCert().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_VoteRegDelegCert, a, b);\n  }\n};\n_VoteRegDelegCert.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_VoteRegDelegCert.typeName = \"utxorpc.v1alpha.cardano.VoteRegDelegCert\";\n_VoteRegDelegCert.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"stake_credential\", kind: \"message\", T: StakeCredential },\n  { no: 2, name: \"drep\", kind: \"message\", T: DRep },\n  {\n    no: 3,\n    name: \"coin\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  }\n]);\nvar VoteRegDelegCert = _VoteRegDelegCert;\nvar _StakeVoteRegDelegCert = class _StakeVoteRegDelegCert extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from field: bytes pool_keyhash = 2;\n     */\n    this.poolKeyhash = new Uint8Array(0);\n    /**\n     * @generated from field: uint64 coin = 4;\n     */\n    this.coin = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _StakeVoteRegDelegCert().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _StakeVoteRegDelegCert().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _StakeVoteRegDelegCert().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_StakeVoteRegDelegCert, a, b);\n  }\n};\n_StakeVoteRegDelegCert.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_StakeVoteRegDelegCert.typeName = \"utxorpc.v1alpha.cardano.StakeVoteRegDelegCert\";\n_StakeVoteRegDelegCert.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"stake_credential\", kind: \"message\", T: StakeCredential },\n  {\n    no: 2,\n    name: \"pool_keyhash\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  },\n  { no: 3, name: \"drep\", kind: \"message\", T: DRep },\n  {\n    no: 4,\n    name: \"coin\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  }\n]);\nvar StakeVoteRegDelegCert = _StakeVoteRegDelegCert;\nvar _AuthCommitteeHotCert = class _AuthCommitteeHotCert extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _AuthCommitteeHotCert().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _AuthCommitteeHotCert().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _AuthCommitteeHotCert().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_AuthCommitteeHotCert, a, b);\n  }\n};\n_AuthCommitteeHotCert.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_AuthCommitteeHotCert.typeName = \"utxorpc.v1alpha.cardano.AuthCommitteeHotCert\";\n_AuthCommitteeHotCert.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"committee_cold_credential\", kind: \"message\", T: StakeCredential },\n  { no: 2, name: \"committee_hot_credential\", kind: \"message\", T: StakeCredential }\n]);\nvar AuthCommitteeHotCert = _AuthCommitteeHotCert;\nvar _Anchor = class _Anchor extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from field: string url = 1;\n     */\n    this.url = \"\";\n    /**\n     * @generated from field: bytes content_hash = 2;\n     */\n    this.contentHash = new Uint8Array(0);\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _Anchor().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _Anchor().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _Anchor().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_Anchor, a, b);\n  }\n};\n_Anchor.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_Anchor.typeName = \"utxorpc.v1alpha.cardano.Anchor\";\n_Anchor.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  {\n    no: 1,\n    name: \"url\",\n    kind: \"scalar\",\n    T: 9\n    /* ScalarType.STRING */\n  },\n  {\n    no: 2,\n    name: \"content_hash\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  }\n]);\nvar Anchor = _Anchor;\nvar _ResignCommitteeColdCert = class _ResignCommitteeColdCert extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _ResignCommitteeColdCert().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _ResignCommitteeColdCert().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _ResignCommitteeColdCert().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_ResignCommitteeColdCert, a, b);\n  }\n};\n_ResignCommitteeColdCert.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_ResignCommitteeColdCert.typeName = \"utxorpc.v1alpha.cardano.ResignCommitteeColdCert\";\n_ResignCommitteeColdCert.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"committee_cold_credential\", kind: \"message\", T: StakeCredential },\n  { no: 2, name: \"anchor\", kind: \"message\", T: Anchor }\n]);\nvar ResignCommitteeColdCert = _ResignCommitteeColdCert;\nvar _RegDRepCert = class _RegDRepCert extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from field: uint64 coin = 2;\n     */\n    this.coin = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _RegDRepCert().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _RegDRepCert().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _RegDRepCert().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_RegDRepCert, a, b);\n  }\n};\n_RegDRepCert.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_RegDRepCert.typeName = \"utxorpc.v1alpha.cardano.RegDRepCert\";\n_RegDRepCert.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"drep_credential\", kind: \"message\", T: StakeCredential },\n  {\n    no: 2,\n    name: \"coin\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  },\n  { no: 3, name: \"anchor\", kind: \"message\", T: Anchor }\n]);\nvar RegDRepCert = _RegDRepCert;\nvar _UnRegDRepCert = class _UnRegDRepCert extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from field: uint64 coin = 2;\n     */\n    this.coin = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _UnRegDRepCert().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _UnRegDRepCert().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _UnRegDRepCert().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_UnRegDRepCert, a, b);\n  }\n};\n_UnRegDRepCert.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_UnRegDRepCert.typeName = \"utxorpc.v1alpha.cardano.UnRegDRepCert\";\n_UnRegDRepCert.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"drep_credential\", kind: \"message\", T: StakeCredential },\n  {\n    no: 2,\n    name: \"coin\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  }\n]);\nvar UnRegDRepCert = _UnRegDRepCert;\nvar _UpdateDRepCert = class _UpdateDRepCert extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _UpdateDRepCert().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _UpdateDRepCert().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _UpdateDRepCert().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_UpdateDRepCert, a, b);\n  }\n};\n_UpdateDRepCert.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_UpdateDRepCert.typeName = \"utxorpc.v1alpha.cardano.UpdateDRepCert\";\n_UpdateDRepCert.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"drep_credential\", kind: \"message\", T: StakeCredential },\n  { no: 2, name: \"anchor\", kind: \"message\", T: Anchor }\n]);\nvar UpdateDRepCert = _UpdateDRepCert;\nvar _AddressPattern = class _AddressPattern extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * The address should match this exact address value.\n     *\n     * @generated from field: bytes exact_address = 1;\n     */\n    this.exactAddress = new Uint8Array(0);\n    /**\n     * The payment part of the address should match this value.\n     *\n     * @generated from field: bytes payment_part = 2;\n     */\n    this.paymentPart = new Uint8Array(0);\n    /**\n     * The delegation part of the address should match this value.\n     *\n     * @generated from field: bytes delegation_part = 3;\n     */\n    this.delegationPart = new Uint8Array(0);\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _AddressPattern().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _AddressPattern().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _AddressPattern().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_AddressPattern, a, b);\n  }\n};\n_AddressPattern.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_AddressPattern.typeName = \"utxorpc.v1alpha.cardano.AddressPattern\";\n_AddressPattern.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  {\n    no: 1,\n    name: \"exact_address\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  },\n  {\n    no: 2,\n    name: \"payment_part\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  },\n  {\n    no: 3,\n    name: \"delegation_part\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  }\n]);\nvar AddressPattern = _AddressPattern;\nvar _AssetPattern = class _AssetPattern extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * The asset should belong to this policy id\n     *\n     * @generated from field: bytes policy_id = 1;\n     */\n    this.policyId = new Uint8Array(0);\n    /**\n     * The asset should present this name\n     *\n     * @generated from field: bytes asset_name = 2;\n     */\n    this.assetName = new Uint8Array(0);\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _AssetPattern().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _AssetPattern().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _AssetPattern().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_AssetPattern, a, b);\n  }\n};\n_AssetPattern.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_AssetPattern.typeName = \"utxorpc.v1alpha.cardano.AssetPattern\";\n_AssetPattern.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  {\n    no: 1,\n    name: \"policy_id\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  },\n  {\n    no: 2,\n    name: \"asset_name\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  }\n]);\nvar AssetPattern = _AssetPattern;\nvar _TxOutputPattern = class _TxOutputPattern extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _TxOutputPattern().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _TxOutputPattern().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _TxOutputPattern().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_TxOutputPattern, a, b);\n  }\n};\n_TxOutputPattern.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_TxOutputPattern.typeName = \"utxorpc.v1alpha.cardano.TxOutputPattern\";\n_TxOutputPattern.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"address\", kind: \"message\", T: AddressPattern },\n  { no: 2, name: \"asset\", kind: \"message\", T: AssetPattern }\n]);\nvar TxOutputPattern = _TxOutputPattern;\nvar _TxPattern = class _TxPattern extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _TxPattern().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _TxPattern().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _TxPattern().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_TxPattern, a, b);\n  }\n};\n_TxPattern.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_TxPattern.typeName = \"utxorpc.v1alpha.cardano.TxPattern\";\n_TxPattern.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"consumes\", kind: \"message\", T: TxOutputPattern },\n  { no: 2, name: \"produces\", kind: \"message\", T: TxOutputPattern },\n  { no: 3, name: \"has_address\", kind: \"message\", T: AddressPattern },\n  { no: 4, name: \"moves_asset\", kind: \"message\", T: AssetPattern },\n  { no: 5, name: \"mints_asset\", kind: \"message\", T: AssetPattern }\n]);\nvar TxPattern = _TxPattern;\nvar _ExUnits = class _ExUnits extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from field: uint64 steps = 1;\n     */\n    this.steps = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    /**\n     * @generated from field: uint64 memory = 2;\n     */\n    this.memory = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _ExUnits().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _ExUnits().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _ExUnits().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_ExUnits, a, b);\n  }\n};\n_ExUnits.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_ExUnits.typeName = \"utxorpc.v1alpha.cardano.ExUnits\";\n_ExUnits.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  {\n    no: 1,\n    name: \"steps\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  },\n  {\n    no: 2,\n    name: \"memory\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  }\n]);\nvar ExUnits = _ExUnits;\nvar _ExPrices = class _ExPrices extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _ExPrices().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _ExPrices().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _ExPrices().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_ExPrices, a, b);\n  }\n};\n_ExPrices.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_ExPrices.typeName = \"utxorpc.v1alpha.cardano.ExPrices\";\n_ExPrices.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"steps\", kind: \"message\", T: RationalNumber },\n  { no: 2, name: \"memory\", kind: \"message\", T: RationalNumber }\n]);\nvar ExPrices = _ExPrices;\nvar _ProtocolVersion = class _ProtocolVersion extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from field: uint32 major = 1;\n     */\n    this.major = 0;\n    /**\n     * @generated from field: uint32 minor = 2;\n     */\n    this.minor = 0;\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _ProtocolVersion().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _ProtocolVersion().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _ProtocolVersion().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_ProtocolVersion, a, b);\n  }\n};\n_ProtocolVersion.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_ProtocolVersion.typeName = \"utxorpc.v1alpha.cardano.ProtocolVersion\";\n_ProtocolVersion.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  {\n    no: 1,\n    name: \"major\",\n    kind: \"scalar\",\n    T: 13\n    /* ScalarType.UINT32 */\n  },\n  {\n    no: 2,\n    name: \"minor\",\n    kind: \"scalar\",\n    T: 13\n    /* ScalarType.UINT32 */\n  }\n]);\nvar ProtocolVersion = _ProtocolVersion;\nvar _CostModel = class _CostModel extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from field: repeated int64 values = 1;\n     */\n    this.values = [];\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _CostModel().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _CostModel().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _CostModel().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_CostModel, a, b);\n  }\n};\n_CostModel.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_CostModel.typeName = \"utxorpc.v1alpha.cardano.CostModel\";\n_CostModel.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"values\", kind: \"scalar\", T: 3, repeated: true }\n]);\nvar CostModel = _CostModel;\nvar _CostModels = class _CostModels extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _CostModels().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _CostModels().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _CostModels().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_CostModels, a, b);\n  }\n};\n_CostModels.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_CostModels.typeName = \"utxorpc.v1alpha.cardano.CostModels\";\n_CostModels.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"plutus_v1\", kind: \"message\", T: CostModel },\n  { no: 2, name: \"plutus_v2\", kind: \"message\", T: CostModel },\n  { no: 3, name: \"plutus_v3\", kind: \"message\", T: CostModel }\n]);\nvar CostModels = _CostModels;\nvar _PParams = class _PParams extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * The number of coins per UTXO byte.\n     *\n     * @generated from field: uint64 coins_per_utxo_byte = 1;\n     */\n    this.coinsPerUtxoByte = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    /**\n     * The maximum transaction size.\n     *\n     * @generated from field: uint64 max_tx_size = 2;\n     */\n    this.maxTxSize = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    /**\n     * The minimum fee coefficient.\n     *\n     * @generated from field: uint64 min_fee_coefficient = 3;\n     */\n    this.minFeeCoefficient = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    /**\n     * The minimum fee constant.\n     *\n     * @generated from field: uint64 min_fee_constant = 4;\n     */\n    this.minFeeConstant = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    /**\n     * The maximum block body size.\n     *\n     * @generated from field: uint64 max_block_body_size = 5;\n     */\n    this.maxBlockBodySize = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    /**\n     * The maximum block header size.\n     *\n     * @generated from field: uint64 max_block_header_size = 6;\n     */\n    this.maxBlockHeaderSize = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    /**\n     * The stake key deposit.\n     *\n     * @generated from field: uint64 stake_key_deposit = 7;\n     */\n    this.stakeKeyDeposit = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    /**\n     * The pool deposit.\n     *\n     * @generated from field: uint64 pool_deposit = 8;\n     */\n    this.poolDeposit = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    /**\n     * The pool retirement epoch bound.\n     *\n     * @generated from field: uint64 pool_retirement_epoch_bound = 9;\n     */\n    this.poolRetirementEpochBound = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    /**\n     * The desired number of pools.\n     *\n     * @generated from field: uint64 desired_number_of_pools = 10;\n     */\n    this.desiredNumberOfPools = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    /**\n     * The minimum pool cost.\n     *\n     * @generated from field: uint64 min_pool_cost = 14;\n     */\n    this.minPoolCost = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    /**\n     * The maximum value size.\n     *\n     * @generated from field: uint64 max_value_size = 16;\n     */\n    this.maxValueSize = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    /**\n     * The collateral percentage.\n     *\n     * @generated from field: uint64 collateral_percentage = 17;\n     */\n    this.collateralPercentage = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    /**\n     * The maximum collateral inputs.\n     *\n     * @generated from field: uint64 max_collateral_inputs = 18;\n     */\n    this.maxCollateralInputs = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _PParams().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _PParams().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _PParams().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_PParams, a, b);\n  }\n};\n_PParams.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_PParams.typeName = \"utxorpc.v1alpha.cardano.PParams\";\n_PParams.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  {\n    no: 1,\n    name: \"coins_per_utxo_byte\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  },\n  {\n    no: 2,\n    name: \"max_tx_size\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  },\n  {\n    no: 3,\n    name: \"min_fee_coefficient\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  },\n  {\n    no: 4,\n    name: \"min_fee_constant\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  },\n  {\n    no: 5,\n    name: \"max_block_body_size\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  },\n  {\n    no: 6,\n    name: \"max_block_header_size\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  },\n  {\n    no: 7,\n    name: \"stake_key_deposit\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  },\n  {\n    no: 8,\n    name: \"pool_deposit\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  },\n  {\n    no: 9,\n    name: \"pool_retirement_epoch_bound\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  },\n  {\n    no: 10,\n    name: \"desired_number_of_pools\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  },\n  { no: 11, name: \"pool_influence\", kind: \"message\", T: RationalNumber },\n  { no: 12, name: \"monetary_expansion\", kind: \"message\", T: RationalNumber },\n  { no: 13, name: \"treasury_expansion\", kind: \"message\", T: RationalNumber },\n  {\n    no: 14,\n    name: \"min_pool_cost\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  },\n  { no: 15, name: \"protocol_version\", kind: \"message\", T: ProtocolVersion },\n  {\n    no: 16,\n    name: \"max_value_size\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  },\n  {\n    no: 17,\n    name: \"collateral_percentage\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  },\n  {\n    no: 18,\n    name: \"max_collateral_inputs\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  },\n  { no: 19, name: \"cost_models\", kind: \"message\", T: CostModels },\n  { no: 20, name: \"prices\", kind: \"message\", T: ExPrices },\n  { no: 21, name: \"max_execution_units_per_transaction\", kind: \"message\", T: ExUnits },\n  { no: 22, name: \"max_execution_units_per_block\", kind: \"message\", T: ExUnits }\n]);\nvar PParams = _PParams;\nvar _EvalError = class _EvalError extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from field: string msg = 1;\n     */\n    this.msg = \"\";\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _EvalError().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _EvalError().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _EvalError().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_EvalError, a, b);\n  }\n};\n_EvalError.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_EvalError.typeName = \"utxorpc.v1alpha.cardano.EvalError\";\n_EvalError.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  {\n    no: 1,\n    name: \"msg\",\n    kind: \"scalar\",\n    T: 9\n    /* ScalarType.STRING */\n  }\n]);\nvar EvalError = _EvalError;\nvar _EvalTrace = class _EvalTrace extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from field: string msg = 1;\n     */\n    this.msg = \"\";\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _EvalTrace().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _EvalTrace().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _EvalTrace().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_EvalTrace, a, b);\n  }\n};\n_EvalTrace.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_EvalTrace.typeName = \"utxorpc.v1alpha.cardano.EvalTrace\";\n_EvalTrace.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  {\n    no: 1,\n    name: \"msg\",\n    kind: \"scalar\",\n    T: 9\n    /* ScalarType.STRING */\n  }\n]);\nvar EvalTrace = _EvalTrace;\nvar _TxEval = class _TxEval extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from field: uint64 fee = 1;\n     */\n    this.fee = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    /**\n     * @generated from field: repeated utxorpc.v1alpha.cardano.EvalError errors = 3;\n     */\n    this.errors = [];\n    /**\n     * @generated from field: repeated utxorpc.v1alpha.cardano.EvalTrace traces = 4;\n     */\n    this.traces = [];\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _TxEval().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _TxEval().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _TxEval().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_TxEval, a, b);\n  }\n};\n_TxEval.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_TxEval.typeName = \"utxorpc.v1alpha.cardano.TxEval\";\n_TxEval.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  {\n    no: 1,\n    name: \"fee\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  },\n  { no: 2, name: \"ex_units\", kind: \"message\", T: ExUnits },\n  { no: 3, name: \"errors\", kind: \"message\", T: EvalError, repeated: true },\n  { no: 4, name: \"traces\", kind: \"message\", T: EvalTrace, repeated: true }\n]);\nvar TxEval = _TxEval;\n\n// src/utxorpc/v1alpha/query/query_pb.ts\nvar query_pb_exports = {};\n__export(query_pb_exports, {\n  AnyChainDatum: () => AnyChainDatum,\n  AnyChainParams: () => AnyChainParams,\n  AnyUtxoData: () => AnyUtxoData,\n  AnyUtxoPattern: () => AnyUtxoPattern,\n  ChainPoint: () => ChainPoint,\n  ReadDataRequest: () => ReadDataRequest,\n  ReadDataResponse: () => ReadDataResponse,\n  ReadParamsRequest: () => ReadParamsRequest,\n  ReadParamsResponse: () => ReadParamsResponse,\n  ReadUtxosRequest: () => ReadUtxosRequest,\n  ReadUtxosResponse: () => ReadUtxosResponse,\n  SearchUtxosRequest: () => SearchUtxosRequest,\n  SearchUtxosResponse: () => SearchUtxosResponse,\n  TxoRef: () => TxoRef,\n  UtxoPredicate: () => UtxoPredicate\n});\n\nvar _ChainPoint = class _ChainPoint extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * Slot number.\n     *\n     * @generated from field: uint64 slot = 1;\n     */\n    this.slot = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    /**\n     * Block hash.\n     *\n     * @generated from field: bytes hash = 2;\n     */\n    this.hash = new Uint8Array(0);\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _ChainPoint().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _ChainPoint().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _ChainPoint().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_ChainPoint, a, b);\n  }\n};\n_ChainPoint.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_ChainPoint.typeName = \"utxorpc.v1alpha.query.ChainPoint\";\n_ChainPoint.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  {\n    no: 1,\n    name: \"slot\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  },\n  {\n    no: 2,\n    name: \"hash\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  }\n]);\nvar ChainPoint = _ChainPoint;\nvar _TxoRef = class _TxoRef extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * Tx hash.\n     *\n     * @generated from field: bytes hash = 1;\n     */\n    this.hash = new Uint8Array(0);\n    /**\n     * Output index.\n     *\n     * @generated from field: uint32 index = 2;\n     */\n    this.index = 0;\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _TxoRef().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _TxoRef().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _TxoRef().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_TxoRef, a, b);\n  }\n};\n_TxoRef.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_TxoRef.typeName = \"utxorpc.v1alpha.query.TxoRef\";\n_TxoRef.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  {\n    no: 1,\n    name: \"hash\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  },\n  {\n    no: 2,\n    name: \"index\",\n    kind: \"scalar\",\n    T: 13\n    /* ScalarType.UINT32 */\n  }\n]);\nvar TxoRef = _TxoRef;\nvar _ReadParamsRequest = class _ReadParamsRequest extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _ReadParamsRequest().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _ReadParamsRequest().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _ReadParamsRequest().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_ReadParamsRequest, a, b);\n  }\n};\n_ReadParamsRequest.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_ReadParamsRequest.typeName = \"utxorpc.v1alpha.query.ReadParamsRequest\";\n_ReadParamsRequest.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"field_mask\", kind: \"message\", T: _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_3__.FieldMask }\n]);\nvar ReadParamsRequest = _ReadParamsRequest;\nvar _AnyChainParams = class _AnyChainParams extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from oneof utxorpc.v1alpha.query.AnyChainParams.params\n     */\n    this.params = { case: void 0 };\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _AnyChainParams().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _AnyChainParams().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _AnyChainParams().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_AnyChainParams, a, b);\n  }\n};\n_AnyChainParams.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_AnyChainParams.typeName = \"utxorpc.v1alpha.query.AnyChainParams\";\n_AnyChainParams.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"cardano\", kind: \"message\", T: PParams, oneof: \"params\" }\n]);\nvar AnyChainParams = _AnyChainParams;\nvar _ReadParamsResponse = class _ReadParamsResponse extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _ReadParamsResponse().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _ReadParamsResponse().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _ReadParamsResponse().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_ReadParamsResponse, a, b);\n  }\n};\n_ReadParamsResponse.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_ReadParamsResponse.typeName = \"utxorpc.v1alpha.query.ReadParamsResponse\";\n_ReadParamsResponse.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"values\", kind: \"message\", T: AnyChainParams },\n  { no: 2, name: \"ledger_tip\", kind: \"message\", T: ChainPoint }\n]);\nvar ReadParamsResponse = _ReadParamsResponse;\nvar _AnyUtxoPattern = class _AnyUtxoPattern extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from oneof utxorpc.v1alpha.query.AnyUtxoPattern.utxo_pattern\n     */\n    this.utxoPattern = { case: void 0 };\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _AnyUtxoPattern().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _AnyUtxoPattern().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _AnyUtxoPattern().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_AnyUtxoPattern, a, b);\n  }\n};\n_AnyUtxoPattern.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_AnyUtxoPattern.typeName = \"utxorpc.v1alpha.query.AnyUtxoPattern\";\n_AnyUtxoPattern.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"cardano\", kind: \"message\", T: TxOutputPattern, oneof: \"utxo_pattern\" }\n]);\nvar AnyUtxoPattern = _AnyUtxoPattern;\nvar _UtxoPredicate = class _UtxoPredicate extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * Predicate is true if tx doesn't exhibit pattern.\n     *\n     * @generated from field: repeated utxorpc.v1alpha.query.UtxoPredicate not = 2;\n     */\n    this.not = [];\n    /**\n     * Predicate is true if utxo exhibits all of the patterns.\n     *\n     * @generated from field: repeated utxorpc.v1alpha.query.UtxoPredicate all_of = 3;\n     */\n    this.allOf = [];\n    /**\n     * Predicate is true if utxo exhibits any of the patterns.\n     *\n     * @generated from field: repeated utxorpc.v1alpha.query.UtxoPredicate any_of = 4;\n     */\n    this.anyOf = [];\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _UtxoPredicate().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _UtxoPredicate().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _UtxoPredicate().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_UtxoPredicate, a, b);\n  }\n};\n_UtxoPredicate.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_UtxoPredicate.typeName = \"utxorpc.v1alpha.query.UtxoPredicate\";\n_UtxoPredicate.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"match\", kind: \"message\", T: AnyUtxoPattern },\n  { no: 2, name: \"not\", kind: \"message\", T: _UtxoPredicate, repeated: true },\n  { no: 3, name: \"all_of\", kind: \"message\", T: _UtxoPredicate, repeated: true },\n  { no: 4, name: \"any_of\", kind: \"message\", T: _UtxoPredicate, repeated: true }\n]);\nvar UtxoPredicate = _UtxoPredicate;\nvar _AnyUtxoData = class _AnyUtxoData extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * Original bytes as defined by the chain\n     *\n     * @generated from field: bytes native_bytes = 1;\n     */\n    this.nativeBytes = new Uint8Array(0);\n    /**\n     * @generated from oneof utxorpc.v1alpha.query.AnyUtxoData.parsed_state\n     */\n    this.parsedState = { case: void 0 };\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _AnyUtxoData().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _AnyUtxoData().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _AnyUtxoData().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_AnyUtxoData, a, b);\n  }\n};\n_AnyUtxoData.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_AnyUtxoData.typeName = \"utxorpc.v1alpha.query.AnyUtxoData\";\n_AnyUtxoData.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  {\n    no: 1,\n    name: \"native_bytes\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  },\n  { no: 2, name: \"txo_ref\", kind: \"message\", T: TxoRef },\n  { no: 3, name: \"cardano\", kind: \"message\", T: TxOutput, oneof: \"parsed_state\" }\n]);\nvar AnyUtxoData = _AnyUtxoData;\nvar _ReadUtxosRequest = class _ReadUtxosRequest extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * List of keys UTxOs.\n     *\n     * @generated from field: repeated utxorpc.v1alpha.query.TxoRef keys = 1;\n     */\n    this.keys = [];\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _ReadUtxosRequest().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _ReadUtxosRequest().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _ReadUtxosRequest().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_ReadUtxosRequest, a, b);\n  }\n};\n_ReadUtxosRequest.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_ReadUtxosRequest.typeName = \"utxorpc.v1alpha.query.ReadUtxosRequest\";\n_ReadUtxosRequest.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"keys\", kind: \"message\", T: TxoRef, repeated: true },\n  { no: 2, name: \"field_mask\", kind: \"message\", T: _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_3__.FieldMask }\n]);\nvar ReadUtxosRequest = _ReadUtxosRequest;\nvar _ReadUtxosResponse = class _ReadUtxosResponse extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * List of UTxOs.\n     *\n     * @generated from field: repeated utxorpc.v1alpha.query.AnyUtxoData items = 1;\n     */\n    this.items = [];\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _ReadUtxosResponse().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _ReadUtxosResponse().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _ReadUtxosResponse().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_ReadUtxosResponse, a, b);\n  }\n};\n_ReadUtxosResponse.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_ReadUtxosResponse.typeName = \"utxorpc.v1alpha.query.ReadUtxosResponse\";\n_ReadUtxosResponse.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"items\", kind: \"message\", T: AnyUtxoData, repeated: true },\n  { no: 2, name: \"ledger_tip\", kind: \"message\", T: ChainPoint }\n]);\nvar ReadUtxosResponse = _ReadUtxosResponse;\nvar _SearchUtxosRequest = class _SearchUtxosRequest extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * The maximum number of items to return.\n     *\n     * @generated from field: int32 max_items = 3;\n     */\n    this.maxItems = 0;\n    /**\n     * The next_page_token value returned from a previous request, if any.\n     *\n     * @generated from field: string start_token = 4;\n     */\n    this.startToken = \"\";\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _SearchUtxosRequest().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _SearchUtxosRequest().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _SearchUtxosRequest().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_SearchUtxosRequest, a, b);\n  }\n};\n_SearchUtxosRequest.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_SearchUtxosRequest.typeName = \"utxorpc.v1alpha.query.SearchUtxosRequest\";\n_SearchUtxosRequest.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"predicate\", kind: \"message\", T: UtxoPredicate },\n  { no: 2, name: \"field_mask\", kind: \"message\", T: _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_3__.FieldMask },\n  {\n    no: 3,\n    name: \"max_items\",\n    kind: \"scalar\",\n    T: 5\n    /* ScalarType.INT32 */\n  },\n  {\n    no: 4,\n    name: \"start_token\",\n    kind: \"scalar\",\n    T: 9\n    /* ScalarType.STRING */\n  }\n]);\nvar SearchUtxosRequest = _SearchUtxosRequest;\nvar _SearchUtxosResponse = class _SearchUtxosResponse extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * List of UTxOs.\n     *\n     * @generated from field: repeated utxorpc.v1alpha.query.AnyUtxoData items = 1;\n     */\n    this.items = [];\n    /**\n     * Token to retrieve the next page of results, or empty if there are no more results.\n     *\n     * @generated from field: string next_token = 3;\n     */\n    this.nextToken = \"\";\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _SearchUtxosResponse().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _SearchUtxosResponse().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _SearchUtxosResponse().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_SearchUtxosResponse, a, b);\n  }\n};\n_SearchUtxosResponse.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_SearchUtxosResponse.typeName = \"utxorpc.v1alpha.query.SearchUtxosResponse\";\n_SearchUtxosResponse.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"items\", kind: \"message\", T: AnyUtxoData, repeated: true },\n  { no: 2, name: \"ledger_tip\", kind: \"message\", T: ChainPoint },\n  {\n    no: 3,\n    name: \"next_token\",\n    kind: \"scalar\",\n    T: 9\n    /* ScalarType.STRING */\n  }\n]);\nvar SearchUtxosResponse = _SearchUtxosResponse;\nvar _ReadDataRequest = class _ReadDataRequest extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from field: repeated bytes keys = 1;\n     */\n    this.keys = [];\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _ReadDataRequest().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _ReadDataRequest().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _ReadDataRequest().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_ReadDataRequest, a, b);\n  }\n};\n_ReadDataRequest.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_ReadDataRequest.typeName = \"utxorpc.v1alpha.query.ReadDataRequest\";\n_ReadDataRequest.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"keys\", kind: \"scalar\", T: 12, repeated: true },\n  { no: 2, name: \"field_mask\", kind: \"message\", T: _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_3__.FieldMask }\n]);\nvar ReadDataRequest = _ReadDataRequest;\nvar _AnyChainDatum = class _AnyChainDatum extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * Original bytes as defined by the chain\n     *\n     * @generated from field: bytes native_bytes = 1;\n     */\n    this.nativeBytes = new Uint8Array(0);\n    /**\n     * @generated from field: bytes key = 2;\n     */\n    this.key = new Uint8Array(0);\n    /**\n     * @generated from oneof utxorpc.v1alpha.query.AnyChainDatum.parsed_state\n     */\n    this.parsedState = { case: void 0 };\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _AnyChainDatum().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _AnyChainDatum().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _AnyChainDatum().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_AnyChainDatum, a, b);\n  }\n};\n_AnyChainDatum.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_AnyChainDatum.typeName = \"utxorpc.v1alpha.query.AnyChainDatum\";\n_AnyChainDatum.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  {\n    no: 1,\n    name: \"native_bytes\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  },\n  {\n    no: 2,\n    name: \"key\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  },\n  { no: 3, name: \"cardano\", kind: \"message\", T: PlutusData, oneof: \"parsed_state\" }\n]);\nvar AnyChainDatum = _AnyChainDatum;\nvar _ReadDataResponse = class _ReadDataResponse extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * The value of each datum.\n     *\n     * @generated from field: repeated utxorpc.v1alpha.query.AnyChainDatum values = 1;\n     */\n    this.values = [];\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _ReadDataResponse().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _ReadDataResponse().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _ReadDataResponse().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_ReadDataResponse, a, b);\n  }\n};\n_ReadDataResponse.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_ReadDataResponse.typeName = \"utxorpc.v1alpha.query.ReadDataResponse\";\n_ReadDataResponse.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"values\", kind: \"message\", T: AnyChainDatum, repeated: true },\n  { no: 2, name: \"ledger_tip\", kind: \"message\", T: ChainPoint }\n]);\nvar ReadDataResponse = _ReadDataResponse;\n\n// src/utxorpc/v1alpha/query/query_connect.ts\nvar query_connect_exports = {};\n__export(query_connect_exports, {\n  QueryService: () => QueryService\n});\n\nvar QueryService = {\n  typeName: \"utxorpc.v1alpha.query.QueryService\",\n  methods: {\n    /**\n     * Get overall chain state.\n     *\n     * @generated from rpc utxorpc.v1alpha.query.QueryService.ReadParams\n     */\n    readParams: {\n      name: \"ReadParams\",\n      I: ReadParamsRequest,\n      O: ReadParamsResponse,\n      kind: _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_4__.MethodKind.Unary\n    },\n    /**\n     * Read specific UTxOs by reference.\n     *\n     * @generated from rpc utxorpc.v1alpha.query.QueryService.ReadUtxos\n     */\n    readUtxos: {\n      name: \"ReadUtxos\",\n      I: ReadUtxosRequest,\n      O: ReadUtxosResponse,\n      kind: _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_4__.MethodKind.Unary\n    },\n    /**\n     * Search for UTxO based on a pattern.\n     *\n     * @generated from rpc utxorpc.v1alpha.query.QueryService.SearchUtxos\n     */\n    searchUtxos: {\n      name: \"SearchUtxos\",\n      I: SearchUtxosRequest,\n      O: SearchUtxosResponse,\n      kind: _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_4__.MethodKind.Unary\n    },\n    /**\n     * Read specific datum by hash\n     *\n     * @generated from rpc utxorpc.v1alpha.query.QueryService.ReadData\n     */\n    readData: {\n      name: \"ReadData\",\n      I: ReadDataRequest,\n      O: ReadDataResponse,\n      kind: _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_4__.MethodKind.Unary\n    }\n  }\n};\n\n// src/utxorpc/v1alpha/submit/submit_pb.ts\nvar submit_pb_exports = {};\n__export(submit_pb_exports, {\n  AnyChainEval: () => AnyChainEval,\n  AnyChainTx: () => AnyChainTx,\n  AnyChainTxPattern: () => AnyChainTxPattern,\n  EvalTxRequest: () => EvalTxRequest,\n  EvalTxResponse: () => EvalTxResponse,\n  ReadMempoolRequest: () => ReadMempoolRequest,\n  ReadMempoolResponse: () => ReadMempoolResponse,\n  Stage: () => Stage,\n  SubmitTxRequest: () => SubmitTxRequest,\n  SubmitTxResponse: () => SubmitTxResponse,\n  TxInMempool: () => TxInMempool,\n  TxPredicate: () => TxPredicate,\n  WaitForTxRequest: () => WaitForTxRequest,\n  WaitForTxResponse: () => WaitForTxResponse,\n  WatchMempoolRequest: () => WatchMempoolRequest,\n  WatchMempoolResponse: () => WatchMempoolResponse\n});\n\nvar Stage = /* @__PURE__ */ ((Stage2) => {\n  Stage2[Stage2[\"UNSPECIFIED\"] = 0] = \"UNSPECIFIED\";\n  Stage2[Stage2[\"ACKNOWLEDGED\"] = 1] = \"ACKNOWLEDGED\";\n  Stage2[Stage2[\"MEMPOOL\"] = 2] = \"MEMPOOL\";\n  Stage2[Stage2[\"NETWORK\"] = 3] = \"NETWORK\";\n  Stage2[Stage2[\"CONFIRMED\"] = 4] = \"CONFIRMED\";\n  return Stage2;\n})(Stage || {});\n_bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.setEnumType(Stage, \"utxorpc.v1alpha.submit.Stage\", [\n  { no: 0, name: \"STAGE_UNSPECIFIED\" },\n  { no: 1, name: \"STAGE_ACKNOWLEDGED\" },\n  { no: 2, name: \"STAGE_MEMPOOL\" },\n  { no: 3, name: \"STAGE_NETWORK\" },\n  { no: 4, name: \"STAGE_CONFIRMED\" }\n]);\nvar _AnyChainTx = class _AnyChainTx extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from oneof utxorpc.v1alpha.submit.AnyChainTx.type\n     */\n    this.type = { case: void 0 };\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _AnyChainTx().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _AnyChainTx().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _AnyChainTx().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_AnyChainTx, a, b);\n  }\n};\n_AnyChainTx.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_AnyChainTx.typeName = \"utxorpc.v1alpha.submit.AnyChainTx\";\n_AnyChainTx.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"raw\", kind: \"scalar\", T: 12, oneof: \"type\" }\n]);\nvar AnyChainTx = _AnyChainTx;\nvar _EvalTxRequest = class _EvalTxRequest extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * List of transactions to evaluate.\n     *\n     * @generated from field: repeated utxorpc.v1alpha.submit.AnyChainTx tx = 1;\n     */\n    this.tx = [];\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _EvalTxRequest().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _EvalTxRequest().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _EvalTxRequest().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_EvalTxRequest, a, b);\n  }\n};\n_EvalTxRequest.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_EvalTxRequest.typeName = \"utxorpc.v1alpha.submit.EvalTxRequest\";\n_EvalTxRequest.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"tx\", kind: \"message\", T: AnyChainTx, repeated: true }\n]);\nvar EvalTxRequest = _EvalTxRequest;\nvar _AnyChainEval = class _AnyChainEval extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from oneof utxorpc.v1alpha.submit.AnyChainEval.chain\n     */\n    this.chain = { case: void 0 };\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _AnyChainEval().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _AnyChainEval().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _AnyChainEval().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_AnyChainEval, a, b);\n  }\n};\n_AnyChainEval.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_AnyChainEval.typeName = \"utxorpc.v1alpha.submit.AnyChainEval\";\n_AnyChainEval.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"cardano\", kind: \"message\", T: TxEval, oneof: \"chain\" }\n]);\nvar AnyChainEval = _AnyChainEval;\nvar _EvalTxResponse = class _EvalTxResponse extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from field: repeated utxorpc.v1alpha.submit.AnyChainEval report = 1;\n     */\n    this.report = [];\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _EvalTxResponse().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _EvalTxResponse().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _EvalTxResponse().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_EvalTxResponse, a, b);\n  }\n};\n_EvalTxResponse.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_EvalTxResponse.typeName = \"utxorpc.v1alpha.submit.EvalTxResponse\";\n_EvalTxResponse.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"report\", kind: \"message\", T: AnyChainEval, repeated: true }\n]);\nvar EvalTxResponse = _EvalTxResponse;\nvar _SubmitTxRequest = class _SubmitTxRequest extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * List of transactions to submit.\n     *\n     * @generated from field: repeated utxorpc.v1alpha.submit.AnyChainTx tx = 1;\n     */\n    this.tx = [];\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _SubmitTxRequest().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _SubmitTxRequest().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _SubmitTxRequest().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_SubmitTxRequest, a, b);\n  }\n};\n_SubmitTxRequest.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_SubmitTxRequest.typeName = \"utxorpc.v1alpha.submit.SubmitTxRequest\";\n_SubmitTxRequest.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"tx\", kind: \"message\", T: AnyChainTx, repeated: true }\n]);\nvar SubmitTxRequest = _SubmitTxRequest;\nvar _SubmitTxResponse = class _SubmitTxResponse extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * List of transaction references.\n     *\n     * @generated from field: repeated bytes ref = 1;\n     */\n    this.ref = [];\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _SubmitTxResponse().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _SubmitTxResponse().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _SubmitTxResponse().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_SubmitTxResponse, a, b);\n  }\n};\n_SubmitTxResponse.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_SubmitTxResponse.typeName = \"utxorpc.v1alpha.submit.SubmitTxResponse\";\n_SubmitTxResponse.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"ref\", kind: \"scalar\", T: 12, repeated: true }\n]);\nvar SubmitTxResponse = _SubmitTxResponse;\nvar _TxInMempool = class _TxInMempool extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * The transaction reference.\n     *\n     * @generated from field: bytes ref = 1;\n     */\n    this.ref = new Uint8Array(0);\n    /**\n     * Original bytes as defined by the chain\n     *\n     * @generated from field: bytes native_bytes = 2;\n     */\n    this.nativeBytes = new Uint8Array(0);\n    /**\n     * The current stage of the tx\n     *\n     * @generated from field: utxorpc.v1alpha.submit.Stage stage = 3;\n     */\n    this.stage = 0 /* UNSPECIFIED */;\n    /**\n     * @generated from oneof utxorpc.v1alpha.submit.TxInMempool.parsed_state\n     */\n    this.parsedState = { case: void 0 };\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _TxInMempool().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _TxInMempool().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _TxInMempool().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_TxInMempool, a, b);\n  }\n};\n_TxInMempool.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_TxInMempool.typeName = \"utxorpc.v1alpha.submit.TxInMempool\";\n_TxInMempool.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  {\n    no: 1,\n    name: \"ref\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  },\n  {\n    no: 2,\n    name: \"native_bytes\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  },\n  { no: 3, name: \"stage\", kind: \"enum\", T: _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.getEnumType(Stage) },\n  { no: 4, name: \"cardano\", kind: \"message\", T: Tx, oneof: \"parsed_state\" }\n]);\nvar TxInMempool = _TxInMempool;\nvar _ReadMempoolRequest = class _ReadMempoolRequest extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _ReadMempoolRequest().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _ReadMempoolRequest().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _ReadMempoolRequest().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_ReadMempoolRequest, a, b);\n  }\n};\n_ReadMempoolRequest.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_ReadMempoolRequest.typeName = \"utxorpc.v1alpha.submit.ReadMempoolRequest\";\n_ReadMempoolRequest.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => []);\nvar ReadMempoolRequest = _ReadMempoolRequest;\nvar _ReadMempoolResponse = class _ReadMempoolResponse extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * List of transaction currently on the mempool.\n     *\n     * @generated from field: repeated utxorpc.v1alpha.submit.TxInMempool items = 1;\n     */\n    this.items = [];\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _ReadMempoolResponse().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _ReadMempoolResponse().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _ReadMempoolResponse().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_ReadMempoolResponse, a, b);\n  }\n};\n_ReadMempoolResponse.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_ReadMempoolResponse.typeName = \"utxorpc.v1alpha.submit.ReadMempoolResponse\";\n_ReadMempoolResponse.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"items\", kind: \"message\", T: TxInMempool, repeated: true }\n]);\nvar ReadMempoolResponse = _ReadMempoolResponse;\nvar _WaitForTxRequest = class _WaitForTxRequest extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * List of transaction references to wait for.\n     *\n     * @generated from field: repeated bytes ref = 1;\n     */\n    this.ref = [];\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _WaitForTxRequest().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _WaitForTxRequest().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _WaitForTxRequest().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_WaitForTxRequest, a, b);\n  }\n};\n_WaitForTxRequest.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_WaitForTxRequest.typeName = \"utxorpc.v1alpha.submit.WaitForTxRequest\";\n_WaitForTxRequest.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"ref\", kind: \"scalar\", T: 12, repeated: true }\n]);\nvar WaitForTxRequest = _WaitForTxRequest;\nvar _WaitForTxResponse = class _WaitForTxResponse extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * Transaction reference.\n     *\n     * @generated from field: bytes ref = 1;\n     */\n    this.ref = new Uint8Array(0);\n    /**\n     * Stage reached by the transaction.\n     *\n     * @generated from field: utxorpc.v1alpha.submit.Stage stage = 2;\n     */\n    this.stage = 0 /* UNSPECIFIED */;\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _WaitForTxResponse().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _WaitForTxResponse().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _WaitForTxResponse().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_WaitForTxResponse, a, b);\n  }\n};\n_WaitForTxResponse.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_WaitForTxResponse.typeName = \"utxorpc.v1alpha.submit.WaitForTxResponse\";\n_WaitForTxResponse.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  {\n    no: 1,\n    name: \"ref\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  },\n  { no: 2, name: \"stage\", kind: \"enum\", T: _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.getEnumType(Stage) }\n]);\nvar WaitForTxResponse = _WaitForTxResponse;\nvar _AnyChainTxPattern = class _AnyChainTxPattern extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from oneof utxorpc.v1alpha.submit.AnyChainTxPattern.chain\n     */\n    this.chain = { case: void 0 };\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _AnyChainTxPattern().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _AnyChainTxPattern().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _AnyChainTxPattern().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_AnyChainTxPattern, a, b);\n  }\n};\n_AnyChainTxPattern.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_AnyChainTxPattern.typeName = \"utxorpc.v1alpha.submit.AnyChainTxPattern\";\n_AnyChainTxPattern.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"cardano\", kind: \"message\", T: TxPattern, oneof: \"chain\" }\n]);\nvar AnyChainTxPattern = _AnyChainTxPattern;\nvar _TxPredicate = class _TxPredicate extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * Predicate is true if tx doesn't exhibit pattern.\n     *\n     * @generated from field: repeated utxorpc.v1alpha.submit.TxPredicate not = 2;\n     */\n    this.not = [];\n    /**\n     * Predicate is true if tx exhibits all of the patterns.\n     *\n     * @generated from field: repeated utxorpc.v1alpha.submit.TxPredicate all_of = 3;\n     */\n    this.allOf = [];\n    /**\n     * Predicate is true if tx exhibits any of the patterns.\n     *\n     * @generated from field: repeated utxorpc.v1alpha.submit.TxPredicate any_of = 4;\n     */\n    this.anyOf = [];\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _TxPredicate().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _TxPredicate().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _TxPredicate().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_TxPredicate, a, b);\n  }\n};\n_TxPredicate.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_TxPredicate.typeName = \"utxorpc.v1alpha.submit.TxPredicate\";\n_TxPredicate.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"match\", kind: \"message\", T: AnyChainTxPattern },\n  { no: 2, name: \"not\", kind: \"message\", T: _TxPredicate, repeated: true },\n  { no: 3, name: \"all_of\", kind: \"message\", T: _TxPredicate, repeated: true },\n  { no: 4, name: \"any_of\", kind: \"message\", T: _TxPredicate, repeated: true }\n]);\nvar TxPredicate = _TxPredicate;\nvar _WatchMempoolRequest = class _WatchMempoolRequest extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _WatchMempoolRequest().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _WatchMempoolRequest().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _WatchMempoolRequest().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_WatchMempoolRequest, a, b);\n  }\n};\n_WatchMempoolRequest.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_WatchMempoolRequest.typeName = \"utxorpc.v1alpha.submit.WatchMempoolRequest\";\n_WatchMempoolRequest.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"predicate\", kind: \"message\", T: TxPredicate },\n  { no: 2, name: \"field_mask\", kind: \"message\", T: _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_3__.FieldMask }\n]);\nvar WatchMempoolRequest = _WatchMempoolRequest;\nvar _WatchMempoolResponse = class _WatchMempoolResponse extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _WatchMempoolResponse().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _WatchMempoolResponse().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _WatchMempoolResponse().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_WatchMempoolResponse, a, b);\n  }\n};\n_WatchMempoolResponse.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_WatchMempoolResponse.typeName = \"utxorpc.v1alpha.submit.WatchMempoolResponse\";\n_WatchMempoolResponse.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"tx\", kind: \"message\", T: TxInMempool }\n]);\nvar WatchMempoolResponse = _WatchMempoolResponse;\n\n// src/utxorpc/v1alpha/submit/submit_connect.ts\nvar submit_connect_exports = {};\n__export(submit_connect_exports, {\n  SubmitService: () => SubmitService\n});\n\nvar SubmitService = {\n  typeName: \"utxorpc.v1alpha.submit.SubmitService\",\n  methods: {\n    /**\n     * Evaluates a transaction without submitting it.\n     *\n     * @generated from rpc utxorpc.v1alpha.submit.SubmitService.EvalTx\n     */\n    evalTx: {\n      name: \"EvalTx\",\n      I: EvalTxRequest,\n      O: EvalTxResponse,\n      kind: _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_4__.MethodKind.Unary\n    },\n    /**\n     * Submit transactions to the blockchain.\n     *\n     * @generated from rpc utxorpc.v1alpha.submit.SubmitService.SubmitTx\n     */\n    submitTx: {\n      name: \"SubmitTx\",\n      I: SubmitTxRequest,\n      O: SubmitTxResponse,\n      kind: _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_4__.MethodKind.Unary\n    },\n    /**\n     * Wait for transactions to reach a certain stage and stream the updates.\n     *\n     * @generated from rpc utxorpc.v1alpha.submit.SubmitService.WaitForTx\n     */\n    waitForTx: {\n      name: \"WaitForTx\",\n      I: WaitForTxRequest,\n      O: WaitForTxResponse,\n      kind: _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_4__.MethodKind.ServerStreaming\n    },\n    /**\n     * Returns a point-in-time snapshot of the mempool.\n     *\n     * @generated from rpc utxorpc.v1alpha.submit.SubmitService.ReadMempool\n     */\n    readMempool: {\n      name: \"ReadMempool\",\n      I: ReadMempoolRequest,\n      O: ReadMempoolResponse,\n      kind: _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_4__.MethodKind.Unary\n    },\n    /**\n     * Stream transactions from the mempool matching the specified predicates.\n     *\n     * @generated from rpc utxorpc.v1alpha.submit.SubmitService.WatchMempool\n     */\n    watchMempool: {\n      name: \"WatchMempool\",\n      I: WatchMempoolRequest,\n      O: WatchMempoolResponse,\n      kind: _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_4__.MethodKind.ServerStreaming\n    }\n  }\n};\n\n// src/utxorpc/v1alpha/sync/sync_pb.ts\nvar sync_pb_exports = {};\n__export(sync_pb_exports, {\n  AnyChainBlock: () => AnyChainBlock,\n  BlockRef: () => BlockRef,\n  DumpHistoryRequest: () => DumpHistoryRequest,\n  DumpHistoryResponse: () => DumpHistoryResponse,\n  FetchBlockRequest: () => FetchBlockRequest,\n  FetchBlockResponse: () => FetchBlockResponse,\n  FollowTipRequest: () => FollowTipRequest,\n  FollowTipResponse: () => FollowTipResponse\n});\n\nvar _BlockRef = class _BlockRef extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * Height or slot number (depending on the blockchain)\n     *\n     * @generated from field: uint64 index = 1;\n     */\n    this.index = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    /**\n     * Hash of the content of the block\n     *\n     * @generated from field: bytes hash = 2;\n     */\n    this.hash = new Uint8Array(0);\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _BlockRef().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _BlockRef().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _BlockRef().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_BlockRef, a, b);\n  }\n};\n_BlockRef.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_BlockRef.typeName = \"utxorpc.v1alpha.sync.BlockRef\";\n_BlockRef.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  {\n    no: 1,\n    name: \"index\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  },\n  {\n    no: 2,\n    name: \"hash\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  }\n]);\nvar BlockRef = _BlockRef;\nvar _AnyChainBlock = class _AnyChainBlock extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * Original bytes as defined by the chain\n     *\n     * @generated from field: bytes native_bytes = 1;\n     */\n    this.nativeBytes = new Uint8Array(0);\n    /**\n     * @generated from oneof utxorpc.v1alpha.sync.AnyChainBlock.chain\n     */\n    this.chain = { case: void 0 };\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _AnyChainBlock().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _AnyChainBlock().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _AnyChainBlock().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_AnyChainBlock, a, b);\n  }\n};\n_AnyChainBlock.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_AnyChainBlock.typeName = \"utxorpc.v1alpha.sync.AnyChainBlock\";\n_AnyChainBlock.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  {\n    no: 1,\n    name: \"native_bytes\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  },\n  { no: 2, name: \"cardano\", kind: \"message\", T: Block, oneof: \"chain\" }\n]);\nvar AnyChainBlock = _AnyChainBlock;\nvar _FetchBlockRequest = class _FetchBlockRequest extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * List of block references.\n     *\n     * @generated from field: repeated utxorpc.v1alpha.sync.BlockRef ref = 1;\n     */\n    this.ref = [];\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _FetchBlockRequest().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _FetchBlockRequest().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _FetchBlockRequest().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_FetchBlockRequest, a, b);\n  }\n};\n_FetchBlockRequest.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_FetchBlockRequest.typeName = \"utxorpc.v1alpha.sync.FetchBlockRequest\";\n_FetchBlockRequest.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"ref\", kind: \"message\", T: BlockRef, repeated: true },\n  { no: 2, name: \"field_mask\", kind: \"message\", T: _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_3__.FieldMask }\n]);\nvar FetchBlockRequest = _FetchBlockRequest;\nvar _FetchBlockResponse = class _FetchBlockResponse extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * List of fetched blocks.\n     *\n     * @generated from field: repeated utxorpc.v1alpha.sync.AnyChainBlock block = 1;\n     */\n    this.block = [];\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _FetchBlockResponse().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _FetchBlockResponse().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _FetchBlockResponse().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_FetchBlockResponse, a, b);\n  }\n};\n_FetchBlockResponse.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_FetchBlockResponse.typeName = \"utxorpc.v1alpha.sync.FetchBlockResponse\";\n_FetchBlockResponse.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"block\", kind: \"message\", T: AnyChainBlock, repeated: true }\n]);\nvar FetchBlockResponse = _FetchBlockResponse;\nvar _DumpHistoryRequest = class _DumpHistoryRequest extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * Maximum number of items to return.\n     *\n     * @generated from field: uint32 max_items = 3;\n     */\n    this.maxItems = 0;\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _DumpHistoryRequest().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _DumpHistoryRequest().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _DumpHistoryRequest().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_DumpHistoryRequest, a, b);\n  }\n};\n_DumpHistoryRequest.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_DumpHistoryRequest.typeName = \"utxorpc.v1alpha.sync.DumpHistoryRequest\";\n_DumpHistoryRequest.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 2, name: \"start_token\", kind: \"message\", T: BlockRef },\n  {\n    no: 3,\n    name: \"max_items\",\n    kind: \"scalar\",\n    T: 13\n    /* ScalarType.UINT32 */\n  },\n  { no: 4, name: \"field_mask\", kind: \"message\", T: _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_3__.FieldMask }\n]);\nvar DumpHistoryRequest = _DumpHistoryRequest;\nvar _DumpHistoryResponse = class _DumpHistoryResponse extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * List of blocks in the history.\n     *\n     * @generated from field: repeated utxorpc.v1alpha.sync.AnyChainBlock block = 1;\n     */\n    this.block = [];\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _DumpHistoryResponse().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _DumpHistoryResponse().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _DumpHistoryResponse().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_DumpHistoryResponse, a, b);\n  }\n};\n_DumpHistoryResponse.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_DumpHistoryResponse.typeName = \"utxorpc.v1alpha.sync.DumpHistoryResponse\";\n_DumpHistoryResponse.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"block\", kind: \"message\", T: AnyChainBlock, repeated: true },\n  { no: 2, name: \"next_token\", kind: \"message\", T: BlockRef }\n]);\nvar DumpHistoryResponse = _DumpHistoryResponse;\nvar _FollowTipRequest = class _FollowTipRequest extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * List of block references to find the intersection.\n     *\n     * @generated from field: repeated utxorpc.v1alpha.sync.BlockRef intersect = 1;\n     */\n    this.intersect = [];\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _FollowTipRequest().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _FollowTipRequest().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _FollowTipRequest().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_FollowTipRequest, a, b);\n  }\n};\n_FollowTipRequest.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_FollowTipRequest.typeName = \"utxorpc.v1alpha.sync.FollowTipRequest\";\n_FollowTipRequest.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"intersect\", kind: \"message\", T: BlockRef, repeated: true },\n  { no: 2, name: \"field_mask\", kind: \"message\", T: _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_3__.FieldMask }\n]);\nvar FollowTipRequest = _FollowTipRequest;\nvar _FollowTipResponse = class _FollowTipResponse extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from oneof utxorpc.v1alpha.sync.FollowTipResponse.action\n     */\n    this.action = { case: void 0 };\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _FollowTipResponse().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _FollowTipResponse().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _FollowTipResponse().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_FollowTipResponse, a, b);\n  }\n};\n_FollowTipResponse.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_FollowTipResponse.typeName = \"utxorpc.v1alpha.sync.FollowTipResponse\";\n_FollowTipResponse.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"apply\", kind: \"message\", T: AnyChainBlock, oneof: \"action\" },\n  { no: 2, name: \"undo\", kind: \"message\", T: AnyChainBlock, oneof: \"action\" },\n  { no: 3, name: \"reset\", kind: \"message\", T: BlockRef, oneof: \"action\" }\n]);\nvar FollowTipResponse = _FollowTipResponse;\n\n// src/utxorpc/v1alpha/sync/sync_connect.ts\nvar sync_connect_exports = {};\n__export(sync_connect_exports, {\n  SyncService: () => SyncService\n});\n\nvar SyncService = {\n  typeName: \"utxorpc.v1alpha.sync.SyncService\",\n  methods: {\n    /**\n     * Fetch a block by its reference.\n     *\n     * @generated from rpc utxorpc.v1alpha.sync.SyncService.FetchBlock\n     */\n    fetchBlock: {\n      name: \"FetchBlock\",\n      I: FetchBlockRequest,\n      O: FetchBlockResponse,\n      kind: _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_4__.MethodKind.Unary\n    },\n    /**\n     * Dump the block history.\n     *\n     * @generated from rpc utxorpc.v1alpha.sync.SyncService.DumpHistory\n     */\n    dumpHistory: {\n      name: \"DumpHistory\",\n      I: DumpHistoryRequest,\n      O: DumpHistoryResponse,\n      kind: _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_4__.MethodKind.Unary\n    },\n    /**\n     * Follow the tip of the blockchain.\n     *\n     * @generated from rpc utxorpc.v1alpha.sync.SyncService.FollowTip\n     */\n    followTip: {\n      name: \"FollowTip\",\n      I: FollowTipRequest,\n      O: FollowTipResponse,\n      kind: _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_4__.MethodKind.ServerStreaming\n    }\n  }\n};\n\n// src/utxorpc/v1alpha/watch/watch_pb.ts\nvar watch_pb_exports = {};\n__export(watch_pb_exports, {\n  AnyChainTx: () => AnyChainTx2,\n  AnyChainTxPattern: () => AnyChainTxPattern2,\n  BlockRef: () => BlockRef2,\n  TxPredicate: () => TxPredicate2,\n  WatchTxRequest: () => WatchTxRequest,\n  WatchTxResponse: () => WatchTxResponse\n});\n\nvar _BlockRef2 = class _BlockRef2 extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * Height or slot number (depending on the blockchain)\n     *\n     * @generated from field: uint64 index = 1;\n     */\n    this.index = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_2__.protoInt64.zero;\n    /**\n     * Hash of the content of the block\n     *\n     * @generated from field: bytes hash = 2;\n     */\n    this.hash = new Uint8Array(0);\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _BlockRef2().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _BlockRef2().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _BlockRef2().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_BlockRef2, a, b);\n  }\n};\n_BlockRef2.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_BlockRef2.typeName = \"utxorpc.v1alpha.watch.BlockRef\";\n_BlockRef2.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  {\n    no: 1,\n    name: \"index\",\n    kind: \"scalar\",\n    T: 4\n    /* ScalarType.UINT64 */\n  },\n  {\n    no: 2,\n    name: \"hash\",\n    kind: \"scalar\",\n    T: 12\n    /* ScalarType.BYTES */\n  }\n]);\nvar BlockRef2 = _BlockRef2;\nvar _AnyChainTxPattern2 = class _AnyChainTxPattern2 extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from oneof utxorpc.v1alpha.watch.AnyChainTxPattern.chain\n     */\n    this.chain = { case: void 0 };\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _AnyChainTxPattern2().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _AnyChainTxPattern2().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _AnyChainTxPattern2().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_AnyChainTxPattern2, a, b);\n  }\n};\n_AnyChainTxPattern2.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_AnyChainTxPattern2.typeName = \"utxorpc.v1alpha.watch.AnyChainTxPattern\";\n_AnyChainTxPattern2.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"cardano\", kind: \"message\", T: TxPattern, oneof: \"chain\" }\n]);\nvar AnyChainTxPattern2 = _AnyChainTxPattern2;\nvar _TxPredicate2 = class _TxPredicate2 extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * Predicate is true if tx doesn't exhibit pattern.\n     *\n     * @generated from field: repeated utxorpc.v1alpha.watch.TxPredicate not = 2;\n     */\n    this.not = [];\n    /**\n     * Predicate is true if tx exhibits all of the patterns.\n     *\n     * @generated from field: repeated utxorpc.v1alpha.watch.TxPredicate all_of = 3;\n     */\n    this.allOf = [];\n    /**\n     * Predicate is true if tx exhibits any of the patterns.\n     *\n     * @generated from field: repeated utxorpc.v1alpha.watch.TxPredicate any_of = 4;\n     */\n    this.anyOf = [];\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _TxPredicate2().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _TxPredicate2().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _TxPredicate2().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_TxPredicate2, a, b);\n  }\n};\n_TxPredicate2.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_TxPredicate2.typeName = \"utxorpc.v1alpha.watch.TxPredicate\";\n_TxPredicate2.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"match\", kind: \"message\", T: AnyChainTxPattern2 },\n  { no: 2, name: \"not\", kind: \"message\", T: _TxPredicate2, repeated: true },\n  { no: 3, name: \"all_of\", kind: \"message\", T: _TxPredicate2, repeated: true },\n  { no: 4, name: \"any_of\", kind: \"message\", T: _TxPredicate2, repeated: true }\n]);\nvar TxPredicate2 = _TxPredicate2;\nvar _WatchTxRequest = class _WatchTxRequest extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * List of block references to find the intersection.\n     *\n     * @generated from field: repeated utxorpc.v1alpha.watch.BlockRef intersect = 3;\n     */\n    this.intersect = [];\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _WatchTxRequest().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _WatchTxRequest().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _WatchTxRequest().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_WatchTxRequest, a, b);\n  }\n};\n_WatchTxRequest.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_WatchTxRequest.typeName = \"utxorpc.v1alpha.watch.WatchTxRequest\";\n_WatchTxRequest.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"predicate\", kind: \"message\", T: TxPredicate2 },\n  { no: 2, name: \"field_mask\", kind: \"message\", T: _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_3__.FieldMask },\n  { no: 3, name: \"intersect\", kind: \"message\", T: BlockRef2, repeated: true }\n]);\nvar WatchTxRequest = _WatchTxRequest;\nvar _AnyChainTx2 = class _AnyChainTx2 extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from oneof utxorpc.v1alpha.watch.AnyChainTx.chain\n     */\n    this.chain = { case: void 0 };\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _AnyChainTx2().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _AnyChainTx2().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _AnyChainTx2().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_AnyChainTx2, a, b);\n  }\n};\n_AnyChainTx2.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_AnyChainTx2.typeName = \"utxorpc.v1alpha.watch.AnyChainTx\";\n_AnyChainTx2.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"cardano\", kind: \"message\", T: Tx, oneof: \"chain\" }\n]);\nvar AnyChainTx2 = _AnyChainTx2;\nvar _WatchTxResponse = class _WatchTxResponse extends _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_1__.Message {\n  constructor(data) {\n    super();\n    /**\n     * @generated from oneof utxorpc.v1alpha.watch.WatchTxResponse.action\n     */\n    this.action = { case: void 0 };\n    _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.initPartial(data, this);\n  }\n  static fromBinary(bytes, options) {\n    return new _WatchTxResponse().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new _WatchTxResponse().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new _WatchTxResponse().fromJsonString(jsonString, options);\n  }\n  static equals(a, b) {\n    return _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.equals(_WatchTxResponse, a, b);\n  }\n};\n_WatchTxResponse.runtime = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3;\n_WatchTxResponse.typeName = \"utxorpc.v1alpha.watch.WatchTxResponse\";\n_WatchTxResponse.fields = _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_0__.proto3.util.newFieldList(() => [\n  { no: 1, name: \"apply\", kind: \"message\", T: AnyChainTx2, oneof: \"action\" },\n  { no: 2, name: \"undo\", kind: \"message\", T: AnyChainTx2, oneof: \"action\" }\n]);\nvar WatchTxResponse = _WatchTxResponse;\n\n// src/utxorpc/v1alpha/watch/watch_connect.ts\nvar watch_connect_exports = {};\n__export(watch_connect_exports, {\n  WatchService: () => WatchService\n});\n\nvar WatchService = {\n  typeName: \"utxorpc.v1alpha.watch.WatchService\",\n  methods: {\n    /**\n     * Stream transactions from the chain matching the specified predicates.\n     *\n     * @generated from rpc utxorpc.v1alpha.watch.WatchService.WatchTx\n     */\n    watchTx: {\n      name: \"WatchTx\",\n      I: WatchTxRequest,\n      O: WatchTxResponse,\n      kind: _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_4__.MethodKind.ServerStreaming\n    }\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@utxorpc/spec/lib/index.mjs\n");

/***/ })

};
;