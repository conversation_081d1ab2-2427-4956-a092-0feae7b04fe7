import * as Crypto from '@cardano-sdk/crypto';
import { Cardano } from '../..';
import { HydratedTxBody, Lovelace } from '../types';
export interface ImplicitCoin {
    withdrawals?: Lovelace;
    input?: Lovelace;
    deposit?: Lovelace;
    reclaimDeposit?: Lovelace;
}
export declare const computeImplicitCoin: ({ stakeKeyDeposit, poolDeposit }: Pick<Cardano.ProtocolParameters, 'stakeKeyDeposit' | 'poolDeposit'>, { certificates, proposalProcedures, withdrawals }: Pick<HydratedTxBody, 'certificates' | 'proposalProcedures' | 'withdrawals'>, rewardAccounts?: Cardano.RewardAccount[], dRepKeyHash?: Crypto.Ed25519KeyHashHex) => ImplicitCoin;
//# sourceMappingURL=computeImplicitCoin.d.ts.map