import { OpaqueString } from '@cardano-sdk/util';
import { Metadatum } from '../../Cardano';
export declare type Uri = OpaqueString<'Uri'>;
export declare const Uri: (uri: string) => Uri;
export declare type ImageMediaType = OpaqueString<'ImageMediaType'>;
export declare const ImageMediaType: (mediaType: string) => ImageMediaType;
export declare type MediaType = OpaqueString<'MediaType'>;
export declare const MediaType: (mediaType: string) => MediaType;
export interface NftMetadataFile {
    name?: string;
    mediaType: MediaType;
    src: Uri;
    otherProperties?: Map<string, Metadatum>;
}
export interface NftMetadata {
    name: string;
    image: Uri;
    version: string;
    mediaType?: ImageMediaType;
    files?: NftMetadataFile[];
    description?: string;
    otherProperties?: Map<string, Metadatum>;
}
//# sourceMappingURL=types.d.ts.map