import { Constitution } from './Constitution.js';
import { GovernanceActionId } from '../../Common/GovernanceActionId.js';
import { HexBlob } from '@cardano-sdk/util';
import type * as Cardano from '../../../Cardano/index.js';
export declare class NewConstitution {
    #private;
    constructor(constitution: Constitution, govActionId?: GovernanceActionId);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): NewConstitution;
    toCore(): Cardano.NewConstitution;
    static fromCore(newConstitution: Cardano.NewConstitution): NewConstitution;
    govActionId(): GovernanceActionId | undefined;
    constitution(): Constitution;
}
//# sourceMappingURL=NewConstitution.d.ts.map