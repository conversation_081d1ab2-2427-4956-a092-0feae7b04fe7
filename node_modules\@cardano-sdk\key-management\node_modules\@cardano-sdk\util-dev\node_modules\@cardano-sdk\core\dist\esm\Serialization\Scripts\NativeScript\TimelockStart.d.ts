import { HexBlob } from '@cardano-sdk/util';
import type * as Cardano from '../../../Cardano/index.js';
export declare class TimelockStart {
    #private;
    constructor(slot: Cardano.Slot);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): TimelockStart;
    toCore(): Cardano.RequireTimeAfterScript;
    static fromCore(script: Cardano.RequireTimeAfterScript): TimelockStart;
    slot(): Cardano.Slot;
    setSlot(slot: Cardano.Slot): void;
}
//# sourceMappingURL=TimelockStart.d.ts.map