import { AssetInfo } from '../types';
import { Cardano } from '../..';
import { NftMetadata } from './types';
import { Logger } from 'ts-log';
declare type AssetIdParts = Pick<AssetInfo, 'policyId' | 'name'>;
export declare const fromMetadatum: (asset: AssetIdParts, metadata: Cardano.TxMetadata | undefined, logger: Logger, strict?: boolean) => NftMetadata | null;
export {};
//# sourceMappingURL=fromMetadatum.d.ts.map