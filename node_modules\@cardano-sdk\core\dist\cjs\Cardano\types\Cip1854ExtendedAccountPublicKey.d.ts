import { Bip32PublicKeyHex } from '@cardano-sdk/crypto';
import { OpaqueString } from '@cardano-sdk/util';
export declare type Cip1854ExtendedAccountPublicKey = OpaqueString<'Cip1854PublicKey'>;
export declare const Cip1854ExtendedAccountPublicKey: {
    (value: string): Cip1854ExtendedAccountPublicKey;
    fromBip32PublicKeyHex(value: Bip32PublicKeyHex): Cip1854ExtendedAccountPublicKey;
    toBip32PublicKeyHex(value: Cip1854ExtendedAccountPublicKey): Bip32PublicKeyHex;
};
//# sourceMappingURL=Cip1854ExtendedAccountPublicKey.d.ts.map