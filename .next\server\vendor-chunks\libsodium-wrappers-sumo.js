/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/libsodium-wrappers-sumo";
exports.ids = ["vendor-chunks/libsodium-wrappers-sumo"];
exports.modules = {

/***/ "(ssr)/./node_modules/libsodium-wrappers-sumo/dist/modules-sumo/libsodium-wrappers.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/libsodium-wrappers-sumo/dist/modules-sumo/libsodium-wrappers.js ***!
  \**************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval("var __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;!function(e){function a(e,a){\"use strict\";var r,t=\"uint8array\",_=a.ready.then((function(){function t(){if(0!==r._sodium_init())throw new Error(\"libsodium was not correctly initialized.\");for(var a=[\"crypto_aead_aegis128l_decrypt\",\"crypto_aead_aegis128l_decrypt_detached\",\"crypto_aead_aegis128l_encrypt\",\"crypto_aead_aegis128l_encrypt_detached\",\"crypto_aead_aegis128l_keygen\",\"crypto_aead_aegis256_decrypt\",\"crypto_aead_aegis256_decrypt_detached\",\"crypto_aead_aegis256_encrypt\",\"crypto_aead_aegis256_encrypt_detached\",\"crypto_aead_aegis256_keygen\",\"crypto_aead_chacha20poly1305_decrypt\",\"crypto_aead_chacha20poly1305_decrypt_detached\",\"crypto_aead_chacha20poly1305_encrypt\",\"crypto_aead_chacha20poly1305_encrypt_detached\",\"crypto_aead_chacha20poly1305_ietf_decrypt\",\"crypto_aead_chacha20poly1305_ietf_decrypt_detached\",\"crypto_aead_chacha20poly1305_ietf_encrypt\",\"crypto_aead_chacha20poly1305_ietf_encrypt_detached\",\"crypto_aead_chacha20poly1305_ietf_keygen\",\"crypto_aead_chacha20poly1305_keygen\",\"crypto_aead_xchacha20poly1305_ietf_decrypt\",\"crypto_aead_xchacha20poly1305_ietf_decrypt_detached\",\"crypto_aead_xchacha20poly1305_ietf_encrypt\",\"crypto_aead_xchacha20poly1305_ietf_encrypt_detached\",\"crypto_aead_xchacha20poly1305_ietf_keygen\",\"crypto_auth\",\"crypto_auth_hmacsha256\",\"crypto_auth_hmacsha256_final\",\"crypto_auth_hmacsha256_init\",\"crypto_auth_hmacsha256_keygen\",\"crypto_auth_hmacsha256_update\",\"crypto_auth_hmacsha256_verify\",\"crypto_auth_hmacsha512\",\"crypto_auth_hmacsha512256\",\"crypto_auth_hmacsha512256_final\",\"crypto_auth_hmacsha512256_init\",\"crypto_auth_hmacsha512256_keygen\",\"crypto_auth_hmacsha512256_update\",\"crypto_auth_hmacsha512256_verify\",\"crypto_auth_hmacsha512_final\",\"crypto_auth_hmacsha512_init\",\"crypto_auth_hmacsha512_keygen\",\"crypto_auth_hmacsha512_update\",\"crypto_auth_hmacsha512_verify\",\"crypto_auth_keygen\",\"crypto_auth_verify\",\"crypto_box_beforenm\",\"crypto_box_curve25519xchacha20poly1305_beforenm\",\"crypto_box_curve25519xchacha20poly1305_detached\",\"crypto_box_curve25519xchacha20poly1305_detached_afternm\",\"crypto_box_curve25519xchacha20poly1305_easy\",\"crypto_box_curve25519xchacha20poly1305_easy_afternm\",\"crypto_box_curve25519xchacha20poly1305_keypair\",\"crypto_box_curve25519xchacha20poly1305_open_detached\",\"crypto_box_curve25519xchacha20poly1305_open_detached_afternm\",\"crypto_box_curve25519xchacha20poly1305_open_easy\",\"crypto_box_curve25519xchacha20poly1305_open_easy_afternm\",\"crypto_box_curve25519xchacha20poly1305_seal\",\"crypto_box_curve25519xchacha20poly1305_seal_open\",\"crypto_box_curve25519xchacha20poly1305_seed_keypair\",\"crypto_box_detached\",\"crypto_box_easy\",\"crypto_box_easy_afternm\",\"crypto_box_keypair\",\"crypto_box_open_detached\",\"crypto_box_open_easy\",\"crypto_box_open_easy_afternm\",\"crypto_box_seal\",\"crypto_box_seal_open\",\"crypto_box_seed_keypair\",\"crypto_core_ed25519_add\",\"crypto_core_ed25519_from_hash\",\"crypto_core_ed25519_from_uniform\",\"crypto_core_ed25519_is_valid_point\",\"crypto_core_ed25519_random\",\"crypto_core_ed25519_scalar_add\",\"crypto_core_ed25519_scalar_complement\",\"crypto_core_ed25519_scalar_invert\",\"crypto_core_ed25519_scalar_mul\",\"crypto_core_ed25519_scalar_negate\",\"crypto_core_ed25519_scalar_random\",\"crypto_core_ed25519_scalar_reduce\",\"crypto_core_ed25519_scalar_sub\",\"crypto_core_ed25519_sub\",\"crypto_core_hchacha20\",\"crypto_core_hsalsa20\",\"crypto_core_ristretto255_add\",\"crypto_core_ristretto255_from_hash\",\"crypto_core_ristretto255_is_valid_point\",\"crypto_core_ristretto255_random\",\"crypto_core_ristretto255_scalar_add\",\"crypto_core_ristretto255_scalar_complement\",\"crypto_core_ristretto255_scalar_invert\",\"crypto_core_ristretto255_scalar_mul\",\"crypto_core_ristretto255_scalar_negate\",\"crypto_core_ristretto255_scalar_random\",\"crypto_core_ristretto255_scalar_reduce\",\"crypto_core_ristretto255_scalar_sub\",\"crypto_core_ristretto255_sub\",\"crypto_generichash\",\"crypto_generichash_blake2b_salt_personal\",\"crypto_generichash_final\",\"crypto_generichash_init\",\"crypto_generichash_keygen\",\"crypto_generichash_update\",\"crypto_hash\",\"crypto_hash_sha256\",\"crypto_hash_sha256_final\",\"crypto_hash_sha256_init\",\"crypto_hash_sha256_update\",\"crypto_hash_sha512\",\"crypto_hash_sha512_final\",\"crypto_hash_sha512_init\",\"crypto_hash_sha512_update\",\"crypto_kdf_derive_from_key\",\"crypto_kdf_keygen\",\"crypto_kx_client_session_keys\",\"crypto_kx_keypair\",\"crypto_kx_seed_keypair\",\"crypto_kx_server_session_keys\",\"crypto_onetimeauth\",\"crypto_onetimeauth_final\",\"crypto_onetimeauth_init\",\"crypto_onetimeauth_keygen\",\"crypto_onetimeauth_update\",\"crypto_onetimeauth_verify\",\"crypto_pwhash\",\"crypto_pwhash_scryptsalsa208sha256\",\"crypto_pwhash_scryptsalsa208sha256_ll\",\"crypto_pwhash_scryptsalsa208sha256_str\",\"crypto_pwhash_scryptsalsa208sha256_str_verify\",\"crypto_pwhash_str\",\"crypto_pwhash_str_needs_rehash\",\"crypto_pwhash_str_verify\",\"crypto_scalarmult\",\"crypto_scalarmult_base\",\"crypto_scalarmult_ed25519\",\"crypto_scalarmult_ed25519_base\",\"crypto_scalarmult_ed25519_base_noclamp\",\"crypto_scalarmult_ed25519_noclamp\",\"crypto_scalarmult_ristretto255\",\"crypto_scalarmult_ristretto255_base\",\"crypto_secretbox_detached\",\"crypto_secretbox_easy\",\"crypto_secretbox_keygen\",\"crypto_secretbox_open_detached\",\"crypto_secretbox_open_easy\",\"crypto_secretstream_xchacha20poly1305_init_pull\",\"crypto_secretstream_xchacha20poly1305_init_push\",\"crypto_secretstream_xchacha20poly1305_keygen\",\"crypto_secretstream_xchacha20poly1305_pull\",\"crypto_secretstream_xchacha20poly1305_push\",\"crypto_secretstream_xchacha20poly1305_rekey\",\"crypto_shorthash\",\"crypto_shorthash_keygen\",\"crypto_shorthash_siphashx24\",\"crypto_sign\",\"crypto_sign_detached\",\"crypto_sign_ed25519_pk_to_curve25519\",\"crypto_sign_ed25519_sk_to_curve25519\",\"crypto_sign_ed25519_sk_to_pk\",\"crypto_sign_ed25519_sk_to_seed\",\"crypto_sign_final_create\",\"crypto_sign_final_verify\",\"crypto_sign_init\",\"crypto_sign_keypair\",\"crypto_sign_open\",\"crypto_sign_seed_keypair\",\"crypto_sign_update\",\"crypto_sign_verify_detached\",\"crypto_stream_chacha20\",\"crypto_stream_chacha20_ietf_xor\",\"crypto_stream_chacha20_ietf_xor_ic\",\"crypto_stream_chacha20_keygen\",\"crypto_stream_chacha20_xor\",\"crypto_stream_chacha20_xor_ic\",\"crypto_stream_keygen\",\"crypto_stream_xchacha20_keygen\",\"crypto_stream_xchacha20_xor\",\"crypto_stream_xchacha20_xor_ic\",\"randombytes_buf\",\"randombytes_buf_deterministic\",\"randombytes_close\",\"randombytes_random\",\"randombytes_set_implementation\",\"randombytes_stir\",\"randombytes_uniform\",\"sodium_version_string\"],t=[x,k,S,T,w,Y,B,A,M,I,K,N,L,O,U,C,P,R,X,G,D,F,V,H,W,q,j,z,J,Q,Z,$,ee,ae,re,te,_e,ne,se,ce,oe,he,pe,ye,ie,le,ue,de,ve,ge,be,fe,me,Ee,xe,ke,Se,Te,we,Ye,Be,Ae,Me,Ie,Ke,Ne,Le,Oe,Ue,Ce,Pe,Re,Xe,Ge,De,Fe,Ve,He,We,qe,je,ze,Je,Qe,Ze,$e,ea,aa,ra,ta,_a,na,sa,ca,oa,ha,pa,ya,ia,la,ua,da,va,ga,ba,fa,ma,Ea,xa,ka,Sa,Ta,wa,Ya,Ba,Aa,Ma,Ia,Ka,Na,La,Oa,Ua,Ca,Pa,Ra,Xa,Ga,Da,Fa,Va,Ha,Wa,qa,ja,za,Ja,Qa,Za,$a,er,ar,rr,tr,_r,nr,sr,cr,or,hr,pr,yr,ir,lr,ur,dr,vr,gr,br,fr,mr,Er,xr,kr,Sr,Tr,wr,Yr,Br,Ar,Mr,Ir,Kr,Nr,Lr,Or,Ur,Cr,Pr,Rr,Xr,Gr,Dr,Fr,Vr,Hr,Wr,qr],_=0;_<t.length;_++)\"function\"==typeof r[\"_\"+a[_]]&&(e[a[_]]=t[_]);var n=[\"SODIUM_LIBRARY_VERSION_MAJOR\",\"SODIUM_LIBRARY_VERSION_MINOR\",\"crypto_aead_aegis128l_ABYTES\",\"crypto_aead_aegis128l_KEYBYTES\",\"crypto_aead_aegis128l_MESSAGEBYTES_MAX\",\"crypto_aead_aegis128l_NPUBBYTES\",\"crypto_aead_aegis128l_NSECBYTES\",\"crypto_aead_aegis256_ABYTES\",\"crypto_aead_aegis256_KEYBYTES\",\"crypto_aead_aegis256_MESSAGEBYTES_MAX\",\"crypto_aead_aegis256_NPUBBYTES\",\"crypto_aead_aegis256_NSECBYTES\",\"crypto_aead_aes256gcm_ABYTES\",\"crypto_aead_aes256gcm_KEYBYTES\",\"crypto_aead_aes256gcm_MESSAGEBYTES_MAX\",\"crypto_aead_aes256gcm_NPUBBYTES\",\"crypto_aead_aes256gcm_NSECBYTES\",\"crypto_aead_chacha20poly1305_ABYTES\",\"crypto_aead_chacha20poly1305_IETF_ABYTES\",\"crypto_aead_chacha20poly1305_IETF_KEYBYTES\",\"crypto_aead_chacha20poly1305_IETF_MESSAGEBYTES_MAX\",\"crypto_aead_chacha20poly1305_IETF_NPUBBYTES\",\"crypto_aead_chacha20poly1305_IETF_NSECBYTES\",\"crypto_aead_chacha20poly1305_KEYBYTES\",\"crypto_aead_chacha20poly1305_MESSAGEBYTES_MAX\",\"crypto_aead_chacha20poly1305_NPUBBYTES\",\"crypto_aead_chacha20poly1305_NSECBYTES\",\"crypto_aead_chacha20poly1305_ietf_ABYTES\",\"crypto_aead_chacha20poly1305_ietf_KEYBYTES\",\"crypto_aead_chacha20poly1305_ietf_MESSAGEBYTES_MAX\",\"crypto_aead_chacha20poly1305_ietf_NPUBBYTES\",\"crypto_aead_chacha20poly1305_ietf_NSECBYTES\",\"crypto_aead_xchacha20poly1305_IETF_ABYTES\",\"crypto_aead_xchacha20poly1305_IETF_KEYBYTES\",\"crypto_aead_xchacha20poly1305_IETF_MESSAGEBYTES_MAX\",\"crypto_aead_xchacha20poly1305_IETF_NPUBBYTES\",\"crypto_aead_xchacha20poly1305_IETF_NSECBYTES\",\"crypto_aead_xchacha20poly1305_ietf_ABYTES\",\"crypto_aead_xchacha20poly1305_ietf_KEYBYTES\",\"crypto_aead_xchacha20poly1305_ietf_MESSAGEBYTES_MAX\",\"crypto_aead_xchacha20poly1305_ietf_NPUBBYTES\",\"crypto_aead_xchacha20poly1305_ietf_NSECBYTES\",\"crypto_auth_BYTES\",\"crypto_auth_KEYBYTES\",\"crypto_auth_hmacsha256_BYTES\",\"crypto_auth_hmacsha256_KEYBYTES\",\"crypto_auth_hmacsha512256_BYTES\",\"crypto_auth_hmacsha512256_KEYBYTES\",\"crypto_auth_hmacsha512_BYTES\",\"crypto_auth_hmacsha512_KEYBYTES\",\"crypto_box_BEFORENMBYTES\",\"crypto_box_MACBYTES\",\"crypto_box_MESSAGEBYTES_MAX\",\"crypto_box_NONCEBYTES\",\"crypto_box_PUBLICKEYBYTES\",\"crypto_box_SEALBYTES\",\"crypto_box_SECRETKEYBYTES\",\"crypto_box_SEEDBYTES\",\"crypto_box_curve25519xchacha20poly1305_BEFORENMBYTES\",\"crypto_box_curve25519xchacha20poly1305_MACBYTES\",\"crypto_box_curve25519xchacha20poly1305_MESSAGEBYTES_MAX\",\"crypto_box_curve25519xchacha20poly1305_NONCEBYTES\",\"crypto_box_curve25519xchacha20poly1305_PUBLICKEYBYTES\",\"crypto_box_curve25519xchacha20poly1305_SEALBYTES\",\"crypto_box_curve25519xchacha20poly1305_SECRETKEYBYTES\",\"crypto_box_curve25519xchacha20poly1305_SEEDBYTES\",\"crypto_box_curve25519xsalsa20poly1305_BEFORENMBYTES\",\"crypto_box_curve25519xsalsa20poly1305_MACBYTES\",\"crypto_box_curve25519xsalsa20poly1305_MESSAGEBYTES_MAX\",\"crypto_box_curve25519xsalsa20poly1305_NONCEBYTES\",\"crypto_box_curve25519xsalsa20poly1305_PUBLICKEYBYTES\",\"crypto_box_curve25519xsalsa20poly1305_SECRETKEYBYTES\",\"crypto_box_curve25519xsalsa20poly1305_SEEDBYTES\",\"crypto_core_ed25519_BYTES\",\"crypto_core_ed25519_HASHBYTES\",\"crypto_core_ed25519_NONREDUCEDSCALARBYTES\",\"crypto_core_ed25519_SCALARBYTES\",\"crypto_core_ed25519_UNIFORMBYTES\",\"crypto_core_hchacha20_CONSTBYTES\",\"crypto_core_hchacha20_INPUTBYTES\",\"crypto_core_hchacha20_KEYBYTES\",\"crypto_core_hchacha20_OUTPUTBYTES\",\"crypto_core_hsalsa20_CONSTBYTES\",\"crypto_core_hsalsa20_INPUTBYTES\",\"crypto_core_hsalsa20_KEYBYTES\",\"crypto_core_hsalsa20_OUTPUTBYTES\",\"crypto_core_ristretto255_BYTES\",\"crypto_core_ristretto255_HASHBYTES\",\"crypto_core_ristretto255_NONREDUCEDSCALARBYTES\",\"crypto_core_ristretto255_SCALARBYTES\",\"crypto_core_salsa2012_CONSTBYTES\",\"crypto_core_salsa2012_INPUTBYTES\",\"crypto_core_salsa2012_KEYBYTES\",\"crypto_core_salsa2012_OUTPUTBYTES\",\"crypto_core_salsa208_CONSTBYTES\",\"crypto_core_salsa208_INPUTBYTES\",\"crypto_core_salsa208_KEYBYTES\",\"crypto_core_salsa208_OUTPUTBYTES\",\"crypto_core_salsa20_CONSTBYTES\",\"crypto_core_salsa20_INPUTBYTES\",\"crypto_core_salsa20_KEYBYTES\",\"crypto_core_salsa20_OUTPUTBYTES\",\"crypto_generichash_BYTES\",\"crypto_generichash_BYTES_MAX\",\"crypto_generichash_BYTES_MIN\",\"crypto_generichash_KEYBYTES\",\"crypto_generichash_KEYBYTES_MAX\",\"crypto_generichash_KEYBYTES_MIN\",\"crypto_generichash_blake2b_BYTES\",\"crypto_generichash_blake2b_BYTES_MAX\",\"crypto_generichash_blake2b_BYTES_MIN\",\"crypto_generichash_blake2b_KEYBYTES\",\"crypto_generichash_blake2b_KEYBYTES_MAX\",\"crypto_generichash_blake2b_KEYBYTES_MIN\",\"crypto_generichash_blake2b_PERSONALBYTES\",\"crypto_generichash_blake2b_SALTBYTES\",\"crypto_hash_BYTES\",\"crypto_hash_sha256_BYTES\",\"crypto_hash_sha512_BYTES\",\"crypto_kdf_BYTES_MAX\",\"crypto_kdf_BYTES_MIN\",\"crypto_kdf_CONTEXTBYTES\",\"crypto_kdf_KEYBYTES\",\"crypto_kdf_blake2b_BYTES_MAX\",\"crypto_kdf_blake2b_BYTES_MIN\",\"crypto_kdf_blake2b_CONTEXTBYTES\",\"crypto_kdf_blake2b_KEYBYTES\",\"crypto_kdf_hkdf_sha256_BYTES_MAX\",\"crypto_kdf_hkdf_sha256_BYTES_MIN\",\"crypto_kdf_hkdf_sha256_KEYBYTES\",\"crypto_kdf_hkdf_sha512_BYTES_MAX\",\"crypto_kdf_hkdf_sha512_BYTES_MIN\",\"crypto_kdf_hkdf_sha512_KEYBYTES\",\"crypto_kx_PUBLICKEYBYTES\",\"crypto_kx_SECRETKEYBYTES\",\"crypto_kx_SEEDBYTES\",\"crypto_kx_SESSIONKEYBYTES\",\"crypto_onetimeauth_BYTES\",\"crypto_onetimeauth_KEYBYTES\",\"crypto_onetimeauth_poly1305_BYTES\",\"crypto_onetimeauth_poly1305_KEYBYTES\",\"crypto_pwhash_ALG_ARGON2I13\",\"crypto_pwhash_ALG_ARGON2ID13\",\"crypto_pwhash_ALG_DEFAULT\",\"crypto_pwhash_BYTES_MAX\",\"crypto_pwhash_BYTES_MIN\",\"crypto_pwhash_MEMLIMIT_INTERACTIVE\",\"crypto_pwhash_MEMLIMIT_MAX\",\"crypto_pwhash_MEMLIMIT_MIN\",\"crypto_pwhash_MEMLIMIT_MODERATE\",\"crypto_pwhash_MEMLIMIT_SENSITIVE\",\"crypto_pwhash_OPSLIMIT_INTERACTIVE\",\"crypto_pwhash_OPSLIMIT_MAX\",\"crypto_pwhash_OPSLIMIT_MIN\",\"crypto_pwhash_OPSLIMIT_MODERATE\",\"crypto_pwhash_OPSLIMIT_SENSITIVE\",\"crypto_pwhash_PASSWD_MAX\",\"crypto_pwhash_PASSWD_MIN\",\"crypto_pwhash_SALTBYTES\",\"crypto_pwhash_STRBYTES\",\"crypto_pwhash_argon2i_BYTES_MAX\",\"crypto_pwhash_argon2i_BYTES_MIN\",\"crypto_pwhash_argon2i_MEMLIMIT_INTERACTIVE\",\"crypto_pwhash_argon2i_MEMLIMIT_MAX\",\"crypto_pwhash_argon2i_MEMLIMIT_MIN\",\"crypto_pwhash_argon2i_MEMLIMIT_MODERATE\",\"crypto_pwhash_argon2i_MEMLIMIT_SENSITIVE\",\"crypto_pwhash_argon2i_OPSLIMIT_INTERACTIVE\",\"crypto_pwhash_argon2i_OPSLIMIT_MAX\",\"crypto_pwhash_argon2i_OPSLIMIT_MIN\",\"crypto_pwhash_argon2i_OPSLIMIT_MODERATE\",\"crypto_pwhash_argon2i_OPSLIMIT_SENSITIVE\",\"crypto_pwhash_argon2i_PASSWD_MAX\",\"crypto_pwhash_argon2i_PASSWD_MIN\",\"crypto_pwhash_argon2i_SALTBYTES\",\"crypto_pwhash_argon2i_STRBYTES\",\"crypto_pwhash_argon2id_BYTES_MAX\",\"crypto_pwhash_argon2id_BYTES_MIN\",\"crypto_pwhash_argon2id_MEMLIMIT_INTERACTIVE\",\"crypto_pwhash_argon2id_MEMLIMIT_MAX\",\"crypto_pwhash_argon2id_MEMLIMIT_MIN\",\"crypto_pwhash_argon2id_MEMLIMIT_MODERATE\",\"crypto_pwhash_argon2id_MEMLIMIT_SENSITIVE\",\"crypto_pwhash_argon2id_OPSLIMIT_INTERACTIVE\",\"crypto_pwhash_argon2id_OPSLIMIT_MAX\",\"crypto_pwhash_argon2id_OPSLIMIT_MIN\",\"crypto_pwhash_argon2id_OPSLIMIT_MODERATE\",\"crypto_pwhash_argon2id_OPSLIMIT_SENSITIVE\",\"crypto_pwhash_argon2id_PASSWD_MAX\",\"crypto_pwhash_argon2id_PASSWD_MIN\",\"crypto_pwhash_argon2id_SALTBYTES\",\"crypto_pwhash_argon2id_STRBYTES\",\"crypto_pwhash_scryptsalsa208sha256_BYTES_MAX\",\"crypto_pwhash_scryptsalsa208sha256_BYTES_MIN\",\"crypto_pwhash_scryptsalsa208sha256_MEMLIMIT_INTERACTIVE\",\"crypto_pwhash_scryptsalsa208sha256_MEMLIMIT_MAX\",\"crypto_pwhash_scryptsalsa208sha256_MEMLIMIT_MIN\",\"crypto_pwhash_scryptsalsa208sha256_MEMLIMIT_SENSITIVE\",\"crypto_pwhash_scryptsalsa208sha256_OPSLIMIT_INTERACTIVE\",\"crypto_pwhash_scryptsalsa208sha256_OPSLIMIT_MAX\",\"crypto_pwhash_scryptsalsa208sha256_OPSLIMIT_MIN\",\"crypto_pwhash_scryptsalsa208sha256_OPSLIMIT_SENSITIVE\",\"crypto_pwhash_scryptsalsa208sha256_PASSWD_MAX\",\"crypto_pwhash_scryptsalsa208sha256_PASSWD_MIN\",\"crypto_pwhash_scryptsalsa208sha256_SALTBYTES\",\"crypto_pwhash_scryptsalsa208sha256_STRBYTES\",\"crypto_scalarmult_BYTES\",\"crypto_scalarmult_SCALARBYTES\",\"crypto_scalarmult_curve25519_BYTES\",\"crypto_scalarmult_curve25519_SCALARBYTES\",\"crypto_scalarmult_ed25519_BYTES\",\"crypto_scalarmult_ed25519_SCALARBYTES\",\"crypto_scalarmult_ristretto255_BYTES\",\"crypto_scalarmult_ristretto255_SCALARBYTES\",\"crypto_secretbox_KEYBYTES\",\"crypto_secretbox_MACBYTES\",\"crypto_secretbox_MESSAGEBYTES_MAX\",\"crypto_secretbox_NONCEBYTES\",\"crypto_secretbox_xchacha20poly1305_KEYBYTES\",\"crypto_secretbox_xchacha20poly1305_MACBYTES\",\"crypto_secretbox_xchacha20poly1305_MESSAGEBYTES_MAX\",\"crypto_secretbox_xchacha20poly1305_NONCEBYTES\",\"crypto_secretbox_xsalsa20poly1305_KEYBYTES\",\"crypto_secretbox_xsalsa20poly1305_MACBYTES\",\"crypto_secretbox_xsalsa20poly1305_MESSAGEBYTES_MAX\",\"crypto_secretbox_xsalsa20poly1305_NONCEBYTES\",\"crypto_secretstream_xchacha20poly1305_ABYTES\",\"crypto_secretstream_xchacha20poly1305_HEADERBYTES\",\"crypto_secretstream_xchacha20poly1305_KEYBYTES\",\"crypto_secretstream_xchacha20poly1305_MESSAGEBYTES_MAX\",\"crypto_secretstream_xchacha20poly1305_TAG_FINAL\",\"crypto_secretstream_xchacha20poly1305_TAG_MESSAGE\",\"crypto_secretstream_xchacha20poly1305_TAG_PUSH\",\"crypto_secretstream_xchacha20poly1305_TAG_REKEY\",\"crypto_shorthash_BYTES\",\"crypto_shorthash_KEYBYTES\",\"crypto_shorthash_siphash24_BYTES\",\"crypto_shorthash_siphash24_KEYBYTES\",\"crypto_shorthash_siphashx24_BYTES\",\"crypto_shorthash_siphashx24_KEYBYTES\",\"crypto_sign_BYTES\",\"crypto_sign_MESSAGEBYTES_MAX\",\"crypto_sign_PUBLICKEYBYTES\",\"crypto_sign_SECRETKEYBYTES\",\"crypto_sign_SEEDBYTES\",\"crypto_sign_ed25519_BYTES\",\"crypto_sign_ed25519_MESSAGEBYTES_MAX\",\"crypto_sign_ed25519_PUBLICKEYBYTES\",\"crypto_sign_ed25519_SECRETKEYBYTES\",\"crypto_sign_ed25519_SEEDBYTES\",\"crypto_stream_KEYBYTES\",\"crypto_stream_MESSAGEBYTES_MAX\",\"crypto_stream_NONCEBYTES\",\"crypto_stream_chacha20_IETF_KEYBYTES\",\"crypto_stream_chacha20_IETF_MESSAGEBYTES_MAX\",\"crypto_stream_chacha20_IETF_NONCEBYTES\",\"crypto_stream_chacha20_KEYBYTES\",\"crypto_stream_chacha20_MESSAGEBYTES_MAX\",\"crypto_stream_chacha20_NONCEBYTES\",\"crypto_stream_chacha20_ietf_KEYBYTES\",\"crypto_stream_chacha20_ietf_MESSAGEBYTES_MAX\",\"crypto_stream_chacha20_ietf_NONCEBYTES\",\"crypto_stream_salsa2012_KEYBYTES\",\"crypto_stream_salsa2012_MESSAGEBYTES_MAX\",\"crypto_stream_salsa2012_NONCEBYTES\",\"crypto_stream_salsa208_KEYBYTES\",\"crypto_stream_salsa208_MESSAGEBYTES_MAX\",\"crypto_stream_salsa208_NONCEBYTES\",\"crypto_stream_salsa20_KEYBYTES\",\"crypto_stream_salsa20_MESSAGEBYTES_MAX\",\"crypto_stream_salsa20_NONCEBYTES\",\"crypto_stream_xchacha20_KEYBYTES\",\"crypto_stream_xchacha20_MESSAGEBYTES_MAX\",\"crypto_stream_xchacha20_NONCEBYTES\",\"crypto_stream_xsalsa20_KEYBYTES\",\"crypto_stream_xsalsa20_MESSAGEBYTES_MAX\",\"crypto_stream_xsalsa20_NONCEBYTES\",\"crypto_verify_16_BYTES\",\"crypto_verify_32_BYTES\",\"crypto_verify_64_BYTES\"];for(_=0;_<n.length;_++)\"function\"==typeof(c=r[\"_\"+n[_].toLowerCase()])&&(e[n[_]]=c());var s=[\"SODIUM_VERSION_STRING\",\"crypto_pwhash_STRPREFIX\",\"crypto_pwhash_argon2i_STRPREFIX\",\"crypto_pwhash_argon2id_STRPREFIX\",\"crypto_pwhash_scryptsalsa208sha256_STRPREFIX\"];for(_=0;_<s.length;_++){var c;\"function\"==typeof(c=r[\"_\"+s[_].toLowerCase()])&&(e[s[_]]=r.UTF8ToString(c()))}}r=a;try{t();var _=new Uint8Array([98,97,108,108,115]),n=e.randombytes_buf(e.crypto_secretbox_NONCEBYTES),s=e.randombytes_buf(e.crypto_secretbox_KEYBYTES),c=e.crypto_secretbox_easy(_,n,s),o=e.crypto_secretbox_open_easy(c,n,s);if(e.memcmp(_,o))return}catch(e){if(null==r.useBackupModule)throw new Error(\"Both wasm and asm failed to load\"+e)}r.useBackupModule(),t()}));function n(e){if(\"function\"==typeof TextEncoder)return(new TextEncoder).encode(e);e=unescape(encodeURIComponent(e));for(var a=new Uint8Array(e.length),r=0,t=e.length;r<t;r++)a[r]=e.charCodeAt(r);return a}function s(e){if(\"function\"==typeof TextDecoder)return new TextDecoder(\"utf-8\",{fatal:!0}).decode(e);var a=8192,r=Math.ceil(e.length/a);if(r<=1)try{return decodeURIComponent(escape(String.fromCharCode.apply(null,e)))}catch(e){throw new TypeError(\"The encoded data was not valid.\")}for(var t=\"\",_=0,n=0;n<r;n++){var c=Array.prototype.slice.call(e,n*a+_,(n+1)*a+_);if(0!=c.length){var o,h=c.length,p=0;do{var y=c[--h];y>=240?(p=4,o=!0):y>=224?(p=3,o=!0):y>=192?(p=2,o=!0):y<128&&(p=1,o=!0)}while(!o);for(var i=p-(c.length-h),l=0;l<i;l++)_--,c.pop();t+=s(c)}}return t}function c(e){e=E(null,e,\"input\");for(var a,r,t,_=\"\",n=0;n<e.length;n++)t=87+(r=15&e[n])+(r-10>>8&-39)<<8|87+(a=e[n]>>>4)+(a-10>>8&-39),_+=String.fromCharCode(255&t)+String.fromCharCode(t>>>8);return _}var o={ORIGINAL:1,ORIGINAL_NO_PADDING:3,URLSAFE:5,URLSAFE_NO_PADDING:7};function h(e){if(null==e)return o.URLSAFE_NO_PADDING;if(e!==o.ORIGINAL&&e!==o.ORIGINAL_NO_PADDING&&e!==o.URLSAFE&&e!=o.URLSAFE_NO_PADDING)throw new Error(\"unsupported base64 variant\");return e}function p(e,a){a=h(a),e=E(_,e,\"input\");var t,_=[],n=0|Math.floor(e.length/3),c=e.length-3*n,o=4*n+(0!==c?2&a?2+(c>>>1):4:0),p=new u(o+1),y=d(e);return _.push(y),_.push(p.address),0===r._sodium_bin2base64(p.address,p.length,y,e.length,a)&&b(_,\"conversion failed\"),p.length=o,t=s(p.to_Uint8Array()),g(_),t}function y(e,a){var r=a||t;if(!i(r))throw new Error(r+\" output format is not available\");if(e instanceof u){if(\"uint8array\"===r)return e.to_Uint8Array();if(\"text\"===r)return s(e.to_Uint8Array());if(\"hex\"===r)return c(e.to_Uint8Array());if(\"base64\"===r)return p(e.to_Uint8Array(),o.URLSAFE_NO_PADDING);throw new Error('What is output format \"'+r+'\"?')}if(\"object\"==typeof e){for(var _=Object.keys(e),n={},h=0;h<_.length;h++)n[_[h]]=y(e[_[h]],r);return n}if(\"string\"==typeof e)return e;throw new TypeError(\"Cannot format output\")}function i(e){for(var a=[\"uint8array\",\"text\",\"hex\",\"base64\"],r=0;r<a.length;r++)if(a[r]===e)return!0;return!1}function l(e){if(e){if(\"string\"!=typeof e)throw new TypeError(\"When defined, the output format must be a string\");if(!i(e))throw new Error(e+\" is not a supported output format\")}}function u(e){this.length=e,this.address=v(e)}function d(e){var a=v(e.length);return r.HEAPU8.set(e,a),a}function v(e){var a=r._malloc(e);if(0===a)throw{message:\"_malloc() failed\",length:e};return a}function g(e){if(e)for(var a=0;a<e.length;a++)t=e[a],r._free(t);var t}function b(e,a){throw g(e),new Error(a)}function f(e,a){throw g(e),new TypeError(a)}function m(e,a,r){null==a&&f(e,r+\" cannot be null or undefined\")}function E(e,a,r){return m(e,a,r),a instanceof Uint8Array?a:\"string\"==typeof a?n(a):void f(e,\"unsupported input type for \"+r)}function x(e,a,t,_,n,s){var c=[];l(s);var o=null;null!=e&&(o=d(e=E(c,e,\"secret_nonce\")),e.length,c.push(o)),a=E(c,a,\"ciphertext\");var h,p=r._crypto_aead_aegis128l_abytes(),i=a.length;i<p&&f(c,\"ciphertext is too short\"),h=d(a),c.push(h);var v=null,m=0;null!=t&&(v=d(t=E(c,t,\"additional_data\")),m=t.length,c.push(v)),_=E(c,_,\"public_nonce\");var x,k=0|r._crypto_aead_aegis128l_npubbytes();_.length!==k&&f(c,\"invalid public_nonce length\"),x=d(_),c.push(x),n=E(c,n,\"key\");var S,T=0|r._crypto_aead_aegis128l_keybytes();n.length!==T&&f(c,\"invalid key length\"),S=d(n),c.push(S);var w=new u(i-r._crypto_aead_aegis128l_abytes()|0),Y=w.address;if(c.push(Y),0===r._crypto_aead_aegis128l_decrypt(Y,null,o,h,i,0,v,m,0,x,S)){var B=y(w,s);return g(c),B}b(c,\"ciphertext cannot be decrypted using that key\")}function k(e,a,t,_,n,s,c){var o=[];l(c);var h=null;null!=e&&(h=d(e=E(o,e,\"secret_nonce\")),e.length,o.push(h));var p=d(a=E(o,a,\"ciphertext\")),i=a.length;o.push(p),t=E(o,t,\"mac\");var v,m=0|r._crypto_box_macbytes();t.length!==m&&f(o,\"invalid mac length\"),v=d(t),o.push(v);var x=null,k=0;null!=_&&(x=d(_=E(o,_,\"additional_data\")),k=_.length,o.push(x)),n=E(o,n,\"public_nonce\");var S,T=0|r._crypto_aead_aegis128l_npubbytes();n.length!==T&&f(o,\"invalid public_nonce length\"),S=d(n),o.push(S),s=E(o,s,\"key\");var w,Y=0|r._crypto_aead_aegis128l_keybytes();s.length!==Y&&f(o,\"invalid key length\"),w=d(s),o.push(w);var B=new u(0|i),A=B.address;if(o.push(A),0===r._crypto_aead_aegis128l_decrypt_detached(A,h,p,i,0,v,x,k,0,S,w)){var M=y(B,c);return g(o),M}b(o,\"ciphertext cannot be decrypted using that key\")}function S(e,a,t,_,n,s){var c=[];l(s);var o=d(e=E(c,e,\"message\")),h=e.length;c.push(o);var p=null,i=0;null!=a&&(p=d(a=E(c,a,\"additional_data\")),i=a.length,c.push(p));var v=null;null!=t&&(v=d(t=E(c,t,\"secret_nonce\")),t.length,c.push(v)),_=E(c,_,\"public_nonce\");var m,x=0|r._crypto_aead_aegis128l_npubbytes();_.length!==x&&f(c,\"invalid public_nonce length\"),m=d(_),c.push(m),n=E(c,n,\"key\");var k,S=0|r._crypto_aead_aegis128l_keybytes();n.length!==S&&f(c,\"invalid key length\"),k=d(n),c.push(k);var T=new u(h+r._crypto_aead_aegis128l_abytes()|0),w=T.address;if(c.push(w),0===r._crypto_aead_aegis128l_encrypt(w,null,o,h,0,p,i,0,v,m,k)){var Y=y(T,s);return g(c),Y}b(c,\"invalid usage\")}function T(e,a,t,_,n,s){var c=[];l(s);var o=d(e=E(c,e,\"message\")),h=e.length;c.push(o);var p=null,i=0;null!=a&&(p=d(a=E(c,a,\"additional_data\")),i=a.length,c.push(p));var v=null;null!=t&&(v=d(t=E(c,t,\"secret_nonce\")),t.length,c.push(v)),_=E(c,_,\"public_nonce\");var m,x=0|r._crypto_aead_aegis128l_npubbytes();_.length!==x&&f(c,\"invalid public_nonce length\"),m=d(_),c.push(m),n=E(c,n,\"key\");var k,S=0|r._crypto_aead_aegis128l_keybytes();n.length!==S&&f(c,\"invalid key length\"),k=d(n),c.push(k);var T=new u(0|h),w=T.address;c.push(w);var Y=new u(0|r._crypto_aead_aegis128l_abytes()),B=Y.address;if(c.push(B),0===r._crypto_aead_aegis128l_encrypt_detached(w,B,null,o,h,0,p,i,0,v,m,k)){var A=y({ciphertext:T,mac:Y},s);return g(c),A}b(c,\"invalid usage\")}function w(e){var a=[];l(e);var t=new u(0|r._crypto_aead_aegis128l_keybytes()),_=t.address;a.push(_),r._crypto_aead_aegis128l_keygen(_);var n=y(t,e);return g(a),n}function Y(e,a,t,_,n,s){var c=[];l(s);var o=null;null!=e&&(o=d(e=E(c,e,\"secret_nonce\")),e.length,c.push(o)),a=E(c,a,\"ciphertext\");var h,p=r._crypto_aead_aegis256_abytes(),i=a.length;i<p&&f(c,\"ciphertext is too short\"),h=d(a),c.push(h);var v=null,m=0;null!=t&&(v=d(t=E(c,t,\"additional_data\")),m=t.length,c.push(v)),_=E(c,_,\"public_nonce\");var x,k=0|r._crypto_aead_aegis256_npubbytes();_.length!==k&&f(c,\"invalid public_nonce length\"),x=d(_),c.push(x),n=E(c,n,\"key\");var S,T=0|r._crypto_aead_aegis256_keybytes();n.length!==T&&f(c,\"invalid key length\"),S=d(n),c.push(S);var w=new u(i-r._crypto_aead_aegis256_abytes()|0),Y=w.address;if(c.push(Y),0===r._crypto_aead_aegis256_decrypt(Y,null,o,h,i,0,v,m,0,x,S)){var B=y(w,s);return g(c),B}b(c,\"ciphertext cannot be decrypted using that key\")}function B(e,a,t,_,n,s,c){var o=[];l(c);var h=null;null!=e&&(h=d(e=E(o,e,\"secret_nonce\")),e.length,o.push(h));var p=d(a=E(o,a,\"ciphertext\")),i=a.length;o.push(p),t=E(o,t,\"mac\");var v,m=0|r._crypto_box_macbytes();t.length!==m&&f(o,\"invalid mac length\"),v=d(t),o.push(v);var x=null,k=0;null!=_&&(x=d(_=E(o,_,\"additional_data\")),k=_.length,o.push(x)),n=E(o,n,\"public_nonce\");var S,T=0|r._crypto_aead_aegis256_npubbytes();n.length!==T&&f(o,\"invalid public_nonce length\"),S=d(n),o.push(S),s=E(o,s,\"key\");var w,Y=0|r._crypto_aead_aegis256_keybytes();s.length!==Y&&f(o,\"invalid key length\"),w=d(s),o.push(w);var B=new u(0|i),A=B.address;if(o.push(A),0===r._crypto_aead_aegis256_decrypt_detached(A,h,p,i,0,v,x,k,0,S,w)){var M=y(B,c);return g(o),M}b(o,\"ciphertext cannot be decrypted using that key\")}function A(e,a,t,_,n,s){var c=[];l(s);var o=d(e=E(c,e,\"message\")),h=e.length;c.push(o);var p=null,i=0;null!=a&&(p=d(a=E(c,a,\"additional_data\")),i=a.length,c.push(p));var v=null;null!=t&&(v=d(t=E(c,t,\"secret_nonce\")),t.length,c.push(v)),_=E(c,_,\"public_nonce\");var m,x=0|r._crypto_aead_aegis256_npubbytes();_.length!==x&&f(c,\"invalid public_nonce length\"),m=d(_),c.push(m),n=E(c,n,\"key\");var k,S=0|r._crypto_aead_aegis256_keybytes();n.length!==S&&f(c,\"invalid key length\"),k=d(n),c.push(k);var T=new u(h+r._crypto_aead_aegis256_abytes()|0),w=T.address;if(c.push(w),0===r._crypto_aead_aegis256_encrypt(w,null,o,h,0,p,i,0,v,m,k)){var Y=y(T,s);return g(c),Y}b(c,\"invalid usage\")}function M(e,a,t,_,n,s){var c=[];l(s);var o=d(e=E(c,e,\"message\")),h=e.length;c.push(o);var p=null,i=0;null!=a&&(p=d(a=E(c,a,\"additional_data\")),i=a.length,c.push(p));var v=null;null!=t&&(v=d(t=E(c,t,\"secret_nonce\")),t.length,c.push(v)),_=E(c,_,\"public_nonce\");var m,x=0|r._crypto_aead_aegis256_npubbytes();_.length!==x&&f(c,\"invalid public_nonce length\"),m=d(_),c.push(m),n=E(c,n,\"key\");var k,S=0|r._crypto_aead_aegis256_keybytes();n.length!==S&&f(c,\"invalid key length\"),k=d(n),c.push(k);var T=new u(0|h),w=T.address;c.push(w);var Y=new u(0|r._crypto_aead_aegis256_abytes()),B=Y.address;if(c.push(B),0===r._crypto_aead_aegis256_encrypt_detached(w,B,null,o,h,0,p,i,0,v,m,k)){var A=y({ciphertext:T,mac:Y},s);return g(c),A}b(c,\"invalid usage\")}function I(e){var a=[];l(e);var t=new u(0|r._crypto_aead_aegis256_keybytes()),_=t.address;a.push(_),r._crypto_aead_aegis256_keygen(_);var n=y(t,e);return g(a),n}function K(e,a,t,_,n,s){var c=[];l(s);var o=null;null!=e&&(o=d(e=E(c,e,\"secret_nonce\")),e.length,c.push(o)),a=E(c,a,\"ciphertext\");var h,p=r._crypto_aead_chacha20poly1305_abytes(),i=a.length;i<p&&f(c,\"ciphertext is too short\"),h=d(a),c.push(h);var v=null,m=0;null!=t&&(v=d(t=E(c,t,\"additional_data\")),m=t.length,c.push(v)),_=E(c,_,\"public_nonce\");var x,k=0|r._crypto_aead_chacha20poly1305_npubbytes();_.length!==k&&f(c,\"invalid public_nonce length\"),x=d(_),c.push(x),n=E(c,n,\"key\");var S,T=0|r._crypto_aead_chacha20poly1305_keybytes();n.length!==T&&f(c,\"invalid key length\"),S=d(n),c.push(S);var w=new u(i-r._crypto_aead_chacha20poly1305_abytes()|0),Y=w.address;if(c.push(Y),0===r._crypto_aead_chacha20poly1305_decrypt(Y,null,o,h,i,0,v,m,0,x,S)){var B=y(w,s);return g(c),B}b(c,\"ciphertext cannot be decrypted using that key\")}function N(e,a,t,_,n,s,c){var o=[];l(c);var h=null;null!=e&&(h=d(e=E(o,e,\"secret_nonce\")),e.length,o.push(h));var p=d(a=E(o,a,\"ciphertext\")),i=a.length;o.push(p),t=E(o,t,\"mac\");var v,m=0|r._crypto_box_macbytes();t.length!==m&&f(o,\"invalid mac length\"),v=d(t),o.push(v);var x=null,k=0;null!=_&&(x=d(_=E(o,_,\"additional_data\")),k=_.length,o.push(x)),n=E(o,n,\"public_nonce\");var S,T=0|r._crypto_aead_chacha20poly1305_npubbytes();n.length!==T&&f(o,\"invalid public_nonce length\"),S=d(n),o.push(S),s=E(o,s,\"key\");var w,Y=0|r._crypto_aead_chacha20poly1305_keybytes();s.length!==Y&&f(o,\"invalid key length\"),w=d(s),o.push(w);var B=new u(0|i),A=B.address;if(o.push(A),0===r._crypto_aead_chacha20poly1305_decrypt_detached(A,h,p,i,0,v,x,k,0,S,w)){var M=y(B,c);return g(o),M}b(o,\"ciphertext cannot be decrypted using that key\")}function L(e,a,t,_,n,s){var c=[];l(s);var o=d(e=E(c,e,\"message\")),h=e.length;c.push(o);var p=null,i=0;null!=a&&(p=d(a=E(c,a,\"additional_data\")),i=a.length,c.push(p));var v=null;null!=t&&(v=d(t=E(c,t,\"secret_nonce\")),t.length,c.push(v)),_=E(c,_,\"public_nonce\");var m,x=0|r._crypto_aead_chacha20poly1305_npubbytes();_.length!==x&&f(c,\"invalid public_nonce length\"),m=d(_),c.push(m),n=E(c,n,\"key\");var k,S=0|r._crypto_aead_chacha20poly1305_keybytes();n.length!==S&&f(c,\"invalid key length\"),k=d(n),c.push(k);var T=new u(h+r._crypto_aead_chacha20poly1305_abytes()|0),w=T.address;if(c.push(w),0===r._crypto_aead_chacha20poly1305_encrypt(w,null,o,h,0,p,i,0,v,m,k)){var Y=y(T,s);return g(c),Y}b(c,\"invalid usage\")}function O(e,a,t,_,n,s){var c=[];l(s);var o=d(e=E(c,e,\"message\")),h=e.length;c.push(o);var p=null,i=0;null!=a&&(p=d(a=E(c,a,\"additional_data\")),i=a.length,c.push(p));var v=null;null!=t&&(v=d(t=E(c,t,\"secret_nonce\")),t.length,c.push(v)),_=E(c,_,\"public_nonce\");var m,x=0|r._crypto_aead_chacha20poly1305_npubbytes();_.length!==x&&f(c,\"invalid public_nonce length\"),m=d(_),c.push(m),n=E(c,n,\"key\");var k,S=0|r._crypto_aead_chacha20poly1305_keybytes();n.length!==S&&f(c,\"invalid key length\"),k=d(n),c.push(k);var T=new u(0|h),w=T.address;c.push(w);var Y=new u(0|r._crypto_aead_chacha20poly1305_abytes()),B=Y.address;if(c.push(B),0===r._crypto_aead_chacha20poly1305_encrypt_detached(w,B,null,o,h,0,p,i,0,v,m,k)){var A=y({ciphertext:T,mac:Y},s);return g(c),A}b(c,\"invalid usage\")}function U(e,a,t,_,n,s){var c=[];l(s);var o=null;null!=e&&(o=d(e=E(c,e,\"secret_nonce\")),e.length,c.push(o)),a=E(c,a,\"ciphertext\");var h,p=r._crypto_aead_chacha20poly1305_ietf_abytes(),i=a.length;i<p&&f(c,\"ciphertext is too short\"),h=d(a),c.push(h);var v=null,m=0;null!=t&&(v=d(t=E(c,t,\"additional_data\")),m=t.length,c.push(v)),_=E(c,_,\"public_nonce\");var x,k=0|r._crypto_aead_chacha20poly1305_ietf_npubbytes();_.length!==k&&f(c,\"invalid public_nonce length\"),x=d(_),c.push(x),n=E(c,n,\"key\");var S,T=0|r._crypto_aead_chacha20poly1305_ietf_keybytes();n.length!==T&&f(c,\"invalid key length\"),S=d(n),c.push(S);var w=new u(i-r._crypto_aead_chacha20poly1305_ietf_abytes()|0),Y=w.address;if(c.push(Y),0===r._crypto_aead_chacha20poly1305_ietf_decrypt(Y,null,o,h,i,0,v,m,0,x,S)){var B=y(w,s);return g(c),B}b(c,\"ciphertext cannot be decrypted using that key\")}function C(e,a,t,_,n,s,c){var o=[];l(c);var h=null;null!=e&&(h=d(e=E(o,e,\"secret_nonce\")),e.length,o.push(h));var p=d(a=E(o,a,\"ciphertext\")),i=a.length;o.push(p),t=E(o,t,\"mac\");var v,m=0|r._crypto_box_macbytes();t.length!==m&&f(o,\"invalid mac length\"),v=d(t),o.push(v);var x=null,k=0;null!=_&&(x=d(_=E(o,_,\"additional_data\")),k=_.length,o.push(x)),n=E(o,n,\"public_nonce\");var S,T=0|r._crypto_aead_chacha20poly1305_ietf_npubbytes();n.length!==T&&f(o,\"invalid public_nonce length\"),S=d(n),o.push(S),s=E(o,s,\"key\");var w,Y=0|r._crypto_aead_chacha20poly1305_ietf_keybytes();s.length!==Y&&f(o,\"invalid key length\"),w=d(s),o.push(w);var B=new u(0|i),A=B.address;if(o.push(A),0===r._crypto_aead_chacha20poly1305_ietf_decrypt_detached(A,h,p,i,0,v,x,k,0,S,w)){var M=y(B,c);return g(o),M}b(o,\"ciphertext cannot be decrypted using that key\")}function P(e,a,t,_,n,s){var c=[];l(s);var o=d(e=E(c,e,\"message\")),h=e.length;c.push(o);var p=null,i=0;null!=a&&(p=d(a=E(c,a,\"additional_data\")),i=a.length,c.push(p));var v=null;null!=t&&(v=d(t=E(c,t,\"secret_nonce\")),t.length,c.push(v)),_=E(c,_,\"public_nonce\");var m,x=0|r._crypto_aead_chacha20poly1305_ietf_npubbytes();_.length!==x&&f(c,\"invalid public_nonce length\"),m=d(_),c.push(m),n=E(c,n,\"key\");var k,S=0|r._crypto_aead_chacha20poly1305_ietf_keybytes();n.length!==S&&f(c,\"invalid key length\"),k=d(n),c.push(k);var T=new u(h+r._crypto_aead_chacha20poly1305_ietf_abytes()|0),w=T.address;if(c.push(w),0===r._crypto_aead_chacha20poly1305_ietf_encrypt(w,null,o,h,0,p,i,0,v,m,k)){var Y=y(T,s);return g(c),Y}b(c,\"invalid usage\")}function R(e,a,t,_,n,s){var c=[];l(s);var o=d(e=E(c,e,\"message\")),h=e.length;c.push(o);var p=null,i=0;null!=a&&(p=d(a=E(c,a,\"additional_data\")),i=a.length,c.push(p));var v=null;null!=t&&(v=d(t=E(c,t,\"secret_nonce\")),t.length,c.push(v)),_=E(c,_,\"public_nonce\");var m,x=0|r._crypto_aead_chacha20poly1305_ietf_npubbytes();_.length!==x&&f(c,\"invalid public_nonce length\"),m=d(_),c.push(m),n=E(c,n,\"key\");var k,S=0|r._crypto_aead_chacha20poly1305_ietf_keybytes();n.length!==S&&f(c,\"invalid key length\"),k=d(n),c.push(k);var T=new u(0|h),w=T.address;c.push(w);var Y=new u(0|r._crypto_aead_chacha20poly1305_ietf_abytes()),B=Y.address;if(c.push(B),0===r._crypto_aead_chacha20poly1305_ietf_encrypt_detached(w,B,null,o,h,0,p,i,0,v,m,k)){var A=y({ciphertext:T,mac:Y},s);return g(c),A}b(c,\"invalid usage\")}function X(e){var a=[];l(e);var t=new u(0|r._crypto_aead_chacha20poly1305_ietf_keybytes()),_=t.address;a.push(_),r._crypto_aead_chacha20poly1305_ietf_keygen(_);var n=y(t,e);return g(a),n}function G(e){var a=[];l(e);var t=new u(0|r._crypto_aead_chacha20poly1305_keybytes()),_=t.address;a.push(_),r._crypto_aead_chacha20poly1305_keygen(_);var n=y(t,e);return g(a),n}function D(e,a,t,_,n,s){var c=[];l(s);var o=null;null!=e&&(o=d(e=E(c,e,\"secret_nonce\")),e.length,c.push(o)),a=E(c,a,\"ciphertext\");var h,p=r._crypto_aead_xchacha20poly1305_ietf_abytes(),i=a.length;i<p&&f(c,\"ciphertext is too short\"),h=d(a),c.push(h);var v=null,m=0;null!=t&&(v=d(t=E(c,t,\"additional_data\")),m=t.length,c.push(v)),_=E(c,_,\"public_nonce\");var x,k=0|r._crypto_aead_xchacha20poly1305_ietf_npubbytes();_.length!==k&&f(c,\"invalid public_nonce length\"),x=d(_),c.push(x),n=E(c,n,\"key\");var S,T=0|r._crypto_aead_xchacha20poly1305_ietf_keybytes();n.length!==T&&f(c,\"invalid key length\"),S=d(n),c.push(S);var w=new u(i-r._crypto_aead_xchacha20poly1305_ietf_abytes()|0),Y=w.address;if(c.push(Y),0===r._crypto_aead_xchacha20poly1305_ietf_decrypt(Y,null,o,h,i,0,v,m,0,x,S)){var B=y(w,s);return g(c),B}b(c,\"ciphertext cannot be decrypted using that key\")}function F(e,a,t,_,n,s,c){var o=[];l(c);var h=null;null!=e&&(h=d(e=E(o,e,\"secret_nonce\")),e.length,o.push(h));var p=d(a=E(o,a,\"ciphertext\")),i=a.length;o.push(p),t=E(o,t,\"mac\");var v,m=0|r._crypto_box_macbytes();t.length!==m&&f(o,\"invalid mac length\"),v=d(t),o.push(v);var x=null,k=0;null!=_&&(x=d(_=E(o,_,\"additional_data\")),k=_.length,o.push(x)),n=E(o,n,\"public_nonce\");var S,T=0|r._crypto_aead_xchacha20poly1305_ietf_npubbytes();n.length!==T&&f(o,\"invalid public_nonce length\"),S=d(n),o.push(S),s=E(o,s,\"key\");var w,Y=0|r._crypto_aead_xchacha20poly1305_ietf_keybytes();s.length!==Y&&f(o,\"invalid key length\"),w=d(s),o.push(w);var B=new u(0|i),A=B.address;if(o.push(A),0===r._crypto_aead_xchacha20poly1305_ietf_decrypt_detached(A,h,p,i,0,v,x,k,0,S,w)){var M=y(B,c);return g(o),M}b(o,\"ciphertext cannot be decrypted using that key\")}function V(e,a,t,_,n,s){var c=[];l(s);var o=d(e=E(c,e,\"message\")),h=e.length;c.push(o);var p=null,i=0;null!=a&&(p=d(a=E(c,a,\"additional_data\")),i=a.length,c.push(p));var v=null;null!=t&&(v=d(t=E(c,t,\"secret_nonce\")),t.length,c.push(v)),_=E(c,_,\"public_nonce\");var m,x=0|r._crypto_aead_xchacha20poly1305_ietf_npubbytes();_.length!==x&&f(c,\"invalid public_nonce length\"),m=d(_),c.push(m),n=E(c,n,\"key\");var k,S=0|r._crypto_aead_xchacha20poly1305_ietf_keybytes();n.length!==S&&f(c,\"invalid key length\"),k=d(n),c.push(k);var T=new u(h+r._crypto_aead_xchacha20poly1305_ietf_abytes()|0),w=T.address;if(c.push(w),0===r._crypto_aead_xchacha20poly1305_ietf_encrypt(w,null,o,h,0,p,i,0,v,m,k)){var Y=y(T,s);return g(c),Y}b(c,\"invalid usage\")}function H(e,a,t,_,n,s){var c=[];l(s);var o=d(e=E(c,e,\"message\")),h=e.length;c.push(o);var p=null,i=0;null!=a&&(p=d(a=E(c,a,\"additional_data\")),i=a.length,c.push(p));var v=null;null!=t&&(v=d(t=E(c,t,\"secret_nonce\")),t.length,c.push(v)),_=E(c,_,\"public_nonce\");var m,x=0|r._crypto_aead_xchacha20poly1305_ietf_npubbytes();_.length!==x&&f(c,\"invalid public_nonce length\"),m=d(_),c.push(m),n=E(c,n,\"key\");var k,S=0|r._crypto_aead_xchacha20poly1305_ietf_keybytes();n.length!==S&&f(c,\"invalid key length\"),k=d(n),c.push(k);var T=new u(0|h),w=T.address;c.push(w);var Y=new u(0|r._crypto_aead_xchacha20poly1305_ietf_abytes()),B=Y.address;if(c.push(B),0===r._crypto_aead_xchacha20poly1305_ietf_encrypt_detached(w,B,null,o,h,0,p,i,0,v,m,k)){var A=y({ciphertext:T,mac:Y},s);return g(c),A}b(c,\"invalid usage\")}function W(e){var a=[];l(e);var t=new u(0|r._crypto_aead_xchacha20poly1305_ietf_keybytes()),_=t.address;a.push(_),r._crypto_aead_xchacha20poly1305_ietf_keygen(_);var n=y(t,e);return g(a),n}function q(e,a,t){var _=[];l(t);var n=d(e=E(_,e,\"message\")),s=e.length;_.push(n),a=E(_,a,\"key\");var c,o=0|r._crypto_auth_keybytes();a.length!==o&&f(_,\"invalid key length\"),c=d(a),_.push(c);var h=new u(0|r._crypto_auth_bytes()),p=h.address;if(_.push(p),!(0|r._crypto_auth(p,n,s,0,c))){var i=y(h,t);return g(_),i}b(_,\"invalid usage\")}function j(e,a,t){var _=[];l(t);var n=d(e=E(_,e,\"message\")),s=e.length;_.push(n),a=E(_,a,\"key\");var c,o=0|r._crypto_auth_hmacsha256_keybytes();a.length!==o&&f(_,\"invalid key length\"),c=d(a),_.push(c);var h=new u(0|r._crypto_auth_hmacsha256_bytes()),p=h.address;if(_.push(p),!(0|r._crypto_auth_hmacsha256(p,n,s,0,c))){var i=y(h,t);return g(_),i}b(_,\"invalid usage\")}function z(e,a){var t=[];l(a),m(t,e,\"state_address\");var _=new u(0|r._crypto_auth_hmacsha256_bytes()),n=_.address;if(t.push(n),!(0|r._crypto_auth_hmacsha256_final(e,n))){var s=(r._free(e),y(_,a));return g(t),s}b(t,\"invalid usage\")}function J(e,a){var t=[];l(a);var _=null,n=0;null!=e&&(_=d(e=E(t,e,\"key\")),n=e.length,t.push(_));var s=new u(208).address;if(!(0|r._crypto_auth_hmacsha256_init(s,_,n))){var c=s;return g(t),c}b(t,\"invalid usage\")}function Q(e){var a=[];l(e);var t=new u(0|r._crypto_auth_hmacsha256_keybytes()),_=t.address;a.push(_),r._crypto_auth_hmacsha256_keygen(_);var n=y(t,e);return g(a),n}function Z(e,a,t){var _=[];l(t),m(_,e,\"state_address\");var n=d(a=E(_,a,\"message_chunk\")),s=a.length;_.push(n),0|r._crypto_auth_hmacsha256_update(e,n,s)&&b(_,\"invalid usage\"),g(_)}function $(e,a,t){var _=[];e=E(_,e,\"tag\");var n,s=0|r._crypto_auth_hmacsha256_bytes();e.length!==s&&f(_,\"invalid tag length\"),n=d(e),_.push(n);var c=d(a=E(_,a,\"message\")),o=a.length;_.push(c),t=E(_,t,\"key\");var h,p=0|r._crypto_auth_hmacsha256_keybytes();t.length!==p&&f(_,\"invalid key length\"),h=d(t),_.push(h);var y=!(0|r._crypto_auth_hmacsha256_verify(n,c,o,0,h));return g(_),y}function ee(e,a,t){var _=[];l(t);var n=d(e=E(_,e,\"message\")),s=e.length;_.push(n),a=E(_,a,\"key\");var c,o=0|r._crypto_auth_hmacsha512_keybytes();a.length!==o&&f(_,\"invalid key length\"),c=d(a),_.push(c);var h=new u(0|r._crypto_auth_hmacsha512_bytes()),p=h.address;if(_.push(p),!(0|r._crypto_auth_hmacsha512(p,n,s,0,c))){var i=y(h,t);return g(_),i}b(_,\"invalid usage\")}function ae(e,a,t){var _=[];l(t);var n=d(e=E(_,e,\"message\")),s=e.length;_.push(n),a=E(_,a,\"key\");var c,o=0|r._crypto_auth_hmacsha512256_keybytes();a.length!==o&&f(_,\"invalid key length\"),c=d(a),_.push(c);var h=new u(0|r._crypto_auth_hmacsha512256_bytes()),p=h.address;if(_.push(p),!(0|r._crypto_auth_hmacsha512256(p,n,s,0,c))){var i=y(h,t);return g(_),i}b(_,\"invalid usage\")}function re(e,a){var t=[];l(a),m(t,e,\"state_address\");var _=new u(0|r._crypto_auth_hmacsha512256_bytes()),n=_.address;if(t.push(n),!(0|r._crypto_auth_hmacsha512256_final(e,n))){var s=(r._free(e),y(_,a));return g(t),s}b(t,\"invalid usage\")}function te(e,a){var t=[];l(a);var _=null,n=0;null!=e&&(_=d(e=E(t,e,\"key\")),n=e.length,t.push(_));var s=new u(416).address;if(!(0|r._crypto_auth_hmacsha512256_init(s,_,n))){var c=s;return g(t),c}b(t,\"invalid usage\")}function _e(e){var a=[];l(e);var t=new u(0|r._crypto_auth_hmacsha512256_keybytes()),_=t.address;a.push(_),r._crypto_auth_hmacsha512256_keygen(_);var n=y(t,e);return g(a),n}function ne(e,a,t){var _=[];l(t),m(_,e,\"state_address\");var n=d(a=E(_,a,\"message_chunk\")),s=a.length;_.push(n),0|r._crypto_auth_hmacsha512256_update(e,n,s)&&b(_,\"invalid usage\"),g(_)}function se(e,a,t){var _=[];e=E(_,e,\"tag\");var n,s=0|r._crypto_auth_hmacsha512256_bytes();e.length!==s&&f(_,\"invalid tag length\"),n=d(e),_.push(n);var c=d(a=E(_,a,\"message\")),o=a.length;_.push(c),t=E(_,t,\"key\");var h,p=0|r._crypto_auth_hmacsha512256_keybytes();t.length!==p&&f(_,\"invalid key length\"),h=d(t),_.push(h);var y=!(0|r._crypto_auth_hmacsha512256_verify(n,c,o,0,h));return g(_),y}function ce(e,a){var t=[];l(a),m(t,e,\"state_address\");var _=new u(0|r._crypto_auth_hmacsha512_bytes()),n=_.address;if(t.push(n),!(0|r._crypto_auth_hmacsha512_final(e,n))){var s=(r._free(e),y(_,a));return g(t),s}b(t,\"invalid usage\")}function oe(e,a){var t=[];l(a);var _=null,n=0;null!=e&&(_=d(e=E(t,e,\"key\")),n=e.length,t.push(_));var s=new u(416).address;if(!(0|r._crypto_auth_hmacsha512_init(s,_,n))){var c=s;return g(t),c}b(t,\"invalid usage\")}function he(e){var a=[];l(e);var t=new u(0|r._crypto_auth_hmacsha512_keybytes()),_=t.address;a.push(_),r._crypto_auth_hmacsha512_keygen(_);var n=y(t,e);return g(a),n}function pe(e,a,t){var _=[];l(t),m(_,e,\"state_address\");var n=d(a=E(_,a,\"message_chunk\")),s=a.length;_.push(n),0|r._crypto_auth_hmacsha512_update(e,n,s)&&b(_,\"invalid usage\"),g(_)}function ye(e,a,t){var _=[];e=E(_,e,\"tag\");var n,s=0|r._crypto_auth_hmacsha512_bytes();e.length!==s&&f(_,\"invalid tag length\"),n=d(e),_.push(n);var c=d(a=E(_,a,\"message\")),o=a.length;_.push(c),t=E(_,t,\"key\");var h,p=0|r._crypto_auth_hmacsha512_keybytes();t.length!==p&&f(_,\"invalid key length\"),h=d(t),_.push(h);var y=!(0|r._crypto_auth_hmacsha512_verify(n,c,o,0,h));return g(_),y}function ie(e){var a=[];l(e);var t=new u(0|r._crypto_auth_keybytes()),_=t.address;a.push(_),r._crypto_auth_keygen(_);var n=y(t,e);return g(a),n}function le(e,a,t){var _=[];e=E(_,e,\"tag\");var n,s=0|r._crypto_auth_bytes();e.length!==s&&f(_,\"invalid tag length\"),n=d(e),_.push(n);var c=d(a=E(_,a,\"message\")),o=a.length;_.push(c),t=E(_,t,\"key\");var h,p=0|r._crypto_auth_keybytes();t.length!==p&&f(_,\"invalid key length\"),h=d(t),_.push(h);var y=!(0|r._crypto_auth_verify(n,c,o,0,h));return g(_),y}function ue(e,a,t){var _=[];l(t),e=E(_,e,\"publicKey\");var n,s=0|r._crypto_box_publickeybytes();e.length!==s&&f(_,\"invalid publicKey length\"),n=d(e),_.push(n),a=E(_,a,\"privateKey\");var c,o=0|r._crypto_box_secretkeybytes();a.length!==o&&f(_,\"invalid privateKey length\"),c=d(a),_.push(c);var h=new u(0|r._crypto_box_beforenmbytes()),p=h.address;if(_.push(p),!(0|r._crypto_box_beforenm(p,n,c))){var i=y(h,t);return g(_),i}b(_,\"invalid usage\")}function de(e,a,t){var _=[];l(t),e=E(_,e,\"publicKey\");var n,s=0|r._crypto_box_curve25519xchacha20poly1305_publickeybytes();e.length!==s&&f(_,\"invalid publicKey length\"),n=d(e),_.push(n),a=E(_,a,\"privateKey\");var c,o=0|r._crypto_box_curve25519xchacha20poly1305_secretkeybytes();a.length!==o&&f(_,\"invalid privateKey length\"),c=d(a),_.push(c);var h=new u(0|r._crypto_box_curve25519xchacha20poly1305_beforenmbytes()),p=h.address;if(_.push(p),!(0|r._crypto_box_curve25519xchacha20poly1305_beforenm(p,n,c))){var i=y(h,t);return g(_),i}b(_,\"invalid usage\")}function ve(e,a,t,_,n){var s=[];l(n);var c=d(e=E(s,e,\"message\")),o=e.length;s.push(c),a=E(s,a,\"nonce\");var h,p=0|r._crypto_box_curve25519xchacha20poly1305_noncebytes();a.length!==p&&f(s,\"invalid nonce length\"),h=d(a),s.push(h),t=E(s,t,\"publicKey\");var i,v=0|r._crypto_box_curve25519xchacha20poly1305_publickeybytes();t.length!==v&&f(s,\"invalid publicKey length\"),i=d(t),s.push(i),_=E(s,_,\"privateKey\");var m,x=0|r._crypto_box_curve25519xchacha20poly1305_secretkeybytes();_.length!==x&&f(s,\"invalid privateKey length\"),m=d(_),s.push(m);var k=new u(0|o),S=k.address;s.push(S);var T=new u(0|r._crypto_box_curve25519xchacha20poly1305_macbytes()),w=T.address;if(s.push(w),!(0|r._crypto_box_curve25519xchacha20poly1305_detached(S,w,c,o,0,h,i,m))){var Y=y({ciphertext:k,mac:T},n);return g(s),Y}b(s,\"invalid usage\")}function ge(e,a,t,_){var n=[];l(_);var s=d(e=E(n,e,\"message\")),c=e.length;n.push(s),a=E(n,a,\"nonce\");var o,h=0|r._crypto_box_curve25519xchacha20poly1305_noncebytes();a.length!==h&&f(n,\"invalid nonce length\"),o=d(a),n.push(o),t=E(n,t,\"sharedKey\");var p,i=0|r._crypto_box_curve25519xchacha20poly1305_beforenmbytes();t.length!==i&&f(n,\"invalid sharedKey length\"),p=d(t),n.push(p);var v=new u(0|c),m=v.address;n.push(m);var x=new u(0|r._crypto_box_curve25519xchacha20poly1305_macbytes()),k=x.address;if(n.push(k),!(0|r._crypto_box_curve25519xchacha20poly1305_detached_afternm(m,k,s,c,0,o,p))){var S=y({ciphertext:v,mac:x},_);return g(n),S}b(n,\"invalid usage\")}function be(e,a,t,_,n){var s=[];l(n);var c=d(e=E(s,e,\"message\")),o=e.length;s.push(c),a=E(s,a,\"nonce\");var h,p=0|r._crypto_box_curve25519xchacha20poly1305_noncebytes();a.length!==p&&f(s,\"invalid nonce length\"),h=d(a),s.push(h),t=E(s,t,\"publicKey\");var i,v=0|r._crypto_box_curve25519xchacha20poly1305_publickeybytes();t.length!==v&&f(s,\"invalid publicKey length\"),i=d(t),s.push(i),_=E(s,_,\"privateKey\");var m,x=0|r._crypto_box_curve25519xchacha20poly1305_secretkeybytes();_.length!==x&&f(s,\"invalid privateKey length\"),m=d(_),s.push(m);var k=new u(o+r._crypto_box_curve25519xchacha20poly1305_macbytes()|0),S=k.address;if(s.push(S),!(0|r._crypto_box_curve25519xchacha20poly1305_easy(S,c,o,0,h,i,m))){var T=y(k,n);return g(s),T}b(s,\"invalid usage\")}function fe(e,a,t,_){var n=[];l(_);var s=d(e=E(n,e,\"message\")),c=e.length;n.push(s),a=E(n,a,\"nonce\");var o,h=0|r._crypto_box_curve25519xchacha20poly1305_noncebytes();a.length!==h&&f(n,\"invalid nonce length\"),o=d(a),n.push(o),t=E(n,t,\"sharedKey\");var p,i=0|r._crypto_box_curve25519xchacha20poly1305_beforenmbytes();t.length!==i&&f(n,\"invalid sharedKey length\"),p=d(t),n.push(p);var v=new u(c+r._crypto_box_curve25519xchacha20poly1305_macbytes()|0),m=v.address;if(n.push(m),!(0|r._crypto_box_curve25519xchacha20poly1305_easy_afternm(m,s,c,0,o,p))){var x=y(v,_);return g(n),x}b(n,\"invalid usage\")}function me(e){var a=[];l(e);var t=new u(0|r._crypto_box_curve25519xchacha20poly1305_publickeybytes()),_=t.address;a.push(_);var n=new u(0|r._crypto_box_curve25519xchacha20poly1305_secretkeybytes()),s=n.address;a.push(s),r._crypto_box_curve25519xchacha20poly1305_keypair(_,s);var c=y({publicKey:t,privateKey:n,keyType:\"curve25519\"},e);return g(a),c}function Ee(e,a,t,_,n,s){var c=[];l(s);var o=d(e=E(c,e,\"ciphertext\")),h=e.length;c.push(o),a=E(c,a,\"mac\");var p,i=0|r._crypto_box_curve25519xchacha20poly1305_macbytes();a.length!==i&&f(c,\"invalid mac length\"),p=d(a),c.push(p),t=E(c,t,\"nonce\");var v,m=0|r._crypto_box_curve25519xchacha20poly1305_noncebytes();t.length!==m&&f(c,\"invalid nonce length\"),v=d(t),c.push(v),_=E(c,_,\"publicKey\");var x,k=0|r._crypto_box_curve25519xchacha20poly1305_publickeybytes();_.length!==k&&f(c,\"invalid publicKey length\"),x=d(_),c.push(x),n=E(c,n,\"privateKey\");var S,T=0|r._crypto_box_curve25519xchacha20poly1305_secretkeybytes();n.length!==T&&f(c,\"invalid privateKey length\"),S=d(n),c.push(S);var w=new u(0|h),Y=w.address;if(c.push(Y),!(0|r._crypto_box_curve25519xchacha20poly1305_open_detached(Y,o,p,h,0,v,x,S))){var B=y(w,s);return g(c),B}b(c,\"incorrect key pair for the given ciphertext\")}function xe(e,a,t,_,n){var s=[];l(n);var c=d(e=E(s,e,\"ciphertext\")),o=e.length;s.push(c),a=E(s,a,\"mac\");var h,p=0|r._crypto_box_curve25519xchacha20poly1305_macbytes();a.length!==p&&f(s,\"invalid mac length\"),h=d(a),s.push(h),t=E(s,t,\"nonce\");var i,v=0|r._crypto_box_curve25519xchacha20poly1305_noncebytes();t.length!==v&&f(s,\"invalid nonce length\"),i=d(t),s.push(i),_=E(s,_,\"sharedKey\");var m,x=0|r._crypto_box_curve25519xchacha20poly1305_beforenmbytes();_.length!==x&&f(s,\"invalid sharedKey length\"),m=d(_),s.push(m);var k=new u(0|o),S=k.address;if(s.push(S),!(0|r._crypto_box_curve25519xchacha20poly1305_open_detached_afternm(S,c,h,o,0,i,m))){var T=y(k,n);return g(s),T}b(s,\"incorrect secret key for the given ciphertext\")}function ke(e,a,t,_,n){var s=[];l(n),e=E(s,e,\"ciphertext\");var c,o=r._crypto_box_curve25519xchacha20poly1305_macbytes(),h=e.length;h<o&&f(s,\"ciphertext is too short\"),c=d(e),s.push(c),a=E(s,a,\"nonce\");var p,i=0|r._crypto_box_curve25519xchacha20poly1305_noncebytes();a.length!==i&&f(s,\"invalid nonce length\"),p=d(a),s.push(p),t=E(s,t,\"publicKey\");var v,m=0|r._crypto_box_curve25519xchacha20poly1305_publickeybytes();t.length!==m&&f(s,\"invalid publicKey length\"),v=d(t),s.push(v),_=E(s,_,\"privateKey\");var x,k=0|r._crypto_box_curve25519xchacha20poly1305_secretkeybytes();_.length!==k&&f(s,\"invalid privateKey length\"),x=d(_),s.push(x);var S=new u(h-r._crypto_box_curve25519xchacha20poly1305_macbytes()|0),T=S.address;if(s.push(T),!(0|r._crypto_box_curve25519xchacha20poly1305_open_easy(T,c,h,0,p,v,x))){var w=y(S,n);return g(s),w}b(s,\"incorrect key pair for the given ciphertext\")}function Se(e,a,t,_){var n=[];l(_);var s=d(e=E(n,e,\"ciphertext\")),c=e.length;n.push(s),a=E(n,a,\"nonce\");var o,h=0|r._crypto_box_curve25519xchacha20poly1305_noncebytes();a.length!==h&&f(n,\"invalid nonce length\"),o=d(a),n.push(o),t=E(n,t,\"sharedKey\");var p,i=0|r._crypto_box_curve25519xchacha20poly1305_beforenmbytes();t.length!==i&&f(n,\"invalid sharedKey length\"),p=d(t),n.push(p);var v=new u(c-r._crypto_box_curve25519xchacha20poly1305_macbytes()|0),m=v.address;if(n.push(m),!(0|r._crypto_box_curve25519xchacha20poly1305_open_easy_afternm(m,s,c,0,o,p))){var x=y(v,_);return g(n),x}b(n,\"incorrect secret key for the given ciphertext\")}function Te(e,a,t){var _=[];l(t);var n=d(e=E(_,e,\"message\")),s=e.length;_.push(n),a=E(_,a,\"publicKey\");var c,o=0|r._crypto_box_curve25519xchacha20poly1305_publickeybytes();a.length!==o&&f(_,\"invalid publicKey length\"),c=d(a),_.push(c);var h=new u(s+r._crypto_box_curve25519xchacha20poly1305_sealbytes()|0),p=h.address;_.push(p),r._crypto_box_curve25519xchacha20poly1305_seal(p,n,s,0,c);var i=y(h,t);return g(_),i}function we(e,a,t,_){var n=[];l(_),e=E(n,e,\"ciphertext\");var s,c=r._crypto_box_curve25519xchacha20poly1305_sealbytes(),o=e.length;o<c&&f(n,\"ciphertext is too short\"),s=d(e),n.push(s),a=E(n,a,\"publicKey\");var h,p=0|r._crypto_box_curve25519xchacha20poly1305_publickeybytes();a.length!==p&&f(n,\"invalid publicKey length\"),h=d(a),n.push(h),t=E(n,t,\"secretKey\");var i,v=0|r._crypto_box_curve25519xchacha20poly1305_secretkeybytes();t.length!==v&&f(n,\"invalid secretKey length\"),i=d(t),n.push(i);var b=new u(o-r._crypto_box_curve25519xchacha20poly1305_sealbytes()|0),m=b.address;n.push(m),r._crypto_box_curve25519xchacha20poly1305_seal_open(m,s,o,0,h,i);var x=y(b,_);return g(n),x}function Ye(e,a){var t=[];l(a),e=E(t,e,\"seed\");var _,n=0|r._crypto_box_curve25519xchacha20poly1305_seedbytes();e.length!==n&&f(t,\"invalid seed length\"),_=d(e),t.push(_);var s=new u(0|r._crypto_box_curve25519xchacha20poly1305_publickeybytes()),c=s.address;t.push(c);var o=new u(0|r._crypto_box_curve25519xchacha20poly1305_secretkeybytes()),h=o.address;if(t.push(h),!(0|r._crypto_box_curve25519xchacha20poly1305_seed_keypair(c,h,_))){var p={publicKey:y(s,a),privateKey:y(o,a),keyType:\"x25519\"};return g(t),p}b(t,\"invalid usage\")}function Be(e,a,t,_,n){var s=[];l(n);var c=d(e=E(s,e,\"message\")),o=e.length;s.push(c),a=E(s,a,\"nonce\");var h,p=0|r._crypto_box_noncebytes();a.length!==p&&f(s,\"invalid nonce length\"),h=d(a),s.push(h),t=E(s,t,\"publicKey\");var i,v=0|r._crypto_box_publickeybytes();t.length!==v&&f(s,\"invalid publicKey length\"),i=d(t),s.push(i),_=E(s,_,\"privateKey\");var m,x=0|r._crypto_box_secretkeybytes();_.length!==x&&f(s,\"invalid privateKey length\"),m=d(_),s.push(m);var k=new u(0|o),S=k.address;s.push(S);var T=new u(0|r._crypto_box_macbytes()),w=T.address;if(s.push(w),!(0|r._crypto_box_detached(S,w,c,o,0,h,i,m))){var Y=y({ciphertext:k,mac:T},n);return g(s),Y}b(s,\"invalid usage\")}function Ae(e,a,t,_,n){var s=[];l(n);var c=d(e=E(s,e,\"message\")),o=e.length;s.push(c),a=E(s,a,\"nonce\");var h,p=0|r._crypto_box_noncebytes();a.length!==p&&f(s,\"invalid nonce length\"),h=d(a),s.push(h),t=E(s,t,\"publicKey\");var i,v=0|r._crypto_box_publickeybytes();t.length!==v&&f(s,\"invalid publicKey length\"),i=d(t),s.push(i),_=E(s,_,\"privateKey\");var m,x=0|r._crypto_box_secretkeybytes();_.length!==x&&f(s,\"invalid privateKey length\"),m=d(_),s.push(m);var k=new u(o+r._crypto_box_macbytes()|0),S=k.address;if(s.push(S),!(0|r._crypto_box_easy(S,c,o,0,h,i,m))){var T=y(k,n);return g(s),T}b(s,\"invalid usage\")}function Me(e,a,t,_){var n=[];l(_);var s=d(e=E(n,e,\"message\")),c=e.length;n.push(s),a=E(n,a,\"nonce\");var o,h=0|r._crypto_box_noncebytes();a.length!==h&&f(n,\"invalid nonce length\"),o=d(a),n.push(o),t=E(n,t,\"sharedKey\");var p,i=0|r._crypto_box_beforenmbytes();t.length!==i&&f(n,\"invalid sharedKey length\"),p=d(t),n.push(p);var v=new u(c+r._crypto_box_macbytes()|0),m=v.address;if(n.push(m),!(0|r._crypto_box_easy_afternm(m,s,c,0,o,p))){var x=y(v,_);return g(n),x}b(n,\"invalid usage\")}function Ie(e){var a=[];l(e);var t=new u(0|r._crypto_box_publickeybytes()),_=t.address;a.push(_);var n=new u(0|r._crypto_box_secretkeybytes()),s=n.address;if(a.push(s),!(0|r._crypto_box_keypair(_,s))){var c={publicKey:y(t,e),privateKey:y(n,e),keyType:\"x25519\"};return g(a),c}b(a,\"internal error\")}function Ke(e,a,t,_,n,s){var c=[];l(s);var o=d(e=E(c,e,\"ciphertext\")),h=e.length;c.push(o),a=E(c,a,\"mac\");var p,i=0|r._crypto_box_macbytes();a.length!==i&&f(c,\"invalid mac length\"),p=d(a),c.push(p),t=E(c,t,\"nonce\");var v,m=0|r._crypto_box_noncebytes();t.length!==m&&f(c,\"invalid nonce length\"),v=d(t),c.push(v),_=E(c,_,\"publicKey\");var x,k=0|r._crypto_box_publickeybytes();_.length!==k&&f(c,\"invalid publicKey length\"),x=d(_),c.push(x),n=E(c,n,\"privateKey\");var S,T=0|r._crypto_box_secretkeybytes();n.length!==T&&f(c,\"invalid privateKey length\"),S=d(n),c.push(S);var w=new u(0|h),Y=w.address;if(c.push(Y),!(0|r._crypto_box_open_detached(Y,o,p,h,0,v,x,S))){var B=y(w,s);return g(c),B}b(c,\"incorrect key pair for the given ciphertext\")}function Ne(e,a,t,_,n){var s=[];l(n),e=E(s,e,\"ciphertext\");var c,o=r._crypto_box_macbytes(),h=e.length;h<o&&f(s,\"ciphertext is too short\"),c=d(e),s.push(c),a=E(s,a,\"nonce\");var p,i=0|r._crypto_box_noncebytes();a.length!==i&&f(s,\"invalid nonce length\"),p=d(a),s.push(p),t=E(s,t,\"publicKey\");var v,m=0|r._crypto_box_publickeybytes();t.length!==m&&f(s,\"invalid publicKey length\"),v=d(t),s.push(v),_=E(s,_,\"privateKey\");var x,k=0|r._crypto_box_secretkeybytes();_.length!==k&&f(s,\"invalid privateKey length\"),x=d(_),s.push(x);var S=new u(h-r._crypto_box_macbytes()|0),T=S.address;if(s.push(T),!(0|r._crypto_box_open_easy(T,c,h,0,p,v,x))){var w=y(S,n);return g(s),w}b(s,\"incorrect key pair for the given ciphertext\")}function Le(e,a,t,_){var n=[];l(_);var s=d(e=E(n,e,\"ciphertext\")),c=e.length;n.push(s),a=E(n,a,\"nonce\");var o,h=0|r._crypto_box_noncebytes();a.length!==h&&f(n,\"invalid nonce length\"),o=d(a),n.push(o),t=E(n,t,\"sharedKey\");var p,i=0|r._crypto_box_beforenmbytes();t.length!==i&&f(n,\"invalid sharedKey length\"),p=d(t),n.push(p);var v=new u(c-r._crypto_box_macbytes()|0),m=v.address;if(n.push(m),!(0|r._crypto_box_open_easy_afternm(m,s,c,0,o,p))){var x=y(v,_);return g(n),x}b(n,\"incorrect secret key for the given ciphertext\")}function Oe(e,a,t){var _=[];l(t);var n=d(e=E(_,e,\"message\")),s=e.length;_.push(n),a=E(_,a,\"publicKey\");var c,o=0|r._crypto_box_publickeybytes();a.length!==o&&f(_,\"invalid publicKey length\"),c=d(a),_.push(c);var h=new u(s+r._crypto_box_sealbytes()|0),p=h.address;if(_.push(p),!(0|r._crypto_box_seal(p,n,s,0,c))){var i=y(h,t);return g(_),i}b(_,\"invalid usage\")}function Ue(e,a,t,_){var n=[];l(_),e=E(n,e,\"ciphertext\");var s,c=r._crypto_box_sealbytes(),o=e.length;o<c&&f(n,\"ciphertext is too short\"),s=d(e),n.push(s),a=E(n,a,\"publicKey\");var h,p=0|r._crypto_box_publickeybytes();a.length!==p&&f(n,\"invalid publicKey length\"),h=d(a),n.push(h),t=E(n,t,\"privateKey\");var i,v=0|r._crypto_box_secretkeybytes();t.length!==v&&f(n,\"invalid privateKey length\"),i=d(t),n.push(i);var m=new u(o-r._crypto_box_sealbytes()|0),x=m.address;if(n.push(x),!(0|r._crypto_box_seal_open(x,s,o,0,h,i))){var k=y(m,_);return g(n),k}b(n,\"incorrect key pair for the given ciphertext\")}function Ce(e,a){var t=[];l(a),e=E(t,e,\"seed\");var _,n=0|r._crypto_box_seedbytes();e.length!==n&&f(t,\"invalid seed length\"),_=d(e),t.push(_);var s=new u(0|r._crypto_box_publickeybytes()),c=s.address;t.push(c);var o=new u(0|r._crypto_box_secretkeybytes()),h=o.address;if(t.push(h),!(0|r._crypto_box_seed_keypair(c,h,_))){var p={publicKey:y(s,a),privateKey:y(o,a),keyType:\"x25519\"};return g(t),p}b(t,\"invalid usage\")}function Pe(e,a,t){var _=[];l(t),e=E(_,e,\"p\");var n,s=0|r._crypto_core_ed25519_bytes();e.length!==s&&f(_,\"invalid p length\"),n=d(e),_.push(n),a=E(_,a,\"q\");var c,o=0|r._crypto_core_ed25519_bytes();a.length!==o&&f(_,\"invalid q length\"),c=d(a),_.push(c);var h=new u(0|r._crypto_core_ed25519_bytes()),p=h.address;if(_.push(p),!(0|r._crypto_core_ed25519_add(p,n,c))){var i=y(h,t);return g(_),i}b(_,\"input is an invalid element\")}function Re(e,a){var t=[];l(a);var _=d(e=E(t,e,\"r\"));e.length,t.push(_);var n=new u(0|r._crypto_core_ed25519_bytes()),s=n.address;if(t.push(s),!(0|r._crypto_core_ed25519_from_hash(s,_))){var c=y(n,a);return g(t),c}b(t,\"invalid usage\")}function Xe(e,a){var t=[];l(a);var _=d(e=E(t,e,\"r\"));e.length,t.push(_);var n=new u(0|r._crypto_core_ed25519_bytes()),s=n.address;if(t.push(s),!(0|r._crypto_core_ed25519_from_uniform(s,_))){var c=y(n,a);return g(t),c}b(t,\"invalid usage\")}function Ge(e,a){var t=[];l(a),e=E(t,e,\"repr\");var _,n=0|r._crypto_core_ed25519_bytes();e.length!==n&&f(t,\"invalid repr length\"),_=d(e),t.push(_);var s=1==(0|r._crypto_core_ed25519_is_valid_point(_));return g(t),s}function De(e){var a=[];l(e);var t=new u(0|r._crypto_core_ed25519_bytes()),_=t.address;a.push(_),r._crypto_core_ed25519_random(_);var n=y(t,e);return g(a),n}function Fe(e,a,t){var _=[];l(t),e=E(_,e,\"x\");var n,s=0|r._crypto_core_ed25519_scalarbytes();e.length!==s&&f(_,\"invalid x length\"),n=d(e),_.push(n),a=E(_,a,\"y\");var c,o=0|r._crypto_core_ed25519_scalarbytes();a.length!==o&&f(_,\"invalid y length\"),c=d(a),_.push(c);var h=new u(0|r._crypto_core_ed25519_scalarbytes()),p=h.address;_.push(p),r._crypto_core_ed25519_scalar_add(p,n,c);var i=y(h,t);return g(_),i}function Ve(e,a){var t=[];l(a),e=E(t,e,\"s\");var _,n=0|r._crypto_core_ed25519_scalarbytes();e.length!==n&&f(t,\"invalid s length\"),_=d(e),t.push(_);var s=new u(0|r._crypto_core_ed25519_scalarbytes()),c=s.address;t.push(c),r._crypto_core_ed25519_scalar_complement(c,_);var o=y(s,a);return g(t),o}function He(e,a){var t=[];l(a),e=E(t,e,\"s\");var _,n=0|r._crypto_core_ed25519_scalarbytes();e.length!==n&&f(t,\"invalid s length\"),_=d(e),t.push(_);var s=new u(0|r._crypto_core_ed25519_scalarbytes()),c=s.address;if(t.push(c),!(0|r._crypto_core_ed25519_scalar_invert(c,_))){var o=y(s,a);return g(t),o}b(t,\"invalid reciprocate\")}function We(e,a,t){var _=[];l(t),e=E(_,e,\"x\");var n,s=0|r._crypto_core_ed25519_scalarbytes();e.length!==s&&f(_,\"invalid x length\"),n=d(e),_.push(n),a=E(_,a,\"y\");var c,o=0|r._crypto_core_ed25519_scalarbytes();a.length!==o&&f(_,\"invalid y length\"),c=d(a),_.push(c);var h=new u(0|r._crypto_core_ed25519_scalarbytes()),p=h.address;_.push(p),r._crypto_core_ed25519_scalar_mul(p,n,c);var i=y(h,t);return g(_),i}function qe(e,a){var t=[];l(a),e=E(t,e,\"s\");var _,n=0|r._crypto_core_ed25519_scalarbytes();e.length!==n&&f(t,\"invalid s length\"),_=d(e),t.push(_);var s=new u(0|r._crypto_core_ed25519_scalarbytes()),c=s.address;t.push(c),r._crypto_core_ed25519_scalar_negate(c,_);var o=y(s,a);return g(t),o}function je(e){var a=[];l(e);var t=new u(0|r._crypto_core_ed25519_scalarbytes()),_=t.address;a.push(_),r._crypto_core_ed25519_scalar_random(_);var n=y(t,e);return g(a),n}function ze(e,a){var t=[];l(a),e=E(t,e,\"sample\");var _,n=0|r._crypto_core_ed25519_nonreducedscalarbytes();e.length!==n&&f(t,\"invalid sample length\"),_=d(e),t.push(_);var s=new u(0|r._crypto_core_ed25519_scalarbytes()),c=s.address;t.push(c),r._crypto_core_ed25519_scalar_reduce(c,_);var o=y(s,a);return g(t),o}function Je(e,a,t){var _=[];l(t),e=E(_,e,\"x\");var n,s=0|r._crypto_core_ed25519_scalarbytes();e.length!==s&&f(_,\"invalid x length\"),n=d(e),_.push(n),a=E(_,a,\"y\");var c,o=0|r._crypto_core_ed25519_scalarbytes();a.length!==o&&f(_,\"invalid y length\"),c=d(a),_.push(c);var h=new u(0|r._crypto_core_ed25519_scalarbytes()),p=h.address;_.push(p),r._crypto_core_ed25519_scalar_sub(p,n,c);var i=y(h,t);return g(_),i}function Qe(e,a,t){var _=[];l(t),e=E(_,e,\"p\");var n,s=0|r._crypto_core_ed25519_bytes();e.length!==s&&f(_,\"invalid p length\"),n=d(e),_.push(n),a=E(_,a,\"q\");var c,o=0|r._crypto_core_ed25519_bytes();a.length!==o&&f(_,\"invalid q length\"),c=d(a),_.push(c);var h=new u(0|r._crypto_core_ed25519_bytes()),p=h.address;if(_.push(p),!(0|r._crypto_core_ed25519_sub(p,n,c))){var i=y(h,t);return g(_),i}b(_,\"input is an invalid element\")}function Ze(e,a,t,_){var n=[];l(_),e=E(n,e,\"input\");var s,c=0|r._crypto_core_hchacha20_inputbytes();e.length!==c&&f(n,\"invalid input length\"),s=d(e),n.push(s),a=E(n,a,\"privateKey\");var o,h=0|r._crypto_core_hchacha20_keybytes();a.length!==h&&f(n,\"invalid privateKey length\"),o=d(a),n.push(o);var p=null;null!=t&&(p=d(t=E(n,t,\"constant\")),t.length,n.push(p));var i=new u(0|r._crypto_core_hchacha20_outputbytes()),v=i.address;if(n.push(v),!(0|r._crypto_core_hchacha20(v,s,o,p))){var m=y(i,_);return g(n),m}b(n,\"invalid usage\")}function $e(e,a,t,_){var n=[];l(_),e=E(n,e,\"input\");var s,c=0|r._crypto_core_hsalsa20_inputbytes();e.length!==c&&f(n,\"invalid input length\"),s=d(e),n.push(s),a=E(n,a,\"privateKey\");var o,h=0|r._crypto_core_hsalsa20_keybytes();a.length!==h&&f(n,\"invalid privateKey length\"),o=d(a),n.push(o);var p=null;null!=t&&(p=d(t=E(n,t,\"constant\")),t.length,n.push(p));var i=new u(0|r._crypto_core_hsalsa20_outputbytes()),v=i.address;if(n.push(v),!(0|r._crypto_core_hsalsa20(v,s,o,p))){var m=y(i,_);return g(n),m}b(n,\"invalid usage\")}function ea(e,a,t){var _=[];l(t),e=E(_,e,\"p\");var n,s=0|r._crypto_core_ristretto255_bytes();e.length!==s&&f(_,\"invalid p length\"),n=d(e),_.push(n),a=E(_,a,\"q\");var c,o=0|r._crypto_core_ristretto255_bytes();a.length!==o&&f(_,\"invalid q length\"),c=d(a),_.push(c);var h=new u(0|r._crypto_core_ristretto255_bytes()),p=h.address;if(_.push(p),!(0|r._crypto_core_ristretto255_add(p,n,c))){var i=y(h,t);return g(_),i}b(_,\"input is an invalid element\")}function aa(e,a){var t=[];l(a);var _=d(e=E(t,e,\"r\"));e.length,t.push(_);var n=new u(0|r._crypto_core_ristretto255_bytes()),s=n.address;if(t.push(s),!(0|r._crypto_core_ristretto255_from_hash(s,_))){var c=y(n,a);return g(t),c}b(t,\"invalid usage\")}function ra(e,a){var t=[];l(a),e=E(t,e,\"repr\");var _,n=0|r._crypto_core_ristretto255_bytes();e.length!==n&&f(t,\"invalid repr length\"),_=d(e),t.push(_);var s=1==(0|r._crypto_core_ristretto255_is_valid_point(_));return g(t),s}function ta(e){var a=[];l(e);var t=new u(0|r._crypto_core_ristretto255_bytes()),_=t.address;a.push(_),r._crypto_core_ristretto255_random(_);var n=y(t,e);return g(a),n}function _a(e,a,t){var _=[];l(t),e=E(_,e,\"x\");var n,s=0|r._crypto_core_ristretto255_scalarbytes();e.length!==s&&f(_,\"invalid x length\"),n=d(e),_.push(n),a=E(_,a,\"y\");var c,o=0|r._crypto_core_ristretto255_scalarbytes();a.length!==o&&f(_,\"invalid y length\"),c=d(a),_.push(c);var h=new u(0|r._crypto_core_ristretto255_scalarbytes()),p=h.address;_.push(p),r._crypto_core_ristretto255_scalar_add(p,n,c);var i=y(h,t);return g(_),i}function na(e,a){var t=[];l(a),e=E(t,e,\"s\");var _,n=0|r._crypto_core_ristretto255_scalarbytes();e.length!==n&&f(t,\"invalid s length\"),_=d(e),t.push(_);var s=new u(0|r._crypto_core_ristretto255_scalarbytes()),c=s.address;t.push(c),r._crypto_core_ristretto255_scalar_complement(c,_);var o=y(s,a);return g(t),o}function sa(e,a){var t=[];l(a),e=E(t,e,\"s\");var _,n=0|r._crypto_core_ristretto255_scalarbytes();e.length!==n&&f(t,\"invalid s length\"),_=d(e),t.push(_);var s=new u(0|r._crypto_core_ristretto255_scalarbytes()),c=s.address;if(t.push(c),!(0|r._crypto_core_ristretto255_scalar_invert(c,_))){var o=y(s,a);return g(t),o}b(t,\"invalid reciprocate\")}function ca(e,a,t){var _=[];l(t),e=E(_,e,\"x\");var n,s=0|r._crypto_core_ristretto255_scalarbytes();e.length!==s&&f(_,\"invalid x length\"),n=d(e),_.push(n),a=E(_,a,\"y\");var c,o=0|r._crypto_core_ristretto255_scalarbytes();a.length!==o&&f(_,\"invalid y length\"),c=d(a),_.push(c);var h=new u(0|r._crypto_core_ristretto255_scalarbytes()),p=h.address;_.push(p),r._crypto_core_ristretto255_scalar_mul(p,n,c);var i=y(h,t);return g(_),i}function oa(e,a){var t=[];l(a),e=E(t,e,\"s\");var _,n=0|r._crypto_core_ristretto255_scalarbytes();e.length!==n&&f(t,\"invalid s length\"),_=d(e),t.push(_);var s=new u(0|r._crypto_core_ristretto255_scalarbytes()),c=s.address;t.push(c),r._crypto_core_ristretto255_scalar_negate(c,_);var o=y(s,a);return g(t),o}function ha(e){var a=[];l(e);var t=new u(0|r._crypto_core_ristretto255_scalarbytes()),_=t.address;a.push(_),r._crypto_core_ristretto255_scalar_random(_);var n=y(t,e);return g(a),n}function pa(e,a){var t=[];l(a),e=E(t,e,\"sample\");var _,n=0|r._crypto_core_ristretto255_nonreducedscalarbytes();e.length!==n&&f(t,\"invalid sample length\"),_=d(e),t.push(_);var s=new u(0|r._crypto_core_ristretto255_scalarbytes()),c=s.address;t.push(c),r._crypto_core_ristretto255_scalar_reduce(c,_);var o=y(s,a);return g(t),o}function ya(e,a,t){var _=[];l(t),e=E(_,e,\"x\");var n,s=0|r._crypto_core_ristretto255_scalarbytes();e.length!==s&&f(_,\"invalid x length\"),n=d(e),_.push(n),a=E(_,a,\"y\");var c,o=0|r._crypto_core_ristretto255_scalarbytes();a.length!==o&&f(_,\"invalid y length\"),c=d(a),_.push(c);var h=new u(0|r._crypto_core_ristretto255_scalarbytes()),p=h.address;_.push(p),r._crypto_core_ristretto255_scalar_sub(p,n,c);var i=y(h,t);return g(_),i}function ia(e,a,t){var _=[];l(t),e=E(_,e,\"p\");var n,s=0|r._crypto_core_ristretto255_bytes();e.length!==s&&f(_,\"invalid p length\"),n=d(e),_.push(n),a=E(_,a,\"q\");var c,o=0|r._crypto_core_ristretto255_bytes();a.length!==o&&f(_,\"invalid q length\"),c=d(a),_.push(c);var h=new u(0|r._crypto_core_ristretto255_bytes()),p=h.address;if(_.push(p),!(0|r._crypto_core_ristretto255_sub(p,n,c))){var i=y(h,t);return g(_),i}b(_,\"input is an invalid element\")}function la(e,a,t,_){var n=[];l(_),m(n,e,\"hash_length\"),(\"number\"!=typeof e||(0|e)!==e||e<0)&&f(n,\"hash_length must be an unsigned integer\");var s=d(a=E(n,a,\"message\")),c=a.length;n.push(s);var o=null,h=0;null!=t&&(o=d(t=E(n,t,\"key\")),h=t.length,n.push(o));var p=new u(e|=0),i=p.address;if(n.push(i),!(0|r._crypto_generichash(i,e,s,c,0,o,h))){var v=y(p,_);return g(n),v}b(n,\"invalid usage\")}function ua(e,a,t,_,n){var s=[];l(n),m(s,e,\"subkey_len\"),(\"number\"!=typeof e||(0|e)!==e||e<0)&&f(s,\"subkey_len must be an unsigned integer\");var c=null,o=0;null!=a&&(c=d(a=E(s,a,\"key\")),o=a.length,s.push(c));var h=null,p=0;null!=t&&(t=E(s,t,\"id\"),p=0|r._crypto_generichash_blake2b_saltbytes(),t.length!==p&&f(s,\"invalid id length\"),h=d(t),s.push(h));var i=null,v=0;null!=_&&(_=E(s,_,\"ctx\"),v=0|r._crypto_generichash_blake2b_personalbytes(),_.length!==v&&f(s,\"invalid ctx length\"),i=d(_),s.push(i));var x=new u(0|e),k=x.address;if(s.push(k),!(0|r._crypto_generichash_blake2b_salt_personal(k,e,null,0,0,c,o,h,i))){var S=y(x,n);return g(s),S}b(s,\"invalid usage\")}function da(e,a,t){var _=[];l(t),m(_,e,\"state_address\"),m(_,a,\"hash_length\"),(\"number\"!=typeof a||(0|a)!==a||a<0)&&f(_,\"hash_length must be an unsigned integer\");var n=new u(a|=0),s=n.address;if(_.push(s),!(0|r._crypto_generichash_final(e,s,a))){var c=(r._free(e),y(n,t));return g(_),c}b(_,\"invalid usage\")}function va(e,a,t){var _=[];l(t);var n=null,s=0;null!=e&&(n=d(e=E(_,e,\"key\")),s=e.length,_.push(n)),m(_,a,\"hash_length\"),(\"number\"!=typeof a||(0|a)!==a||a<0)&&f(_,\"hash_length must be an unsigned integer\");var c=new u(357).address;if(!(0|r._crypto_generichash_init(c,n,s,a))){var o=c;return g(_),o}b(_,\"invalid usage\")}function ga(e){var a=[];l(e);var t=new u(0|r._crypto_generichash_keybytes()),_=t.address;a.push(_),r._crypto_generichash_keygen(_);var n=y(t,e);return g(a),n}function ba(e,a,t){var _=[];l(t),m(_,e,\"state_address\");var n=d(a=E(_,a,\"message_chunk\")),s=a.length;_.push(n),0|r._crypto_generichash_update(e,n,s)&&b(_,\"invalid usage\"),g(_)}function fa(e,a){var t=[];l(a);var _=d(e=E(t,e,\"message\")),n=e.length;t.push(_);var s=new u(0|r._crypto_hash_bytes()),c=s.address;if(t.push(c),!(0|r._crypto_hash(c,_,n,0))){var o=y(s,a);return g(t),o}b(t,\"invalid usage\")}function ma(e,a){var t=[];l(a);var _=d(e=E(t,e,\"message\")),n=e.length;t.push(_);var s=new u(0|r._crypto_hash_sha256_bytes()),c=s.address;if(t.push(c),!(0|r._crypto_hash_sha256(c,_,n,0))){var o=y(s,a);return g(t),o}b(t,\"invalid usage\")}function Ea(e,a){var t=[];l(a),m(t,e,\"state_address\");var _=new u(0|r._crypto_hash_sha256_bytes()),n=_.address;if(t.push(n),!(0|r._crypto_hash_sha256_final(e,n))){var s=(r._free(e),y(_,a));return g(t),s}b(t,\"invalid usage\")}function xa(e){var a=[];l(e);var t=new u(104).address;if(!(0|r._crypto_hash_sha256_init(t))){var _=t;return g(a),_}b(a,\"invalid usage\")}function ka(e,a,t){var _=[];l(t),m(_,e,\"state_address\");var n=d(a=E(_,a,\"message_chunk\")),s=a.length;_.push(n),0|r._crypto_hash_sha256_update(e,n,s)&&b(_,\"invalid usage\"),g(_)}function Sa(e,a){var t=[];l(a);var _=d(e=E(t,e,\"message\")),n=e.length;t.push(_);var s=new u(0|r._crypto_hash_sha512_bytes()),c=s.address;if(t.push(c),!(0|r._crypto_hash_sha512(c,_,n,0))){var o=y(s,a);return g(t),o}b(t,\"invalid usage\")}function Ta(e,a){var t=[];l(a),m(t,e,\"state_address\");var _=new u(0|r._crypto_hash_sha512_bytes()),n=_.address;if(t.push(n),!(0|r._crypto_hash_sha512_final(e,n))){var s=(r._free(e),y(_,a));return g(t),s}b(t,\"invalid usage\")}function wa(e){var a=[];l(e);var t=new u(208).address;if(!(0|r._crypto_hash_sha512_init(t))){var _=t;return g(a),_}b(a,\"invalid usage\")}function Ya(e,a,t){var _=[];l(t),m(_,e,\"state_address\");var n=d(a=E(_,a,\"message_chunk\")),s=a.length;_.push(n),0|r._crypto_hash_sha512_update(e,n,s)&&b(_,\"invalid usage\"),g(_)}function Ba(e,a,t,_,s){var c=[];l(s),m(c,e,\"subkey_len\"),(\"number\"!=typeof e||(0|e)!==e||e<0)&&f(c,\"subkey_len must be an unsigned integer\"),m(c,a,\"subkey_id\");var o,h=0;if(\"bigint\"==typeof a&&a>=BigInt(0)){const e=a>>BigInt(32);e>BigInt(4294967295)&&f(c,\"subkey_id cannot be more than 64 bits\"),h=Number(e),o=Number(a&BigInt(4294967295))}else\"number\"==typeof a&&(0|a)===a&&a>=0?o=a:f(c,\"subkey_id must be an unsigned integer or bigint\");\"string\"!=typeof t&&f(c,\"ctx must be a string\"),t=n(t+\"\\0\"),null!=i&&t.length-1!==i&&f(c,\"invalid ctx length\");var p=d(t),i=t.length-1;c.push(p),_=E(c,_,\"key\");var v,b=0|r._crypto_kdf_keybytes();_.length!==b&&f(c,\"invalid key length\"),v=d(_),c.push(v);var x=new u(0|e),k=x.address;c.push(k),r._crypto_kdf_derive_from_key(k,e,o,h,p,v);var S=y(x,s);return g(c),S}function Aa(e){var a=[];l(e);var t=new u(0|r._crypto_kdf_keybytes()),_=t.address;a.push(_),r._crypto_kdf_keygen(_);var n=y(t,e);return g(a),n}function Ma(e,a,t,_){var n=[];l(_),e=E(n,e,\"clientPublicKey\");var s,c=0|r._crypto_kx_publickeybytes();e.length!==c&&f(n,\"invalid clientPublicKey length\"),s=d(e),n.push(s),a=E(n,a,\"clientSecretKey\");var o,h=0|r._crypto_kx_secretkeybytes();a.length!==h&&f(n,\"invalid clientSecretKey length\"),o=d(a),n.push(o),t=E(n,t,\"serverPublicKey\");var p,i=0|r._crypto_kx_publickeybytes();t.length!==i&&f(n,\"invalid serverPublicKey length\"),p=d(t),n.push(p);var v=new u(0|r._crypto_kx_sessionkeybytes()),m=v.address;n.push(m);var x=new u(0|r._crypto_kx_sessionkeybytes()),k=x.address;if(n.push(k),!(0|r._crypto_kx_client_session_keys(m,k,s,o,p))){var S=y({sharedRx:v,sharedTx:x},_);return g(n),S}b(n,\"invalid usage\")}function Ia(e){var a=[];l(e);var t=new u(0|r._crypto_kx_publickeybytes()),_=t.address;a.push(_);var n=new u(0|r._crypto_kx_secretkeybytes()),s=n.address;if(a.push(s),!(0|r._crypto_kx_keypair(_,s))){var c={publicKey:y(t,e),privateKey:y(n,e),keyType:\"x25519\"};return g(a),c}b(a,\"internal error\")}function Ka(e,a){var t=[];l(a),e=E(t,e,\"seed\");var _,n=0|r._crypto_kx_seedbytes();e.length!==n&&f(t,\"invalid seed length\"),_=d(e),t.push(_);var s=new u(0|r._crypto_kx_publickeybytes()),c=s.address;t.push(c);var o=new u(0|r._crypto_kx_secretkeybytes()),h=o.address;if(t.push(h),!(0|r._crypto_kx_seed_keypair(c,h,_))){var p={publicKey:y(s,a),privateKey:y(o,a),keyType:\"x25519\"};return g(t),p}b(t,\"internal error\")}function Na(e,a,t,_){var n=[];l(_),e=E(n,e,\"serverPublicKey\");var s,c=0|r._crypto_kx_publickeybytes();e.length!==c&&f(n,\"invalid serverPublicKey length\"),s=d(e),n.push(s),a=E(n,a,\"serverSecretKey\");var o,h=0|r._crypto_kx_secretkeybytes();a.length!==h&&f(n,\"invalid serverSecretKey length\"),o=d(a),n.push(o),t=E(n,t,\"clientPublicKey\");var p,i=0|r._crypto_kx_publickeybytes();t.length!==i&&f(n,\"invalid clientPublicKey length\"),p=d(t),n.push(p);var v=new u(0|r._crypto_kx_sessionkeybytes()),m=v.address;n.push(m);var x=new u(0|r._crypto_kx_sessionkeybytes()),k=x.address;if(n.push(k),!(0|r._crypto_kx_server_session_keys(m,k,s,o,p))){var S=y({sharedRx:v,sharedTx:x},_);return g(n),S}b(n,\"invalid usage\")}function La(e,a,t){var _=[];l(t);var n=d(e=E(_,e,\"message\")),s=e.length;_.push(n),a=E(_,a,\"key\");var c,o=0|r._crypto_onetimeauth_keybytes();a.length!==o&&f(_,\"invalid key length\"),c=d(a),_.push(c);var h=new u(0|r._crypto_onetimeauth_bytes()),p=h.address;if(_.push(p),!(0|r._crypto_onetimeauth(p,n,s,0,c))){var i=y(h,t);return g(_),i}b(_,\"invalid usage\")}function Oa(e,a){var t=[];l(a),m(t,e,\"state_address\");var _=new u(0|r._crypto_onetimeauth_bytes()),n=_.address;if(t.push(n),!(0|r._crypto_onetimeauth_final(e,n))){var s=(r._free(e),y(_,a));return g(t),s}b(t,\"invalid usage\")}function Ua(e,a){var t=[];l(a);var _=null;null!=e&&(_=d(e=E(t,e,\"key\")),e.length,t.push(_));var n=new u(144).address;if(!(0|r._crypto_onetimeauth_init(n,_))){var s=n;return g(t),s}b(t,\"invalid usage\")}function Ca(e){var a=[];l(e);var t=new u(0|r._crypto_onetimeauth_keybytes()),_=t.address;a.push(_),r._crypto_onetimeauth_keygen(_);var n=y(t,e);return g(a),n}function Pa(e,a,t){var _=[];l(t),m(_,e,\"state_address\");var n=d(a=E(_,a,\"message_chunk\")),s=a.length;_.push(n),0|r._crypto_onetimeauth_update(e,n,s)&&b(_,\"invalid usage\"),g(_)}function Ra(e,a,t){var _=[];e=E(_,e,\"hash\");var n,s=0|r._crypto_onetimeauth_bytes();e.length!==s&&f(_,\"invalid hash length\"),n=d(e),_.push(n);var c=d(a=E(_,a,\"message\")),o=a.length;_.push(c),t=E(_,t,\"key\");var h,p=0|r._crypto_onetimeauth_keybytes();t.length!==p&&f(_,\"invalid key length\"),h=d(t),_.push(h);var y=!(0|r._crypto_onetimeauth_verify(n,c,o,0,h));return g(_),y}function Xa(e,a,t,_,n,s,c){var o=[];l(c),m(o,e,\"keyLength\"),(\"number\"!=typeof e||(0|e)!==e||e<0)&&f(o,\"keyLength must be an unsigned integer\");var h=d(a=E(o,a,\"password\")),p=a.length;o.push(h),t=E(o,t,\"salt\");var i,v=0|r._crypto_pwhash_saltbytes();t.length!==v&&f(o,\"invalid salt length\"),i=d(t),o.push(i),m(o,_,\"opsLimit\"),(\"number\"!=typeof _||(0|_)!==_||_<0)&&f(o,\"opsLimit must be an unsigned integer\"),m(o,n,\"memLimit\"),(\"number\"!=typeof n||(0|n)!==n||n<0)&&f(o,\"memLimit must be an unsigned integer\"),m(o,s,\"algorithm\"),(\"number\"!=typeof s||(0|s)!==s||s<0)&&f(o,\"algorithm must be an unsigned integer\");var x=new u(0|e),k=x.address;if(o.push(k),!(0|r._crypto_pwhash(k,e,0,h,p,0,i,_,0,n,s))){var S=y(x,c);return g(o),S}b(o,\"invalid usage\")}function Ga(e,a,t,_,n,s){var c=[];l(s),m(c,e,\"keyLength\"),(\"number\"!=typeof e||(0|e)!==e||e<0)&&f(c,\"keyLength must be an unsigned integer\");var o=d(a=E(c,a,\"password\")),h=a.length;c.push(o),t=E(c,t,\"salt\");var p,i=0|r._crypto_pwhash_scryptsalsa208sha256_saltbytes();t.length!==i&&f(c,\"invalid salt length\"),p=d(t),c.push(p),m(c,_,\"opsLimit\"),(\"number\"!=typeof _||(0|_)!==_||_<0)&&f(c,\"opsLimit must be an unsigned integer\"),m(c,n,\"memLimit\"),(\"number\"!=typeof n||(0|n)!==n||n<0)&&f(c,\"memLimit must be an unsigned integer\");var v=new u(0|e),x=v.address;if(c.push(x),!(0|r._crypto_pwhash_scryptsalsa208sha256(x,e,0,o,h,0,p,_,0,n))){var k=y(v,s);return g(c),k}b(c,\"invalid usage\")}function Da(e,a,t,_,n,s,c){var o=[];l(c);var h=d(e=E(o,e,\"password\")),p=e.length;o.push(h);var i=d(a=E(o,a,\"salt\")),v=a.length;o.push(i),m(o,t,\"opsLimit\"),(\"number\"!=typeof t||(0|t)!==t||t<0)&&f(o,\"opsLimit must be an unsigned integer\"),m(o,_,\"r\"),(\"number\"!=typeof _||(0|_)!==_||_<0)&&f(o,\"r must be an unsigned integer\"),m(o,n,\"p\"),(\"number\"!=typeof n||(0|n)!==n||n<0)&&f(o,\"p must be an unsigned integer\"),m(o,s,\"keyLength\"),(\"number\"!=typeof s||(0|s)!==s||s<0)&&f(o,\"keyLength must be an unsigned integer\");var x=new u(0|s),k=x.address;if(o.push(k),!(0|r._crypto_pwhash_scryptsalsa208sha256_ll(h,p,i,v,t,0,_,n,k,s))){var S=y(x,c);return g(o),S}b(o,\"invalid usage\")}function Fa(e,a,t,_){var n=[];l(_);var s=d(e=E(n,e,\"password\")),c=e.length;n.push(s),m(n,a,\"opsLimit\"),(\"number\"!=typeof a||(0|a)!==a||a<0)&&f(n,\"opsLimit must be an unsigned integer\"),m(n,t,\"memLimit\"),(\"number\"!=typeof t||(0|t)!==t||t<0)&&f(n,\"memLimit must be an unsigned integer\");var o=new u(0|r._crypto_pwhash_scryptsalsa208sha256_strbytes()).address;if(n.push(o),!(0|r._crypto_pwhash_scryptsalsa208sha256_str(o,s,c,0,a,0,t))){var h=r.UTF8ToString(o);return g(n),h}b(n,\"invalid usage\")}function Va(e,a,t){var _=[];l(t),\"string\"!=typeof e&&f(_,\"hashed_password must be a string\"),e=n(e+\"\\0\"),null!=c&&e.length-1!==c&&f(_,\"invalid hashed_password length\");var s=d(e),c=e.length-1;_.push(s);var o=d(a=E(_,a,\"password\")),h=a.length;_.push(o);var p=!(0|r._crypto_pwhash_scryptsalsa208sha256_str_verify(s,o,h,0));return g(_),p}function Ha(e,a,t,_){var n=[];l(_);var s=d(e=E(n,e,\"password\")),c=e.length;n.push(s),m(n,a,\"opsLimit\"),(\"number\"!=typeof a||(0|a)!==a||a<0)&&f(n,\"opsLimit must be an unsigned integer\"),m(n,t,\"memLimit\"),(\"number\"!=typeof t||(0|t)!==t||t<0)&&f(n,\"memLimit must be an unsigned integer\");var o=new u(0|r._crypto_pwhash_strbytes()).address;if(n.push(o),!(0|r._crypto_pwhash_str(o,s,c,0,a,0,t))){var h=r.UTF8ToString(o);return g(n),h}b(n,\"invalid usage\")}function Wa(e,a,t,_){var s=[];l(_),\"string\"!=typeof e&&f(s,\"hashed_password must be a string\"),e=n(e+\"\\0\"),null!=o&&e.length-1!==o&&f(s,\"invalid hashed_password length\");var c=d(e),o=e.length-1;s.push(c),m(s,a,\"opsLimit\"),(\"number\"!=typeof a||(0|a)!==a||a<0)&&f(s,\"opsLimit must be an unsigned integer\"),m(s,t,\"memLimit\"),(\"number\"!=typeof t||(0|t)!==t||t<0)&&f(s,\"memLimit must be an unsigned integer\");var h=!!(0|r._crypto_pwhash_str_needs_rehash(c,a,0,t));return g(s),h}function qa(e,a,t){var _=[];l(t),\"string\"!=typeof e&&f(_,\"hashed_password must be a string\"),e=n(e+\"\\0\"),null!=c&&e.length-1!==c&&f(_,\"invalid hashed_password length\");var s=d(e),c=e.length-1;_.push(s);var o=d(a=E(_,a,\"password\")),h=a.length;_.push(o);var p=!(0|r._crypto_pwhash_str_verify(s,o,h,0));return g(_),p}function ja(e,a,t){var _=[];l(t),e=E(_,e,\"privateKey\");var n,s=0|r._crypto_scalarmult_scalarbytes();e.length!==s&&f(_,\"invalid privateKey length\"),n=d(e),_.push(n),a=E(_,a,\"publicKey\");var c,o=0|r._crypto_scalarmult_bytes();a.length!==o&&f(_,\"invalid publicKey length\"),c=d(a),_.push(c);var h=new u(0|r._crypto_scalarmult_bytes()),p=h.address;if(_.push(p),!(0|r._crypto_scalarmult(p,n,c))){var i=y(h,t);return g(_),i}b(_,\"weak public key\")}function za(e,a){var t=[];l(a),e=E(t,e,\"privateKey\");var _,n=0|r._crypto_scalarmult_scalarbytes();e.length!==n&&f(t,\"invalid privateKey length\"),_=d(e),t.push(_);var s=new u(0|r._crypto_scalarmult_bytes()),c=s.address;if(t.push(c),!(0|r._crypto_scalarmult_base(c,_))){var o=y(s,a);return g(t),o}b(t,\"unknown error\")}function Ja(e,a,t){var _=[];l(t),e=E(_,e,\"n\");var n,s=0|r._crypto_scalarmult_ed25519_scalarbytes();e.length!==s&&f(_,\"invalid n length\"),n=d(e),_.push(n),a=E(_,a,\"p\");var c,o=0|r._crypto_scalarmult_ed25519_bytes();a.length!==o&&f(_,\"invalid p length\"),c=d(a),_.push(c);var h=new u(0|r._crypto_scalarmult_ed25519_bytes()),p=h.address;if(_.push(p),!(0|r._crypto_scalarmult_ed25519(p,n,c))){var i=y(h,t);return g(_),i}b(_,\"invalid point or scalar is 0\")}function Qa(e,a){var t=[];l(a),e=E(t,e,\"scalar\");var _,n=0|r._crypto_scalarmult_ed25519_scalarbytes();e.length!==n&&f(t,\"invalid scalar length\"),_=d(e),t.push(_);var s=new u(0|r._crypto_scalarmult_ed25519_bytes()),c=s.address;if(t.push(c),!(0|r._crypto_scalarmult_ed25519_base(c,_))){var o=y(s,a);return g(t),o}b(t,\"scalar is 0\")}function Za(e,a){var t=[];l(a),e=E(t,e,\"scalar\");var _,n=0|r._crypto_scalarmult_ed25519_scalarbytes();e.length!==n&&f(t,\"invalid scalar length\"),_=d(e),t.push(_);var s=new u(0|r._crypto_scalarmult_ed25519_bytes()),c=s.address;if(t.push(c),!(0|r._crypto_scalarmult_ed25519_base_noclamp(c,_))){var o=y(s,a);return g(t),o}b(t,\"scalar is 0\")}function $a(e,a,t){var _=[];l(t),e=E(_,e,\"n\");var n,s=0|r._crypto_scalarmult_ed25519_scalarbytes();e.length!==s&&f(_,\"invalid n length\"),n=d(e),_.push(n),a=E(_,a,\"p\");var c,o=0|r._crypto_scalarmult_ed25519_bytes();a.length!==o&&f(_,\"invalid p length\"),c=d(a),_.push(c);var h=new u(0|r._crypto_scalarmult_ed25519_bytes()),p=h.address;if(_.push(p),!(0|r._crypto_scalarmult_ed25519_noclamp(p,n,c))){var i=y(h,t);return g(_),i}b(_,\"invalid point or scalar is 0\")}function er(e,a,t){var _=[];l(t),e=E(_,e,\"scalar\");var n,s=0|r._crypto_scalarmult_ristretto255_scalarbytes();e.length!==s&&f(_,\"invalid scalar length\"),n=d(e),_.push(n),a=E(_,a,\"element\");var c,o=0|r._crypto_scalarmult_ristretto255_bytes();a.length!==o&&f(_,\"invalid element length\"),c=d(a),_.push(c);var h=new u(0|r._crypto_scalarmult_ristretto255_bytes()),p=h.address;if(_.push(p),!(0|r._crypto_scalarmult_ristretto255(p,n,c))){var i=y(h,t);return g(_),i}b(_,\"result is identity element\")}function ar(e,a){var t=[];l(a),e=E(t,e,\"scalar\");var _,n=0|r._crypto_core_ristretto255_scalarbytes();e.length!==n&&f(t,\"invalid scalar length\"),_=d(e),t.push(_);var s=new u(0|r._crypto_core_ristretto255_bytes()),c=s.address;if(t.push(c),!(0|r._crypto_scalarmult_ristretto255_base(c,_))){var o=y(s,a);return g(t),o}b(t,\"scalar is 0\")}function rr(e,a,t,_){var n=[];l(_);var s=d(e=E(n,e,\"message\")),c=e.length;n.push(s),a=E(n,a,\"nonce\");var o,h=0|r._crypto_secretbox_noncebytes();a.length!==h&&f(n,\"invalid nonce length\"),o=d(a),n.push(o),t=E(n,t,\"key\");var p,i=0|r._crypto_secretbox_keybytes();t.length!==i&&f(n,\"invalid key length\"),p=d(t),n.push(p);var v=new u(0|c),m=v.address;n.push(m);var x=new u(0|r._crypto_secretbox_macbytes()),k=x.address;if(n.push(k),!(0|r._crypto_secretbox_detached(m,k,s,c,0,o,p))){var S=y({mac:x,cipher:v},_);return g(n),S}b(n,\"invalid usage\")}function tr(e,a,t,_){var n=[];l(_);var s=d(e=E(n,e,\"message\")),c=e.length;n.push(s),a=E(n,a,\"nonce\");var o,h=0|r._crypto_secretbox_noncebytes();a.length!==h&&f(n,\"invalid nonce length\"),o=d(a),n.push(o),t=E(n,t,\"key\");var p,i=0|r._crypto_secretbox_keybytes();t.length!==i&&f(n,\"invalid key length\"),p=d(t),n.push(p);var v=new u(c+r._crypto_secretbox_macbytes()|0),m=v.address;if(n.push(m),!(0|r._crypto_secretbox_easy(m,s,c,0,o,p))){var x=y(v,_);return g(n),x}b(n,\"invalid usage\")}function _r(e){var a=[];l(e);var t=new u(0|r._crypto_secretbox_keybytes()),_=t.address;a.push(_),r._crypto_secretbox_keygen(_);var n=y(t,e);return g(a),n}function nr(e,a,t,_,n){var s=[];l(n);var c=d(e=E(s,e,\"ciphertext\")),o=e.length;s.push(c),a=E(s,a,\"mac\");var h,p=0|r._crypto_secretbox_macbytes();a.length!==p&&f(s,\"invalid mac length\"),h=d(a),s.push(h),t=E(s,t,\"nonce\");var i,v=0|r._crypto_secretbox_noncebytes();t.length!==v&&f(s,\"invalid nonce length\"),i=d(t),s.push(i),_=E(s,_,\"key\");var m,x=0|r._crypto_secretbox_keybytes();_.length!==x&&f(s,\"invalid key length\"),m=d(_),s.push(m);var k=new u(0|o),S=k.address;if(s.push(S),!(0|r._crypto_secretbox_open_detached(S,c,h,o,0,i,m))){var T=y(k,n);return g(s),T}b(s,\"wrong secret key for the given ciphertext\")}function sr(e,a,t,_){var n=[];l(_),e=E(n,e,\"ciphertext\");var s,c=r._crypto_secretbox_macbytes(),o=e.length;o<c&&f(n,\"ciphertext is too short\"),s=d(e),n.push(s),a=E(n,a,\"nonce\");var h,p=0|r._crypto_secretbox_noncebytes();a.length!==p&&f(n,\"invalid nonce length\"),h=d(a),n.push(h),t=E(n,t,\"key\");var i,v=0|r._crypto_secretbox_keybytes();t.length!==v&&f(n,\"invalid key length\"),i=d(t),n.push(i);var m=new u(o-r._crypto_secretbox_macbytes()|0),x=m.address;if(n.push(x),!(0|r._crypto_secretbox_open_easy(x,s,o,0,h,i))){var k=y(m,_);return g(n),k}b(n,\"wrong secret key for the given ciphertext\")}function cr(e,a,t){var _=[];l(t),e=E(_,e,\"header\");var n,s=0|r._crypto_secretstream_xchacha20poly1305_headerbytes();e.length!==s&&f(_,\"invalid header length\"),n=d(e),_.push(n),a=E(_,a,\"key\");var c,o=0|r._crypto_secretstream_xchacha20poly1305_keybytes();a.length!==o&&f(_,\"invalid key length\"),c=d(a),_.push(c);var h=new u(52).address;if(!(0|r._crypto_secretstream_xchacha20poly1305_init_pull(h,n,c))){var p=h;return g(_),p}b(_,\"invalid usage\")}function or(e,a){var t=[];l(a),e=E(t,e,\"key\");var _,n=0|r._crypto_secretstream_xchacha20poly1305_keybytes();e.length!==n&&f(t,\"invalid key length\"),_=d(e),t.push(_);var s=new u(52).address,c=new u(0|r._crypto_secretstream_xchacha20poly1305_headerbytes()),o=c.address;if(t.push(o),!(0|r._crypto_secretstream_xchacha20poly1305_init_push(s,o,_))){var h={state:s,header:y(c,a)};return g(t),h}b(t,\"invalid usage\")}function hr(e){var a=[];l(e);var t=new u(0|r._crypto_secretstream_xchacha20poly1305_keybytes()),_=t.address;a.push(_),r._crypto_secretstream_xchacha20poly1305_keygen(_);var n=y(t,e);return g(a),n}function pr(e,a,t,_){var n=[];l(_),m(n,e,\"state_address\"),a=E(n,a,\"cipher\");var s,c=r._crypto_secretstream_xchacha20poly1305_abytes(),o=a.length;o<c&&f(n,\"cipher is too short\"),s=d(a),n.push(s);var h=null,p=0;null!=t&&(h=d(t=E(n,t,\"ad\")),p=t.length,n.push(h));var i=new u(o-r._crypto_secretstream_xchacha20poly1305_abytes()|0),b=i.address;n.push(b);var x,k=(x=v(1),n.push(x),(k=0===r._crypto_secretstream_xchacha20poly1305_pull(e,b,0,x,s,o,0,h,p)&&{tag:r.HEAPU8[x],message:i})&&{message:y(k.message,_),tag:k.tag});return g(n),k}function yr(e,a,t,_,n){var s=[];l(n),m(s,e,\"state_address\");var c=d(a=E(s,a,\"message_chunk\")),o=a.length;s.push(c);var h=null,p=0;null!=t&&(h=d(t=E(s,t,\"ad\")),p=t.length,s.push(h)),m(s,_,\"tag\"),(\"number\"!=typeof _||(0|_)!==_||_<0)&&f(s,\"tag must be an unsigned integer\");var i=new u(o+r._crypto_secretstream_xchacha20poly1305_abytes()|0),v=i.address;if(s.push(v),!(0|r._crypto_secretstream_xchacha20poly1305_push(e,v,0,c,o,0,h,p,0,_))){var x=y(i,n);return g(s),x}b(s,\"invalid usage\")}function ir(e,a){var t=[];return l(a),m(t,e,\"state_address\"),r._crypto_secretstream_xchacha20poly1305_rekey(e),g(t),!0}function lr(e,a,t){var _=[];l(t);var n=d(e=E(_,e,\"message\")),s=e.length;_.push(n),a=E(_,a,\"key\");var c,o=0|r._crypto_shorthash_keybytes();a.length!==o&&f(_,\"invalid key length\"),c=d(a),_.push(c);var h=new u(0|r._crypto_shorthash_bytes()),p=h.address;if(_.push(p),!(0|r._crypto_shorthash(p,n,s,0,c))){var i=y(h,t);return g(_),i}b(_,\"invalid usage\")}function ur(e){var a=[];l(e);var t=new u(0|r._crypto_shorthash_keybytes()),_=t.address;a.push(_),r._crypto_shorthash_keygen(_);var n=y(t,e);return g(a),n}function dr(e,a,t){var _=[];l(t);var n=d(e=E(_,e,\"message\")),s=e.length;_.push(n),a=E(_,a,\"key\");var c,o=0|r._crypto_shorthash_siphashx24_keybytes();a.length!==o&&f(_,\"invalid key length\"),c=d(a),_.push(c);var h=new u(0|r._crypto_shorthash_siphashx24_bytes()),p=h.address;if(_.push(p),!(0|r._crypto_shorthash_siphashx24(p,n,s,0,c))){var i=y(h,t);return g(_),i}b(_,\"invalid usage\")}function vr(e,a,t){var _=[];l(t);var n=d(e=E(_,e,\"message\")),s=e.length;_.push(n),a=E(_,a,\"privateKey\");var c,o=0|r._crypto_sign_secretkeybytes();a.length!==o&&f(_,\"invalid privateKey length\"),c=d(a),_.push(c);var h=new u(e.length+r._crypto_sign_bytes()|0),p=h.address;if(_.push(p),!(0|r._crypto_sign(p,null,n,s,0,c))){var i=y(h,t);return g(_),i}b(_,\"invalid usage\")}function gr(e,a,t){var _=[];l(t);var n=d(e=E(_,e,\"message\")),s=e.length;_.push(n),a=E(_,a,\"privateKey\");var c,o=0|r._crypto_sign_secretkeybytes();a.length!==o&&f(_,\"invalid privateKey length\"),c=d(a),_.push(c);var h=new u(0|r._crypto_sign_bytes()),p=h.address;if(_.push(p),!(0|r._crypto_sign_detached(p,null,n,s,0,c))){var i=y(h,t);return g(_),i}b(_,\"invalid usage\")}function br(e,a){var t=[];l(a),e=E(t,e,\"edPk\");var _,n=0|r._crypto_sign_publickeybytes();e.length!==n&&f(t,\"invalid edPk length\"),_=d(e),t.push(_);var s=new u(0|r._crypto_scalarmult_scalarbytes()),c=s.address;if(t.push(c),!(0|r._crypto_sign_ed25519_pk_to_curve25519(c,_))){var o=y(s,a);return g(t),o}b(t,\"invalid key\")}function fr(e,a){var t=[];l(a),e=E(t,e,\"edSk\");var _,n=0|r._crypto_sign_secretkeybytes();e.length!==n&&f(t,\"invalid edSk length\"),_=d(e),t.push(_);var s=new u(0|r._crypto_scalarmult_scalarbytes()),c=s.address;if(t.push(c),!(0|r._crypto_sign_ed25519_sk_to_curve25519(c,_))){var o=y(s,a);return g(t),o}b(t,\"invalid key\")}function mr(e,a){var t=[];l(a),e=E(t,e,\"privateKey\");var _,n=0|r._crypto_sign_secretkeybytes();e.length!==n&&f(t,\"invalid privateKey length\"),_=d(e),t.push(_);var s=new u(0|r._crypto_sign_publickeybytes()),c=s.address;if(t.push(c),!(0|r._crypto_sign_ed25519_sk_to_pk(c,_))){var o=y(s,a);return g(t),o}b(t,\"invalid key\")}function Er(e,a){var t=[];l(a),e=E(t,e,\"privateKey\");var _,n=0|r._crypto_sign_secretkeybytes();e.length!==n&&f(t,\"invalid privateKey length\"),_=d(e),t.push(_);var s=new u(0|r._crypto_sign_seedbytes()),c=s.address;if(t.push(c),!(0|r._crypto_sign_ed25519_sk_to_seed(c,_))){var o=y(s,a);return g(t),o}b(t,\"invalid key\")}function xr(e,a,t){var _=[];l(t),m(_,e,\"state_address\"),a=E(_,a,\"privateKey\");var n,s=0|r._crypto_sign_secretkeybytes();a.length!==s&&f(_,\"invalid privateKey length\"),n=d(a),_.push(n);var c=new u(0|r._crypto_sign_bytes()),o=c.address;if(_.push(o),!(0|r._crypto_sign_final_create(e,o,null,n))){var h=(r._free(e),y(c,t));return g(_),h}b(_,\"invalid usage\")}function kr(e,a,t,_){var n=[];l(_),m(n,e,\"state_address\"),a=E(n,a,\"signature\");var s,c=0|r._crypto_sign_bytes();a.length!==c&&f(n,\"invalid signature length\"),s=d(a),n.push(s),t=E(n,t,\"publicKey\");var o,h=0|r._crypto_sign_publickeybytes();t.length!==h&&f(n,\"invalid publicKey length\"),o=d(t),n.push(o);var p=!(0|r._crypto_sign_final_verify(e,s,o));return g(n),p}function Sr(e){var a=[];l(e);var t=new u(208).address;if(!(0|r._crypto_sign_init(t))){var _=t;return g(a),_}b(a,\"internal error\")}function Tr(e){var a=[];l(e);var t=new u(0|r._crypto_sign_publickeybytes()),_=t.address;a.push(_);var n=new u(0|r._crypto_sign_secretkeybytes()),s=n.address;if(a.push(s),!(0|r._crypto_sign_keypair(_,s))){var c={publicKey:y(t,e),privateKey:y(n,e),keyType:\"ed25519\"};return g(a),c}b(a,\"internal error\")}function wr(e,a,t){var _=[];l(t),e=E(_,e,\"signedMessage\");var n,s=r._crypto_sign_bytes(),c=e.length;c<s&&f(_,\"signedMessage is too short\"),n=d(e),_.push(n),a=E(_,a,\"publicKey\");var o,h=0|r._crypto_sign_publickeybytes();a.length!==h&&f(_,\"invalid publicKey length\"),o=d(a),_.push(o);var p=new u(c-r._crypto_sign_bytes()|0),i=p.address;if(_.push(i),!(0|r._crypto_sign_open(i,null,n,c,0,o))){var v=y(p,t);return g(_),v}b(_,\"incorrect signature for the given public key\")}function Yr(e,a){var t=[];l(a),e=E(t,e,\"seed\");var _,n=0|r._crypto_sign_seedbytes();e.length!==n&&f(t,\"invalid seed length\"),_=d(e),t.push(_);var s=new u(0|r._crypto_sign_publickeybytes()),c=s.address;t.push(c);var o=new u(0|r._crypto_sign_secretkeybytes()),h=o.address;if(t.push(h),!(0|r._crypto_sign_seed_keypair(c,h,_))){var p={publicKey:y(s,a),privateKey:y(o,a),keyType:\"ed25519\"};return g(t),p}b(t,\"invalid usage\")}function Br(e,a,t){var _=[];l(t),m(_,e,\"state_address\");var n=d(a=E(_,a,\"message_chunk\")),s=a.length;_.push(n),0|r._crypto_sign_update(e,n,s,0)&&b(_,\"invalid usage\"),g(_)}function Ar(e,a,t){var _=[];e=E(_,e,\"signature\");var n,s=0|r._crypto_sign_bytes();e.length!==s&&f(_,\"invalid signature length\"),n=d(e),_.push(n);var c=d(a=E(_,a,\"message\")),o=a.length;_.push(c),t=E(_,t,\"publicKey\");var h,p=0|r._crypto_sign_publickeybytes();t.length!==p&&f(_,\"invalid publicKey length\"),h=d(t),_.push(h);var y=!(0|r._crypto_sign_verify_detached(n,c,o,0,h));return g(_),y}function Mr(e,a,t,_){var n=[];l(_),m(n,e,\"outLength\"),(\"number\"!=typeof e||(0|e)!==e||e<0)&&f(n,\"outLength must be an unsigned integer\"),a=E(n,a,\"key\");var s,c=0|r._crypto_stream_chacha20_keybytes();a.length!==c&&f(n,\"invalid key length\"),s=d(a),n.push(s),t=E(n,t,\"nonce\");var o,h=0|r._crypto_stream_chacha20_noncebytes();t.length!==h&&f(n,\"invalid nonce length\"),o=d(t),n.push(o);var p=new u(0|e),i=p.address;n.push(i),r._crypto_stream_chacha20(i,e,0,o,s);var v=y(p,_);return g(n),v}function Ir(e,a,t,_){var n=[];l(_);var s=d(e=E(n,e,\"input_message\")),c=e.length;n.push(s),a=E(n,a,\"nonce\");var o,h=0|r._crypto_stream_chacha20_ietf_noncebytes();a.length!==h&&f(n,\"invalid nonce length\"),o=d(a),n.push(o),t=E(n,t,\"key\");var p,i=0|r._crypto_stream_chacha20_ietf_keybytes();t.length!==i&&f(n,\"invalid key length\"),p=d(t),n.push(p);var v=new u(0|c),m=v.address;if(n.push(m),0===r._crypto_stream_chacha20_ietf_xor(m,s,c,0,o,p)){var x=y(v,_);return g(n),x}b(n,\"invalid usage\")}function Kr(e,a,t,_,n){var s=[];l(n);var c=d(e=E(s,e,\"input_message\")),o=e.length;s.push(c),a=E(s,a,\"nonce\");var h,p=0|r._crypto_stream_chacha20_ietf_noncebytes();a.length!==p&&f(s,\"invalid nonce length\"),h=d(a),s.push(h),m(s,t,\"nonce_increment\"),(\"number\"!=typeof t||(0|t)!==t||t<0)&&f(s,\"nonce_increment must be an unsigned integer\"),_=E(s,_,\"key\");var i,v=0|r._crypto_stream_chacha20_ietf_keybytes();_.length!==v&&f(s,\"invalid key length\"),i=d(_),s.push(i);var x=new u(0|o),k=x.address;if(s.push(k),0===r._crypto_stream_chacha20_ietf_xor_ic(k,c,o,0,h,t,i)){var S=y(x,n);return g(s),S}b(s,\"invalid usage\")}function Nr(e){var a=[];l(e);var t=new u(0|r._crypto_stream_chacha20_keybytes()),_=t.address;a.push(_),r._crypto_stream_chacha20_keygen(_);var n=y(t,e);return g(a),n}function Lr(e,a,t,_){var n=[];l(_);var s=d(e=E(n,e,\"input_message\")),c=e.length;n.push(s),a=E(n,a,\"nonce\");var o,h=0|r._crypto_stream_chacha20_noncebytes();a.length!==h&&f(n,\"invalid nonce length\"),o=d(a),n.push(o),t=E(n,t,\"key\");var p,i=0|r._crypto_stream_chacha20_keybytes();t.length!==i&&f(n,\"invalid key length\"),p=d(t),n.push(p);var v=new u(0|c),m=v.address;if(n.push(m),0===r._crypto_stream_chacha20_xor(m,s,c,0,o,p)){var x=y(v,_);return g(n),x}b(n,\"invalid usage\")}function Or(e,a,t,_,n){var s=[];l(n);var c=d(e=E(s,e,\"input_message\")),o=e.length;s.push(c),a=E(s,a,\"nonce\");var h,p=0|r._crypto_stream_chacha20_noncebytes();a.length!==p&&f(s,\"invalid nonce length\"),h=d(a),s.push(h),m(s,t,\"nonce_increment\"),(\"number\"!=typeof t||(0|t)!==t||t<0)&&f(s,\"nonce_increment must be an unsigned integer\"),_=E(s,_,\"key\");var i,v=0|r._crypto_stream_chacha20_keybytes();_.length!==v&&f(s,\"invalid key length\"),i=d(_),s.push(i);var x=new u(0|o),k=x.address;if(s.push(k),0===r._crypto_stream_chacha20_xor_ic(k,c,o,0,h,t,0,i)){var S=y(x,n);return g(s),S}b(s,\"invalid usage\")}function Ur(e){var a=[];l(e);var t=new u(0|r._crypto_stream_keybytes()),_=t.address;a.push(_),r._crypto_stream_keygen(_);var n=y(t,e);return g(a),n}function Cr(e){var a=[];l(e);var t=new u(0|r._crypto_stream_xchacha20_keybytes()),_=t.address;a.push(_),r._crypto_stream_xchacha20_keygen(_);var n=y(t,e);return g(a),n}function Pr(e,a,t,_){var n=[];l(_);var s=d(e=E(n,e,\"input_message\")),c=e.length;n.push(s),a=E(n,a,\"nonce\");var o,h=0|r._crypto_stream_xchacha20_noncebytes();a.length!==h&&f(n,\"invalid nonce length\"),o=d(a),n.push(o),t=E(n,t,\"key\");var p,i=0|r._crypto_stream_xchacha20_keybytes();t.length!==i&&f(n,\"invalid key length\"),p=d(t),n.push(p);var v=new u(0|c),m=v.address;if(n.push(m),0===r._crypto_stream_xchacha20_xor(m,s,c,0,o,p)){var x=y(v,_);return g(n),x}b(n,\"invalid usage\")}function Rr(e,a,t,_,n){var s=[];l(n);var c=d(e=E(s,e,\"input_message\")),o=e.length;s.push(c),a=E(s,a,\"nonce\");var h,p=0|r._crypto_stream_xchacha20_noncebytes();a.length!==p&&f(s,\"invalid nonce length\"),h=d(a),s.push(h),m(s,t,\"nonce_increment\"),(\"number\"!=typeof t||(0|t)!==t||t<0)&&f(s,\"nonce_increment must be an unsigned integer\"),_=E(s,_,\"key\");var i,v=0|r._crypto_stream_xchacha20_keybytes();_.length!==v&&f(s,\"invalid key length\"),i=d(_),s.push(i);var x=new u(0|o),k=x.address;if(s.push(k),0===r._crypto_stream_xchacha20_xor_ic(k,c,o,0,h,t,0,i)){var S=y(x,n);return g(s),S}b(s,\"invalid usage\")}function Xr(e,a){var t=[];l(a),m(t,e,\"length\"),(\"number\"!=typeof e||(0|e)!==e||e<0)&&f(t,\"length must be an unsigned integer\");var _=new u(0|e),n=_.address;t.push(n),r._randombytes_buf(n,e);var s=y(_,a);return g(t),s}function Gr(e,a,t){var _=[];l(t),m(_,e,\"length\"),(\"number\"!=typeof e||(0|e)!==e||e<0)&&f(_,\"length must be an unsigned integer\"),a=E(_,a,\"seed\");var n,s=0|r._randombytes_seedbytes();a.length!==s&&f(_,\"invalid seed length\"),n=d(a),_.push(n);var c=new u(0|e),o=c.address;_.push(o),r._randombytes_buf_deterministic(o,e,n);var h=y(c,t);return g(_),h}function Dr(e){l(e),r._randombytes_close()}function Fr(e){l(e);var a=r._randombytes_random()>>>0;return g([]),a}function Vr(e,a){var t=[];l(a);for(var _=r._malloc(24),n=0;n<6;n++)r.setValue(_+4*n,r.Runtime.addFunction(e[[\"implementation_name\",\"random\",\"stir\",\"uniform\",\"buf\",\"close\"][n]]),\"i32\");0|r._randombytes_set_implementation(_)&&b(t,\"unsupported implementation\"),g(t)}function Hr(e){l(e),r._randombytes_stir()}function Wr(e,a){var t=[];l(a),m(t,e,\"upper_bound\"),(\"number\"!=typeof e||(0|e)!==e||e<0)&&f(t,\"upper_bound must be an unsigned integer\");var _=r._randombytes_uniform(e)>>>0;return g(t),_}function qr(){var e=r._sodium_version_string(),a=r.UTF8ToString(e);return g([]),a}return u.prototype.to_Uint8Array=function(){var e=new Uint8Array(this.length);return e.set(r.HEAPU8.subarray(this.address,this.address+this.length)),e},e.add=function(e,a){if(!(e instanceof Uint8Array&&a instanceof Uint8Array))throw new TypeError(\"Only Uint8Array instances can added\");var r=e.length,t=0,_=0;if(a.length!=e.length)throw new TypeError(\"Arguments must have the same length\");for(_=0;_<r;_++)t>>=8,t+=e[_]+a[_],e[_]=255&t},e.base64_variants=o,e.compare=function(e,a){if(!(e instanceof Uint8Array&&a instanceof Uint8Array))throw new TypeError(\"Only Uint8Array instances can be compared\");if(e.length!==a.length)throw new TypeError(\"Only instances of identical length can be compared\");for(var r=0,t=1,_=e.length;_-- >0;)r|=a[_]-e[_]>>8&t,t&=(a[_]^e[_])-1>>8;return r+r+t-1},e.from_base64=function(e,a){a=h(a);var t,_=[],n=new u(3*(e=E(_,e,\"input\")).length/4),s=d(e),c=v(4),o=v(4);return _.push(s),_.push(n.address),_.push(n.result_bin_len_p),_.push(n.b64_end_p),0!==r._sodium_base642bin(n.address,n.length,s,e.length,0,c,o,a)&&b(_,\"invalid input\"),r.getValue(o,\"i32\")-s!==e.length&&b(_,\"incomplete input\"),n.length=r.getValue(c,\"i32\"),t=n.to_Uint8Array(),g(_),t},e.from_hex=function(e){var a,t=[],_=new u((e=E(t,e,\"input\")).length/2),n=d(e),s=v(4);return t.push(n),t.push(_.address),t.push(_.hex_end_p),0!==r._sodium_hex2bin(_.address,_.length,n,e.length,0,0,s)&&b(t,\"invalid input\"),r.getValue(s,\"i32\")-n!==e.length&&b(t,\"incomplete input\"),a=_.to_Uint8Array(),g(t),a},e.from_string=n,e.increment=function(e){if(!(e instanceof Uint8Array))throw new TypeError(\"Only Uint8Array instances can be incremented\");for(var a=256,r=0,t=e.length;r<t;r++)a>>=8,a+=e[r],e[r]=255&a},e.is_zero=function(e){if(!(e instanceof Uint8Array))throw new TypeError(\"Only Uint8Array instances can be checked\");for(var a=0,r=0,t=e.length;r<t;r++)a|=e[r];return 0===a},e.libsodium=a,e.memcmp=function(e,a){if(!(e instanceof Uint8Array&&a instanceof Uint8Array))throw new TypeError(\"Only Uint8Array instances can be compared\");if(e.length!==a.length)throw new TypeError(\"Only instances of identical length can be compared\");for(var r=0,t=0,_=e.length;t<_;t++)r|=e[t]^a[t];return 0===r},e.memzero=function(e){if(!(e instanceof Uint8Array))throw new TypeError(\"Only Uint8Array instances can be wiped\");for(var a=0,r=e.length;a<r;a++)e[a]=0},e.output_formats=function(){return[\"uint8array\",\"text\",\"hex\",\"base64\"]},e.pad=function(e,a){if(!(e instanceof Uint8Array))throw new TypeError(\"buffer must be a Uint8Array\");if((a|=0)<=0)throw new Error(\"block size must be > 0\");var t,_=[],n=v(4),s=1,c=0,o=0|e.length,h=new u(o+a);_.push(n),_.push(h.address);for(var p=h.address,y=h.address+o+a;p<y;p++)r.HEAPU8[p]=e[c],c+=s=1&~((65535&((o-=s)>>>48|o>>>32|o>>>16|o))-1>>16);return 0!==r._sodium_pad(n,h.address,e.length,a,h.length)&&b(_,\"internal error\"),h.length=r.getValue(n,\"i32\"),t=h.to_Uint8Array(),g(_),t},e.unpad=function(e,a){if(!(e instanceof Uint8Array))throw new TypeError(\"buffer must be a Uint8Array\");if((a|=0)<=0)throw new Error(\"block size must be > 0\");var t=[],_=d(e),n=v(4);return t.push(_),t.push(n),0!==r._sodium_unpad(n,_,e.length,a)&&b(t,\"unsupported/invalid padding\"),e=(e=new Uint8Array(e)).subarray(0,r.getValue(n,\"i32\")),g(t),e},e.ready=_,e.symbols=function(){return Object.keys(e).sort()},e.to_base64=p,e.to_hex=c,e.to_string=s,e}var r=\"object\"==typeof e.sodium&&\"function\"==typeof e.sodium.onload?e.sodium.onload:null; true?!(__WEBPACK_AMD_DEFINE_ARRAY__ = [exports,__webpack_require__(/*! libsodium-sumo */ \"(ssr)/./node_modules/libsodium-sumo/dist/modules-sumo/libsodium-sumo.js\")], __WEBPACK_AMD_DEFINE_FACTORY__ = (a),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?\n\t\t(__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)) : __WEBPACK_AMD_DEFINE_FACTORY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__)):0,r&&e.sodium.ready.then((function(){r(e.sodium)}))}(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/libsodium-wrappers-sumo/dist/modules-sumo/libsodium-wrappers.js\n");

/***/ })

};
;