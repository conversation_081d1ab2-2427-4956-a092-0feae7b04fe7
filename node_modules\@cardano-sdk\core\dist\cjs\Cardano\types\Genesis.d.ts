import { Lovelace } from './Value';
import { NetworkId, NetworkMagic } from '../ChainId';
import { Seconds } from '../../util';
export interface CompactGenesis {
    systemStart: Date;
    networkMagic: NetworkMagic;
    networkId: NetworkId;
    activeSlotsCoefficient: number;
    securityParameter: number;
    epochLength: number;
    slotsPerKesPeriod: number;
    maxKesEvolutions: number;
    slotLength: Seconds;
    updateQuorum: number;
    maxLovelaceSupply: Lovelace;
}
//# sourceMappingURL=Genesis.d.ts.map