import { Hash28ByteBase16 } from '@cardano-sdk/crypto';
import { HexBlob } from '@cardano-sdk/util';
import type * as Cardano from '../../../Cardano/index.js';
export declare class TreasuryWithdrawalsAction {
    #private;
    constructor(withdrawals: Map<Cardano.RewardAccount, Cardano.Lovelace>, policyHash?: Hash28ByteBase16);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): TreasuryWithdrawalsAction;
    toCore(): Cardano.TreasuryWithdrawalsAction;
    static fromCore(treasuryWithdrawalsAction: Cardano.TreasuryWithdrawalsAction): TreasuryWithdrawalsAction;
    withdrawals(): Map<Cardano.RewardAccount, Cardano.Lovelace>;
    policyHash(): Hash28ByteBase16 | undefined;
}
//# sourceMappingURL=TreasuryWithdrawalsAction.d.ts.map