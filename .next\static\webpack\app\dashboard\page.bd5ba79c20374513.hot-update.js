"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_providers_WalletProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/WalletProvider */ \"(app-pages-browser)/./src/components/providers/WalletProvider.tsx\");\n/* harmony import */ var _lib_blockchain__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/blockchain */ \"(app-pages-browser)/./src/lib/blockchain.ts\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Coins_ExternalLink_MapPin_Trophy_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Coins,ExternalLink,MapPin,Trophy,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.mjs\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Coins_ExternalLink_MapPin_Trophy_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Coins,ExternalLink,MapPin,Trophy,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.mjs\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Coins_ExternalLink_MapPin_Trophy_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Coins,ExternalLink,MapPin,Trophy,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coins.mjs\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Coins_ExternalLink_MapPin_Trophy_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Coins,ExternalLink,MapPin,Trophy,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Coins_ExternalLink_MapPin_Trophy_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Coins,ExternalLink,MapPin,Trophy,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.mjs\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Coins_ExternalLink_MapPin_Trophy_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Coins,ExternalLink,MapPin,Trophy,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.mjs\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_blockchain__WEBPACK_IMPORTED_MODULE_3__]);\n_lib_blockchain__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction DashboardPage() {\n    _s();\n    const { connected, wallet, address } = (0,_components_providers_WalletProvider__WEBPACK_IMPORTED_MODULE_2__.useWallet)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [trekBalance, setTrekBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [nfts, setNfts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [adaBalance, setAdaBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('0');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            if (connected && wallet) {\n                loadDashboardData();\n            }\n        }\n    }[\"DashboardPage.useEffect\"], [\n        connected,\n        wallet\n    ]);\n    const loadDashboardData = async ()=>{\n        if (!wallet) return;\n        setLoading(true);\n        try {\n            _lib_blockchain__WEBPACK_IMPORTED_MODULE_3__.blockchainService.setWallet(wallet);\n            // Load wallet data\n            const [balance, trekTokens, trailNFTs] = await Promise.all([\n                _lib_blockchain__WEBPACK_IMPORTED_MODULE_3__.blockchainService.getWalletBalance(),\n                _lib_blockchain__WEBPACK_IMPORTED_MODULE_3__.blockchainService.getTrekTokenBalance(),\n                _lib_blockchain__WEBPACK_IMPORTED_MODULE_3__.blockchainService.getTrailNFTs()\n            ]);\n            setAdaBalance(balance);\n            setTrekBalance(trekTokens);\n            setNfts(trailNFTs);\n        } catch (error) {\n            console.error('Error loading dashboard data:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const formatAddress = (addr)=>{\n        return \"\".concat(addr.slice(0, 8), \"...\").concat(addr.slice(-8));\n    };\n    const formatAda = (lovelace)=>{\n        try {\n            const ada = parseInt(lovelace) / 1000000;\n            return \"\".concat(ada.toFixed(2), \" ADA\");\n        } catch (e) {\n            return '0.00 ADA';\n        }\n    };\n    if (!connected) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Coins_ExternalLink_MapPin_Trophy_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                        children: \"Connect Your Wallet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-6\",\n                        children: \"Please connect your Cardano wallet to view your dashboard.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.location.href = '/',\n                        className: \"bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors\",\n                        children: \"Go to Home\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 67,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-green-50 to-blue-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>window.location.href = '/',\n                                    className: \"flex items-center space-x-2 hover:opacity-80 transition-opacity\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Coins_ExternalLink_MapPin_Trophy_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-8 w-8 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: \"VinTrek\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"hidden md:flex space-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/\",\n                                        className: \"text-gray-700 hover:text-green-600 transition-colors\",\n                                        children: \"Home\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/trails\",\n                                        className: \"text-gray-700 hover:text-green-600 transition-colors\",\n                                        children: \"Trails\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/dashboard\",\n                                        className: \"text-green-600 font-medium\",\n                                        children: \"Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/rewards\",\n                                        className: \"text-gray-700 hover:text-green-600 transition-colors\",\n                                        children: \"Rewards\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Coins_ExternalLink_MapPin_Trophy_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-4 w-4 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-700\",\n                                        children: address ? formatAddress(address) : 'Connected'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                children: \"Your Adventure Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Track your hiking achievements, NFT collection, and TREK token rewards.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this),\n                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow-md p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"ADA Balance\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 130,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: formatAda(adaBalance)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 131,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-100 p-3 rounded-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Coins_ExternalLink_MapPin_Trophy_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"h-6 w-6 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow-md p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"TREK Tokens\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 143,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: trekBalance.toLocaleString()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-yellow-100 p-3 rounded-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Coins_ExternalLink_MapPin_Trophy_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-6 w-6 text-yellow-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow-md p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-600\",\n                                                            children: \"Trail NFTs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: nfts.length\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 157,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-green-100 p-3 rounded-full\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Coins_ExternalLink_MapPin_Trophy_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-6 w-6 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-md p-6 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold text-gray-900 mb-4\",\n                                        children: \"Your Trail NFT Collection\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this),\n                                    nfts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Coins_ExternalLink_MapPin_Trophy_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-16 w-16 text-gray-300 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                children: \"No Trail NFTs Yet\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"Complete your first trail to mint your first NFT certificate!\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>window.location.href = '/trails',\n                                                className: \"bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors\",\n                                                children: \"Explore Trails\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                        children: nfts.map((nft, index)=>{\n                                            var _nft_metadata, _nft_metadata1;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border rounded-lg p-4 hover:shadow-md transition-shadow\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"aspect-square bg-gradient-to-br from-green-100 to-blue-100 rounded-lg mb-4 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Coins_ExternalLink_MapPin_Trophy_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"h-12 w-12 text-green-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 187,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-gray-900 mb-2\",\n                                                        children: ((_nft_metadata = nft.metadata) === null || _nft_metadata === void 0 ? void 0 : _nft_metadata.name) || \"Trail NFT #\".concat(index + 1)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    ((_nft_metadata1 = nft.metadata) === null || _nft_metadata1 === void 0 ? void 0 : _nft_metadata1.attributes) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1 text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Coins_ExternalLink_MapPin_Trophy_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 197,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: nft.metadata.attributes.location\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 198,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 196,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Coins_ExternalLink_MapPin_Trophy_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 201,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: new Date(nft.metadata.attributes.completion_date).toLocaleDateString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 202,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 200,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 rounded-full text-xs \".concat(nft.metadata.attributes.difficulty === 'Easy' ? 'bg-green-100 text-green-800' : nft.metadata.attributes.difficulty === 'Moderate' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'),\n                                                                    children: nft.metadata.attributes.difficulty\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 205,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>window.open(\"https://cardanoscan.io/token/\".concat(nft.unit), '_blank'),\n                                                        className: \"mt-3 w-full flex items-center justify-center space-x-2 text-blue-600 hover:text-blue-700 transition-colors\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Coins_ExternalLink_MapPin_Trophy_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 220,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"View on Explorer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 221,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, nft.unit, true, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-md p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold text-gray-900 mb-4\",\n                                        children: \"Recent Activity\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Coins_ExternalLink_MapPin_Trophy_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-12 w-12 text-gray-300 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Activity tracking coming soon!\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500 mt-2\",\n                                                children: \"We're working on bringing you detailed activity logs and achievement tracking.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"S3WExWI6/CJGQCS4DQqrJ6fYVYA=\", false, function() {\n    return [\n        _components_providers_WalletProvider__WEBPACK_IMPORTED_MODULE_2__.useWallet\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/providers/WalletProvider.tsx":
/*!*****************************************************!*\
  !*** ./src/components/providers/WalletProvider.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalletProvider: () => (/* binding */ WalletProvider),\n/* harmony export */   useWallet: () => (/* binding */ useWallet)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useWallet,WalletProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n// Dynamically import BrowserWallet to avoid SSR issues\nlet BrowserWallet = null;\nif (true) {\n    __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_meshsdk_core_dist_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @meshsdk/core */ \"(app-pages-browser)/./node_modules/@meshsdk/core/dist/index.js\")).then((module)=>{\n        BrowserWallet = module.BrowserWallet;\n    });\n}\nconst WalletContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useWallet() {\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(WalletContext);\n    if (context === undefined) {\n        throw new Error('useWallet must be used within a WalletProvider');\n    }\n    return context;\n}\n_s(useWallet, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nfunction WalletProvider(param) {\n    let { children } = param;\n    _s1();\n    const [wallet, setWallet] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [connected, setConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [connecting, setConnecting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [address, setAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [balance, setBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const getAvailableWallets = ()=>{\n        var _window_cardano, _window_cardano1, _window_cardano2, _window_cardano3;\n        if (false) {}\n        const wallets = [];\n        if ((_window_cardano = window.cardano) === null || _window_cardano === void 0 ? void 0 : _window_cardano.lace) wallets.push('lace');\n        if ((_window_cardano1 = window.cardano) === null || _window_cardano1 === void 0 ? void 0 : _window_cardano1.eternl) wallets.push('eternl');\n        if ((_window_cardano2 = window.cardano) === null || _window_cardano2 === void 0 ? void 0 : _window_cardano2.nami) wallets.push('nami');\n        if ((_window_cardano3 = window.cardano) === null || _window_cardano3 === void 0 ? void 0 : _window_cardano3.flint) wallets.push('flint');\n        return wallets;\n    };\n    const connect = async (walletName)=>{\n        if (false) {}\n        setConnecting(true);\n        try {\n            const browserWallet = await BrowserWallet.enable(walletName);\n            setWallet(browserWallet);\n            // Get wallet address\n            const addresses = await browserWallet.getUsedAddresses();\n            if (addresses.length > 0) {\n                setAddress(addresses[0]);\n            }\n            // Get wallet balance\n            try {\n                const balance = await browserWallet.getBalance();\n                setBalance(balance);\n            } catch (error) {\n                console.warn('Could not fetch balance:', error);\n                setBalance('0');\n            }\n            setConnected(true);\n            // Store connection state\n            localStorage.setItem('vintrek_wallet', walletName);\n        } catch (error) {\n            console.error('Failed to connect wallet:', error);\n            throw error;\n        } finally{\n            setConnecting(false);\n        }\n    };\n    const disconnect = ()=>{\n        setWallet(null);\n        setConnected(false);\n        setAddress(null);\n        setBalance(null);\n        localStorage.removeItem('vintrek_wallet');\n    };\n    // Auto-reconnect on page load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WalletProvider.useEffect\": ()=>{\n            const savedWallet = localStorage.getItem('vintrek_wallet');\n            if (savedWallet && getAvailableWallets().includes(savedWallet)) {\n                connect(savedWallet).catch(console.error);\n            }\n        }\n    }[\"WalletProvider.useEffect\"], []);\n    const value = {\n        wallet,\n        connected,\n        connecting,\n        address,\n        balance,\n        connect,\n        disconnect,\n        getAvailableWallets\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WalletContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\hackothon\\\\Blockchain\\\\Vintrek\\\\src\\\\components\\\\providers\\\\WalletProvider.tsx\",\n        lineNumber: 121,\n        columnNumber: 5\n    }, this);\n}\n_s1(WalletProvider, \"dSBI9X6BQq4oMvol2/Gn4R+d8Wg=\");\n_c = WalletProvider;\nvar _c;\n$RefreshReg$(_c, \"WalletProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/providers/WalletProvider.tsx\n"));

/***/ })

});