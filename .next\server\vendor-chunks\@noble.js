"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@noble";
exports.ids = ["vendor-chunks/@noble"];
exports.modules = {

/***/ "(ssr)/./node_modules/@noble/hashes/_md.js":
/*!*******************************************!*\
  !*** ./node_modules/@noble/hashes/_md.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SHA512_IV = exports.SHA384_IV = exports.SHA224_IV = exports.SHA256_IV = exports.HashMD = void 0;\nexports.setBigUint64 = setBigUint64;\nexports.Chi = Chi;\nexports.Maj = Maj;\n/**\n * Internal Merkle-Damgard hash utils.\n * @module\n */\nconst utils_ts_1 = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@noble/hashes/utils.js\");\n/** Polyfill for Safari 14. https://caniuse.com/mdn-javascript_builtins_dataview_setbiguint64 */\nfunction setBigUint64(view, byteOffset, value, isLE) {\n    if (typeof view.setBigUint64 === 'function')\n        return view.setBigUint64(byteOffset, value, isLE);\n    const _32n = BigInt(32);\n    const _u32_max = BigInt(0xffffffff);\n    const wh = Number((value >> _32n) & _u32_max);\n    const wl = Number(value & _u32_max);\n    const h = isLE ? 4 : 0;\n    const l = isLE ? 0 : 4;\n    view.setUint32(byteOffset + h, wh, isLE);\n    view.setUint32(byteOffset + l, wl, isLE);\n}\n/** Choice: a ? b : c */\nfunction Chi(a, b, c) {\n    return (a & b) ^ (~a & c);\n}\n/** Majority function, true if any two inputs is true. */\nfunction Maj(a, b, c) {\n    return (a & b) ^ (a & c) ^ (b & c);\n}\n/**\n * Merkle-Damgard hash construction base class.\n * Could be used to create MD5, RIPEMD, SHA1, SHA2.\n */\nclass HashMD extends utils_ts_1.Hash {\n    constructor(blockLen, outputLen, padOffset, isLE) {\n        super();\n        this.finished = false;\n        this.length = 0;\n        this.pos = 0;\n        this.destroyed = false;\n        this.blockLen = blockLen;\n        this.outputLen = outputLen;\n        this.padOffset = padOffset;\n        this.isLE = isLE;\n        this.buffer = new Uint8Array(blockLen);\n        this.view = (0, utils_ts_1.createView)(this.buffer);\n    }\n    update(data) {\n        (0, utils_ts_1.aexists)(this);\n        data = (0, utils_ts_1.toBytes)(data);\n        (0, utils_ts_1.abytes)(data);\n        const { view, buffer, blockLen } = this;\n        const len = data.length;\n        for (let pos = 0; pos < len;) {\n            const take = Math.min(blockLen - this.pos, len - pos);\n            // Fast path: we have at least one block in input, cast it to view and process\n            if (take === blockLen) {\n                const dataView = (0, utils_ts_1.createView)(data);\n                for (; blockLen <= len - pos; pos += blockLen)\n                    this.process(dataView, pos);\n                continue;\n            }\n            buffer.set(data.subarray(pos, pos + take), this.pos);\n            this.pos += take;\n            pos += take;\n            if (this.pos === blockLen) {\n                this.process(view, 0);\n                this.pos = 0;\n            }\n        }\n        this.length += data.length;\n        this.roundClean();\n        return this;\n    }\n    digestInto(out) {\n        (0, utils_ts_1.aexists)(this);\n        (0, utils_ts_1.aoutput)(out, this);\n        this.finished = true;\n        // Padding\n        // We can avoid allocation of buffer for padding completely if it\n        // was previously not allocated here. But it won't change performance.\n        const { buffer, view, blockLen, isLE } = this;\n        let { pos } = this;\n        // append the bit '1' to the message\n        buffer[pos++] = 0b10000000;\n        (0, utils_ts_1.clean)(this.buffer.subarray(pos));\n        // we have less than padOffset left in buffer, so we cannot put length in\n        // current block, need process it and pad again\n        if (this.padOffset > blockLen - pos) {\n            this.process(view, 0);\n            pos = 0;\n        }\n        // Pad until full block byte with zeros\n        for (let i = pos; i < blockLen; i++)\n            buffer[i] = 0;\n        // Note: sha512 requires length to be 128bit integer, but length in JS will overflow before that\n        // You need to write around 2 exabytes (u64_max / 8 / (1024**6)) for this to happen.\n        // So we just write lowest 64 bits of that value.\n        setBigUint64(view, blockLen - 8, BigInt(this.length * 8), isLE);\n        this.process(view, 0);\n        const oview = (0, utils_ts_1.createView)(out);\n        const len = this.outputLen;\n        // NOTE: we do division by 4 later, which should be fused in single op with modulo by JIT\n        if (len % 4)\n            throw new Error('_sha2: outputLen should be aligned to 32bit');\n        const outLen = len / 4;\n        const state = this.get();\n        if (outLen > state.length)\n            throw new Error('_sha2: outputLen bigger than state');\n        for (let i = 0; i < outLen; i++)\n            oview.setUint32(4 * i, state[i], isLE);\n    }\n    digest() {\n        const { buffer, outputLen } = this;\n        this.digestInto(buffer);\n        const res = buffer.slice(0, outputLen);\n        this.destroy();\n        return res;\n    }\n    _cloneInto(to) {\n        to || (to = new this.constructor());\n        to.set(...this.get());\n        const { blockLen, buffer, length, finished, destroyed, pos } = this;\n        to.destroyed = destroyed;\n        to.finished = finished;\n        to.length = length;\n        to.pos = pos;\n        if (length % blockLen)\n            to.buffer.set(buffer);\n        return to;\n    }\n    clone() {\n        return this._cloneInto();\n    }\n}\nexports.HashMD = HashMD;\n/**\n * Initial SHA-2 state: fractional parts of square roots of first 16 primes 2..53.\n * Check out `test/misc/sha2-gen-iv.js` for recomputation guide.\n */\n/** Initial SHA256 state. Bits 0..32 of frac part of sqrt of primes 2..19 */\nexports.SHA256_IV = Uint32Array.from([\n    0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a, 0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19,\n]);\n/** Initial SHA224 state. Bits 32..64 of frac part of sqrt of primes 23..53 */\nexports.SHA224_IV = Uint32Array.from([\n    0xc1059ed8, 0x367cd507, 0x3070dd17, 0xf70e5939, 0xffc00b31, 0x68581511, 0x64f98fa7, 0xbefa4fa4,\n]);\n/** Initial SHA384 state. Bits 0..64 of frac part of sqrt of primes 23..53 */\nexports.SHA384_IV = Uint32Array.from([\n    0xcbbb9d5d, 0xc1059ed8, 0x629a292a, 0x367cd507, 0x9159015a, 0x3070dd17, 0x152fecd8, 0xf70e5939,\n    0x67332667, 0xffc00b31, 0x8eb44a87, 0x68581511, 0xdb0c2e0d, 0x64f98fa7, 0x47b5481d, 0xbefa4fa4,\n]);\n/** Initial SHA512 state. Bits 0..64 of frac part of sqrt of primes 2..19 */\nexports.SHA512_IV = Uint32Array.from([\n    0x6a09e667, 0xf3bcc908, 0xbb67ae85, 0x84caa73b, 0x3c6ef372, 0xfe94f82b, 0xa54ff53a, 0x5f1d36f1,\n    0x510e527f, 0xade682d1, 0x9b05688c, 0x2b3e6c1f, 0x1f83d9ab, 0xfb41bd6b, 0x5be0cd19, 0x137e2179,\n]);\n//# sourceMappingURL=_md.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/_md.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/_u64.js":
/*!********************************************!*\
  !*** ./node_modules/@noble/hashes/_u64.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.toBig = exports.shrSL = exports.shrSH = exports.rotrSL = exports.rotrSH = exports.rotrBL = exports.rotrBH = exports.rotr32L = exports.rotr32H = exports.rotlSL = exports.rotlSH = exports.rotlBL = exports.rotlBH = exports.add5L = exports.add5H = exports.add4L = exports.add4H = exports.add3L = exports.add3H = void 0;\nexports.add = add;\nexports.fromBig = fromBig;\nexports.split = split;\n/**\n * Internal helpers for u64. BigUint64Array is too slow as per 2025, so we implement it using Uint32Array.\n * @todo re-check https://issues.chromium.org/issues/42212588\n * @module\n */\nconst U32_MASK64 = /* @__PURE__ */ BigInt(2 ** 32 - 1);\nconst _32n = /* @__PURE__ */ BigInt(32);\nfunction fromBig(n, le = false) {\n    if (le)\n        return { h: Number(n & U32_MASK64), l: Number((n >> _32n) & U32_MASK64) };\n    return { h: Number((n >> _32n) & U32_MASK64) | 0, l: Number(n & U32_MASK64) | 0 };\n}\nfunction split(lst, le = false) {\n    const len = lst.length;\n    let Ah = new Uint32Array(len);\n    let Al = new Uint32Array(len);\n    for (let i = 0; i < len; i++) {\n        const { h, l } = fromBig(lst[i], le);\n        [Ah[i], Al[i]] = [h, l];\n    }\n    return [Ah, Al];\n}\nconst toBig = (h, l) => (BigInt(h >>> 0) << _32n) | BigInt(l >>> 0);\nexports.toBig = toBig;\n// for Shift in [0, 32)\nconst shrSH = (h, _l, s) => h >>> s;\nexports.shrSH = shrSH;\nconst shrSL = (h, l, s) => (h << (32 - s)) | (l >>> s);\nexports.shrSL = shrSL;\n// Right rotate for Shift in [1, 32)\nconst rotrSH = (h, l, s) => (h >>> s) | (l << (32 - s));\nexports.rotrSH = rotrSH;\nconst rotrSL = (h, l, s) => (h << (32 - s)) | (l >>> s);\nexports.rotrSL = rotrSL;\n// Right rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotrBH = (h, l, s) => (h << (64 - s)) | (l >>> (s - 32));\nexports.rotrBH = rotrBH;\nconst rotrBL = (h, l, s) => (h >>> (s - 32)) | (l << (64 - s));\nexports.rotrBL = rotrBL;\n// Right rotate for shift===32 (just swaps l&h)\nconst rotr32H = (_h, l) => l;\nexports.rotr32H = rotr32H;\nconst rotr32L = (h, _l) => h;\nexports.rotr32L = rotr32L;\n// Left rotate for Shift in [1, 32)\nconst rotlSH = (h, l, s) => (h << s) | (l >>> (32 - s));\nexports.rotlSH = rotlSH;\nconst rotlSL = (h, l, s) => (l << s) | (h >>> (32 - s));\nexports.rotlSL = rotlSL;\n// Left rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotlBH = (h, l, s) => (l << (s - 32)) | (h >>> (64 - s));\nexports.rotlBH = rotlBH;\nconst rotlBL = (h, l, s) => (h << (s - 32)) | (l >>> (64 - s));\nexports.rotlBL = rotlBL;\n// JS uses 32-bit signed integers for bitwise operations which means we cannot\n// simple take carry out of low bit sum by shift, we need to use division.\nfunction add(Ah, Al, Bh, Bl) {\n    const l = (Al >>> 0) + (Bl >>> 0);\n    return { h: (Ah + Bh + ((l / 2 ** 32) | 0)) | 0, l: l | 0 };\n}\n// Addition with more than 2 elements\nconst add3L = (Al, Bl, Cl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0);\nexports.add3L = add3L;\nconst add3H = (low, Ah, Bh, Ch) => (Ah + Bh + Ch + ((low / 2 ** 32) | 0)) | 0;\nexports.add3H = add3H;\nconst add4L = (Al, Bl, Cl, Dl) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0);\nexports.add4L = add4L;\nconst add4H = (low, Ah, Bh, Ch, Dh) => (Ah + Bh + Ch + Dh + ((low / 2 ** 32) | 0)) | 0;\nexports.add4H = add4H;\nconst add5L = (Al, Bl, Cl, Dl, El) => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0) + (El >>> 0);\nexports.add5L = add5L;\nconst add5H = (low, Ah, Bh, Ch, Dh, Eh) => (Ah + Bh + Ch + Dh + Eh + ((low / 2 ** 32) | 0)) | 0;\nexports.add5H = add5H;\n// prettier-ignore\nconst u64 = {\n    fromBig, split, toBig,\n    shrSH, shrSL,\n    rotrSH, rotrSL, rotrBH, rotrBL,\n    rotr32H, rotr32L,\n    rotlSH, rotlSL, rotlBH, rotlBL,\n    add, add3L, add3H, add4L, add4H, add5H, add5L,\n};\nexports[\"default\"] = u64;\n//# sourceMappingURL=_u64.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG5vYmxlL2hhc2hlcy9fdTY0LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGFBQWEsR0FBRyxhQUFhLEdBQUcsYUFBYSxHQUFHLGNBQWMsR0FBRyxjQUFjLEdBQUcsY0FBYyxHQUFHLGNBQWMsR0FBRyxlQUFlLEdBQUcsZUFBZSxHQUFHLGNBQWMsR0FBRyxjQUFjLEdBQUcsY0FBYyxHQUFHLGNBQWMsR0FBRyxhQUFhLEdBQUcsYUFBYSxHQUFHLGFBQWEsR0FBRyxhQUFhLEdBQUcsYUFBYSxHQUFHLGFBQWE7QUFDelQsV0FBVztBQUNYLGVBQWU7QUFDZixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLFNBQVM7QUFDN0IsZ0JBQWdCLE9BQU87QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0EsZUFBZTtBQUNmO0FBQ0EsZUFBZTtBQUNmO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQSxhQUFhO0FBQ2I7QUFDQSxhQUFhO0FBQ2I7QUFDQSxhQUFhO0FBQ2I7QUFDQSxhQUFhO0FBQ2I7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWU7QUFDZiIsInNvdXJjZXMiOlsiRDpcXGhhY2tvdGhvblxcQmxvY2tjaGFpblxcVmludHJla1xcbm9kZV9tb2R1bGVzXFxAbm9ibGVcXGhhc2hlc1xcX3U2NC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMudG9CaWcgPSBleHBvcnRzLnNoclNMID0gZXhwb3J0cy5zaHJTSCA9IGV4cG9ydHMucm90clNMID0gZXhwb3J0cy5yb3RyU0ggPSBleHBvcnRzLnJvdHJCTCA9IGV4cG9ydHMucm90ckJIID0gZXhwb3J0cy5yb3RyMzJMID0gZXhwb3J0cy5yb3RyMzJIID0gZXhwb3J0cy5yb3RsU0wgPSBleHBvcnRzLnJvdGxTSCA9IGV4cG9ydHMucm90bEJMID0gZXhwb3J0cy5yb3RsQkggPSBleHBvcnRzLmFkZDVMID0gZXhwb3J0cy5hZGQ1SCA9IGV4cG9ydHMuYWRkNEwgPSBleHBvcnRzLmFkZDRIID0gZXhwb3J0cy5hZGQzTCA9IGV4cG9ydHMuYWRkM0ggPSB2b2lkIDA7XG5leHBvcnRzLmFkZCA9IGFkZDtcbmV4cG9ydHMuZnJvbUJpZyA9IGZyb21CaWc7XG5leHBvcnRzLnNwbGl0ID0gc3BsaXQ7XG4vKipcbiAqIEludGVybmFsIGhlbHBlcnMgZm9yIHU2NC4gQmlnVWludDY0QXJyYXkgaXMgdG9vIHNsb3cgYXMgcGVyIDIwMjUsIHNvIHdlIGltcGxlbWVudCBpdCB1c2luZyBVaW50MzJBcnJheS5cbiAqIEB0b2RvIHJlLWNoZWNrIGh0dHBzOi8vaXNzdWVzLmNocm9taXVtLm9yZy9pc3N1ZXMvNDIyMTI1ODhcbiAqIEBtb2R1bGVcbiAqL1xuY29uc3QgVTMyX01BU0s2NCA9IC8qIEBfX1BVUkVfXyAqLyBCaWdJbnQoMiAqKiAzMiAtIDEpO1xuY29uc3QgXzMybiA9IC8qIEBfX1BVUkVfXyAqLyBCaWdJbnQoMzIpO1xuZnVuY3Rpb24gZnJvbUJpZyhuLCBsZSA9IGZhbHNlKSB7XG4gICAgaWYgKGxlKVxuICAgICAgICByZXR1cm4geyBoOiBOdW1iZXIobiAmIFUzMl9NQVNLNjQpLCBsOiBOdW1iZXIoKG4gPj4gXzMybikgJiBVMzJfTUFTSzY0KSB9O1xuICAgIHJldHVybiB7IGg6IE51bWJlcigobiA+PiBfMzJuKSAmIFUzMl9NQVNLNjQpIHwgMCwgbDogTnVtYmVyKG4gJiBVMzJfTUFTSzY0KSB8IDAgfTtcbn1cbmZ1bmN0aW9uIHNwbGl0KGxzdCwgbGUgPSBmYWxzZSkge1xuICAgIGNvbnN0IGxlbiA9IGxzdC5sZW5ndGg7XG4gICAgbGV0IEFoID0gbmV3IFVpbnQzMkFycmF5KGxlbik7XG4gICAgbGV0IEFsID0gbmV3IFVpbnQzMkFycmF5KGxlbik7XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBsZW47IGkrKykge1xuICAgICAgICBjb25zdCB7IGgsIGwgfSA9IGZyb21CaWcobHN0W2ldLCBsZSk7XG4gICAgICAgIFtBaFtpXSwgQWxbaV1dID0gW2gsIGxdO1xuICAgIH1cbiAgICByZXR1cm4gW0FoLCBBbF07XG59XG5jb25zdCB0b0JpZyA9IChoLCBsKSA9PiAoQmlnSW50KGggPj4+IDApIDw8IF8zMm4pIHwgQmlnSW50KGwgPj4+IDApO1xuZXhwb3J0cy50b0JpZyA9IHRvQmlnO1xuLy8gZm9yIFNoaWZ0IGluIFswLCAzMilcbmNvbnN0IHNoclNIID0gKGgsIF9sLCBzKSA9PiBoID4+PiBzO1xuZXhwb3J0cy5zaHJTSCA9IHNoclNIO1xuY29uc3Qgc2hyU0wgPSAoaCwgbCwgcykgPT4gKGggPDwgKDMyIC0gcykpIHwgKGwgPj4+IHMpO1xuZXhwb3J0cy5zaHJTTCA9IHNoclNMO1xuLy8gUmlnaHQgcm90YXRlIGZvciBTaGlmdCBpbiBbMSwgMzIpXG5jb25zdCByb3RyU0ggPSAoaCwgbCwgcykgPT4gKGggPj4+IHMpIHwgKGwgPDwgKDMyIC0gcykpO1xuZXhwb3J0cy5yb3RyU0ggPSByb3RyU0g7XG5jb25zdCByb3RyU0wgPSAoaCwgbCwgcykgPT4gKGggPDwgKDMyIC0gcykpIHwgKGwgPj4+IHMpO1xuZXhwb3J0cy5yb3RyU0wgPSByb3RyU0w7XG4vLyBSaWdodCByb3RhdGUgZm9yIFNoaWZ0IGluICgzMiwgNjQpLCBOT1RFOiAzMiBpcyBzcGVjaWFsIGNhc2UuXG5jb25zdCByb3RyQkggPSAoaCwgbCwgcykgPT4gKGggPDwgKDY0IC0gcykpIHwgKGwgPj4+IChzIC0gMzIpKTtcbmV4cG9ydHMucm90ckJIID0gcm90ckJIO1xuY29uc3Qgcm90ckJMID0gKGgsIGwsIHMpID0+IChoID4+PiAocyAtIDMyKSkgfCAobCA8PCAoNjQgLSBzKSk7XG5leHBvcnRzLnJvdHJCTCA9IHJvdHJCTDtcbi8vIFJpZ2h0IHJvdGF0ZSBmb3Igc2hpZnQ9PT0zMiAoanVzdCBzd2FwcyBsJmgpXG5jb25zdCByb3RyMzJIID0gKF9oLCBsKSA9PiBsO1xuZXhwb3J0cy5yb3RyMzJIID0gcm90cjMySDtcbmNvbnN0IHJvdHIzMkwgPSAoaCwgX2wpID0+IGg7XG5leHBvcnRzLnJvdHIzMkwgPSByb3RyMzJMO1xuLy8gTGVmdCByb3RhdGUgZm9yIFNoaWZ0IGluIFsxLCAzMilcbmNvbnN0IHJvdGxTSCA9IChoLCBsLCBzKSA9PiAoaCA8PCBzKSB8IChsID4+PiAoMzIgLSBzKSk7XG5leHBvcnRzLnJvdGxTSCA9IHJvdGxTSDtcbmNvbnN0IHJvdGxTTCA9IChoLCBsLCBzKSA9PiAobCA8PCBzKSB8IChoID4+PiAoMzIgLSBzKSk7XG5leHBvcnRzLnJvdGxTTCA9IHJvdGxTTDtcbi8vIExlZnQgcm90YXRlIGZvciBTaGlmdCBpbiAoMzIsIDY0KSwgTk9URTogMzIgaXMgc3BlY2lhbCBjYXNlLlxuY29uc3Qgcm90bEJIID0gKGgsIGwsIHMpID0+IChsIDw8IChzIC0gMzIpKSB8IChoID4+PiAoNjQgLSBzKSk7XG5leHBvcnRzLnJvdGxCSCA9IHJvdGxCSDtcbmNvbnN0IHJvdGxCTCA9IChoLCBsLCBzKSA9PiAoaCA8PCAocyAtIDMyKSkgfCAobCA+Pj4gKDY0IC0gcykpO1xuZXhwb3J0cy5yb3RsQkwgPSByb3RsQkw7XG4vLyBKUyB1c2VzIDMyLWJpdCBzaWduZWQgaW50ZWdlcnMgZm9yIGJpdHdpc2Ugb3BlcmF0aW9ucyB3aGljaCBtZWFucyB3ZSBjYW5ub3Rcbi8vIHNpbXBsZSB0YWtlIGNhcnJ5IG91dCBvZiBsb3cgYml0IHN1bSBieSBzaGlmdCwgd2UgbmVlZCB0byB1c2UgZGl2aXNpb24uXG5mdW5jdGlvbiBhZGQoQWgsIEFsLCBCaCwgQmwpIHtcbiAgICBjb25zdCBsID0gKEFsID4+PiAwKSArIChCbCA+Pj4gMCk7XG4gICAgcmV0dXJuIHsgaDogKEFoICsgQmggKyAoKGwgLyAyICoqIDMyKSB8IDApKSB8IDAsIGw6IGwgfCAwIH07XG59XG4vLyBBZGRpdGlvbiB3aXRoIG1vcmUgdGhhbiAyIGVsZW1lbnRzXG5jb25zdCBhZGQzTCA9IChBbCwgQmwsIENsKSA9PiAoQWwgPj4+IDApICsgKEJsID4+PiAwKSArIChDbCA+Pj4gMCk7XG5leHBvcnRzLmFkZDNMID0gYWRkM0w7XG5jb25zdCBhZGQzSCA9IChsb3csIEFoLCBCaCwgQ2gpID0+IChBaCArIEJoICsgQ2ggKyAoKGxvdyAvIDIgKiogMzIpIHwgMCkpIHwgMDtcbmV4cG9ydHMuYWRkM0ggPSBhZGQzSDtcbmNvbnN0IGFkZDRMID0gKEFsLCBCbCwgQ2wsIERsKSA9PiAoQWwgPj4+IDApICsgKEJsID4+PiAwKSArIChDbCA+Pj4gMCkgKyAoRGwgPj4+IDApO1xuZXhwb3J0cy5hZGQ0TCA9IGFkZDRMO1xuY29uc3QgYWRkNEggPSAobG93LCBBaCwgQmgsIENoLCBEaCkgPT4gKEFoICsgQmggKyBDaCArIERoICsgKChsb3cgLyAyICoqIDMyKSB8IDApKSB8IDA7XG5leHBvcnRzLmFkZDRIID0gYWRkNEg7XG5jb25zdCBhZGQ1TCA9IChBbCwgQmwsIENsLCBEbCwgRWwpID0+IChBbCA+Pj4gMCkgKyAoQmwgPj4+IDApICsgKENsID4+PiAwKSArIChEbCA+Pj4gMCkgKyAoRWwgPj4+IDApO1xuZXhwb3J0cy5hZGQ1TCA9IGFkZDVMO1xuY29uc3QgYWRkNUggPSAobG93LCBBaCwgQmgsIENoLCBEaCwgRWgpID0+IChBaCArIEJoICsgQ2ggKyBEaCArIEVoICsgKChsb3cgLyAyICoqIDMyKSB8IDApKSB8IDA7XG5leHBvcnRzLmFkZDVIID0gYWRkNUg7XG4vLyBwcmV0dGllci1pZ25vcmVcbmNvbnN0IHU2NCA9IHtcbiAgICBmcm9tQmlnLCBzcGxpdCwgdG9CaWcsXG4gICAgc2hyU0gsIHNoclNMLFxuICAgIHJvdHJTSCwgcm90clNMLCByb3RyQkgsIHJvdHJCTCxcbiAgICByb3RyMzJILCByb3RyMzJMLFxuICAgIHJvdGxTSCwgcm90bFNMLCByb3RsQkgsIHJvdGxCTCxcbiAgICBhZGQsIGFkZDNMLCBhZGQzSCwgYWRkNEwsIGFkZDRILCBhZGQ1SCwgYWRkNUwsXG59O1xuZXhwb3J0cy5kZWZhdWx0ID0gdTY0O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9X3U2NC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/_u64.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/cryptoNode.js":
/*!**************************************************!*\
  !*** ./node_modules/@noble/hashes/cryptoNode.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.crypto = void 0;\n/**\n * Internal webcrypto alias.\n * We prefer WebCrypto aka globalThis.crypto, which exists in node.js 16+.\n * Falls back to Node.js built-in crypto for Node.js <=v14.\n * See utils.ts for details.\n * @module\n */\n// @ts-ignore\nconst nc = __webpack_require__(/*! node:crypto */ \"node:crypto\");\nexports.crypto = nc && typeof nc === 'object' && 'webcrypto' in nc\n    ? nc.webcrypto\n    : nc && typeof nc === 'object' && 'randomBytes' in nc\n        ? nc\n        : undefined;\n//# sourceMappingURL=cryptoNode.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG5vYmxlL2hhc2hlcy9jcnlwdG9Ob2RlLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxtQkFBTyxDQUFDLGdDQUFhO0FBQ2hDLGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcaGFja290aG9uXFxCbG9ja2NoYWluXFxWaW50cmVrXFxub2RlX21vZHVsZXNcXEBub2JsZVxcaGFzaGVzXFxjcnlwdG9Ob2RlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5jcnlwdG8gPSB2b2lkIDA7XG4vKipcbiAqIEludGVybmFsIHdlYmNyeXB0byBhbGlhcy5cbiAqIFdlIHByZWZlciBXZWJDcnlwdG8gYWthIGdsb2JhbFRoaXMuY3J5cHRvLCB3aGljaCBleGlzdHMgaW4gbm9kZS5qcyAxNisuXG4gKiBGYWxscyBiYWNrIHRvIE5vZGUuanMgYnVpbHQtaW4gY3J5cHRvIGZvciBOb2RlLmpzIDw9djE0LlxuICogU2VlIHV0aWxzLnRzIGZvciBkZXRhaWxzLlxuICogQG1vZHVsZVxuICovXG4vLyBAdHMtaWdub3JlXG5jb25zdCBuYyA9IHJlcXVpcmUoXCJub2RlOmNyeXB0b1wiKTtcbmV4cG9ydHMuY3J5cHRvID0gbmMgJiYgdHlwZW9mIG5jID09PSAnb2JqZWN0JyAmJiAnd2ViY3J5cHRvJyBpbiBuY1xuICAgID8gbmMud2ViY3J5cHRvXG4gICAgOiBuYyAmJiB0eXBlb2YgbmMgPT09ICdvYmplY3QnICYmICdyYW5kb21CeXRlcycgaW4gbmNcbiAgICAgICAgPyBuY1xuICAgICAgICA6IHVuZGVmaW5lZDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNyeXB0b05vZGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/cryptoNode.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/hmac.js":
/*!********************************************!*\
  !*** ./node_modules/@noble/hashes/hmac.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.hmac = exports.HMAC = void 0;\n/**\n * HMAC: RFC2104 message authentication code.\n * @module\n */\nconst utils_ts_1 = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@noble/hashes/utils.js\");\nclass HMAC extends utils_ts_1.Hash {\n    constructor(hash, _key) {\n        super();\n        this.finished = false;\n        this.destroyed = false;\n        (0, utils_ts_1.ahash)(hash);\n        const key = (0, utils_ts_1.toBytes)(_key);\n        this.iHash = hash.create();\n        if (typeof this.iHash.update !== 'function')\n            throw new Error('Expected instance of class which extends utils.Hash');\n        this.blockLen = this.iHash.blockLen;\n        this.outputLen = this.iHash.outputLen;\n        const blockLen = this.blockLen;\n        const pad = new Uint8Array(blockLen);\n        // blockLen can be bigger than outputLen\n        pad.set(key.length > blockLen ? hash.create().update(key).digest() : key);\n        for (let i = 0; i < pad.length; i++)\n            pad[i] ^= 0x36;\n        this.iHash.update(pad);\n        // By doing update (processing of first block) of outer hash here we can re-use it between multiple calls via clone\n        this.oHash = hash.create();\n        // Undo internal XOR && apply outer XOR\n        for (let i = 0; i < pad.length; i++)\n            pad[i] ^= 0x36 ^ 0x5c;\n        this.oHash.update(pad);\n        (0, utils_ts_1.clean)(pad);\n    }\n    update(buf) {\n        (0, utils_ts_1.aexists)(this);\n        this.iHash.update(buf);\n        return this;\n    }\n    digestInto(out) {\n        (0, utils_ts_1.aexists)(this);\n        (0, utils_ts_1.abytes)(out, this.outputLen);\n        this.finished = true;\n        this.iHash.digestInto(out);\n        this.oHash.update(out);\n        this.oHash.digestInto(out);\n        this.destroy();\n    }\n    digest() {\n        const out = new Uint8Array(this.oHash.outputLen);\n        this.digestInto(out);\n        return out;\n    }\n    _cloneInto(to) {\n        // Create new instance without calling constructor since key already in state and we don't know it.\n        to || (to = Object.create(Object.getPrototypeOf(this), {}));\n        const { oHash, iHash, finished, destroyed, blockLen, outputLen } = this;\n        to = to;\n        to.finished = finished;\n        to.destroyed = destroyed;\n        to.blockLen = blockLen;\n        to.outputLen = outputLen;\n        to.oHash = oHash._cloneInto(to.oHash);\n        to.iHash = iHash._cloneInto(to.iHash);\n        return to;\n    }\n    clone() {\n        return this._cloneInto();\n    }\n    destroy() {\n        this.destroyed = true;\n        this.oHash.destroy();\n        this.iHash.destroy();\n    }\n}\nexports.HMAC = HMAC;\n/**\n * HMAC: RFC2104 message authentication code.\n * @param hash - function that would be used e.g. sha256\n * @param key - message key\n * @param message - message data\n * @example\n * import { hmac } from '@noble/hashes/hmac';\n * import { sha256 } from '@noble/hashes/sha2';\n * const mac1 = hmac(sha256, 'key', 'message');\n */\nconst hmac = (hash, key, message) => new HMAC(hash, key).update(message).digest();\nexports.hmac = hmac;\nexports.hmac.create = (hash, key) => new HMAC(hash, key);\n//# sourceMappingURL=hmac.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/hmac.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/pbkdf2.js":
/*!**********************************************!*\
  !*** ./node_modules/@noble/hashes/pbkdf2.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.pbkdf2 = pbkdf2;\nexports.pbkdf2Async = pbkdf2Async;\n/**\n * PBKDF (RFC 2898). Can be used to create a key from password and salt.\n * @module\n */\nconst hmac_ts_1 = __webpack_require__(/*! ./hmac.js */ \"(ssr)/./node_modules/@noble/hashes/hmac.js\");\n// prettier-ignore\nconst utils_ts_1 = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@noble/hashes/utils.js\");\n// Common prologue and epilogue for sync/async functions\nfunction pbkdf2Init(hash, _password, _salt, _opts) {\n    (0, utils_ts_1.ahash)(hash);\n    const opts = (0, utils_ts_1.checkOpts)({ dkLen: 32, asyncTick: 10 }, _opts);\n    const { c, dkLen, asyncTick } = opts;\n    (0, utils_ts_1.anumber)(c);\n    (0, utils_ts_1.anumber)(dkLen);\n    (0, utils_ts_1.anumber)(asyncTick);\n    if (c < 1)\n        throw new Error('iterations (c) should be >= 1');\n    const password = (0, utils_ts_1.kdfInputToBytes)(_password);\n    const salt = (0, utils_ts_1.kdfInputToBytes)(_salt);\n    // DK = PBKDF2(PRF, Password, Salt, c, dkLen);\n    const DK = new Uint8Array(dkLen);\n    // U1 = PRF(Password, Salt + INT_32_BE(i))\n    const PRF = hmac_ts_1.hmac.create(hash, password);\n    const PRFSalt = PRF._cloneInto().update(salt);\n    return { c, dkLen, asyncTick, DK, PRF, PRFSalt };\n}\nfunction pbkdf2Output(PRF, PRFSalt, DK, prfW, u) {\n    PRF.destroy();\n    PRFSalt.destroy();\n    if (prfW)\n        prfW.destroy();\n    (0, utils_ts_1.clean)(u);\n    return DK;\n}\n/**\n * PBKDF2-HMAC: RFC 2898 key derivation function\n * @param hash - hash function that would be used e.g. sha256\n * @param password - password from which a derived key is generated\n * @param salt - cryptographic salt\n * @param opts - {c, dkLen} where c is work factor and dkLen is output message size\n * @example\n * const key = pbkdf2(sha256, 'password', 'salt', { dkLen: 32, c: Math.pow(2, 18) });\n */\nfunction pbkdf2(hash, password, salt, opts) {\n    const { c, dkLen, DK, PRF, PRFSalt } = pbkdf2Init(hash, password, salt, opts);\n    let prfW; // Working copy\n    const arr = new Uint8Array(4);\n    const view = (0, utils_ts_1.createView)(arr);\n    const u = new Uint8Array(PRF.outputLen);\n    // DK = T1 + T2 + ⋯ + Tdklen/hlen\n    for (let ti = 1, pos = 0; pos < dkLen; ti++, pos += PRF.outputLen) {\n        // Ti = F(Password, Salt, c, i)\n        const Ti = DK.subarray(pos, pos + PRF.outputLen);\n        view.setInt32(0, ti, false);\n        // F(Password, Salt, c, i) = U1 ^ U2 ^ ⋯ ^ Uc\n        // U1 = PRF(Password, Salt + INT_32_BE(i))\n        (prfW = PRFSalt._cloneInto(prfW)).update(arr).digestInto(u);\n        Ti.set(u.subarray(0, Ti.length));\n        for (let ui = 1; ui < c; ui++) {\n            // Uc = PRF(Password, Uc−1)\n            PRF._cloneInto(prfW).update(u).digestInto(u);\n            for (let i = 0; i < Ti.length; i++)\n                Ti[i] ^= u[i];\n        }\n    }\n    return pbkdf2Output(PRF, PRFSalt, DK, prfW, u);\n}\n/**\n * PBKDF2-HMAC: RFC 2898 key derivation function. Async version.\n * @example\n * await pbkdf2Async(sha256, 'password', 'salt', { dkLen: 32, c: 500_000 });\n */\nasync function pbkdf2Async(hash, password, salt, opts) {\n    const { c, dkLen, asyncTick, DK, PRF, PRFSalt } = pbkdf2Init(hash, password, salt, opts);\n    let prfW; // Working copy\n    const arr = new Uint8Array(4);\n    const view = (0, utils_ts_1.createView)(arr);\n    const u = new Uint8Array(PRF.outputLen);\n    // DK = T1 + T2 + ⋯ + Tdklen/hlen\n    for (let ti = 1, pos = 0; pos < dkLen; ti++, pos += PRF.outputLen) {\n        // Ti = F(Password, Salt, c, i)\n        const Ti = DK.subarray(pos, pos + PRF.outputLen);\n        view.setInt32(0, ti, false);\n        // F(Password, Salt, c, i) = U1 ^ U2 ^ ⋯ ^ Uc\n        // U1 = PRF(Password, Salt + INT_32_BE(i))\n        (prfW = PRFSalt._cloneInto(prfW)).update(arr).digestInto(u);\n        Ti.set(u.subarray(0, Ti.length));\n        await (0, utils_ts_1.asyncLoop)(c - 1, asyncTick, () => {\n            // Uc = PRF(Password, Uc−1)\n            PRF._cloneInto(prfW).update(u).digestInto(u);\n            for (let i = 0; i < Ti.length; i++)\n                Ti[i] ^= u[i];\n        });\n    }\n    return pbkdf2Output(PRF, PRFSalt, DK, prfW, u);\n}\n//# sourceMappingURL=pbkdf2.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/pbkdf2.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/sha2.js":
/*!********************************************!*\
  !*** ./node_modules/@noble/hashes/sha2.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.sha512_224 = exports.sha512_256 = exports.sha384 = exports.sha512 = exports.sha224 = exports.sha256 = exports.SHA512_256 = exports.SHA512_224 = exports.SHA384 = exports.SHA512 = exports.SHA224 = exports.SHA256 = void 0;\n/**\n * SHA2 hash function. A.k.a. sha256, sha384, sha512, sha512_224, sha512_256.\n * SHA256 is the fastest hash implementable in JS, even faster than Blake3.\n * Check out [RFC 4634](https://datatracker.ietf.org/doc/html/rfc4634) and\n * [FIPS 180-4](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf).\n * @module\n */\nconst _md_ts_1 = __webpack_require__(/*! ./_md.js */ \"(ssr)/./node_modules/@noble/hashes/_md.js\");\nconst u64 = __webpack_require__(/*! ./_u64.js */ \"(ssr)/./node_modules/@noble/hashes/_u64.js\");\nconst utils_ts_1 = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@noble/hashes/utils.js\");\n/**\n * Round constants:\n * First 32 bits of fractional parts of the cube roots of the first 64 primes 2..311)\n */\n// prettier-ignore\nconst SHA256_K = /* @__PURE__ */ Uint32Array.from([\n    0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,\n    0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,\n    0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,\n    0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,\n    0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,\n    0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,\n    0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,\n    0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2\n]);\n/** Reusable temporary buffer. \"W\" comes straight from spec. */\nconst SHA256_W = /* @__PURE__ */ new Uint32Array(64);\nclass SHA256 extends _md_ts_1.HashMD {\n    constructor(outputLen = 32) {\n        super(64, outputLen, 8, false);\n        // We cannot use array here since array allows indexing by variable\n        // which means optimizer/compiler cannot use registers.\n        this.A = _md_ts_1.SHA256_IV[0] | 0;\n        this.B = _md_ts_1.SHA256_IV[1] | 0;\n        this.C = _md_ts_1.SHA256_IV[2] | 0;\n        this.D = _md_ts_1.SHA256_IV[3] | 0;\n        this.E = _md_ts_1.SHA256_IV[4] | 0;\n        this.F = _md_ts_1.SHA256_IV[5] | 0;\n        this.G = _md_ts_1.SHA256_IV[6] | 0;\n        this.H = _md_ts_1.SHA256_IV[7] | 0;\n    }\n    get() {\n        const { A, B, C, D, E, F, G, H } = this;\n        return [A, B, C, D, E, F, G, H];\n    }\n    // prettier-ignore\n    set(A, B, C, D, E, F, G, H) {\n        this.A = A | 0;\n        this.B = B | 0;\n        this.C = C | 0;\n        this.D = D | 0;\n        this.E = E | 0;\n        this.F = F | 0;\n        this.G = G | 0;\n        this.H = H | 0;\n    }\n    process(view, offset) {\n        // Extend the first 16 words into the remaining 48 words w[16..63] of the message schedule array\n        for (let i = 0; i < 16; i++, offset += 4)\n            SHA256_W[i] = view.getUint32(offset, false);\n        for (let i = 16; i < 64; i++) {\n            const W15 = SHA256_W[i - 15];\n            const W2 = SHA256_W[i - 2];\n            const s0 = (0, utils_ts_1.rotr)(W15, 7) ^ (0, utils_ts_1.rotr)(W15, 18) ^ (W15 >>> 3);\n            const s1 = (0, utils_ts_1.rotr)(W2, 17) ^ (0, utils_ts_1.rotr)(W2, 19) ^ (W2 >>> 10);\n            SHA256_W[i] = (s1 + SHA256_W[i - 7] + s0 + SHA256_W[i - 16]) | 0;\n        }\n        // Compression function main loop, 64 rounds\n        let { A, B, C, D, E, F, G, H } = this;\n        for (let i = 0; i < 64; i++) {\n            const sigma1 = (0, utils_ts_1.rotr)(E, 6) ^ (0, utils_ts_1.rotr)(E, 11) ^ (0, utils_ts_1.rotr)(E, 25);\n            const T1 = (H + sigma1 + (0, _md_ts_1.Chi)(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n            const sigma0 = (0, utils_ts_1.rotr)(A, 2) ^ (0, utils_ts_1.rotr)(A, 13) ^ (0, utils_ts_1.rotr)(A, 22);\n            const T2 = (sigma0 + (0, _md_ts_1.Maj)(A, B, C)) | 0;\n            H = G;\n            G = F;\n            F = E;\n            E = (D + T1) | 0;\n            D = C;\n            C = B;\n            B = A;\n            A = (T1 + T2) | 0;\n        }\n        // Add the compressed chunk to the current hash value\n        A = (A + this.A) | 0;\n        B = (B + this.B) | 0;\n        C = (C + this.C) | 0;\n        D = (D + this.D) | 0;\n        E = (E + this.E) | 0;\n        F = (F + this.F) | 0;\n        G = (G + this.G) | 0;\n        H = (H + this.H) | 0;\n        this.set(A, B, C, D, E, F, G, H);\n    }\n    roundClean() {\n        (0, utils_ts_1.clean)(SHA256_W);\n    }\n    destroy() {\n        this.set(0, 0, 0, 0, 0, 0, 0, 0);\n        (0, utils_ts_1.clean)(this.buffer);\n    }\n}\nexports.SHA256 = SHA256;\nclass SHA224 extends SHA256 {\n    constructor() {\n        super(28);\n        this.A = _md_ts_1.SHA224_IV[0] | 0;\n        this.B = _md_ts_1.SHA224_IV[1] | 0;\n        this.C = _md_ts_1.SHA224_IV[2] | 0;\n        this.D = _md_ts_1.SHA224_IV[3] | 0;\n        this.E = _md_ts_1.SHA224_IV[4] | 0;\n        this.F = _md_ts_1.SHA224_IV[5] | 0;\n        this.G = _md_ts_1.SHA224_IV[6] | 0;\n        this.H = _md_ts_1.SHA224_IV[7] | 0;\n    }\n}\nexports.SHA224 = SHA224;\n// SHA2-512 is slower than sha256 in js because u64 operations are slow.\n// Round contants\n// First 32 bits of the fractional parts of the cube roots of the first 80 primes 2..409\n// prettier-ignore\nconst K512 = /* @__PURE__ */ (() => u64.split([\n    '0x428a2f98d728ae22', '0x7137449123ef65cd', '0xb5c0fbcfec4d3b2f', '0xe9b5dba58189dbbc',\n    '0x3956c25bf348b538', '0x59f111f1b605d019', '0x923f82a4af194f9b', '0xab1c5ed5da6d8118',\n    '0xd807aa98a3030242', '0x12835b0145706fbe', '0x243185be4ee4b28c', '0x550c7dc3d5ffb4e2',\n    '0x72be5d74f27b896f', '0x80deb1fe3b1696b1', '0x9bdc06a725c71235', '0xc19bf174cf692694',\n    '0xe49b69c19ef14ad2', '0xefbe4786384f25e3', '0x0fc19dc68b8cd5b5', '0x240ca1cc77ac9c65',\n    '0x2de92c6f592b0275', '0x4a7484aa6ea6e483', '0x5cb0a9dcbd41fbd4', '0x76f988da831153b5',\n    '0x983e5152ee66dfab', '0xa831c66d2db43210', '0xb00327c898fb213f', '0xbf597fc7beef0ee4',\n    '0xc6e00bf33da88fc2', '0xd5a79147930aa725', '0x06ca6351e003826f', '0x142929670a0e6e70',\n    '0x27b70a8546d22ffc', '0x2e1b21385c26c926', '0x4d2c6dfc5ac42aed', '0x53380d139d95b3df',\n    '0x650a73548baf63de', '0x766a0abb3c77b2a8', '0x81c2c92e47edaee6', '0x92722c851482353b',\n    '0xa2bfe8a14cf10364', '0xa81a664bbc423001', '0xc24b8b70d0f89791', '0xc76c51a30654be30',\n    '0xd192e819d6ef5218', '0xd69906245565a910', '0xf40e35855771202a', '0x106aa07032bbd1b8',\n    '0x19a4c116b8d2d0c8', '0x1e376c085141ab53', '0x2748774cdf8eeb99', '0x34b0bcb5e19b48a8',\n    '0x391c0cb3c5c95a63', '0x4ed8aa4ae3418acb', '0x5b9cca4f7763e373', '0x682e6ff3d6b2b8a3',\n    '0x748f82ee5defb2fc', '0x78a5636f43172f60', '0x84c87814a1f0ab72', '0x8cc702081a6439ec',\n    '0x90befffa23631e28', '0xa4506cebde82bde9', '0xbef9a3f7b2c67915', '0xc67178f2e372532b',\n    '0xca273eceea26619c', '0xd186b8c721c0c207', '0xeada7dd6cde0eb1e', '0xf57d4f7fee6ed178',\n    '0x06f067aa72176fba', '0x0a637dc5a2c898a6', '0x113f9804bef90dae', '0x1b710b35131c471b',\n    '0x28db77f523047d84', '0x32caab7b40c72493', '0x3c9ebe0a15c9bebc', '0x431d67c49c100d4c',\n    '0x4cc5d4becb3e42b6', '0x597f299cfc657e2a', '0x5fcb6fab3ad6faec', '0x6c44198c4a475817'\n].map(n => BigInt(n))))();\nconst SHA512_Kh = /* @__PURE__ */ (() => K512[0])();\nconst SHA512_Kl = /* @__PURE__ */ (() => K512[1])();\n// Reusable temporary buffers\nconst SHA512_W_H = /* @__PURE__ */ new Uint32Array(80);\nconst SHA512_W_L = /* @__PURE__ */ new Uint32Array(80);\nclass SHA512 extends _md_ts_1.HashMD {\n    constructor(outputLen = 64) {\n        super(128, outputLen, 16, false);\n        // We cannot use array here since array allows indexing by variable\n        // which means optimizer/compiler cannot use registers.\n        // h -- high 32 bits, l -- low 32 bits\n        this.Ah = _md_ts_1.SHA512_IV[0] | 0;\n        this.Al = _md_ts_1.SHA512_IV[1] | 0;\n        this.Bh = _md_ts_1.SHA512_IV[2] | 0;\n        this.Bl = _md_ts_1.SHA512_IV[3] | 0;\n        this.Ch = _md_ts_1.SHA512_IV[4] | 0;\n        this.Cl = _md_ts_1.SHA512_IV[5] | 0;\n        this.Dh = _md_ts_1.SHA512_IV[6] | 0;\n        this.Dl = _md_ts_1.SHA512_IV[7] | 0;\n        this.Eh = _md_ts_1.SHA512_IV[8] | 0;\n        this.El = _md_ts_1.SHA512_IV[9] | 0;\n        this.Fh = _md_ts_1.SHA512_IV[10] | 0;\n        this.Fl = _md_ts_1.SHA512_IV[11] | 0;\n        this.Gh = _md_ts_1.SHA512_IV[12] | 0;\n        this.Gl = _md_ts_1.SHA512_IV[13] | 0;\n        this.Hh = _md_ts_1.SHA512_IV[14] | 0;\n        this.Hl = _md_ts_1.SHA512_IV[15] | 0;\n    }\n    // prettier-ignore\n    get() {\n        const { Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl } = this;\n        return [Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl];\n    }\n    // prettier-ignore\n    set(Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl) {\n        this.Ah = Ah | 0;\n        this.Al = Al | 0;\n        this.Bh = Bh | 0;\n        this.Bl = Bl | 0;\n        this.Ch = Ch | 0;\n        this.Cl = Cl | 0;\n        this.Dh = Dh | 0;\n        this.Dl = Dl | 0;\n        this.Eh = Eh | 0;\n        this.El = El | 0;\n        this.Fh = Fh | 0;\n        this.Fl = Fl | 0;\n        this.Gh = Gh | 0;\n        this.Gl = Gl | 0;\n        this.Hh = Hh | 0;\n        this.Hl = Hl | 0;\n    }\n    process(view, offset) {\n        // Extend the first 16 words into the remaining 64 words w[16..79] of the message schedule array\n        for (let i = 0; i < 16; i++, offset += 4) {\n            SHA512_W_H[i] = view.getUint32(offset);\n            SHA512_W_L[i] = view.getUint32((offset += 4));\n        }\n        for (let i = 16; i < 80; i++) {\n            // s0 := (w[i-15] rightrotate 1) xor (w[i-15] rightrotate 8) xor (w[i-15] rightshift 7)\n            const W15h = SHA512_W_H[i - 15] | 0;\n            const W15l = SHA512_W_L[i - 15] | 0;\n            const s0h = u64.rotrSH(W15h, W15l, 1) ^ u64.rotrSH(W15h, W15l, 8) ^ u64.shrSH(W15h, W15l, 7);\n            const s0l = u64.rotrSL(W15h, W15l, 1) ^ u64.rotrSL(W15h, W15l, 8) ^ u64.shrSL(W15h, W15l, 7);\n            // s1 := (w[i-2] rightrotate 19) xor (w[i-2] rightrotate 61) xor (w[i-2] rightshift 6)\n            const W2h = SHA512_W_H[i - 2] | 0;\n            const W2l = SHA512_W_L[i - 2] | 0;\n            const s1h = u64.rotrSH(W2h, W2l, 19) ^ u64.rotrBH(W2h, W2l, 61) ^ u64.shrSH(W2h, W2l, 6);\n            const s1l = u64.rotrSL(W2h, W2l, 19) ^ u64.rotrBL(W2h, W2l, 61) ^ u64.shrSL(W2h, W2l, 6);\n            // SHA256_W[i] = s0 + s1 + SHA256_W[i - 7] + SHA256_W[i - 16];\n            const SUMl = u64.add4L(s0l, s1l, SHA512_W_L[i - 7], SHA512_W_L[i - 16]);\n            const SUMh = u64.add4H(SUMl, s0h, s1h, SHA512_W_H[i - 7], SHA512_W_H[i - 16]);\n            SHA512_W_H[i] = SUMh | 0;\n            SHA512_W_L[i] = SUMl | 0;\n        }\n        let { Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl } = this;\n        // Compression function main loop, 80 rounds\n        for (let i = 0; i < 80; i++) {\n            // S1 := (e rightrotate 14) xor (e rightrotate 18) xor (e rightrotate 41)\n            const sigma1h = u64.rotrSH(Eh, El, 14) ^ u64.rotrSH(Eh, El, 18) ^ u64.rotrBH(Eh, El, 41);\n            const sigma1l = u64.rotrSL(Eh, El, 14) ^ u64.rotrSL(Eh, El, 18) ^ u64.rotrBL(Eh, El, 41);\n            //const T1 = (H + sigma1 + Chi(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n            const CHIh = (Eh & Fh) ^ (~Eh & Gh);\n            const CHIl = (El & Fl) ^ (~El & Gl);\n            // T1 = H + sigma1 + Chi(E, F, G) + SHA512_K[i] + SHA512_W[i]\n            // prettier-ignore\n            const T1ll = u64.add5L(Hl, sigma1l, CHIl, SHA512_Kl[i], SHA512_W_L[i]);\n            const T1h = u64.add5H(T1ll, Hh, sigma1h, CHIh, SHA512_Kh[i], SHA512_W_H[i]);\n            const T1l = T1ll | 0;\n            // S0 := (a rightrotate 28) xor (a rightrotate 34) xor (a rightrotate 39)\n            const sigma0h = u64.rotrSH(Ah, Al, 28) ^ u64.rotrBH(Ah, Al, 34) ^ u64.rotrBH(Ah, Al, 39);\n            const sigma0l = u64.rotrSL(Ah, Al, 28) ^ u64.rotrBL(Ah, Al, 34) ^ u64.rotrBL(Ah, Al, 39);\n            const MAJh = (Ah & Bh) ^ (Ah & Ch) ^ (Bh & Ch);\n            const MAJl = (Al & Bl) ^ (Al & Cl) ^ (Bl & Cl);\n            Hh = Gh | 0;\n            Hl = Gl | 0;\n            Gh = Fh | 0;\n            Gl = Fl | 0;\n            Fh = Eh | 0;\n            Fl = El | 0;\n            ({ h: Eh, l: El } = u64.add(Dh | 0, Dl | 0, T1h | 0, T1l | 0));\n            Dh = Ch | 0;\n            Dl = Cl | 0;\n            Ch = Bh | 0;\n            Cl = Bl | 0;\n            Bh = Ah | 0;\n            Bl = Al | 0;\n            const All = u64.add3L(T1l, sigma0l, MAJl);\n            Ah = u64.add3H(All, T1h, sigma0h, MAJh);\n            Al = All | 0;\n        }\n        // Add the compressed chunk to the current hash value\n        ({ h: Ah, l: Al } = u64.add(this.Ah | 0, this.Al | 0, Ah | 0, Al | 0));\n        ({ h: Bh, l: Bl } = u64.add(this.Bh | 0, this.Bl | 0, Bh | 0, Bl | 0));\n        ({ h: Ch, l: Cl } = u64.add(this.Ch | 0, this.Cl | 0, Ch | 0, Cl | 0));\n        ({ h: Dh, l: Dl } = u64.add(this.Dh | 0, this.Dl | 0, Dh | 0, Dl | 0));\n        ({ h: Eh, l: El } = u64.add(this.Eh | 0, this.El | 0, Eh | 0, El | 0));\n        ({ h: Fh, l: Fl } = u64.add(this.Fh | 0, this.Fl | 0, Fh | 0, Fl | 0));\n        ({ h: Gh, l: Gl } = u64.add(this.Gh | 0, this.Gl | 0, Gh | 0, Gl | 0));\n        ({ h: Hh, l: Hl } = u64.add(this.Hh | 0, this.Hl | 0, Hh | 0, Hl | 0));\n        this.set(Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl);\n    }\n    roundClean() {\n        (0, utils_ts_1.clean)(SHA512_W_H, SHA512_W_L);\n    }\n    destroy() {\n        (0, utils_ts_1.clean)(this.buffer);\n        this.set(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);\n    }\n}\nexports.SHA512 = SHA512;\nclass SHA384 extends SHA512 {\n    constructor() {\n        super(48);\n        this.Ah = _md_ts_1.SHA384_IV[0] | 0;\n        this.Al = _md_ts_1.SHA384_IV[1] | 0;\n        this.Bh = _md_ts_1.SHA384_IV[2] | 0;\n        this.Bl = _md_ts_1.SHA384_IV[3] | 0;\n        this.Ch = _md_ts_1.SHA384_IV[4] | 0;\n        this.Cl = _md_ts_1.SHA384_IV[5] | 0;\n        this.Dh = _md_ts_1.SHA384_IV[6] | 0;\n        this.Dl = _md_ts_1.SHA384_IV[7] | 0;\n        this.Eh = _md_ts_1.SHA384_IV[8] | 0;\n        this.El = _md_ts_1.SHA384_IV[9] | 0;\n        this.Fh = _md_ts_1.SHA384_IV[10] | 0;\n        this.Fl = _md_ts_1.SHA384_IV[11] | 0;\n        this.Gh = _md_ts_1.SHA384_IV[12] | 0;\n        this.Gl = _md_ts_1.SHA384_IV[13] | 0;\n        this.Hh = _md_ts_1.SHA384_IV[14] | 0;\n        this.Hl = _md_ts_1.SHA384_IV[15] | 0;\n    }\n}\nexports.SHA384 = SHA384;\n/**\n * Truncated SHA512/256 and SHA512/224.\n * SHA512_IV is XORed with 0xa5a5a5a5a5a5a5a5, then used as \"intermediary\" IV of SHA512/t.\n * Then t hashes string to produce result IV.\n * See `test/misc/sha2-gen-iv.js`.\n */\n/** SHA512/224 IV */\nconst T224_IV = /* @__PURE__ */ Uint32Array.from([\n    0x8c3d37c8, 0x19544da2, 0x73e19966, 0x89dcd4d6, 0x1dfab7ae, 0x32ff9c82, 0x679dd514, 0x582f9fcf,\n    0x0f6d2b69, 0x7bd44da8, 0x77e36f73, 0x04c48942, 0x3f9d85a8, 0x6a1d36c8, 0x1112e6ad, 0x91d692a1,\n]);\n/** SHA512/256 IV */\nconst T256_IV = /* @__PURE__ */ Uint32Array.from([\n    0x22312194, 0xfc2bf72c, 0x9f555fa3, 0xc84c64c2, 0x2393b86b, 0x6f53b151, 0x96387719, 0x5940eabd,\n    0x96283ee2, 0xa88effe3, 0xbe5e1e25, 0x53863992, 0x2b0199fc, 0x2c85b8aa, 0x0eb72ddc, 0x81c52ca2,\n]);\nclass SHA512_224 extends SHA512 {\n    constructor() {\n        super(28);\n        this.Ah = T224_IV[0] | 0;\n        this.Al = T224_IV[1] | 0;\n        this.Bh = T224_IV[2] | 0;\n        this.Bl = T224_IV[3] | 0;\n        this.Ch = T224_IV[4] | 0;\n        this.Cl = T224_IV[5] | 0;\n        this.Dh = T224_IV[6] | 0;\n        this.Dl = T224_IV[7] | 0;\n        this.Eh = T224_IV[8] | 0;\n        this.El = T224_IV[9] | 0;\n        this.Fh = T224_IV[10] | 0;\n        this.Fl = T224_IV[11] | 0;\n        this.Gh = T224_IV[12] | 0;\n        this.Gl = T224_IV[13] | 0;\n        this.Hh = T224_IV[14] | 0;\n        this.Hl = T224_IV[15] | 0;\n    }\n}\nexports.SHA512_224 = SHA512_224;\nclass SHA512_256 extends SHA512 {\n    constructor() {\n        super(32);\n        this.Ah = T256_IV[0] | 0;\n        this.Al = T256_IV[1] | 0;\n        this.Bh = T256_IV[2] | 0;\n        this.Bl = T256_IV[3] | 0;\n        this.Ch = T256_IV[4] | 0;\n        this.Cl = T256_IV[5] | 0;\n        this.Dh = T256_IV[6] | 0;\n        this.Dl = T256_IV[7] | 0;\n        this.Eh = T256_IV[8] | 0;\n        this.El = T256_IV[9] | 0;\n        this.Fh = T256_IV[10] | 0;\n        this.Fl = T256_IV[11] | 0;\n        this.Gh = T256_IV[12] | 0;\n        this.Gl = T256_IV[13] | 0;\n        this.Hh = T256_IV[14] | 0;\n        this.Hl = T256_IV[15] | 0;\n    }\n}\nexports.SHA512_256 = SHA512_256;\n/**\n * SHA2-256 hash function from RFC 4634.\n *\n * It is the fastest JS hash, even faster than Blake3.\n * To break sha256 using birthday attack, attackers need to try 2^128 hashes.\n * BTC network is doing 2^70 hashes/sec (2^95 hashes/year) as per 2025.\n */\nexports.sha256 = (0, utils_ts_1.createHasher)(() => new SHA256());\n/** SHA2-224 hash function from RFC 4634 */\nexports.sha224 = (0, utils_ts_1.createHasher)(() => new SHA224());\n/** SHA2-512 hash function from RFC 4634. */\nexports.sha512 = (0, utils_ts_1.createHasher)(() => new SHA512());\n/** SHA2-384 hash function from RFC 4634. */\nexports.sha384 = (0, utils_ts_1.createHasher)(() => new SHA384());\n/**\n * SHA2-512/256 \"truncated\" hash function, with improved resistance to length extension attacks.\n * See the paper on [truncated SHA512](https://eprint.iacr.org/2010/548.pdf).\n */\nexports.sha512_256 = (0, utils_ts_1.createHasher)(() => new SHA512_256());\n/**\n * SHA2-512/224 \"truncated\" hash function, with improved resistance to length extension attacks.\n * See the paper on [truncated SHA512](https://eprint.iacr.org/2010/548.pdf).\n */\nexports.sha512_224 = (0, utils_ts_1.createHasher)(() => new SHA512_224());\n//# sourceMappingURL=sha2.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/sha2.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/sha256.js":
/*!**********************************************!*\
  !*** ./node_modules/@noble/hashes/sha256.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.sha224 = exports.SHA224 = exports.sha256 = exports.SHA256 = void 0;\n/**\n * SHA2-256 a.k.a. sha256. In JS, it is the fastest hash, even faster than Blake3.\n *\n * To break sha256 using birthday attack, attackers need to try 2^128 hashes.\n * BTC network is doing 2^70 hashes/sec (2^95 hashes/year) as per 2025.\n *\n * Check out [FIPS 180-4](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf).\n * @module\n * @deprecated\n */\nconst sha2_ts_1 = __webpack_require__(/*! ./sha2.js */ \"(ssr)/./node_modules/@noble/hashes/sha2.js\");\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexports.SHA256 = sha2_ts_1.SHA256;\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexports.sha256 = sha2_ts_1.sha256;\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexports.SHA224 = sha2_ts_1.SHA224;\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexports.sha224 = sha2_ts_1.sha224;\n//# sourceMappingURL=sha256.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG5vYmxlL2hhc2hlcy9zaGEyNTYuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsY0FBYyxHQUFHLGNBQWMsR0FBRyxjQUFjLEdBQUcsY0FBYztBQUNqRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQixtQkFBTyxDQUFDLDZEQUFXO0FBQ3JDO0FBQ0EsY0FBYztBQUNkO0FBQ0EsY0FBYztBQUNkO0FBQ0EsY0FBYztBQUNkO0FBQ0EsY0FBYztBQUNkIiwic291cmNlcyI6WyJEOlxcaGFja290aG9uXFxCbG9ja2NoYWluXFxWaW50cmVrXFxub2RlX21vZHVsZXNcXEBub2JsZVxcaGFzaGVzXFxzaGEyNTYuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLnNoYTIyNCA9IGV4cG9ydHMuU0hBMjI0ID0gZXhwb3J0cy5zaGEyNTYgPSBleHBvcnRzLlNIQTI1NiA9IHZvaWQgMDtcbi8qKlxuICogU0hBMi0yNTYgYS5rLmEuIHNoYTI1Ni4gSW4gSlMsIGl0IGlzIHRoZSBmYXN0ZXN0IGhhc2gsIGV2ZW4gZmFzdGVyIHRoYW4gQmxha2UzLlxuICpcbiAqIFRvIGJyZWFrIHNoYTI1NiB1c2luZyBiaXJ0aGRheSBhdHRhY2ssIGF0dGFja2VycyBuZWVkIHRvIHRyeSAyXjEyOCBoYXNoZXMuXG4gKiBCVEMgbmV0d29yayBpcyBkb2luZyAyXjcwIGhhc2hlcy9zZWMgKDJeOTUgaGFzaGVzL3llYXIpIGFzIHBlciAyMDI1LlxuICpcbiAqIENoZWNrIG91dCBbRklQUyAxODAtNF0oaHR0cHM6Ly9udmxwdWJzLm5pc3QuZ292L25pc3RwdWJzL0ZJUFMvTklTVC5GSVBTLjE4MC00LnBkZikuXG4gKiBAbW9kdWxlXG4gKiBAZGVwcmVjYXRlZFxuICovXG5jb25zdCBzaGEyX3RzXzEgPSByZXF1aXJlKFwiLi9zaGEyLmpzXCIpO1xuLyoqIEBkZXByZWNhdGVkIFVzZSBpbXBvcnQgZnJvbSBgbm9ibGUvaGFzaGVzL3NoYTJgIG1vZHVsZSAqL1xuZXhwb3J0cy5TSEEyNTYgPSBzaGEyX3RzXzEuU0hBMjU2O1xuLyoqIEBkZXByZWNhdGVkIFVzZSBpbXBvcnQgZnJvbSBgbm9ibGUvaGFzaGVzL3NoYTJgIG1vZHVsZSAqL1xuZXhwb3J0cy5zaGEyNTYgPSBzaGEyX3RzXzEuc2hhMjU2O1xuLyoqIEBkZXByZWNhdGVkIFVzZSBpbXBvcnQgZnJvbSBgbm9ibGUvaGFzaGVzL3NoYTJgIG1vZHVsZSAqL1xuZXhwb3J0cy5TSEEyMjQgPSBzaGEyX3RzXzEuU0hBMjI0O1xuLyoqIEBkZXByZWNhdGVkIFVzZSBpbXBvcnQgZnJvbSBgbm9ibGUvaGFzaGVzL3NoYTJgIG1vZHVsZSAqL1xuZXhwb3J0cy5zaGEyMjQgPSBzaGEyX3RzXzEuc2hhMjI0O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c2hhMjU2LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/sha256.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/sha512.js":
/*!**********************************************!*\
  !*** ./node_modules/@noble/hashes/sha512.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.sha512_256 = exports.SHA512_256 = exports.sha512_224 = exports.SHA512_224 = exports.sha384 = exports.SHA384 = exports.sha512 = exports.SHA512 = void 0;\n/**\n * SHA2-512 a.k.a. sha512 and sha384. It is slower than sha256 in js because u64 operations are slow.\n *\n * Check out [RFC 4634](https://datatracker.ietf.org/doc/html/rfc4634) and\n * [the paper on truncated SHA512/256](https://eprint.iacr.org/2010/548.pdf).\n * @module\n * @deprecated\n */\nconst sha2_ts_1 = __webpack_require__(/*! ./sha2.js */ \"(ssr)/./node_modules/@noble/hashes/sha2.js\");\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexports.SHA512 = sha2_ts_1.SHA512;\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexports.sha512 = sha2_ts_1.sha512;\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexports.SHA384 = sha2_ts_1.SHA384;\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexports.sha384 = sha2_ts_1.sha384;\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexports.SHA512_224 = sha2_ts_1.SHA512_224;\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexports.sha512_224 = sha2_ts_1.sha512_224;\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexports.SHA512_256 = sha2_ts_1.SHA512_256;\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexports.sha512_256 = sha2_ts_1.sha512_256;\n//# sourceMappingURL=sha512.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG5vYmxlL2hhc2hlcy9zaGE1MTIuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsa0JBQWtCLEdBQUcsa0JBQWtCLEdBQUcsa0JBQWtCLEdBQUcsa0JBQWtCLEdBQUcsY0FBYyxHQUFHLGNBQWMsR0FBRyxjQUFjLEdBQUcsY0FBYztBQUNySjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLG1CQUFPLENBQUMsNkRBQVc7QUFDckM7QUFDQSxjQUFjO0FBQ2Q7QUFDQSxjQUFjO0FBQ2Q7QUFDQSxjQUFjO0FBQ2Q7QUFDQSxjQUFjO0FBQ2Q7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQSxrQkFBa0I7QUFDbEIiLCJzb3VyY2VzIjpbIkQ6XFxoYWNrb3Rob25cXEJsb2NrY2hhaW5cXFZpbnRyZWtcXG5vZGVfbW9kdWxlc1xcQG5vYmxlXFxoYXNoZXNcXHNoYTUxMi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuc2hhNTEyXzI1NiA9IGV4cG9ydHMuU0hBNTEyXzI1NiA9IGV4cG9ydHMuc2hhNTEyXzIyNCA9IGV4cG9ydHMuU0hBNTEyXzIyNCA9IGV4cG9ydHMuc2hhMzg0ID0gZXhwb3J0cy5TSEEzODQgPSBleHBvcnRzLnNoYTUxMiA9IGV4cG9ydHMuU0hBNTEyID0gdm9pZCAwO1xuLyoqXG4gKiBTSEEyLTUxMiBhLmsuYS4gc2hhNTEyIGFuZCBzaGEzODQuIEl0IGlzIHNsb3dlciB0aGFuIHNoYTI1NiBpbiBqcyBiZWNhdXNlIHU2NCBvcGVyYXRpb25zIGFyZSBzbG93LlxuICpcbiAqIENoZWNrIG91dCBbUkZDIDQ2MzRdKGh0dHBzOi8vZGF0YXRyYWNrZXIuaWV0Zi5vcmcvZG9jL2h0bWwvcmZjNDYzNCkgYW5kXG4gKiBbdGhlIHBhcGVyIG9uIHRydW5jYXRlZCBTSEE1MTIvMjU2XShodHRwczovL2VwcmludC5pYWNyLm9yZy8yMDEwLzU0OC5wZGYpLlxuICogQG1vZHVsZVxuICogQGRlcHJlY2F0ZWRcbiAqL1xuY29uc3Qgc2hhMl90c18xID0gcmVxdWlyZShcIi4vc2hhMi5qc1wiKTtcbi8qKiBAZGVwcmVjYXRlZCBVc2UgaW1wb3J0IGZyb20gYG5vYmxlL2hhc2hlcy9zaGEyYCBtb2R1bGUgKi9cbmV4cG9ydHMuU0hBNTEyID0gc2hhMl90c18xLlNIQTUxMjtcbi8qKiBAZGVwcmVjYXRlZCBVc2UgaW1wb3J0IGZyb20gYG5vYmxlL2hhc2hlcy9zaGEyYCBtb2R1bGUgKi9cbmV4cG9ydHMuc2hhNTEyID0gc2hhMl90c18xLnNoYTUxMjtcbi8qKiBAZGVwcmVjYXRlZCBVc2UgaW1wb3J0IGZyb20gYG5vYmxlL2hhc2hlcy9zaGEyYCBtb2R1bGUgKi9cbmV4cG9ydHMuU0hBMzg0ID0gc2hhMl90c18xLlNIQTM4NDtcbi8qKiBAZGVwcmVjYXRlZCBVc2UgaW1wb3J0IGZyb20gYG5vYmxlL2hhc2hlcy9zaGEyYCBtb2R1bGUgKi9cbmV4cG9ydHMuc2hhMzg0ID0gc2hhMl90c18xLnNoYTM4NDtcbi8qKiBAZGVwcmVjYXRlZCBVc2UgaW1wb3J0IGZyb20gYG5vYmxlL2hhc2hlcy9zaGEyYCBtb2R1bGUgKi9cbmV4cG9ydHMuU0hBNTEyXzIyNCA9IHNoYTJfdHNfMS5TSEE1MTJfMjI0O1xuLyoqIEBkZXByZWNhdGVkIFVzZSBpbXBvcnQgZnJvbSBgbm9ibGUvaGFzaGVzL3NoYTJgIG1vZHVsZSAqL1xuZXhwb3J0cy5zaGE1MTJfMjI0ID0gc2hhMl90c18xLnNoYTUxMl8yMjQ7XG4vKiogQGRlcHJlY2F0ZWQgVXNlIGltcG9ydCBmcm9tIGBub2JsZS9oYXNoZXMvc2hhMmAgbW9kdWxlICovXG5leHBvcnRzLlNIQTUxMl8yNTYgPSBzaGEyX3RzXzEuU0hBNTEyXzI1Njtcbi8qKiBAZGVwcmVjYXRlZCBVc2UgaW1wb3J0IGZyb20gYG5vYmxlL2hhc2hlcy9zaGEyYCBtb2R1bGUgKi9cbmV4cG9ydHMuc2hhNTEyXzI1NiA9IHNoYTJfdHNfMS5zaGE1MTJfMjU2O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c2hhNTEyLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/sha512.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@noble/hashes/utils.js":
/*!*********************************************!*\
  !*** ./node_modules/@noble/hashes/utils.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/**\n * Utilities for hex, bytes, CSPRNG.\n * @module\n */\n/*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.wrapXOFConstructorWithOpts = exports.wrapConstructorWithOpts = exports.wrapConstructor = exports.Hash = exports.nextTick = exports.swap32IfBE = exports.byteSwapIfBE = exports.swap8IfBE = exports.isLE = void 0;\nexports.isBytes = isBytes;\nexports.anumber = anumber;\nexports.abytes = abytes;\nexports.ahash = ahash;\nexports.aexists = aexists;\nexports.aoutput = aoutput;\nexports.u8 = u8;\nexports.u32 = u32;\nexports.clean = clean;\nexports.createView = createView;\nexports.rotr = rotr;\nexports.rotl = rotl;\nexports.byteSwap = byteSwap;\nexports.byteSwap32 = byteSwap32;\nexports.bytesToHex = bytesToHex;\nexports.hexToBytes = hexToBytes;\nexports.asyncLoop = asyncLoop;\nexports.utf8ToBytes = utf8ToBytes;\nexports.bytesToUtf8 = bytesToUtf8;\nexports.toBytes = toBytes;\nexports.kdfInputToBytes = kdfInputToBytes;\nexports.concatBytes = concatBytes;\nexports.checkOpts = checkOpts;\nexports.createHasher = createHasher;\nexports.createOptHasher = createOptHasher;\nexports.createXOFer = createXOFer;\nexports.randomBytes = randomBytes;\n// We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// node.js versions earlier than v19 don't declare it in global scope.\n// For node.js, package.json#exports field mapping rewrites import\n// from `crypto` to `cryptoNode`, which imports native module.\n// Makes the utils un-importable in browsers without a bundler.\n// Once node.js 18 is deprecated (2025-04-30), we can just drop the import.\nconst crypto_1 = __webpack_require__(/*! @noble/hashes/crypto */ \"(ssr)/./node_modules/@noble/hashes/cryptoNode.js\");\n/** Checks if something is Uint8Array. Be careful: nodejs Buffer will return true. */\nfunction isBytes(a) {\n    return a instanceof Uint8Array || (ArrayBuffer.isView(a) && a.constructor.name === 'Uint8Array');\n}\n/** Asserts something is positive integer. */\nfunction anumber(n) {\n    if (!Number.isSafeInteger(n) || n < 0)\n        throw new Error('positive integer expected, got ' + n);\n}\n/** Asserts something is Uint8Array. */\nfunction abytes(b, ...lengths) {\n    if (!isBytes(b))\n        throw new Error('Uint8Array expected');\n    if (lengths.length > 0 && !lengths.includes(b.length))\n        throw new Error('Uint8Array expected of length ' + lengths + ', got length=' + b.length);\n}\n/** Asserts something is hash */\nfunction ahash(h) {\n    if (typeof h !== 'function' || typeof h.create !== 'function')\n        throw new Error('Hash should be wrapped by utils.createHasher');\n    anumber(h.outputLen);\n    anumber(h.blockLen);\n}\n/** Asserts a hash instance has not been destroyed / finished */\nfunction aexists(instance, checkFinished = true) {\n    if (instance.destroyed)\n        throw new Error('Hash instance has been destroyed');\n    if (checkFinished && instance.finished)\n        throw new Error('Hash#digest() has already been called');\n}\n/** Asserts output is properly-sized byte array */\nfunction aoutput(out, instance) {\n    abytes(out);\n    const min = instance.outputLen;\n    if (out.length < min) {\n        throw new Error('digestInto() expects output buffer of length at least ' + min);\n    }\n}\n/** Cast u8 / u16 / u32 to u8. */\nfunction u8(arr) {\n    return new Uint8Array(arr.buffer, arr.byteOffset, arr.byteLength);\n}\n/** Cast u8 / u16 / u32 to u32. */\nfunction u32(arr) {\n    return new Uint32Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 4));\n}\n/** Zeroize a byte array. Warning: JS provides no guarantees. */\nfunction clean(...arrays) {\n    for (let i = 0; i < arrays.length; i++) {\n        arrays[i].fill(0);\n    }\n}\n/** Create DataView of an array for easy byte-level manipulation. */\nfunction createView(arr) {\n    return new DataView(arr.buffer, arr.byteOffset, arr.byteLength);\n}\n/** The rotate right (circular right shift) operation for uint32 */\nfunction rotr(word, shift) {\n    return (word << (32 - shift)) | (word >>> shift);\n}\n/** The rotate left (circular left shift) operation for uint32 */\nfunction rotl(word, shift) {\n    return (word << shift) | ((word >>> (32 - shift)) >>> 0);\n}\n/** Is current platform little-endian? Most are. Big-Endian platform: IBM */\nexports.isLE = (() => new Uint8Array(new Uint32Array([0x11223344]).buffer)[0] === 0x44)();\n/** The byte swap operation for uint32 */\nfunction byteSwap(word) {\n    return (((word << 24) & 0xff000000) |\n        ((word << 8) & 0xff0000) |\n        ((word >>> 8) & 0xff00) |\n        ((word >>> 24) & 0xff));\n}\n/** Conditionally byte swap if on a big-endian platform */\nexports.swap8IfBE = exports.isLE\n    ? (n) => n\n    : (n) => byteSwap(n);\n/** @deprecated */\nexports.byteSwapIfBE = exports.swap8IfBE;\n/** In place byte swap for Uint32Array */\nfunction byteSwap32(arr) {\n    for (let i = 0; i < arr.length; i++) {\n        arr[i] = byteSwap(arr[i]);\n    }\n    return arr;\n}\nexports.swap32IfBE = exports.isLE\n    ? (u) => u\n    : byteSwap32;\n// Built-in hex conversion https://caniuse.com/mdn-javascript_builtins_uint8array_fromhex\nconst hasHexBuiltin = /* @__PURE__ */ (() => \n// @ts-ignore\ntypeof Uint8Array.from([]).toHex === 'function' && typeof Uint8Array.fromHex === 'function')();\n// Array where index 0xf0 (240) is mapped to string 'f0'\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) => i.toString(16).padStart(2, '0'));\n/**\n * Convert byte array to hex string. Uses built-in function, when available.\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nfunction bytesToHex(bytes) {\n    abytes(bytes);\n    // @ts-ignore\n    if (hasHexBuiltin)\n        return bytes.toHex();\n    // pre-caching improves the speed 6x\n    let hex = '';\n    for (let i = 0; i < bytes.length; i++) {\n        hex += hexes[bytes[i]];\n    }\n    return hex;\n}\n// We use optimized technique to convert hex string to byte array\nconst asciis = { _0: 48, _9: 57, A: 65, F: 70, a: 97, f: 102 };\nfunction asciiToBase16(ch) {\n    if (ch >= asciis._0 && ch <= asciis._9)\n        return ch - asciis._0; // '2' => 50-48\n    if (ch >= asciis.A && ch <= asciis.F)\n        return ch - (asciis.A - 10); // 'B' => 66-(65-10)\n    if (ch >= asciis.a && ch <= asciis.f)\n        return ch - (asciis.a - 10); // 'b' => 98-(97-10)\n    return;\n}\n/**\n * Convert hex string to byte array. Uses built-in function, when available.\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nfunction hexToBytes(hex) {\n    if (typeof hex !== 'string')\n        throw new Error('hex string expected, got ' + typeof hex);\n    // @ts-ignore\n    if (hasHexBuiltin)\n        return Uint8Array.fromHex(hex);\n    const hl = hex.length;\n    const al = hl / 2;\n    if (hl % 2)\n        throw new Error('hex string expected, got unpadded hex of length ' + hl);\n    const array = new Uint8Array(al);\n    for (let ai = 0, hi = 0; ai < al; ai++, hi += 2) {\n        const n1 = asciiToBase16(hex.charCodeAt(hi));\n        const n2 = asciiToBase16(hex.charCodeAt(hi + 1));\n        if (n1 === undefined || n2 === undefined) {\n            const char = hex[hi] + hex[hi + 1];\n            throw new Error('hex string expected, got non-hex character \"' + char + '\" at index ' + hi);\n        }\n        array[ai] = n1 * 16 + n2; // multiply first octet, e.g. 'a3' => 10*16+3 => 160 + 3 => 163\n    }\n    return array;\n}\n/**\n * There is no setImmediate in browser and setTimeout is slow.\n * Call of async fn will return Promise, which will be fullfiled only on\n * next scheduler queue processing step and this is exactly what we need.\n */\nconst nextTick = async () => { };\nexports.nextTick = nextTick;\n/** Returns control to thread each 'tick' ms to avoid blocking. */\nasync function asyncLoop(iters, tick, cb) {\n    let ts = Date.now();\n    for (let i = 0; i < iters; i++) {\n        cb(i);\n        // Date.now() is not monotonic, so in case if clock goes backwards we return return control too\n        const diff = Date.now() - ts;\n        if (diff >= 0 && diff < tick)\n            continue;\n        await (0, exports.nextTick)();\n        ts += diff;\n    }\n}\n/**\n * Converts string to bytes using UTF8 encoding.\n * @example utf8ToBytes('abc') // Uint8Array.from([97, 98, 99])\n */\nfunction utf8ToBytes(str) {\n    if (typeof str !== 'string')\n        throw new Error('string expected');\n    return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n/**\n * Converts bytes to string using UTF8 encoding.\n * @example bytesToUtf8(Uint8Array.from([97, 98, 99])) // 'abc'\n */\nfunction bytesToUtf8(bytes) {\n    return new TextDecoder().decode(bytes);\n}\n/**\n * Normalizes (non-hex) string or Uint8Array to Uint8Array.\n * Warning: when Uint8Array is passed, it would NOT get copied.\n * Keep in mind for future mutable operations.\n */\nfunction toBytes(data) {\n    if (typeof data === 'string')\n        data = utf8ToBytes(data);\n    abytes(data);\n    return data;\n}\n/**\n * Helper for KDFs: consumes uint8array or string.\n * When string is passed, does utf8 decoding, using TextDecoder.\n */\nfunction kdfInputToBytes(data) {\n    if (typeof data === 'string')\n        data = utf8ToBytes(data);\n    abytes(data);\n    return data;\n}\n/** Copies several Uint8Arrays into one. */\nfunction concatBytes(...arrays) {\n    let sum = 0;\n    for (let i = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        abytes(a);\n        sum += a.length;\n    }\n    const res = new Uint8Array(sum);\n    for (let i = 0, pad = 0; i < arrays.length; i++) {\n        const a = arrays[i];\n        res.set(a, pad);\n        pad += a.length;\n    }\n    return res;\n}\nfunction checkOpts(defaults, opts) {\n    if (opts !== undefined && {}.toString.call(opts) !== '[object Object]')\n        throw new Error('options should be object or undefined');\n    const merged = Object.assign(defaults, opts);\n    return merged;\n}\n/** For runtime check if class implements interface */\nclass Hash {\n}\nexports.Hash = Hash;\n/** Wraps hash function, creating an interface on top of it */\nfunction createHasher(hashCons) {\n    const hashC = (msg) => hashCons().update(toBytes(msg)).digest();\n    const tmp = hashCons();\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = () => hashCons();\n    return hashC;\n}\nfunction createOptHasher(hashCons) {\n    const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n    const tmp = hashCons({});\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (opts) => hashCons(opts);\n    return hashC;\n}\nfunction createXOFer(hashCons) {\n    const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n    const tmp = hashCons({});\n    hashC.outputLen = tmp.outputLen;\n    hashC.blockLen = tmp.blockLen;\n    hashC.create = (opts) => hashCons(opts);\n    return hashC;\n}\nexports.wrapConstructor = createHasher;\nexports.wrapConstructorWithOpts = createOptHasher;\nexports.wrapXOFConstructorWithOpts = createXOFer;\n/** Cryptographically secure PRNG. Uses internal OS-level `crypto.getRandomValues`. */\nfunction randomBytes(bytesLength = 32) {\n    if (crypto_1.crypto && typeof crypto_1.crypto.getRandomValues === 'function') {\n        return crypto_1.crypto.getRandomValues(new Uint8Array(bytesLength));\n    }\n    // Legacy Node.js compatibility\n    if (crypto_1.crypto && typeof crypto_1.crypto.randomBytes === 'function') {\n        return Uint8Array.from(crypto_1.crypto.randomBytes(bytesLength));\n    }\n    throw new Error('crypto.getRandomValues must be defined');\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG5vYmxlL2hhc2hlcy91dGlscy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsa0NBQWtDLEdBQUcsK0JBQStCLEdBQUcsdUJBQXVCLEdBQUcsWUFBWSxHQUFHLGdCQUFnQixHQUFHLGtCQUFrQixHQUFHLG9CQUFvQixHQUFHLGlCQUFpQixHQUFHLFlBQVk7QUFDL00sZUFBZTtBQUNmLGVBQWU7QUFDZixjQUFjO0FBQ2QsYUFBYTtBQUNiLGVBQWU7QUFDZixlQUFlO0FBQ2YsVUFBVTtBQUNWLFdBQVc7QUFDWCxhQUFhO0FBQ2Isa0JBQWtCO0FBQ2xCLFlBQVk7QUFDWixZQUFZO0FBQ1osZ0JBQWdCO0FBQ2hCLGtCQUFrQjtBQUNsQixrQkFBa0I7QUFDbEIsa0JBQWtCO0FBQ2xCLGlCQUFpQjtBQUNqQixtQkFBbUI7QUFDbkIsbUJBQW1CO0FBQ25CLGVBQWU7QUFDZix1QkFBdUI7QUFDdkIsbUJBQW1CO0FBQ25CLGlCQUFpQjtBQUNqQixvQkFBb0I7QUFDcEIsdUJBQXVCO0FBQ3ZCLG1CQUFtQjtBQUNuQixtQkFBbUI7QUFDbkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLG1CQUFPLENBQUMsOEVBQXNCO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixtQkFBbUI7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQSxvQkFBb0I7QUFDcEI7QUFDQTtBQUNBLG9CQUFvQixnQkFBZ0I7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQ0FBMkMsYUFBYTtBQUN4RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLGtCQUFrQjtBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQSwrQkFBK0I7QUFDL0I7QUFDQSxxQ0FBcUM7QUFDckM7QUFDQSxxQ0FBcUM7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZCQUE2QixTQUFTO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQztBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEI7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLFdBQVc7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwREFBMEQ7QUFDMUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixtQkFBbUI7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZCQUE2QixtQkFBbUI7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0M7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQjtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQjtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCO0FBQ3ZCLCtCQUErQjtBQUMvQixrQ0FBa0M7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcaGFja290aG9uXFxCbG9ja2NoYWluXFxWaW50cmVrXFxub2RlX21vZHVsZXNcXEBub2JsZVxcaGFzaGVzXFx1dGlscy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8qKlxuICogVXRpbGl0aWVzIGZvciBoZXgsIGJ5dGVzLCBDU1BSTkcuXG4gKiBAbW9kdWxlXG4gKi9cbi8qISBub2JsZS1oYXNoZXMgLSBNSVQgTGljZW5zZSAoYykgMjAyMiBQYXVsIE1pbGxlciAocGF1bG1pbGxyLmNvbSkgKi9cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMud3JhcFhPRkNvbnN0cnVjdG9yV2l0aE9wdHMgPSBleHBvcnRzLndyYXBDb25zdHJ1Y3RvcldpdGhPcHRzID0gZXhwb3J0cy53cmFwQ29uc3RydWN0b3IgPSBleHBvcnRzLkhhc2ggPSBleHBvcnRzLm5leHRUaWNrID0gZXhwb3J0cy5zd2FwMzJJZkJFID0gZXhwb3J0cy5ieXRlU3dhcElmQkUgPSBleHBvcnRzLnN3YXA4SWZCRSA9IGV4cG9ydHMuaXNMRSA9IHZvaWQgMDtcbmV4cG9ydHMuaXNCeXRlcyA9IGlzQnl0ZXM7XG5leHBvcnRzLmFudW1iZXIgPSBhbnVtYmVyO1xuZXhwb3J0cy5hYnl0ZXMgPSBhYnl0ZXM7XG5leHBvcnRzLmFoYXNoID0gYWhhc2g7XG5leHBvcnRzLmFleGlzdHMgPSBhZXhpc3RzO1xuZXhwb3J0cy5hb3V0cHV0ID0gYW91dHB1dDtcbmV4cG9ydHMudTggPSB1ODtcbmV4cG9ydHMudTMyID0gdTMyO1xuZXhwb3J0cy5jbGVhbiA9IGNsZWFuO1xuZXhwb3J0cy5jcmVhdGVWaWV3ID0gY3JlYXRlVmlldztcbmV4cG9ydHMucm90ciA9IHJvdHI7XG5leHBvcnRzLnJvdGwgPSByb3RsO1xuZXhwb3J0cy5ieXRlU3dhcCA9IGJ5dGVTd2FwO1xuZXhwb3J0cy5ieXRlU3dhcDMyID0gYnl0ZVN3YXAzMjtcbmV4cG9ydHMuYnl0ZXNUb0hleCA9IGJ5dGVzVG9IZXg7XG5leHBvcnRzLmhleFRvQnl0ZXMgPSBoZXhUb0J5dGVzO1xuZXhwb3J0cy5hc3luY0xvb3AgPSBhc3luY0xvb3A7XG5leHBvcnRzLnV0ZjhUb0J5dGVzID0gdXRmOFRvQnl0ZXM7XG5leHBvcnRzLmJ5dGVzVG9VdGY4ID0gYnl0ZXNUb1V0Zjg7XG5leHBvcnRzLnRvQnl0ZXMgPSB0b0J5dGVzO1xuZXhwb3J0cy5rZGZJbnB1dFRvQnl0ZXMgPSBrZGZJbnB1dFRvQnl0ZXM7XG5leHBvcnRzLmNvbmNhdEJ5dGVzID0gY29uY2F0Qnl0ZXM7XG5leHBvcnRzLmNoZWNrT3B0cyA9IGNoZWNrT3B0cztcbmV4cG9ydHMuY3JlYXRlSGFzaGVyID0gY3JlYXRlSGFzaGVyO1xuZXhwb3J0cy5jcmVhdGVPcHRIYXNoZXIgPSBjcmVhdGVPcHRIYXNoZXI7XG5leHBvcnRzLmNyZWF0ZVhPRmVyID0gY3JlYXRlWE9GZXI7XG5leHBvcnRzLnJhbmRvbUJ5dGVzID0gcmFuZG9tQnl0ZXM7XG4vLyBXZSB1c2UgV2ViQ3J5cHRvIGFrYSBnbG9iYWxUaGlzLmNyeXB0bywgd2hpY2ggZXhpc3RzIGluIGJyb3dzZXJzIGFuZCBub2RlLmpzIDE2Ky5cbi8vIG5vZGUuanMgdmVyc2lvbnMgZWFybGllciB0aGFuIHYxOSBkb24ndCBkZWNsYXJlIGl0IGluIGdsb2JhbCBzY29wZS5cbi8vIEZvciBub2RlLmpzLCBwYWNrYWdlLmpzb24jZXhwb3J0cyBmaWVsZCBtYXBwaW5nIHJld3JpdGVzIGltcG9ydFxuLy8gZnJvbSBgY3J5cHRvYCB0byBgY3J5cHRvTm9kZWAsIHdoaWNoIGltcG9ydHMgbmF0aXZlIG1vZHVsZS5cbi8vIE1ha2VzIHRoZSB1dGlscyB1bi1pbXBvcnRhYmxlIGluIGJyb3dzZXJzIHdpdGhvdXQgYSBidW5kbGVyLlxuLy8gT25jZSBub2RlLmpzIDE4IGlzIGRlcHJlY2F0ZWQgKDIwMjUtMDQtMzApLCB3ZSBjYW4ganVzdCBkcm9wIHRoZSBpbXBvcnQuXG5jb25zdCBjcnlwdG9fMSA9IHJlcXVpcmUoXCJAbm9ibGUvaGFzaGVzL2NyeXB0b1wiKTtcbi8qKiBDaGVja3MgaWYgc29tZXRoaW5nIGlzIFVpbnQ4QXJyYXkuIEJlIGNhcmVmdWw6IG5vZGVqcyBCdWZmZXIgd2lsbCByZXR1cm4gdHJ1ZS4gKi9cbmZ1bmN0aW9uIGlzQnl0ZXMoYSkge1xuICAgIHJldHVybiBhIGluc3RhbmNlb2YgVWludDhBcnJheSB8fCAoQXJyYXlCdWZmZXIuaXNWaWV3KGEpICYmIGEuY29uc3RydWN0b3IubmFtZSA9PT0gJ1VpbnQ4QXJyYXknKTtcbn1cbi8qKiBBc3NlcnRzIHNvbWV0aGluZyBpcyBwb3NpdGl2ZSBpbnRlZ2VyLiAqL1xuZnVuY3Rpb24gYW51bWJlcihuKSB7XG4gICAgaWYgKCFOdW1iZXIuaXNTYWZlSW50ZWdlcihuKSB8fCBuIDwgMClcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdwb3NpdGl2ZSBpbnRlZ2VyIGV4cGVjdGVkLCBnb3QgJyArIG4pO1xufVxuLyoqIEFzc2VydHMgc29tZXRoaW5nIGlzIFVpbnQ4QXJyYXkuICovXG5mdW5jdGlvbiBhYnl0ZXMoYiwgLi4ubGVuZ3Rocykge1xuICAgIGlmICghaXNCeXRlcyhiKSlcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdVaW50OEFycmF5IGV4cGVjdGVkJyk7XG4gICAgaWYgKGxlbmd0aHMubGVuZ3RoID4gMCAmJiAhbGVuZ3Rocy5pbmNsdWRlcyhiLmxlbmd0aCkpXG4gICAgICAgIHRocm93IG5ldyBFcnJvcignVWludDhBcnJheSBleHBlY3RlZCBvZiBsZW5ndGggJyArIGxlbmd0aHMgKyAnLCBnb3QgbGVuZ3RoPScgKyBiLmxlbmd0aCk7XG59XG4vKiogQXNzZXJ0cyBzb21ldGhpbmcgaXMgaGFzaCAqL1xuZnVuY3Rpb24gYWhhc2goaCkge1xuICAgIGlmICh0eXBlb2YgaCAhPT0gJ2Z1bmN0aW9uJyB8fCB0eXBlb2YgaC5jcmVhdGUgIT09ICdmdW5jdGlvbicpXG4gICAgICAgIHRocm93IG5ldyBFcnJvcignSGFzaCBzaG91bGQgYmUgd3JhcHBlZCBieSB1dGlscy5jcmVhdGVIYXNoZXInKTtcbiAgICBhbnVtYmVyKGgub3V0cHV0TGVuKTtcbiAgICBhbnVtYmVyKGguYmxvY2tMZW4pO1xufVxuLyoqIEFzc2VydHMgYSBoYXNoIGluc3RhbmNlIGhhcyBub3QgYmVlbiBkZXN0cm95ZWQgLyBmaW5pc2hlZCAqL1xuZnVuY3Rpb24gYWV4aXN0cyhpbnN0YW5jZSwgY2hlY2tGaW5pc2hlZCA9IHRydWUpIHtcbiAgICBpZiAoaW5zdGFuY2UuZGVzdHJveWVkKVxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0hhc2ggaW5zdGFuY2UgaGFzIGJlZW4gZGVzdHJveWVkJyk7XG4gICAgaWYgKGNoZWNrRmluaXNoZWQgJiYgaW5zdGFuY2UuZmluaXNoZWQpXG4gICAgICAgIHRocm93IG5ldyBFcnJvcignSGFzaCNkaWdlc3QoKSBoYXMgYWxyZWFkeSBiZWVuIGNhbGxlZCcpO1xufVxuLyoqIEFzc2VydHMgb3V0cHV0IGlzIHByb3Blcmx5LXNpemVkIGJ5dGUgYXJyYXkgKi9cbmZ1bmN0aW9uIGFvdXRwdXQob3V0LCBpbnN0YW5jZSkge1xuICAgIGFieXRlcyhvdXQpO1xuICAgIGNvbnN0IG1pbiA9IGluc3RhbmNlLm91dHB1dExlbjtcbiAgICBpZiAob3V0Lmxlbmd0aCA8IG1pbikge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ2RpZ2VzdEludG8oKSBleHBlY3RzIG91dHB1dCBidWZmZXIgb2YgbGVuZ3RoIGF0IGxlYXN0ICcgKyBtaW4pO1xuICAgIH1cbn1cbi8qKiBDYXN0IHU4IC8gdTE2IC8gdTMyIHRvIHU4LiAqL1xuZnVuY3Rpb24gdTgoYXJyKSB7XG4gICAgcmV0dXJuIG5ldyBVaW50OEFycmF5KGFyci5idWZmZXIsIGFyci5ieXRlT2Zmc2V0LCBhcnIuYnl0ZUxlbmd0aCk7XG59XG4vKiogQ2FzdCB1OCAvIHUxNiAvIHUzMiB0byB1MzIuICovXG5mdW5jdGlvbiB1MzIoYXJyKSB7XG4gICAgcmV0dXJuIG5ldyBVaW50MzJBcnJheShhcnIuYnVmZmVyLCBhcnIuYnl0ZU9mZnNldCwgTWF0aC5mbG9vcihhcnIuYnl0ZUxlbmd0aCAvIDQpKTtcbn1cbi8qKiBaZXJvaXplIGEgYnl0ZSBhcnJheS4gV2FybmluZzogSlMgcHJvdmlkZXMgbm8gZ3VhcmFudGVlcy4gKi9cbmZ1bmN0aW9uIGNsZWFuKC4uLmFycmF5cykge1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgYXJyYXlzLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgIGFycmF5c1tpXS5maWxsKDApO1xuICAgIH1cbn1cbi8qKiBDcmVhdGUgRGF0YVZpZXcgb2YgYW4gYXJyYXkgZm9yIGVhc3kgYnl0ZS1sZXZlbCBtYW5pcHVsYXRpb24uICovXG5mdW5jdGlvbiBjcmVhdGVWaWV3KGFycikge1xuICAgIHJldHVybiBuZXcgRGF0YVZpZXcoYXJyLmJ1ZmZlciwgYXJyLmJ5dGVPZmZzZXQsIGFyci5ieXRlTGVuZ3RoKTtcbn1cbi8qKiBUaGUgcm90YXRlIHJpZ2h0IChjaXJjdWxhciByaWdodCBzaGlmdCkgb3BlcmF0aW9uIGZvciB1aW50MzIgKi9cbmZ1bmN0aW9uIHJvdHIod29yZCwgc2hpZnQpIHtcbiAgICByZXR1cm4gKHdvcmQgPDwgKDMyIC0gc2hpZnQpKSB8ICh3b3JkID4+PiBzaGlmdCk7XG59XG4vKiogVGhlIHJvdGF0ZSBsZWZ0IChjaXJjdWxhciBsZWZ0IHNoaWZ0KSBvcGVyYXRpb24gZm9yIHVpbnQzMiAqL1xuZnVuY3Rpb24gcm90bCh3b3JkLCBzaGlmdCkge1xuICAgIHJldHVybiAod29yZCA8PCBzaGlmdCkgfCAoKHdvcmQgPj4+ICgzMiAtIHNoaWZ0KSkgPj4+IDApO1xufVxuLyoqIElzIGN1cnJlbnQgcGxhdGZvcm0gbGl0dGxlLWVuZGlhbj8gTW9zdCBhcmUuIEJpZy1FbmRpYW4gcGxhdGZvcm06IElCTSAqL1xuZXhwb3J0cy5pc0xFID0gKCgpID0+IG5ldyBVaW50OEFycmF5KG5ldyBVaW50MzJBcnJheShbMHgxMTIyMzM0NF0pLmJ1ZmZlcilbMF0gPT09IDB4NDQpKCk7XG4vKiogVGhlIGJ5dGUgc3dhcCBvcGVyYXRpb24gZm9yIHVpbnQzMiAqL1xuZnVuY3Rpb24gYnl0ZVN3YXAod29yZCkge1xuICAgIHJldHVybiAoKCh3b3JkIDw8IDI0KSAmIDB4ZmYwMDAwMDApIHxcbiAgICAgICAgKCh3b3JkIDw8IDgpICYgMHhmZjAwMDApIHxcbiAgICAgICAgKCh3b3JkID4+PiA4KSAmIDB4ZmYwMCkgfFxuICAgICAgICAoKHdvcmQgPj4+IDI0KSAmIDB4ZmYpKTtcbn1cbi8qKiBDb25kaXRpb25hbGx5IGJ5dGUgc3dhcCBpZiBvbiBhIGJpZy1lbmRpYW4gcGxhdGZvcm0gKi9cbmV4cG9ydHMuc3dhcDhJZkJFID0gZXhwb3J0cy5pc0xFXG4gICAgPyAobikgPT4gblxuICAgIDogKG4pID0+IGJ5dGVTd2FwKG4pO1xuLyoqIEBkZXByZWNhdGVkICovXG5leHBvcnRzLmJ5dGVTd2FwSWZCRSA9IGV4cG9ydHMuc3dhcDhJZkJFO1xuLyoqIEluIHBsYWNlIGJ5dGUgc3dhcCBmb3IgVWludDMyQXJyYXkgKi9cbmZ1bmN0aW9uIGJ5dGVTd2FwMzIoYXJyKSB7XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBhcnIubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgYXJyW2ldID0gYnl0ZVN3YXAoYXJyW2ldKTtcbiAgICB9XG4gICAgcmV0dXJuIGFycjtcbn1cbmV4cG9ydHMuc3dhcDMySWZCRSA9IGV4cG9ydHMuaXNMRVxuICAgID8gKHUpID0+IHVcbiAgICA6IGJ5dGVTd2FwMzI7XG4vLyBCdWlsdC1pbiBoZXggY29udmVyc2lvbiBodHRwczovL2Nhbml1c2UuY29tL21kbi1qYXZhc2NyaXB0X2J1aWx0aW5zX3VpbnQ4YXJyYXlfZnJvbWhleFxuY29uc3QgaGFzSGV4QnVpbHRpbiA9IC8qIEBfX1BVUkVfXyAqLyAoKCkgPT4gXG4vLyBAdHMtaWdub3JlXG50eXBlb2YgVWludDhBcnJheS5mcm9tKFtdKS50b0hleCA9PT0gJ2Z1bmN0aW9uJyAmJiB0eXBlb2YgVWludDhBcnJheS5mcm9tSGV4ID09PSAnZnVuY3Rpb24nKSgpO1xuLy8gQXJyYXkgd2hlcmUgaW5kZXggMHhmMCAoMjQwKSBpcyBtYXBwZWQgdG8gc3RyaW5nICdmMCdcbmNvbnN0IGhleGVzID0gLyogQF9fUFVSRV9fICovIEFycmF5LmZyb20oeyBsZW5ndGg6IDI1NiB9LCAoXywgaSkgPT4gaS50b1N0cmluZygxNikucGFkU3RhcnQoMiwgJzAnKSk7XG4vKipcbiAqIENvbnZlcnQgYnl0ZSBhcnJheSB0byBoZXggc3RyaW5nLiBVc2VzIGJ1aWx0LWluIGZ1bmN0aW9uLCB3aGVuIGF2YWlsYWJsZS5cbiAqIEBleGFtcGxlIGJ5dGVzVG9IZXgoVWludDhBcnJheS5mcm9tKFsweGNhLCAweGZlLCAweDAxLCAweDIzXSkpIC8vICdjYWZlMDEyMydcbiAqL1xuZnVuY3Rpb24gYnl0ZXNUb0hleChieXRlcykge1xuICAgIGFieXRlcyhieXRlcyk7XG4gICAgLy8gQHRzLWlnbm9yZVxuICAgIGlmIChoYXNIZXhCdWlsdGluKVxuICAgICAgICByZXR1cm4gYnl0ZXMudG9IZXgoKTtcbiAgICAvLyBwcmUtY2FjaGluZyBpbXByb3ZlcyB0aGUgc3BlZWQgNnhcbiAgICBsZXQgaGV4ID0gJyc7XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBieXRlcy5sZW5ndGg7IGkrKykge1xuICAgICAgICBoZXggKz0gaGV4ZXNbYnl0ZXNbaV1dO1xuICAgIH1cbiAgICByZXR1cm4gaGV4O1xufVxuLy8gV2UgdXNlIG9wdGltaXplZCB0ZWNobmlxdWUgdG8gY29udmVydCBoZXggc3RyaW5nIHRvIGJ5dGUgYXJyYXlcbmNvbnN0IGFzY2lpcyA9IHsgXzA6IDQ4LCBfOTogNTcsIEE6IDY1LCBGOiA3MCwgYTogOTcsIGY6IDEwMiB9O1xuZnVuY3Rpb24gYXNjaWlUb0Jhc2UxNihjaCkge1xuICAgIGlmIChjaCA+PSBhc2NpaXMuXzAgJiYgY2ggPD0gYXNjaWlzLl85KVxuICAgICAgICByZXR1cm4gY2ggLSBhc2NpaXMuXzA7IC8vICcyJyA9PiA1MC00OFxuICAgIGlmIChjaCA+PSBhc2NpaXMuQSAmJiBjaCA8PSBhc2NpaXMuRilcbiAgICAgICAgcmV0dXJuIGNoIC0gKGFzY2lpcy5BIC0gMTApOyAvLyAnQicgPT4gNjYtKDY1LTEwKVxuICAgIGlmIChjaCA+PSBhc2NpaXMuYSAmJiBjaCA8PSBhc2NpaXMuZilcbiAgICAgICAgcmV0dXJuIGNoIC0gKGFzY2lpcy5hIC0gMTApOyAvLyAnYicgPT4gOTgtKDk3LTEwKVxuICAgIHJldHVybjtcbn1cbi8qKlxuICogQ29udmVydCBoZXggc3RyaW5nIHRvIGJ5dGUgYXJyYXkuIFVzZXMgYnVpbHQtaW4gZnVuY3Rpb24sIHdoZW4gYXZhaWxhYmxlLlxuICogQGV4YW1wbGUgaGV4VG9CeXRlcygnY2FmZTAxMjMnKSAvLyBVaW50OEFycmF5LmZyb20oWzB4Y2EsIDB4ZmUsIDB4MDEsIDB4MjNdKVxuICovXG5mdW5jdGlvbiBoZXhUb0J5dGVzKGhleCkge1xuICAgIGlmICh0eXBlb2YgaGV4ICE9PSAnc3RyaW5nJylcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdoZXggc3RyaW5nIGV4cGVjdGVkLCBnb3QgJyArIHR5cGVvZiBoZXgpO1xuICAgIC8vIEB0cy1pZ25vcmVcbiAgICBpZiAoaGFzSGV4QnVpbHRpbilcbiAgICAgICAgcmV0dXJuIFVpbnQ4QXJyYXkuZnJvbUhleChoZXgpO1xuICAgIGNvbnN0IGhsID0gaGV4Lmxlbmd0aDtcbiAgICBjb25zdCBhbCA9IGhsIC8gMjtcbiAgICBpZiAoaGwgJSAyKVxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ2hleCBzdHJpbmcgZXhwZWN0ZWQsIGdvdCB1bnBhZGRlZCBoZXggb2YgbGVuZ3RoICcgKyBobCk7XG4gICAgY29uc3QgYXJyYXkgPSBuZXcgVWludDhBcnJheShhbCk7XG4gICAgZm9yIChsZXQgYWkgPSAwLCBoaSA9IDA7IGFpIDwgYWw7IGFpKyssIGhpICs9IDIpIHtcbiAgICAgICAgY29uc3QgbjEgPSBhc2NpaVRvQmFzZTE2KGhleC5jaGFyQ29kZUF0KGhpKSk7XG4gICAgICAgIGNvbnN0IG4yID0gYXNjaWlUb0Jhc2UxNihoZXguY2hhckNvZGVBdChoaSArIDEpKTtcbiAgICAgICAgaWYgKG4xID09PSB1bmRlZmluZWQgfHwgbjIgPT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgY29uc3QgY2hhciA9IGhleFtoaV0gKyBoZXhbaGkgKyAxXTtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignaGV4IHN0cmluZyBleHBlY3RlZCwgZ290IG5vbi1oZXggY2hhcmFjdGVyIFwiJyArIGNoYXIgKyAnXCIgYXQgaW5kZXggJyArIGhpKTtcbiAgICAgICAgfVxuICAgICAgICBhcnJheVthaV0gPSBuMSAqIDE2ICsgbjI7IC8vIG11bHRpcGx5IGZpcnN0IG9jdGV0LCBlLmcuICdhMycgPT4gMTAqMTYrMyA9PiAxNjAgKyAzID0+IDE2M1xuICAgIH1cbiAgICByZXR1cm4gYXJyYXk7XG59XG4vKipcbiAqIFRoZXJlIGlzIG5vIHNldEltbWVkaWF0ZSBpbiBicm93c2VyIGFuZCBzZXRUaW1lb3V0IGlzIHNsb3cuXG4gKiBDYWxsIG9mIGFzeW5jIGZuIHdpbGwgcmV0dXJuIFByb21pc2UsIHdoaWNoIHdpbGwgYmUgZnVsbGZpbGVkIG9ubHkgb25cbiAqIG5leHQgc2NoZWR1bGVyIHF1ZXVlIHByb2Nlc3Npbmcgc3RlcCBhbmQgdGhpcyBpcyBleGFjdGx5IHdoYXQgd2UgbmVlZC5cbiAqL1xuY29uc3QgbmV4dFRpY2sgPSBhc3luYyAoKSA9PiB7IH07XG5leHBvcnRzLm5leHRUaWNrID0gbmV4dFRpY2s7XG4vKiogUmV0dXJucyBjb250cm9sIHRvIHRocmVhZCBlYWNoICd0aWNrJyBtcyB0byBhdm9pZCBibG9ja2luZy4gKi9cbmFzeW5jIGZ1bmN0aW9uIGFzeW5jTG9vcChpdGVycywgdGljaywgY2IpIHtcbiAgICBsZXQgdHMgPSBEYXRlLm5vdygpO1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgaXRlcnM7IGkrKykge1xuICAgICAgICBjYihpKTtcbiAgICAgICAgLy8gRGF0ZS5ub3coKSBpcyBub3QgbW9ub3RvbmljLCBzbyBpbiBjYXNlIGlmIGNsb2NrIGdvZXMgYmFja3dhcmRzIHdlIHJldHVybiByZXR1cm4gY29udHJvbCB0b29cbiAgICAgICAgY29uc3QgZGlmZiA9IERhdGUubm93KCkgLSB0cztcbiAgICAgICAgaWYgKGRpZmYgPj0gMCAmJiBkaWZmIDwgdGljaylcbiAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICBhd2FpdCAoMCwgZXhwb3J0cy5uZXh0VGljaykoKTtcbiAgICAgICAgdHMgKz0gZGlmZjtcbiAgICB9XG59XG4vKipcbiAqIENvbnZlcnRzIHN0cmluZyB0byBieXRlcyB1c2luZyBVVEY4IGVuY29kaW5nLlxuICogQGV4YW1wbGUgdXRmOFRvQnl0ZXMoJ2FiYycpIC8vIFVpbnQ4QXJyYXkuZnJvbShbOTcsIDk4LCA5OV0pXG4gKi9cbmZ1bmN0aW9uIHV0ZjhUb0J5dGVzKHN0cikge1xuICAgIGlmICh0eXBlb2Ygc3RyICE9PSAnc3RyaW5nJylcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdzdHJpbmcgZXhwZWN0ZWQnKTtcbiAgICByZXR1cm4gbmV3IFVpbnQ4QXJyYXkobmV3IFRleHRFbmNvZGVyKCkuZW5jb2RlKHN0cikpOyAvLyBodHRwczovL2J1Z3ppbC5sYS8xNjgxODA5XG59XG4vKipcbiAqIENvbnZlcnRzIGJ5dGVzIHRvIHN0cmluZyB1c2luZyBVVEY4IGVuY29kaW5nLlxuICogQGV4YW1wbGUgYnl0ZXNUb1V0ZjgoVWludDhBcnJheS5mcm9tKFs5NywgOTgsIDk5XSkpIC8vICdhYmMnXG4gKi9cbmZ1bmN0aW9uIGJ5dGVzVG9VdGY4KGJ5dGVzKSB7XG4gICAgcmV0dXJuIG5ldyBUZXh0RGVjb2RlcigpLmRlY29kZShieXRlcyk7XG59XG4vKipcbiAqIE5vcm1hbGl6ZXMgKG5vbi1oZXgpIHN0cmluZyBvciBVaW50OEFycmF5IHRvIFVpbnQ4QXJyYXkuXG4gKiBXYXJuaW5nOiB3aGVuIFVpbnQ4QXJyYXkgaXMgcGFzc2VkLCBpdCB3b3VsZCBOT1QgZ2V0IGNvcGllZC5cbiAqIEtlZXAgaW4gbWluZCBmb3IgZnV0dXJlIG11dGFibGUgb3BlcmF0aW9ucy5cbiAqL1xuZnVuY3Rpb24gdG9CeXRlcyhkYXRhKSB7XG4gICAgaWYgKHR5cGVvZiBkYXRhID09PSAnc3RyaW5nJylcbiAgICAgICAgZGF0YSA9IHV0ZjhUb0J5dGVzKGRhdGEpO1xuICAgIGFieXRlcyhkYXRhKTtcbiAgICByZXR1cm4gZGF0YTtcbn1cbi8qKlxuICogSGVscGVyIGZvciBLREZzOiBjb25zdW1lcyB1aW50OGFycmF5IG9yIHN0cmluZy5cbiAqIFdoZW4gc3RyaW5nIGlzIHBhc3NlZCwgZG9lcyB1dGY4IGRlY29kaW5nLCB1c2luZyBUZXh0RGVjb2Rlci5cbiAqL1xuZnVuY3Rpb24ga2RmSW5wdXRUb0J5dGVzKGRhdGEpIHtcbiAgICBpZiAodHlwZW9mIGRhdGEgPT09ICdzdHJpbmcnKVxuICAgICAgICBkYXRhID0gdXRmOFRvQnl0ZXMoZGF0YSk7XG4gICAgYWJ5dGVzKGRhdGEpO1xuICAgIHJldHVybiBkYXRhO1xufVxuLyoqIENvcGllcyBzZXZlcmFsIFVpbnQ4QXJyYXlzIGludG8gb25lLiAqL1xuZnVuY3Rpb24gY29uY2F0Qnl0ZXMoLi4uYXJyYXlzKSB7XG4gICAgbGV0IHN1bSA9IDA7XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBhcnJheXMubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgY29uc3QgYSA9IGFycmF5c1tpXTtcbiAgICAgICAgYWJ5dGVzKGEpO1xuICAgICAgICBzdW0gKz0gYS5sZW5ndGg7XG4gICAgfVxuICAgIGNvbnN0IHJlcyA9IG5ldyBVaW50OEFycmF5KHN1bSk7XG4gICAgZm9yIChsZXQgaSA9IDAsIHBhZCA9IDA7IGkgPCBhcnJheXMubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgY29uc3QgYSA9IGFycmF5c1tpXTtcbiAgICAgICAgcmVzLnNldChhLCBwYWQpO1xuICAgICAgICBwYWQgKz0gYS5sZW5ndGg7XG4gICAgfVxuICAgIHJldHVybiByZXM7XG59XG5mdW5jdGlvbiBjaGVja09wdHMoZGVmYXVsdHMsIG9wdHMpIHtcbiAgICBpZiAob3B0cyAhPT0gdW5kZWZpbmVkICYmIHt9LnRvU3RyaW5nLmNhbGwob3B0cykgIT09ICdbb2JqZWN0IE9iamVjdF0nKVxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ29wdGlvbnMgc2hvdWxkIGJlIG9iamVjdCBvciB1bmRlZmluZWQnKTtcbiAgICBjb25zdCBtZXJnZWQgPSBPYmplY3QuYXNzaWduKGRlZmF1bHRzLCBvcHRzKTtcbiAgICByZXR1cm4gbWVyZ2VkO1xufVxuLyoqIEZvciBydW50aW1lIGNoZWNrIGlmIGNsYXNzIGltcGxlbWVudHMgaW50ZXJmYWNlICovXG5jbGFzcyBIYXNoIHtcbn1cbmV4cG9ydHMuSGFzaCA9IEhhc2g7XG4vKiogV3JhcHMgaGFzaCBmdW5jdGlvbiwgY3JlYXRpbmcgYW4gaW50ZXJmYWNlIG9uIHRvcCBvZiBpdCAqL1xuZnVuY3Rpb24gY3JlYXRlSGFzaGVyKGhhc2hDb25zKSB7XG4gICAgY29uc3QgaGFzaEMgPSAobXNnKSA9PiBoYXNoQ29ucygpLnVwZGF0ZSh0b0J5dGVzKG1zZykpLmRpZ2VzdCgpO1xuICAgIGNvbnN0IHRtcCA9IGhhc2hDb25zKCk7XG4gICAgaGFzaEMub3V0cHV0TGVuID0gdG1wLm91dHB1dExlbjtcbiAgICBoYXNoQy5ibG9ja0xlbiA9IHRtcC5ibG9ja0xlbjtcbiAgICBoYXNoQy5jcmVhdGUgPSAoKSA9PiBoYXNoQ29ucygpO1xuICAgIHJldHVybiBoYXNoQztcbn1cbmZ1bmN0aW9uIGNyZWF0ZU9wdEhhc2hlcihoYXNoQ29ucykge1xuICAgIGNvbnN0IGhhc2hDID0gKG1zZywgb3B0cykgPT4gaGFzaENvbnMob3B0cykudXBkYXRlKHRvQnl0ZXMobXNnKSkuZGlnZXN0KCk7XG4gICAgY29uc3QgdG1wID0gaGFzaENvbnMoe30pO1xuICAgIGhhc2hDLm91dHB1dExlbiA9IHRtcC5vdXRwdXRMZW47XG4gICAgaGFzaEMuYmxvY2tMZW4gPSB0bXAuYmxvY2tMZW47XG4gICAgaGFzaEMuY3JlYXRlID0gKG9wdHMpID0+IGhhc2hDb25zKG9wdHMpO1xuICAgIHJldHVybiBoYXNoQztcbn1cbmZ1bmN0aW9uIGNyZWF0ZVhPRmVyKGhhc2hDb25zKSB7XG4gICAgY29uc3QgaGFzaEMgPSAobXNnLCBvcHRzKSA9PiBoYXNoQ29ucyhvcHRzKS51cGRhdGUodG9CeXRlcyhtc2cpKS5kaWdlc3QoKTtcbiAgICBjb25zdCB0bXAgPSBoYXNoQ29ucyh7fSk7XG4gICAgaGFzaEMub3V0cHV0TGVuID0gdG1wLm91dHB1dExlbjtcbiAgICBoYXNoQy5ibG9ja0xlbiA9IHRtcC5ibG9ja0xlbjtcbiAgICBoYXNoQy5jcmVhdGUgPSAob3B0cykgPT4gaGFzaENvbnMob3B0cyk7XG4gICAgcmV0dXJuIGhhc2hDO1xufVxuZXhwb3J0cy53cmFwQ29uc3RydWN0b3IgPSBjcmVhdGVIYXNoZXI7XG5leHBvcnRzLndyYXBDb25zdHJ1Y3RvcldpdGhPcHRzID0gY3JlYXRlT3B0SGFzaGVyO1xuZXhwb3J0cy53cmFwWE9GQ29uc3RydWN0b3JXaXRoT3B0cyA9IGNyZWF0ZVhPRmVyO1xuLyoqIENyeXB0b2dyYXBoaWNhbGx5IHNlY3VyZSBQUk5HLiBVc2VzIGludGVybmFsIE9TLWxldmVsIGBjcnlwdG8uZ2V0UmFuZG9tVmFsdWVzYC4gKi9cbmZ1bmN0aW9uIHJhbmRvbUJ5dGVzKGJ5dGVzTGVuZ3RoID0gMzIpIHtcbiAgICBpZiAoY3J5cHRvXzEuY3J5cHRvICYmIHR5cGVvZiBjcnlwdG9fMS5jcnlwdG8uZ2V0UmFuZG9tVmFsdWVzID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICAgIHJldHVybiBjcnlwdG9fMS5jcnlwdG8uZ2V0UmFuZG9tVmFsdWVzKG5ldyBVaW50OEFycmF5KGJ5dGVzTGVuZ3RoKSk7XG4gICAgfVxuICAgIC8vIExlZ2FjeSBOb2RlLmpzIGNvbXBhdGliaWxpdHlcbiAgICBpZiAoY3J5cHRvXzEuY3J5cHRvICYmIHR5cGVvZiBjcnlwdG9fMS5jcnlwdG8ucmFuZG9tQnl0ZXMgPT09ICdmdW5jdGlvbicpIHtcbiAgICAgICAgcmV0dXJuIFVpbnQ4QXJyYXkuZnJvbShjcnlwdG9fMS5jcnlwdG8ucmFuZG9tQnl0ZXMoYnl0ZXNMZW5ndGgpKTtcbiAgICB9XG4gICAgdGhyb3cgbmV3IEVycm9yKCdjcnlwdG8uZ2V0UmFuZG9tVmFsdWVzIG11c3QgYmUgZGVmaW5lZCcpO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dXRpbHMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@noble/hashes/utils.js\n");

/***/ })

};
;