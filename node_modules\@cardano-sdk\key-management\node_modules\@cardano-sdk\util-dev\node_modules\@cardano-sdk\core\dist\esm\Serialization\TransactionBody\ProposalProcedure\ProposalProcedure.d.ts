import { Anchor } from '../../Common/Anchor.js';
import { GovernanceActionKind } from './GovernanceActionKind.js';
import { HardForkInitiationAction } from './HardForkInitiationAction.js';
import { HexBlob } from '@cardano-sdk/util';
import { InfoAction } from './InfoAction.js';
import { NewConstitution } from './NewConstitution.js';
import { NoConfidence } from './NoConfidence.js';
import { ParameterChangeAction } from './ParameterChangeAction.js';
import { TreasuryWithdrawalsAction } from './TreasuryWithdrawalsAction.js';
import { UpdateCommittee } from './UpdateCommittee.js';
import type * as Cardano from '../../../Cardano/index.js';
export declare class ProposalProcedure {
    #private;
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): ProposalProcedure;
    toCore(): Cardano.ProposalProcedure;
    static fromCore(proposalProcedure: Cardano.ProposalProcedure): ProposalProcedure;
    static newParameterChangeAction(deposit: Cardano.Lovelace, rewardAccount: Cardano.RewardAccount, anchor: Anchor, parameterChangeAction: ParameterChangeAction): ProposalProcedure;
    static newHardForkInitiationAction(deposit: Cardano.Lovelace, rewardAccount: Cardano.RewardAccount, anchor: Anchor, hardForkInitiationAction: HardForkInitiationAction): ProposalProcedure;
    static newTreasuryWithdrawalsAction(deposit: Cardano.Lovelace, rewardAccount: Cardano.RewardAccount, anchor: Anchor, treasuryWithdrawalsAction: TreasuryWithdrawalsAction): ProposalProcedure;
    static newNoConfidence(deposit: Cardano.Lovelace, rewardAccount: Cardano.RewardAccount, anchor: Anchor, noConfidence: NoConfidence): ProposalProcedure;
    static newUpdateCommittee(deposit: Cardano.Lovelace, rewardAccount: Cardano.RewardAccount, anchor: Anchor, updateCommittee: UpdateCommittee): ProposalProcedure;
    static newNewConstitution(deposit: Cardano.Lovelace, rewardAccount: Cardano.RewardAccount, anchor: Anchor, newConstitution: NewConstitution): ProposalProcedure;
    static newInfoAction(deposit: Cardano.Lovelace, rewardAccount: Cardano.RewardAccount, anchor: Anchor, infoAction: InfoAction): ProposalProcedure;
    kind(): GovernanceActionKind;
    deposit(): Cardano.Lovelace;
    rewardAccount(): Cardano.RewardAccount;
    anchor(): Anchor;
    getParameterChangeAction(): ParameterChangeAction | undefined;
    getHardForkInitiationAction(): HardForkInitiationAction | undefined;
    getTreasuryWithdrawalsAction(): TreasuryWithdrawalsAction | undefined;
    getNoConfidence(): NoConfidence | undefined;
    getUpdateCommittee(): UpdateCommittee | undefined;
    getNewConstitution(): NewConstitution | undefined;
    getInfoAction(): InfoAction | undefined;
}
//# sourceMappingURL=ProposalProcedure.d.ts.map