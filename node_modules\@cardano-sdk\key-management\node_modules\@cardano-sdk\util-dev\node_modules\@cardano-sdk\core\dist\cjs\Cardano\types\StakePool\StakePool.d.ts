import { EpochNo } from '../Block';
import { Lovelace } from '../Value';
import { Percent } from '@cardano-sdk/util';
import { PoolIdHex } from './primitives';
import { PoolParameters } from './PoolParameters';
export interface StakePoolMetricsStake {
    live: Lovelace;
    active: Lovelace;
}
export interface StakePoolMetricsSize {
    live: Percent;
    active: Percent;
}
export interface StakePoolMetrics {
    blocksCreated: number;
    livePledge: Lovelace;
    stake: StakePoolMetricsStake;
    size: StakePoolMetricsSize;
    saturation: Percent;
    delegators: number;
    apy?: Percent;
    lastRos: Percent;
    ros: Percent;
}
export declare enum StakePoolStatus {
    Activating = "activating",
    Active = "active",
    Retired = "retired",
    Retiring = "retiring"
}
export declare class StakePoolEpochRewards {
    activeStake: Lovelace;
    epoch?: EpochNo;
    epochLength: number;
    epochNo: EpochNo;
    leaderRewards: Lovelace;
    memberActiveStake: <PERSON>lace;
    memberROI?: Percent;
    memberRewards: <PERSON>lace;
    pledge: <PERSON>lace;
    rewards: Lovelace;
}
export interface StakePool extends PoolParameters {
    hexId: PoolIdHex;
    metrics?: StakePoolMetrics;
    status: StakePoolStatus;
    rewardHistory?: StakePoolEpochRewards[];
}
//# sourceMappingURL=StakePool.d.ts.map