"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@bufbuild";
exports.ids = ["vendor-chunks/@bufbuild"];
exports.modules = {

/***/ "(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/binary-encoding.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/binary-encoding.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BinaryReader: () => (/* binding */ BinaryReader),\n/* harmony export */   BinaryWriter: () => (/* binding */ BinaryWriter),\n/* harmony export */   WireType: () => (/* binding */ WireType)\n/* harmony export */ });\n/* harmony import */ var _google_varint_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./google/varint.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/google/varint.js\");\n/* harmony import */ var _private_assert_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./private/assert.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/assert.js\");\n/* harmony import */ var _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./proto-int64.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/proto-int64.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n\n\n/* eslint-disable prefer-const,no-case-declarations,@typescript-eslint/restrict-plus-operands */\n/**\n * Protobuf binary format wire types.\n *\n * A wire type provides just enough information to find the length of the\n * following value.\n *\n * See https://developers.google.com/protocol-buffers/docs/encoding#structure\n */\nvar WireType;\n(function (WireType) {\n    /**\n     * Used for int32, int64, uint32, uint64, sint32, sint64, bool, enum\n     */\n    WireType[WireType[\"Varint\"] = 0] = \"Varint\";\n    /**\n     * Used for fixed64, sfixed64, double.\n     * Always 8 bytes with little-endian byte order.\n     */\n    WireType[WireType[\"Bit64\"] = 1] = \"Bit64\";\n    /**\n     * Used for string, bytes, embedded messages, packed repeated fields\n     *\n     * Only repeated numeric types (types which use the varint, 32-bit,\n     * or 64-bit wire types) can be packed. In proto3, such fields are\n     * packed by default.\n     */\n    WireType[WireType[\"LengthDelimited\"] = 2] = \"LengthDelimited\";\n    /**\n     * Start of a tag-delimited aggregate, such as a proto2 group, or a message\n     * in editions with message_encoding = DELIMITED.\n     */\n    WireType[WireType[\"StartGroup\"] = 3] = \"StartGroup\";\n    /**\n     * End of a tag-delimited aggregate.\n     */\n    WireType[WireType[\"EndGroup\"] = 4] = \"EndGroup\";\n    /**\n     * Used for fixed32, sfixed32, float.\n     * Always 4 bytes with little-endian byte order.\n     */\n    WireType[WireType[\"Bit32\"] = 5] = \"Bit32\";\n})(WireType || (WireType = {}));\nclass BinaryWriter {\n    constructor(textEncoder) {\n        /**\n         * Previous fork states.\n         */\n        this.stack = [];\n        this.textEncoder = textEncoder !== null && textEncoder !== void 0 ? textEncoder : new TextEncoder();\n        this.chunks = [];\n        this.buf = [];\n    }\n    /**\n     * Return all bytes written and reset this writer.\n     */\n    finish() {\n        this.chunks.push(new Uint8Array(this.buf)); // flush the buffer\n        let len = 0;\n        for (let i = 0; i < this.chunks.length; i++)\n            len += this.chunks[i].length;\n        let bytes = new Uint8Array(len);\n        let offset = 0;\n        for (let i = 0; i < this.chunks.length; i++) {\n            bytes.set(this.chunks[i], offset);\n            offset += this.chunks[i].length;\n        }\n        this.chunks = [];\n        return bytes;\n    }\n    /**\n     * Start a new fork for length-delimited data like a message\n     * or a packed repeated field.\n     *\n     * Must be joined later with `join()`.\n     */\n    fork() {\n        this.stack.push({ chunks: this.chunks, buf: this.buf });\n        this.chunks = [];\n        this.buf = [];\n        return this;\n    }\n    /**\n     * Join the last fork. Write its length and bytes, then\n     * return to the previous state.\n     */\n    join() {\n        // get chunk of fork\n        let chunk = this.finish();\n        // restore previous state\n        let prev = this.stack.pop();\n        if (!prev)\n            throw new Error(\"invalid state, fork stack empty\");\n        this.chunks = prev.chunks;\n        this.buf = prev.buf;\n        // write length of chunk as varint\n        this.uint32(chunk.byteLength);\n        return this.raw(chunk);\n    }\n    /**\n     * Writes a tag (field number and wire type).\n     *\n     * Equivalent to `uint32( (fieldNo << 3 | type) >>> 0 )`.\n     *\n     * Generated code should compute the tag ahead of time and call `uint32()`.\n     */\n    tag(fieldNo, type) {\n        return this.uint32(((fieldNo << 3) | type) >>> 0);\n    }\n    /**\n     * Write a chunk of raw bytes.\n     */\n    raw(chunk) {\n        if (this.buf.length) {\n            this.chunks.push(new Uint8Array(this.buf));\n            this.buf = [];\n        }\n        this.chunks.push(chunk);\n        return this;\n    }\n    /**\n     * Write a `uint32` value, an unsigned 32 bit varint.\n     */\n    uint32(value) {\n        (0,_private_assert_js__WEBPACK_IMPORTED_MODULE_0__.assertUInt32)(value);\n        // write value as varint 32, inlined for speed\n        while (value > 0x7f) {\n            this.buf.push((value & 0x7f) | 0x80);\n            value = value >>> 7;\n        }\n        this.buf.push(value);\n        return this;\n    }\n    /**\n     * Write a `int32` value, a signed 32 bit varint.\n     */\n    int32(value) {\n        (0,_private_assert_js__WEBPACK_IMPORTED_MODULE_0__.assertInt32)(value);\n        (0,_google_varint_js__WEBPACK_IMPORTED_MODULE_1__.varint32write)(value, this.buf);\n        return this;\n    }\n    /**\n     * Write a `bool` value, a variant.\n     */\n    bool(value) {\n        this.buf.push(value ? 1 : 0);\n        return this;\n    }\n    /**\n     * Write a `bytes` value, length-delimited arbitrary data.\n     */\n    bytes(value) {\n        this.uint32(value.byteLength); // write length of chunk as varint\n        return this.raw(value);\n    }\n    /**\n     * Write a `string` value, length-delimited data converted to UTF-8 text.\n     */\n    string(value) {\n        let chunk = this.textEncoder.encode(value);\n        this.uint32(chunk.byteLength); // write length of chunk as varint\n        return this.raw(chunk);\n    }\n    /**\n     * Write a `float` value, 32-bit floating point number.\n     */\n    float(value) {\n        (0,_private_assert_js__WEBPACK_IMPORTED_MODULE_0__.assertFloat32)(value);\n        let chunk = new Uint8Array(4);\n        new DataView(chunk.buffer).setFloat32(0, value, true);\n        return this.raw(chunk);\n    }\n    /**\n     * Write a `double` value, a 64-bit floating point number.\n     */\n    double(value) {\n        let chunk = new Uint8Array(8);\n        new DataView(chunk.buffer).setFloat64(0, value, true);\n        return this.raw(chunk);\n    }\n    /**\n     * Write a `fixed32` value, an unsigned, fixed-length 32-bit integer.\n     */\n    fixed32(value) {\n        (0,_private_assert_js__WEBPACK_IMPORTED_MODULE_0__.assertUInt32)(value);\n        let chunk = new Uint8Array(4);\n        new DataView(chunk.buffer).setUint32(0, value, true);\n        return this.raw(chunk);\n    }\n    /**\n     * Write a `sfixed32` value, a signed, fixed-length 32-bit integer.\n     */\n    sfixed32(value) {\n        (0,_private_assert_js__WEBPACK_IMPORTED_MODULE_0__.assertInt32)(value);\n        let chunk = new Uint8Array(4);\n        new DataView(chunk.buffer).setInt32(0, value, true);\n        return this.raw(chunk);\n    }\n    /**\n     * Write a `sint32` value, a signed, zigzag-encoded 32-bit varint.\n     */\n    sint32(value) {\n        (0,_private_assert_js__WEBPACK_IMPORTED_MODULE_0__.assertInt32)(value);\n        // zigzag encode\n        value = ((value << 1) ^ (value >> 31)) >>> 0;\n        (0,_google_varint_js__WEBPACK_IMPORTED_MODULE_1__.varint32write)(value, this.buf);\n        return this;\n    }\n    /**\n     * Write a `fixed64` value, a signed, fixed-length 64-bit integer.\n     */\n    sfixed64(value) {\n        let chunk = new Uint8Array(8), view = new DataView(chunk.buffer), tc = _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.enc(value);\n        view.setInt32(0, tc.lo, true);\n        view.setInt32(4, tc.hi, true);\n        return this.raw(chunk);\n    }\n    /**\n     * Write a `fixed64` value, an unsigned, fixed-length 64 bit integer.\n     */\n    fixed64(value) {\n        let chunk = new Uint8Array(8), view = new DataView(chunk.buffer), tc = _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.uEnc(value);\n        view.setInt32(0, tc.lo, true);\n        view.setInt32(4, tc.hi, true);\n        return this.raw(chunk);\n    }\n    /**\n     * Write a `int64` value, a signed 64-bit varint.\n     */\n    int64(value) {\n        let tc = _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.enc(value);\n        (0,_google_varint_js__WEBPACK_IMPORTED_MODULE_1__.varint64write)(tc.lo, tc.hi, this.buf);\n        return this;\n    }\n    /**\n     * Write a `sint64` value, a signed, zig-zag-encoded 64-bit varint.\n     */\n    sint64(value) {\n        let tc = _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.enc(value), \n        // zigzag encode\n        sign = tc.hi >> 31, lo = (tc.lo << 1) ^ sign, hi = ((tc.hi << 1) | (tc.lo >>> 31)) ^ sign;\n        (0,_google_varint_js__WEBPACK_IMPORTED_MODULE_1__.varint64write)(lo, hi, this.buf);\n        return this;\n    }\n    /**\n     * Write a `uint64` value, an unsigned 64-bit varint.\n     */\n    uint64(value) {\n        let tc = _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.uEnc(value);\n        (0,_google_varint_js__WEBPACK_IMPORTED_MODULE_1__.varint64write)(tc.lo, tc.hi, this.buf);\n        return this;\n    }\n}\nclass BinaryReader {\n    constructor(buf, textDecoder) {\n        this.varint64 = _google_varint_js__WEBPACK_IMPORTED_MODULE_1__.varint64read; // dirty cast for `this`\n        /**\n         * Read a `uint32` field, an unsigned 32 bit varint.\n         */\n        this.uint32 = _google_varint_js__WEBPACK_IMPORTED_MODULE_1__.varint32read; // dirty cast for `this` and access to protected `buf`\n        this.buf = buf;\n        this.len = buf.length;\n        this.pos = 0;\n        this.view = new DataView(buf.buffer, buf.byteOffset, buf.byteLength);\n        this.textDecoder = textDecoder !== null && textDecoder !== void 0 ? textDecoder : new TextDecoder();\n    }\n    /**\n     * Reads a tag - field number and wire type.\n     */\n    tag() {\n        let tag = this.uint32(), fieldNo = tag >>> 3, wireType = tag & 7;\n        if (fieldNo <= 0 || wireType < 0 || wireType > 5)\n            throw new Error(\"illegal tag: field no \" + fieldNo + \" wire type \" + wireType);\n        return [fieldNo, wireType];\n    }\n    /**\n     * Skip one element and return the skipped data.\n     *\n     * When skipping StartGroup, provide the tags field number to check for\n     * matching field number in the EndGroup tag.\n     */\n    skip(wireType, fieldNo) {\n        let start = this.pos;\n        switch (wireType) {\n            case WireType.Varint:\n                while (this.buf[this.pos++] & 0x80) {\n                    // ignore\n                }\n                break;\n            // eslint-disable-next-line\n            // @ts-ignore TS7029: Fallthrough case in switch\n            case WireType.Bit64:\n                this.pos += 4;\n            // eslint-disable-next-line\n            // @ts-ignore TS7029: Fallthrough case in switch\n            case WireType.Bit32:\n                this.pos += 4;\n                break;\n            case WireType.LengthDelimited:\n                let len = this.uint32();\n                this.pos += len;\n                break;\n            case WireType.StartGroup:\n                for (;;) {\n                    const [fn, wt] = this.tag();\n                    if (wt === WireType.EndGroup) {\n                        if (fieldNo !== undefined && fn !== fieldNo) {\n                            throw new Error(\"invalid end group tag\");\n                        }\n                        break;\n                    }\n                    this.skip(wt, fn);\n                }\n                break;\n            default:\n                throw new Error(\"cant skip wire type \" + wireType);\n        }\n        this.assertBounds();\n        return this.buf.subarray(start, this.pos);\n    }\n    /**\n     * Throws error if position in byte array is out of range.\n     */\n    assertBounds() {\n        if (this.pos > this.len)\n            throw new RangeError(\"premature EOF\");\n    }\n    /**\n     * Read a `int32` field, a signed 32 bit varint.\n     */\n    int32() {\n        return this.uint32() | 0;\n    }\n    /**\n     * Read a `sint32` field, a signed, zigzag-encoded 32-bit varint.\n     */\n    sint32() {\n        let zze = this.uint32();\n        // decode zigzag\n        return (zze >>> 1) ^ -(zze & 1);\n    }\n    /**\n     * Read a `int64` field, a signed 64-bit varint.\n     */\n    int64() {\n        return _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.dec(...this.varint64());\n    }\n    /**\n     * Read a `uint64` field, an unsigned 64-bit varint.\n     */\n    uint64() {\n        return _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.uDec(...this.varint64());\n    }\n    /**\n     * Read a `sint64` field, a signed, zig-zag-encoded 64-bit varint.\n     */\n    sint64() {\n        let [lo, hi] = this.varint64();\n        // decode zig zag\n        let s = -(lo & 1);\n        lo = ((lo >>> 1) | ((hi & 1) << 31)) ^ s;\n        hi = (hi >>> 1) ^ s;\n        return _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.dec(lo, hi);\n    }\n    /**\n     * Read a `bool` field, a variant.\n     */\n    bool() {\n        let [lo, hi] = this.varint64();\n        return lo !== 0 || hi !== 0;\n    }\n    /**\n     * Read a `fixed32` field, an unsigned, fixed-length 32-bit integer.\n     */\n    fixed32() {\n        return this.view.getUint32((this.pos += 4) - 4, true);\n    }\n    /**\n     * Read a `sfixed32` field, a signed, fixed-length 32-bit integer.\n     */\n    sfixed32() {\n        return this.view.getInt32((this.pos += 4) - 4, true);\n    }\n    /**\n     * Read a `fixed64` field, an unsigned, fixed-length 64 bit integer.\n     */\n    fixed64() {\n        return _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.uDec(this.sfixed32(), this.sfixed32());\n    }\n    /**\n     * Read a `fixed64` field, a signed, fixed-length 64-bit integer.\n     */\n    sfixed64() {\n        return _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.dec(this.sfixed32(), this.sfixed32());\n    }\n    /**\n     * Read a `float` field, 32-bit floating point number.\n     */\n    float() {\n        return this.view.getFloat32((this.pos += 4) - 4, true);\n    }\n    /**\n     * Read a `double` field, a 64-bit floating point number.\n     */\n    double() {\n        return this.view.getFloat64((this.pos += 8) - 8, true);\n    }\n    /**\n     * Read a `bytes` field, length-delimited arbitrary data.\n     */\n    bytes() {\n        let len = this.uint32(), start = this.pos;\n        this.pos += len;\n        this.assertBounds();\n        return this.buf.subarray(start, start + len);\n    }\n    /**\n     * Read a `string` field, length-delimited data converted to UTF-8 text.\n     */\n    string() {\n        return this.textDecoder.decode(this.bytes());\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/binary-encoding.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/extension-accessor.js":
/*!************************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/extension-accessor.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearExtension: () => (/* binding */ clearExtension),\n/* harmony export */   getExtension: () => (/* binding */ getExtension),\n/* harmony export */   hasExtension: () => (/* binding */ hasExtension),\n/* harmony export */   setExtension: () => (/* binding */ setExtension)\n/* harmony export */ });\n/* harmony import */ var _private_assert_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./private/assert.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/assert.js\");\n/* harmony import */ var _private_extensions_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./private/extensions.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/extensions.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n\n/**\n * Retrieve an extension value from a message.\n *\n * The function never returns undefined. Use hasExtension() to check whether an\n * extension is set. If the extension is not set, this function returns the\n * default value (if one was specified in the protobuf source), or the zero value\n * (for example `0` for numeric types, `[]` for repeated extension fields, and\n * an empty message instance for message fields).\n *\n * Extensions are stored as unknown fields on a message. To mutate an extension\n * value, make sure to store the new value with setExtension() after mutating.\n *\n * If the extension does not extend the given message, an error is raised.\n */\nfunction getExtension(message, extension, options) {\n    assertExtendee(extension, message);\n    const opt = extension.runtime.bin.makeReadOptions(options);\n    const ufs = (0,_private_extensions_js__WEBPACK_IMPORTED_MODULE_0__.filterUnknownFields)(message.getType().runtime.bin.listUnknownFields(message), extension.field);\n    const [container, get] = (0,_private_extensions_js__WEBPACK_IMPORTED_MODULE_0__.createExtensionContainer)(extension);\n    for (const uf of ufs) {\n        extension.runtime.bin.readField(container, opt.readerFactory(uf.data), extension.field, uf.wireType, opt);\n    }\n    return get();\n}\n/**\n * Set an extension value on a message. If the message already has a value for\n * this extension, the value is replaced.\n *\n * If the extension does not extend the given message, an error is raised.\n */\nfunction setExtension(message, extension, value, options) {\n    assertExtendee(extension, message);\n    const readOpt = extension.runtime.bin.makeReadOptions(options);\n    const writeOpt = extension.runtime.bin.makeWriteOptions(options);\n    if (hasExtension(message, extension)) {\n        const ufs = message\n            .getType()\n            .runtime.bin.listUnknownFields(message)\n            .filter((uf) => uf.no != extension.field.no);\n        message.getType().runtime.bin.discardUnknownFields(message);\n        for (const uf of ufs) {\n            message\n                .getType()\n                .runtime.bin.onUnknownField(message, uf.no, uf.wireType, uf.data);\n        }\n    }\n    const writer = writeOpt.writerFactory();\n    let f = extension.field;\n    // Implicit presence does not apply to extensions, see https://github.com/protocolbuffers/protobuf/issues/8234\n    // We patch the field info to use explicit presence:\n    if (!f.opt && !f.repeated && (f.kind == \"enum\" || f.kind == \"scalar\")) {\n        f = Object.assign(Object.assign({}, extension.field), { opt: true });\n    }\n    extension.runtime.bin.writeField(f, value, writer, writeOpt);\n    const reader = readOpt.readerFactory(writer.finish());\n    while (reader.pos < reader.len) {\n        const [no, wireType] = reader.tag();\n        const data = reader.skip(wireType, no);\n        message.getType().runtime.bin.onUnknownField(message, no, wireType, data);\n    }\n}\n/**\n * Remove an extension value from a message.\n *\n * If the extension does not extend the given message, an error is raised.\n */\nfunction clearExtension(message, extension) {\n    assertExtendee(extension, message);\n    if (hasExtension(message, extension)) {\n        const bin = message.getType().runtime.bin;\n        const ufs = bin\n            .listUnknownFields(message)\n            .filter((uf) => uf.no != extension.field.no);\n        bin.discardUnknownFields(message);\n        for (const uf of ufs) {\n            bin.onUnknownField(message, uf.no, uf.wireType, uf.data);\n        }\n    }\n}\n/**\n * Check whether an extension is set on a message.\n */\nfunction hasExtension(message, extension) {\n    const messageType = message.getType();\n    return (extension.extendee.typeName === messageType.typeName &&\n        !!messageType.runtime.bin\n            .listUnknownFields(message)\n            .find((uf) => uf.no == extension.field.no));\n}\nfunction assertExtendee(extension, message) {\n    (0,_private_assert_js__WEBPACK_IMPORTED_MODULE_1__.assert)(extension.extendee.typeName == message.getType().typeName, `extension ${extension.typeName} can only be applied to message ${extension.extendee.typeName}`);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/extension-accessor.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/google/protobuf/any_pb.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/google/protobuf/any_pb.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Any: () => (/* binding */ Any)\n/* harmony export */ });\n/* harmony import */ var _message_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../message.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/message.js\");\n/* harmony import */ var _proto3_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../proto3.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/proto3.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n\n/**\n * `Any` contains an arbitrary serialized protocol buffer message along with a\n * URL that describes the type of the serialized message.\n *\n * Protobuf library provides support to pack/unpack Any values in the form\n * of utility functions or additional generated methods of the Any type.\n *\n * Example 1: Pack and unpack a message in C++.\n *\n *     Foo foo = ...;\n *     Any any;\n *     any.PackFrom(foo);\n *     ...\n *     if (any.UnpackTo(&foo)) {\n *       ...\n *     }\n *\n * Example 2: Pack and unpack a message in Java.\n *\n *     Foo foo = ...;\n *     Any any = Any.pack(foo);\n *     ...\n *     if (any.is(Foo.class)) {\n *       foo = any.unpack(Foo.class);\n *     }\n *     // or ...\n *     if (any.isSameTypeAs(Foo.getDefaultInstance())) {\n *       foo = any.unpack(Foo.getDefaultInstance());\n *     }\n *\n *  Example 3: Pack and unpack a message in Python.\n *\n *     foo = Foo(...)\n *     any = Any()\n *     any.Pack(foo)\n *     ...\n *     if any.Is(Foo.DESCRIPTOR):\n *       any.Unpack(foo)\n *       ...\n *\n *  Example 4: Pack and unpack a message in Go\n *\n *      foo := &pb.Foo{...}\n *      any, err := anypb.New(foo)\n *      if err != nil {\n *        ...\n *      }\n *      ...\n *      foo := &pb.Foo{}\n *      if err := any.UnmarshalTo(foo); err != nil {\n *        ...\n *      }\n *\n * The pack methods provided by protobuf library will by default use\n * 'type.googleapis.com/full.type.name' as the type URL and the unpack\n * methods only use the fully qualified type name after the last '/'\n * in the type URL, for example \"foo.bar.com/x/y.z\" will yield type\n * name \"y.z\".\n *\n * JSON\n * ====\n * The JSON representation of an `Any` value uses the regular\n * representation of the deserialized, embedded message, with an\n * additional field `@type` which contains the type URL. Example:\n *\n *     package google.profile;\n *     message Person {\n *       string first_name = 1;\n *       string last_name = 2;\n *     }\n *\n *     {\n *       \"@type\": \"type.googleapis.com/google.profile.Person\",\n *       \"firstName\": <string>,\n *       \"lastName\": <string>\n *     }\n *\n * If the embedded message type is well-known and has a custom JSON\n * representation, that representation will be embedded adding a field\n * `value` which holds the custom JSON in addition to the `@type`\n * field. Example (for message [google.protobuf.Duration][]):\n *\n *     {\n *       \"@type\": \"type.googleapis.com/google.protobuf.Duration\",\n *       \"value\": \"1.212s\"\n *     }\n *\n *\n * @generated from message google.protobuf.Any\n */\nclass Any extends _message_js__WEBPACK_IMPORTED_MODULE_0__.Message {\n    constructor(data) {\n        super();\n        /**\n         * A URL/resource name that uniquely identifies the type of the serialized\n         * protocol buffer message. This string must contain at least\n         * one \"/\" character. The last segment of the URL's path must represent\n         * the fully qualified name of the type (as in\n         * `path/google.protobuf.Duration`). The name should be in a canonical form\n         * (e.g., leading \".\" is not accepted).\n         *\n         * In practice, teams usually precompile into the binary all types that they\n         * expect it to use in the context of Any. However, for URLs which use the\n         * scheme `http`, `https`, or no scheme, one can optionally set up a type\n         * server that maps type URLs to message definitions as follows:\n         *\n         * * If no scheme is provided, `https` is assumed.\n         * * An HTTP GET on the URL must yield a [google.protobuf.Type][]\n         *   value in binary format, or produce an error.\n         * * Applications are allowed to cache lookup results based on the\n         *   URL, or have them precompiled into a binary to avoid any\n         *   lookup. Therefore, binary compatibility needs to be preserved\n         *   on changes to types. (Use versioned type names to manage\n         *   breaking changes.)\n         *\n         * Note: this functionality is not currently available in the official\n         * protobuf release, and it is not used for type URLs beginning with\n         * type.googleapis.com. As of May 2023, there are no widely used type server\n         * implementations and no plans to implement one.\n         *\n         * Schemes other than `http`, `https` (or the empty scheme) might be\n         * used with implementation specific semantics.\n         *\n         *\n         * @generated from field: string type_url = 1;\n         */\n        this.typeUrl = \"\";\n        /**\n         * Must be a valid serialized protocol buffer of the above specified type.\n         *\n         * @generated from field: bytes value = 2;\n         */\n        this.value = new Uint8Array(0);\n        _proto3_js__WEBPACK_IMPORTED_MODULE_1__.proto3.util.initPartial(data, this);\n    }\n    toJson(options) {\n        var _a;\n        if (this.typeUrl === \"\") {\n            return {};\n        }\n        const typeName = this.typeUrlToName(this.typeUrl);\n        const messageType = (_a = options === null || options === void 0 ? void 0 : options.typeRegistry) === null || _a === void 0 ? void 0 : _a.findMessage(typeName);\n        if (!messageType) {\n            throw new Error(`cannot encode message google.protobuf.Any to JSON: \"${this.typeUrl}\" is not in the type registry`);\n        }\n        const message = messageType.fromBinary(this.value);\n        let json = message.toJson(options);\n        if (typeName.startsWith(\"google.protobuf.\") || (json === null || Array.isArray(json) || typeof json !== \"object\")) {\n            json = { value: json };\n        }\n        json[\"@type\"] = this.typeUrl;\n        return json;\n    }\n    fromJson(json, options) {\n        var _a;\n        if (json === null || Array.isArray(json) || typeof json != \"object\") {\n            throw new Error(`cannot decode message google.protobuf.Any from JSON: expected object but got ${json === null ? \"null\" : Array.isArray(json) ? \"array\" : typeof json}`);\n        }\n        if (Object.keys(json).length == 0) {\n            return this;\n        }\n        const typeUrl = json[\"@type\"];\n        if (typeof typeUrl != \"string\" || typeUrl == \"\") {\n            throw new Error(`cannot decode message google.protobuf.Any from JSON: \"@type\" is empty`);\n        }\n        const typeName = this.typeUrlToName(typeUrl), messageType = (_a = options === null || options === void 0 ? void 0 : options.typeRegistry) === null || _a === void 0 ? void 0 : _a.findMessage(typeName);\n        if (!messageType) {\n            throw new Error(`cannot decode message google.protobuf.Any from JSON: ${typeUrl} is not in the type registry`);\n        }\n        let message;\n        if (typeName.startsWith(\"google.protobuf.\") && Object.prototype.hasOwnProperty.call(json, \"value\")) {\n            message = messageType.fromJson(json[\"value\"], options);\n        }\n        else {\n            const copy = Object.assign({}, json);\n            delete copy[\"@type\"];\n            message = messageType.fromJson(copy, options);\n        }\n        this.packFrom(message);\n        return this;\n    }\n    packFrom(message) {\n        this.value = message.toBinary();\n        this.typeUrl = this.typeNameToUrl(message.getType().typeName);\n    }\n    unpackTo(target) {\n        if (!this.is(target.getType())) {\n            return false;\n        }\n        target.fromBinary(this.value);\n        return true;\n    }\n    unpack(registry) {\n        if (this.typeUrl === \"\") {\n            return undefined;\n        }\n        const messageType = registry.findMessage(this.typeUrlToName(this.typeUrl));\n        if (!messageType) {\n            return undefined;\n        }\n        return messageType.fromBinary(this.value);\n    }\n    is(type) {\n        if (this.typeUrl === '') {\n            return false;\n        }\n        const name = this.typeUrlToName(this.typeUrl);\n        let typeName = '';\n        if (typeof type === 'string') {\n            typeName = type;\n        }\n        else {\n            typeName = type.typeName;\n        }\n        return name === typeName;\n    }\n    typeNameToUrl(name) {\n        return `type.googleapis.com/${name}`;\n    }\n    typeUrlToName(url) {\n        if (!url.length) {\n            throw new Error(`invalid type url: ${url}`);\n        }\n        const slash = url.lastIndexOf(\"/\");\n        const name = slash >= 0 ? url.substring(slash + 1) : url;\n        if (!name.length) {\n            throw new Error(`invalid type url: ${url}`);\n        }\n        return name;\n    }\n    static pack(message) {\n        const any = new Any();\n        any.packFrom(message);\n        return any;\n    }\n    static fromBinary(bytes, options) {\n        return new Any().fromBinary(bytes, options);\n    }\n    static fromJson(jsonValue, options) {\n        return new Any().fromJson(jsonValue, options);\n    }\n    static fromJsonString(jsonString, options) {\n        return new Any().fromJsonString(jsonString, options);\n    }\n    static equals(a, b) {\n        return _proto3_js__WEBPACK_IMPORTED_MODULE_1__.proto3.util.equals(Any, a, b);\n    }\n}\nAny.runtime = _proto3_js__WEBPACK_IMPORTED_MODULE_1__.proto3;\nAny.typeName = \"google.protobuf.Any\";\nAny.fields = _proto3_js__WEBPACK_IMPORTED_MODULE_1__.proto3.util.newFieldList(() => [\n    { no: 1, name: \"type_url\", kind: \"scalar\", T: 9 /* ScalarType.STRING */ },\n    { no: 2, name: \"value\", kind: \"scalar\", T: 12 /* ScalarType.BYTES */ },\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/google/protobuf/any_pb.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/google/protobuf/field_mask_pb.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/google/protobuf/field_mask_pb.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FieldMask: () => (/* binding */ FieldMask)\n/* harmony export */ });\n/* harmony import */ var _message_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../message.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/message.js\");\n/* harmony import */ var _proto3_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../proto3.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/proto3.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n\n/**\n * `FieldMask` represents a set of symbolic field paths, for example:\n *\n *     paths: \"f.a\"\n *     paths: \"f.b.d\"\n *\n * Here `f` represents a field in some root message, `a` and `b`\n * fields in the message found in `f`, and `d` a field found in the\n * message in `f.b`.\n *\n * Field masks are used to specify a subset of fields that should be\n * returned by a get operation or modified by an update operation.\n * Field masks also have a custom JSON encoding (see below).\n *\n * # Field Masks in Projections\n *\n * When used in the context of a projection, a response message or\n * sub-message is filtered by the API to only contain those fields as\n * specified in the mask. For example, if the mask in the previous\n * example is applied to a response message as follows:\n *\n *     f {\n *       a : 22\n *       b {\n *         d : 1\n *         x : 2\n *       }\n *       y : 13\n *     }\n *     z: 8\n *\n * The result will not contain specific values for fields x,y and z\n * (their value will be set to the default, and omitted in proto text\n * output):\n *\n *\n *     f {\n *       a : 22\n *       b {\n *         d : 1\n *       }\n *     }\n *\n * A repeated field is not allowed except at the last position of a\n * paths string.\n *\n * If a FieldMask object is not present in a get operation, the\n * operation applies to all fields (as if a FieldMask of all fields\n * had been specified).\n *\n * Note that a field mask does not necessarily apply to the\n * top-level response message. In case of a REST get operation, the\n * field mask applies directly to the response, but in case of a REST\n * list operation, the mask instead applies to each individual message\n * in the returned resource list. In case of a REST custom method,\n * other definitions may be used. Where the mask applies will be\n * clearly documented together with its declaration in the API.  In\n * any case, the effect on the returned resource/resources is required\n * behavior for APIs.\n *\n * # Field Masks in Update Operations\n *\n * A field mask in update operations specifies which fields of the\n * targeted resource are going to be updated. The API is required\n * to only change the values of the fields as specified in the mask\n * and leave the others untouched. If a resource is passed in to\n * describe the updated values, the API ignores the values of all\n * fields not covered by the mask.\n *\n * If a repeated field is specified for an update operation, new values will\n * be appended to the existing repeated field in the target resource. Note that\n * a repeated field is only allowed in the last position of a `paths` string.\n *\n * If a sub-message is specified in the last position of the field mask for an\n * update operation, then new value will be merged into the existing sub-message\n * in the target resource.\n *\n * For example, given the target message:\n *\n *     f {\n *       b {\n *         d: 1\n *         x: 2\n *       }\n *       c: [1]\n *     }\n *\n * And an update message:\n *\n *     f {\n *       b {\n *         d: 10\n *       }\n *       c: [2]\n *     }\n *\n * then if the field mask is:\n *\n *  paths: [\"f.b\", \"f.c\"]\n *\n * then the result will be:\n *\n *     f {\n *       b {\n *         d: 10\n *         x: 2\n *       }\n *       c: [1, 2]\n *     }\n *\n * An implementation may provide options to override this default behavior for\n * repeated and message fields.\n *\n * In order to reset a field's value to the default, the field must\n * be in the mask and set to the default value in the provided resource.\n * Hence, in order to reset all fields of a resource, provide a default\n * instance of the resource and set all fields in the mask, or do\n * not provide a mask as described below.\n *\n * If a field mask is not present on update, the operation applies to\n * all fields (as if a field mask of all fields has been specified).\n * Note that in the presence of schema evolution, this may mean that\n * fields the client does not know and has therefore not filled into\n * the request will be reset to their default. If this is unwanted\n * behavior, a specific service may require a client to always specify\n * a field mask, producing an error if not.\n *\n * As with get operations, the location of the resource which\n * describes the updated values in the request message depends on the\n * operation kind. In any case, the effect of the field mask is\n * required to be honored by the API.\n *\n * ## Considerations for HTTP REST\n *\n * The HTTP kind of an update operation which uses a field mask must\n * be set to PATCH instead of PUT in order to satisfy HTTP semantics\n * (PUT must only be used for full updates).\n *\n * # JSON Encoding of Field Masks\n *\n * In JSON, a field mask is encoded as a single string where paths are\n * separated by a comma. Fields name in each path are converted\n * to/from lower-camel naming conventions.\n *\n * As an example, consider the following message declarations:\n *\n *     message Profile {\n *       User user = 1;\n *       Photo photo = 2;\n *     }\n *     message User {\n *       string display_name = 1;\n *       string address = 2;\n *     }\n *\n * In proto a field mask for `Profile` may look as such:\n *\n *     mask {\n *       paths: \"user.display_name\"\n *       paths: \"photo\"\n *     }\n *\n * In JSON, the same mask is represented as below:\n *\n *     {\n *       mask: \"user.displayName,photo\"\n *     }\n *\n * # Field Masks and Oneof Fields\n *\n * Field masks treat fields in oneofs just as regular fields. Consider the\n * following message:\n *\n *     message SampleMessage {\n *       oneof test_oneof {\n *         string name = 4;\n *         SubMessage sub_message = 9;\n *       }\n *     }\n *\n * The field mask can be:\n *\n *     mask {\n *       paths: \"name\"\n *     }\n *\n * Or:\n *\n *     mask {\n *       paths: \"sub_message\"\n *     }\n *\n * Note that oneof type names (\"test_oneof\" in this case) cannot be used in\n * paths.\n *\n * ## Field Mask Verification\n *\n * The implementation of any API method which has a FieldMask type field in the\n * request should verify the included field paths, and return an\n * `INVALID_ARGUMENT` error if any path is unmappable.\n *\n * @generated from message google.protobuf.FieldMask\n */\nclass FieldMask extends _message_js__WEBPACK_IMPORTED_MODULE_0__.Message {\n    constructor(data) {\n        super();\n        /**\n         * The set of field mask paths.\n         *\n         * @generated from field: repeated string paths = 1;\n         */\n        this.paths = [];\n        _proto3_js__WEBPACK_IMPORTED_MODULE_1__.proto3.util.initPartial(data, this);\n    }\n    toJson(options) {\n        // Converts snake_case to protoCamelCase according to the convention\n        // used by protoc to convert a field name to a JSON name.\n        function protoCamelCase(snakeCase) {\n            let capNext = false;\n            const b = [];\n            for (let i = 0; i < snakeCase.length; i++) {\n                let c = snakeCase.charAt(i);\n                switch (c) {\n                    case '_':\n                        capNext = true;\n                        break;\n                    case '0':\n                    case '1':\n                    case '2':\n                    case '3':\n                    case '4':\n                    case '5':\n                    case '6':\n                    case '7':\n                    case '8':\n                    case '9':\n                        b.push(c);\n                        capNext = false;\n                        break;\n                    default:\n                        if (capNext) {\n                            capNext = false;\n                            c = c.toUpperCase();\n                        }\n                        b.push(c);\n                        break;\n                }\n            }\n            return b.join('');\n        }\n        return this.paths.map(p => {\n            if (p.match(/_[0-9]?_/g) || p.match(/[A-Z]/g)) {\n                throw new Error(\"cannot encode google.protobuf.FieldMask to JSON: lowerCamelCase of path name \\\"\" + p + \"\\\" is irreversible\");\n            }\n            return protoCamelCase(p);\n        }).join(\",\");\n    }\n    fromJson(json, options) {\n        if (typeof json !== \"string\") {\n            throw new Error(\"cannot decode google.protobuf.FieldMask from JSON: \" + _proto3_js__WEBPACK_IMPORTED_MODULE_1__.proto3.json.debug(json));\n        }\n        if (json === \"\") {\n            return this;\n        }\n        function camelToSnake(str) {\n            if (str.includes(\"_\")) {\n                throw new Error(\"cannot decode google.protobuf.FieldMask from JSON: path names must be lowerCamelCase\");\n            }\n            const sc = str.replace(/[A-Z]/g, letter => \"_\" + letter.toLowerCase());\n            return (sc[0] === \"_\") ? sc.substring(1) : sc;\n        }\n        this.paths = json.split(\",\").map(camelToSnake);\n        return this;\n    }\n    static fromBinary(bytes, options) {\n        return new FieldMask().fromBinary(bytes, options);\n    }\n    static fromJson(jsonValue, options) {\n        return new FieldMask().fromJson(jsonValue, options);\n    }\n    static fromJsonString(jsonString, options) {\n        return new FieldMask().fromJsonString(jsonString, options);\n    }\n    static equals(a, b) {\n        return _proto3_js__WEBPACK_IMPORTED_MODULE_1__.proto3.util.equals(FieldMask, a, b);\n    }\n}\nFieldMask.runtime = _proto3_js__WEBPACK_IMPORTED_MODULE_1__.proto3;\nFieldMask.typeName = \"google.protobuf.FieldMask\";\nFieldMask.fields = _proto3_js__WEBPACK_IMPORTED_MODULE_1__.proto3.util.newFieldList(() => [\n    { no: 1, name: \"paths\", kind: \"scalar\", T: 9 /* ScalarType.STRING */, repeated: true },\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/google/protobuf/field_mask_pb.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/google/varint.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/google/varint.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   int64FromString: () => (/* binding */ int64FromString),\n/* harmony export */   int64ToString: () => (/* binding */ int64ToString),\n/* harmony export */   uInt64ToString: () => (/* binding */ uInt64ToString),\n/* harmony export */   varint32read: () => (/* binding */ varint32read),\n/* harmony export */   varint32write: () => (/* binding */ varint32write),\n/* harmony export */   varint64read: () => (/* binding */ varint64read),\n/* harmony export */   varint64write: () => (/* binding */ varint64write)\n/* harmony export */ });\n// Copyright 2008 Google Inc.  All rights reserved.\n//\n// Redistribution and use in source and binary forms, with or without\n// modification, are permitted provided that the following conditions are\n// met:\n//\n// * Redistributions of source code must retain the above copyright\n// notice, this list of conditions and the following disclaimer.\n// * Redistributions in binary form must reproduce the above\n// copyright notice, this list of conditions and the following disclaimer\n// in the documentation and/or other materials provided with the\n// distribution.\n// * Neither the name of Google Inc. nor the names of its\n// contributors may be used to endorse or promote products derived from\n// this software without specific prior written permission.\n//\n// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS\n// \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT\n// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR\n// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT\n// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,\n// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT\n// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\n// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n//\n// Code generated by the Protocol Buffer compiler is owned by the owner\n// of the input file used when generating it.  This code is not\n// standalone and requires a support library to be linked with it.  This\n// support library is itself covered by the above license.\n/* eslint-disable prefer-const,@typescript-eslint/restrict-plus-operands */\n/**\n * Read a 64 bit varint as two JS numbers.\n *\n * Returns tuple:\n * [0]: low bits\n * [1]: high bits\n *\n * Copyright 2008 Google Inc.  All rights reserved.\n *\n * See https://github.com/protocolbuffers/protobuf/blob/8a71927d74a4ce34efe2d8769fda198f52d20d12/js/experimental/runtime/kernel/buffer_decoder.js#L175\n */\nfunction varint64read() {\n    let lowBits = 0;\n    let highBits = 0;\n    for (let shift = 0; shift < 28; shift += 7) {\n        let b = this.buf[this.pos++];\n        lowBits |= (b & 0x7f) << shift;\n        if ((b & 0x80) == 0) {\n            this.assertBounds();\n            return [lowBits, highBits];\n        }\n    }\n    let middleByte = this.buf[this.pos++];\n    // last four bits of the first 32 bit number\n    lowBits |= (middleByte & 0x0f) << 28;\n    // 3 upper bits are part of the next 32 bit number\n    highBits = (middleByte & 0x70) >> 4;\n    if ((middleByte & 0x80) == 0) {\n        this.assertBounds();\n        return [lowBits, highBits];\n    }\n    for (let shift = 3; shift <= 31; shift += 7) {\n        let b = this.buf[this.pos++];\n        highBits |= (b & 0x7f) << shift;\n        if ((b & 0x80) == 0) {\n            this.assertBounds();\n            return [lowBits, highBits];\n        }\n    }\n    throw new Error(\"invalid varint\");\n}\n/**\n * Write a 64 bit varint, given as two JS numbers, to the given bytes array.\n *\n * Copyright 2008 Google Inc.  All rights reserved.\n *\n * See https://github.com/protocolbuffers/protobuf/blob/8a71927d74a4ce34efe2d8769fda198f52d20d12/js/experimental/runtime/kernel/writer.js#L344\n */\nfunction varint64write(lo, hi, bytes) {\n    for (let i = 0; i < 28; i = i + 7) {\n        const shift = lo >>> i;\n        const hasNext = !(shift >>> 7 == 0 && hi == 0);\n        const byte = (hasNext ? shift | 0x80 : shift) & 0xff;\n        bytes.push(byte);\n        if (!hasNext) {\n            return;\n        }\n    }\n    const splitBits = ((lo >>> 28) & 0x0f) | ((hi & 0x07) << 4);\n    const hasMoreBits = !(hi >> 3 == 0);\n    bytes.push((hasMoreBits ? splitBits | 0x80 : splitBits) & 0xff);\n    if (!hasMoreBits) {\n        return;\n    }\n    for (let i = 3; i < 31; i = i + 7) {\n        const shift = hi >>> i;\n        const hasNext = !(shift >>> 7 == 0);\n        const byte = (hasNext ? shift | 0x80 : shift) & 0xff;\n        bytes.push(byte);\n        if (!hasNext) {\n            return;\n        }\n    }\n    bytes.push((hi >>> 31) & 0x01);\n}\n// constants for binary math\nconst TWO_PWR_32_DBL = 0x100000000;\n/**\n * Parse decimal string of 64 bit integer value as two JS numbers.\n *\n * Copyright 2008 Google Inc.  All rights reserved.\n *\n * See https://github.com/protocolbuffers/protobuf-javascript/blob/a428c58273abad07c66071d9753bc4d1289de426/experimental/runtime/int64.js#L10\n */\nfunction int64FromString(dec) {\n    // Check for minus sign.\n    const minus = dec[0] === \"-\";\n    if (minus) {\n        dec = dec.slice(1);\n    }\n    // Work 6 decimal digits at a time, acting like we're converting base 1e6\n    // digits to binary. This is safe to do with floating point math because\n    // Number.isSafeInteger(ALL_32_BITS * 1e6) == true.\n    const base = 1e6;\n    let lowBits = 0;\n    let highBits = 0;\n    function add1e6digit(begin, end) {\n        // Note: Number('') is 0.\n        const digit1e6 = Number(dec.slice(begin, end));\n        highBits *= base;\n        lowBits = lowBits * base + digit1e6;\n        // Carry bits from lowBits to\n        if (lowBits >= TWO_PWR_32_DBL) {\n            highBits = highBits + ((lowBits / TWO_PWR_32_DBL) | 0);\n            lowBits = lowBits % TWO_PWR_32_DBL;\n        }\n    }\n    add1e6digit(-24, -18);\n    add1e6digit(-18, -12);\n    add1e6digit(-12, -6);\n    add1e6digit(-6);\n    return minus ? negate(lowBits, highBits) : newBits(lowBits, highBits);\n}\n/**\n * Losslessly converts a 64-bit signed integer in 32:32 split representation\n * into a decimal string.\n *\n * Copyright 2008 Google Inc.  All rights reserved.\n *\n * See https://github.com/protocolbuffers/protobuf-javascript/blob/a428c58273abad07c66071d9753bc4d1289de426/experimental/runtime/int64.js#L10\n */\nfunction int64ToString(lo, hi) {\n    let bits = newBits(lo, hi);\n    // If we're treating the input as a signed value and the high bit is set, do\n    // a manual two's complement conversion before the decimal conversion.\n    const negative = (bits.hi & 0x80000000);\n    if (negative) {\n        bits = negate(bits.lo, bits.hi);\n    }\n    const result = uInt64ToString(bits.lo, bits.hi);\n    return negative ? \"-\" + result : result;\n}\n/**\n * Losslessly converts a 64-bit unsigned integer in 32:32 split representation\n * into a decimal string.\n *\n * Copyright 2008 Google Inc.  All rights reserved.\n *\n * See https://github.com/protocolbuffers/protobuf-javascript/blob/a428c58273abad07c66071d9753bc4d1289de426/experimental/runtime/int64.js#L10\n */\nfunction uInt64ToString(lo, hi) {\n    ({ lo, hi } = toUnsigned(lo, hi));\n    // Skip the expensive conversion if the number is small enough to use the\n    // built-in conversions.\n    // Number.MAX_SAFE_INTEGER = 0x001FFFFF FFFFFFFF, thus any number with\n    // highBits <= 0x1FFFFF can be safely expressed with a double and retain\n    // integer precision.\n    // Proven by: Number.isSafeInteger(0x1FFFFF * 2**32 + 0xFFFFFFFF) == true.\n    if (hi <= 0x1FFFFF) {\n        return String(TWO_PWR_32_DBL * hi + lo);\n    }\n    // What this code is doing is essentially converting the input number from\n    // base-2 to base-1e7, which allows us to represent the 64-bit range with\n    // only 3 (very large) digits. Those digits are then trivial to convert to\n    // a base-10 string.\n    // The magic numbers used here are -\n    // 2^24 = 16777216 = (1,6777216) in base-1e7.\n    // 2^48 = 281474976710656 = (2,8147497,6710656) in base-1e7.\n    // Split 32:32 representation into 16:24:24 representation so our\n    // intermediate digits don't overflow.\n    const low = lo & 0xFFFFFF;\n    const mid = ((lo >>> 24) | (hi << 8)) & 0xFFFFFF;\n    const high = (hi >> 16) & 0xFFFF;\n    // Assemble our three base-1e7 digits, ignoring carries. The maximum\n    // value in a digit at this step is representable as a 48-bit integer, which\n    // can be stored in a 64-bit floating point number.\n    let digitA = low + (mid * 6777216) + (high * 6710656);\n    let digitB = mid + (high * 8147497);\n    let digitC = (high * 2);\n    // Apply carries from A to B and from B to C.\n    const base = 10000000;\n    if (digitA >= base) {\n        digitB += Math.floor(digitA / base);\n        digitA %= base;\n    }\n    if (digitB >= base) {\n        digitC += Math.floor(digitB / base);\n        digitB %= base;\n    }\n    // If digitC is 0, then we should have returned in the trivial code path\n    // at the top for non-safe integers. Given this, we can assume both digitB\n    // and digitA need leading zeros.\n    return digitC.toString() + decimalFrom1e7WithLeadingZeros(digitB) +\n        decimalFrom1e7WithLeadingZeros(digitA);\n}\nfunction toUnsigned(lo, hi) {\n    return { lo: lo >>> 0, hi: hi >>> 0 };\n}\nfunction newBits(lo, hi) {\n    return { lo: lo | 0, hi: hi | 0 };\n}\n/**\n * Returns two's compliment negation of input.\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Operators/Bitwise_Operators#Signed_32-bit_integers\n */\nfunction negate(lowBits, highBits) {\n    highBits = ~highBits;\n    if (lowBits) {\n        lowBits = ~lowBits + 1;\n    }\n    else {\n        // If lowBits is 0, then bitwise-not is 0xFFFFFFFF,\n        // adding 1 to that, results in 0x100000000, which leaves\n        // the low bits 0x0 and simply adds one to the high bits.\n        highBits += 1;\n    }\n    return newBits(lowBits, highBits);\n}\n/**\n * Returns decimal representation of digit1e7 with leading zeros.\n */\nconst decimalFrom1e7WithLeadingZeros = (digit1e7) => {\n    const partial = String(digit1e7);\n    return \"0000000\".slice(partial.length) + partial;\n};\n/**\n * Write a 32 bit varint, signed or unsigned. Same as `varint64write(0, value, bytes)`\n *\n * Copyright 2008 Google Inc.  All rights reserved.\n *\n * See https://github.com/protocolbuffers/protobuf/blob/1b18833f4f2a2f681f4e4a25cdf3b0a43115ec26/js/binary/encoder.js#L144\n */\nfunction varint32write(value, bytes) {\n    if (value >= 0) {\n        // write value as varint 32\n        while (value > 0x7f) {\n            bytes.push((value & 0x7f) | 0x80);\n            value = value >>> 7;\n        }\n        bytes.push(value);\n    }\n    else {\n        for (let i = 0; i < 9; i++) {\n            bytes.push((value & 127) | 128);\n            value = value >> 7;\n        }\n        bytes.push(1);\n    }\n}\n/**\n * Read an unsigned 32 bit varint.\n *\n * See https://github.com/protocolbuffers/protobuf/blob/8a71927d74a4ce34efe2d8769fda198f52d20d12/js/experimental/runtime/kernel/buffer_decoder.js#L220\n */\nfunction varint32read() {\n    let b = this.buf[this.pos++];\n    let result = b & 0x7f;\n    if ((b & 0x80) == 0) {\n        this.assertBounds();\n        return result;\n    }\n    b = this.buf[this.pos++];\n    result |= (b & 0x7f) << 7;\n    if ((b & 0x80) == 0) {\n        this.assertBounds();\n        return result;\n    }\n    b = this.buf[this.pos++];\n    result |= (b & 0x7f) << 14;\n    if ((b & 0x80) == 0) {\n        this.assertBounds();\n        return result;\n    }\n    b = this.buf[this.pos++];\n    result |= (b & 0x7f) << 21;\n    if ((b & 0x80) == 0) {\n        this.assertBounds();\n        return result;\n    }\n    // Extract only last 4 bits\n    b = this.buf[this.pos++];\n    result |= (b & 0x0f) << 28;\n    for (let readBytes = 5; (b & 0x80) !== 0 && readBytes < 10; readBytes++)\n        b = this.buf[this.pos++];\n    if ((b & 0x80) != 0)\n        throw new Error(\"invalid varint\");\n    this.assertBounds();\n    // Result can have 32 bits, convert it to unsigned\n    return result >>> 0;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/google/varint.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/is-message.js":
/*!****************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/is-message.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isMessage: () => (/* binding */ isMessage)\n/* harmony export */ });\n/* harmony import */ var _message_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./message.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/message.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n/**\n * Check whether the given object is any subtype of Message or is a specific\n * Message by passing the type.\n *\n * Just like `instanceof`, `isMessage` narrows the type. The advantage of\n * `isMessage` is that it compares identity by the message type name, not by\n * class identity. This makes it robust against the dual package hazard and\n * similar situations, where the same message is duplicated.\n *\n * This function is _mostly_ equivalent to the `instanceof` operator. For\n * example, `isMessage(foo, MyMessage)` is the same as `foo instanceof MyMessage`,\n * and `isMessage(foo)` is the same as `foo instanceof Message`. In most cases,\n * `isMessage` should be preferred over `instanceof`.\n *\n * However, due to the fact that `isMessage` does not use class identity, there\n * are subtle differences between this function and `instanceof`. Notably,\n * calling `isMessage` on an explicit type of Message will return false.\n */\nfunction isMessage(arg, type) {\n    if (arg === null || typeof arg != \"object\") {\n        return false;\n    }\n    if (!Object.getOwnPropertyNames(_message_js__WEBPACK_IMPORTED_MODULE_0__.Message.prototype).every((m) => m in arg && typeof arg[m] == \"function\")) {\n        return false;\n    }\n    const actualType = arg.getType();\n    if (actualType === null ||\n        typeof actualType != \"function\" ||\n        !(\"typeName\" in actualType) ||\n        typeof actualType.typeName != \"string\") {\n        return false;\n    }\n    return type === undefined ? true : actualType.typeName == type.typeName;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/is-message.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/message.js":
/*!*************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/message.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Message: () => (/* binding */ Message)\n/* harmony export */ });\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n/**\n * Message is the base class of every message, generated, or created at\n * runtime.\n *\n * It is _not_ safe to extend this class. If you want to create a message at\n * run time, use proto3.makeMessageType().\n */\nclass Message {\n    /**\n     * Compare with a message of the same type.\n     * Note that this function disregards extensions and unknown fields.\n     */\n    equals(other) {\n        return this.getType().runtime.util.equals(this.getType(), this, other);\n    }\n    /**\n     * Create a deep copy.\n     */\n    clone() {\n        return this.getType().runtime.util.clone(this);\n    }\n    /**\n     * Parse from binary data, merging fields.\n     *\n     * Repeated fields are appended. Map entries are added, overwriting\n     * existing keys.\n     *\n     * If a message field is already present, it will be merged with the\n     * new data.\n     */\n    fromBinary(bytes, options) {\n        const type = this.getType(), format = type.runtime.bin, opt = format.makeReadOptions(options);\n        format.readMessage(this, opt.readerFactory(bytes), bytes.byteLength, opt);\n        return this;\n    }\n    /**\n     * Parse a message from a JSON value.\n     */\n    fromJson(jsonValue, options) {\n        const type = this.getType(), format = type.runtime.json, opt = format.makeReadOptions(options);\n        format.readMessage(type, jsonValue, opt, this);\n        return this;\n    }\n    /**\n     * Parse a message from a JSON string.\n     */\n    fromJsonString(jsonString, options) {\n        let json;\n        try {\n            json = JSON.parse(jsonString);\n        }\n        catch (e) {\n            throw new Error(`cannot decode ${this.getType().typeName} from JSON: ${e instanceof Error ? e.message : String(e)}`);\n        }\n        return this.fromJson(json, options);\n    }\n    /**\n     * Serialize the message to binary data.\n     */\n    toBinary(options) {\n        const type = this.getType(), bin = type.runtime.bin, opt = bin.makeWriteOptions(options), writer = opt.writerFactory();\n        bin.writeMessage(this, writer, opt);\n        return writer.finish();\n    }\n    /**\n     * Serialize the message to a JSON value, a JavaScript value that can be\n     * passed to JSON.stringify().\n     */\n    toJson(options) {\n        const type = this.getType(), json = type.runtime.json, opt = json.makeWriteOptions(options);\n        return json.writeMessage(this, opt);\n    }\n    /**\n     * Serialize the message to a JSON string.\n     */\n    toJsonString(options) {\n        var _a;\n        const value = this.toJson(options);\n        return JSON.stringify(value, null, (_a = options === null || options === void 0 ? void 0 : options.prettySpaces) !== null && _a !== void 0 ? _a : 0);\n    }\n    /**\n     * Override for serialization behavior. This will be invoked when calling\n     * JSON.stringify on this message (i.e. JSON.stringify(msg)).\n     *\n     * Note that this will not serialize google.protobuf.Any with a packed\n     * message because the protobuf JSON format specifies that it needs to be\n     * unpacked, and this is only possible with a type registry to look up the\n     * message type.  As a result, attempting to serialize a message with this\n     * type will throw an Error.\n     *\n     * This method is protected because you should not need to invoke it\n     * directly -- instead use JSON.stringify or toJsonString for\n     * stringified JSON.  Alternatively, if actual JSON is desired, you should\n     * use toJson.\n     */\n    toJSON() {\n        return this.toJson({\n            emitDefaultValues: true,\n        });\n    }\n    /**\n     * Retrieve the MessageType of this message - a singleton that represents\n     * the protobuf message declaration and provides metadata for reflection-\n     * based operations.\n     */\n    getType() {\n        // Any class that extends Message _must_ provide a complete static\n        // implementation of MessageType.\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access,@typescript-eslint/no-unsafe-return\n        return Object.getPrototypeOf(this).constructor;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGJ1ZmJ1aWxkL3Byb3RvYnVmL2Rpc3QvZXNtL21lc3NhZ2UuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2Q0FBNkMseUJBQXlCLGFBQWEsMkNBQTJDO0FBQzlIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXGhhY2tvdGhvblxcQmxvY2tjaGFpblxcVmludHJla1xcbm9kZV9tb2R1bGVzXFxAYnVmYnVpbGRcXHByb3RvYnVmXFxkaXN0XFxlc21cXG1lc3NhZ2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29weXJpZ2h0IDIwMjEtMjAyNCBCdWYgVGVjaG5vbG9naWVzLCBJbmMuXG4vL1xuLy8gTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbi8vIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbi8vIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuLy9cbi8vICAgICAgaHR0cDovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4vL1xuLy8gVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuLy8gZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuLy8gV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4vLyBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4vLyBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbi8qKlxuICogTWVzc2FnZSBpcyB0aGUgYmFzZSBjbGFzcyBvZiBldmVyeSBtZXNzYWdlLCBnZW5lcmF0ZWQsIG9yIGNyZWF0ZWQgYXRcbiAqIHJ1bnRpbWUuXG4gKlxuICogSXQgaXMgX25vdF8gc2FmZSB0byBleHRlbmQgdGhpcyBjbGFzcy4gSWYgeW91IHdhbnQgdG8gY3JlYXRlIGEgbWVzc2FnZSBhdFxuICogcnVuIHRpbWUsIHVzZSBwcm90bzMubWFrZU1lc3NhZ2VUeXBlKCkuXG4gKi9cbmV4cG9ydCBjbGFzcyBNZXNzYWdlIHtcbiAgICAvKipcbiAgICAgKiBDb21wYXJlIHdpdGggYSBtZXNzYWdlIG9mIHRoZSBzYW1lIHR5cGUuXG4gICAgICogTm90ZSB0aGF0IHRoaXMgZnVuY3Rpb24gZGlzcmVnYXJkcyBleHRlbnNpb25zIGFuZCB1bmtub3duIGZpZWxkcy5cbiAgICAgKi9cbiAgICBlcXVhbHMob3RoZXIpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuZ2V0VHlwZSgpLnJ1bnRpbWUudXRpbC5lcXVhbHModGhpcy5nZXRUeXBlKCksIHRoaXMsIG90aGVyKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogQ3JlYXRlIGEgZGVlcCBjb3B5LlxuICAgICAqL1xuICAgIGNsb25lKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5nZXRUeXBlKCkucnVudGltZS51dGlsLmNsb25lKHRoaXMpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBQYXJzZSBmcm9tIGJpbmFyeSBkYXRhLCBtZXJnaW5nIGZpZWxkcy5cbiAgICAgKlxuICAgICAqIFJlcGVhdGVkIGZpZWxkcyBhcmUgYXBwZW5kZWQuIE1hcCBlbnRyaWVzIGFyZSBhZGRlZCwgb3ZlcndyaXRpbmdcbiAgICAgKiBleGlzdGluZyBrZXlzLlxuICAgICAqXG4gICAgICogSWYgYSBtZXNzYWdlIGZpZWxkIGlzIGFscmVhZHkgcHJlc2VudCwgaXQgd2lsbCBiZSBtZXJnZWQgd2l0aCB0aGVcbiAgICAgKiBuZXcgZGF0YS5cbiAgICAgKi9cbiAgICBmcm9tQmluYXJ5KGJ5dGVzLCBvcHRpb25zKSB7XG4gICAgICAgIGNvbnN0IHR5cGUgPSB0aGlzLmdldFR5cGUoKSwgZm9ybWF0ID0gdHlwZS5ydW50aW1lLmJpbiwgb3B0ID0gZm9ybWF0Lm1ha2VSZWFkT3B0aW9ucyhvcHRpb25zKTtcbiAgICAgICAgZm9ybWF0LnJlYWRNZXNzYWdlKHRoaXMsIG9wdC5yZWFkZXJGYWN0b3J5KGJ5dGVzKSwgYnl0ZXMuYnl0ZUxlbmd0aCwgb3B0KTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFBhcnNlIGEgbWVzc2FnZSBmcm9tIGEgSlNPTiB2YWx1ZS5cbiAgICAgKi9cbiAgICBmcm9tSnNvbihqc29uVmFsdWUsIG9wdGlvbnMpIHtcbiAgICAgICAgY29uc3QgdHlwZSA9IHRoaXMuZ2V0VHlwZSgpLCBmb3JtYXQgPSB0eXBlLnJ1bnRpbWUuanNvbiwgb3B0ID0gZm9ybWF0Lm1ha2VSZWFkT3B0aW9ucyhvcHRpb25zKTtcbiAgICAgICAgZm9ybWF0LnJlYWRNZXNzYWdlKHR5cGUsIGpzb25WYWx1ZSwgb3B0LCB0aGlzKTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFBhcnNlIGEgbWVzc2FnZSBmcm9tIGEgSlNPTiBzdHJpbmcuXG4gICAgICovXG4gICAgZnJvbUpzb25TdHJpbmcoanNvblN0cmluZywgb3B0aW9ucykge1xuICAgICAgICBsZXQganNvbjtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGpzb24gPSBKU09OLnBhcnNlKGpzb25TdHJpbmcpO1xuICAgICAgICB9XG4gICAgICAgIGNhdGNoIChlKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYGNhbm5vdCBkZWNvZGUgJHt0aGlzLmdldFR5cGUoKS50eXBlTmFtZX0gZnJvbSBKU09OOiAke2UgaW5zdGFuY2VvZiBFcnJvciA/IGUubWVzc2FnZSA6IFN0cmluZyhlKX1gKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdGhpcy5mcm9tSnNvbihqc29uLCBvcHRpb25zKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogU2VyaWFsaXplIHRoZSBtZXNzYWdlIHRvIGJpbmFyeSBkYXRhLlxuICAgICAqL1xuICAgIHRvQmluYXJ5KG9wdGlvbnMpIHtcbiAgICAgICAgY29uc3QgdHlwZSA9IHRoaXMuZ2V0VHlwZSgpLCBiaW4gPSB0eXBlLnJ1bnRpbWUuYmluLCBvcHQgPSBiaW4ubWFrZVdyaXRlT3B0aW9ucyhvcHRpb25zKSwgd3JpdGVyID0gb3B0LndyaXRlckZhY3RvcnkoKTtcbiAgICAgICAgYmluLndyaXRlTWVzc2FnZSh0aGlzLCB3cml0ZXIsIG9wdCk7XG4gICAgICAgIHJldHVybiB3cml0ZXIuZmluaXNoKCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFNlcmlhbGl6ZSB0aGUgbWVzc2FnZSB0byBhIEpTT04gdmFsdWUsIGEgSmF2YVNjcmlwdCB2YWx1ZSB0aGF0IGNhbiBiZVxuICAgICAqIHBhc3NlZCB0byBKU09OLnN0cmluZ2lmeSgpLlxuICAgICAqL1xuICAgIHRvSnNvbihvcHRpb25zKSB7XG4gICAgICAgIGNvbnN0IHR5cGUgPSB0aGlzLmdldFR5cGUoKSwganNvbiA9IHR5cGUucnVudGltZS5qc29uLCBvcHQgPSBqc29uLm1ha2VXcml0ZU9wdGlvbnMob3B0aW9ucyk7XG4gICAgICAgIHJldHVybiBqc29uLndyaXRlTWVzc2FnZSh0aGlzLCBvcHQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBTZXJpYWxpemUgdGhlIG1lc3NhZ2UgdG8gYSBKU09OIHN0cmluZy5cbiAgICAgKi9cbiAgICB0b0pzb25TdHJpbmcob3B0aW9ucykge1xuICAgICAgICB2YXIgX2E7XG4gICAgICAgIGNvbnN0IHZhbHVlID0gdGhpcy50b0pzb24ob3B0aW9ucyk7XG4gICAgICAgIHJldHVybiBKU09OLnN0cmluZ2lmeSh2YWx1ZSwgbnVsbCwgKF9hID0gb3B0aW9ucyA9PT0gbnVsbCB8fCBvcHRpb25zID09PSB2b2lkIDAgPyB2b2lkIDAgOiBvcHRpb25zLnByZXR0eVNwYWNlcykgIT09IG51bGwgJiYgX2EgIT09IHZvaWQgMCA/IF9hIDogMCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIE92ZXJyaWRlIGZvciBzZXJpYWxpemF0aW9uIGJlaGF2aW9yLiBUaGlzIHdpbGwgYmUgaW52b2tlZCB3aGVuIGNhbGxpbmdcbiAgICAgKiBKU09OLnN0cmluZ2lmeSBvbiB0aGlzIG1lc3NhZ2UgKGkuZS4gSlNPTi5zdHJpbmdpZnkobXNnKSkuXG4gICAgICpcbiAgICAgKiBOb3RlIHRoYXQgdGhpcyB3aWxsIG5vdCBzZXJpYWxpemUgZ29vZ2xlLnByb3RvYnVmLkFueSB3aXRoIGEgcGFja2VkXG4gICAgICogbWVzc2FnZSBiZWNhdXNlIHRoZSBwcm90b2J1ZiBKU09OIGZvcm1hdCBzcGVjaWZpZXMgdGhhdCBpdCBuZWVkcyB0byBiZVxuICAgICAqIHVucGFja2VkLCBhbmQgdGhpcyBpcyBvbmx5IHBvc3NpYmxlIHdpdGggYSB0eXBlIHJlZ2lzdHJ5IHRvIGxvb2sgdXAgdGhlXG4gICAgICogbWVzc2FnZSB0eXBlLiAgQXMgYSByZXN1bHQsIGF0dGVtcHRpbmcgdG8gc2VyaWFsaXplIGEgbWVzc2FnZSB3aXRoIHRoaXNcbiAgICAgKiB0eXBlIHdpbGwgdGhyb3cgYW4gRXJyb3IuXG4gICAgICpcbiAgICAgKiBUaGlzIG1ldGhvZCBpcyBwcm90ZWN0ZWQgYmVjYXVzZSB5b3Ugc2hvdWxkIG5vdCBuZWVkIHRvIGludm9rZSBpdFxuICAgICAqIGRpcmVjdGx5IC0tIGluc3RlYWQgdXNlIEpTT04uc3RyaW5naWZ5IG9yIHRvSnNvblN0cmluZyBmb3JcbiAgICAgKiBzdHJpbmdpZmllZCBKU09OLiAgQWx0ZXJuYXRpdmVseSwgaWYgYWN0dWFsIEpTT04gaXMgZGVzaXJlZCwgeW91IHNob3VsZFxuICAgICAqIHVzZSB0b0pzb24uXG4gICAgICovXG4gICAgdG9KU09OKCkge1xuICAgICAgICByZXR1cm4gdGhpcy50b0pzb24oe1xuICAgICAgICAgICAgZW1pdERlZmF1bHRWYWx1ZXM6IHRydWUsXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBSZXRyaWV2ZSB0aGUgTWVzc2FnZVR5cGUgb2YgdGhpcyBtZXNzYWdlIC0gYSBzaW5nbGV0b24gdGhhdCByZXByZXNlbnRzXG4gICAgICogdGhlIHByb3RvYnVmIG1lc3NhZ2UgZGVjbGFyYXRpb24gYW5kIHByb3ZpZGVzIG1ldGFkYXRhIGZvciByZWZsZWN0aW9uLVxuICAgICAqIGJhc2VkIG9wZXJhdGlvbnMuXG4gICAgICovXG4gICAgZ2V0VHlwZSgpIHtcbiAgICAgICAgLy8gQW55IGNsYXNzIHRoYXQgZXh0ZW5kcyBNZXNzYWdlIF9tdXN0XyBwcm92aWRlIGEgY29tcGxldGUgc3RhdGljXG4gICAgICAgIC8vIGltcGxlbWVudGF0aW9uIG9mIE1lc3NhZ2VUeXBlLlxuICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgQHR5cGVzY3JpcHQtZXNsaW50L25vLXVuc2FmZS1tZW1iZXItYWNjZXNzLEB0eXBlc2NyaXB0LWVzbGludC9uby11bnNhZmUtcmV0dXJuXG4gICAgICAgIHJldHVybiBPYmplY3QuZ2V0UHJvdG90eXBlT2YodGhpcykuY29uc3RydWN0b3I7XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/message.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/assert.js":
/*!********************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/private/assert.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assert: () => (/* binding */ assert),\n/* harmony export */   assertFloat32: () => (/* binding */ assertFloat32),\n/* harmony export */   assertInt32: () => (/* binding */ assertInt32),\n/* harmony export */   assertUInt32: () => (/* binding */ assertUInt32)\n/* harmony export */ });\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n/**\n * Assert that condition is truthy or throw error (with message)\n */\nfunction assert(condition, msg) {\n    // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions -- we want the implicit conversion to boolean\n    if (!condition) {\n        throw new Error(msg);\n    }\n}\nconst FLOAT32_MAX = 3.4028234663852886e38, FLOAT32_MIN = -3.4028234663852886e38, UINT32_MAX = 0xffffffff, INT32_MAX = 0x7fffffff, INT32_MIN = -0x80000000;\n/**\n * Assert a valid signed protobuf 32-bit integer.\n */\nfunction assertInt32(arg) {\n    if (typeof arg !== \"number\")\n        throw new Error(\"invalid int 32: \" + typeof arg);\n    if (!Number.isInteger(arg) || arg > INT32_MAX || arg < INT32_MIN)\n        throw new Error(\"invalid int 32: \" + arg); // eslint-disable-line @typescript-eslint/restrict-plus-operands -- we want the implicit conversion to string\n}\n/**\n * Assert a valid unsigned protobuf 32-bit integer.\n */\nfunction assertUInt32(arg) {\n    if (typeof arg !== \"number\")\n        throw new Error(\"invalid uint 32: \" + typeof arg);\n    if (!Number.isInteger(arg) || arg > UINT32_MAX || arg < 0)\n        throw new Error(\"invalid uint 32: \" + arg); // eslint-disable-line @typescript-eslint/restrict-plus-operands -- we want the implicit conversion to string\n}\n/**\n * Assert a valid protobuf float value.\n */\nfunction assertFloat32(arg) {\n    if (typeof arg !== \"number\")\n        throw new Error(\"invalid float 32: \" + typeof arg);\n    if (!Number.isFinite(arg))\n        return;\n    if (arg > FLOAT32_MAX || arg < FLOAT32_MIN)\n        throw new Error(\"invalid float 32: \" + arg); // eslint-disable-line @typescript-eslint/restrict-plus-operands -- we want the implicit conversion to string\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/assert.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/binary-format.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/private/binary-format.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   makeBinaryFormat: () => (/* binding */ makeBinaryFormat),\n/* harmony export */   writeMapEntry: () => (/* binding */ writeMapEntry)\n/* harmony export */ });\n/* harmony import */ var _binary_encoding_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../binary-encoding.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/binary-encoding.js\");\n/* harmony import */ var _field_wrapper_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./field-wrapper.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/field-wrapper.js\");\n/* harmony import */ var _scalars_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./scalars.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/scalars.js\");\n/* harmony import */ var _assert_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./assert.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/assert.js\");\n/* harmony import */ var _reflect_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./reflect.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/reflect.js\");\n/* harmony import */ var _scalar_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../scalar.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/scalar.js\");\n/* harmony import */ var _is_message_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../is-message.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/is-message.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n\n\n\n\n\n\n\n/* eslint-disable prefer-const,no-case-declarations,@typescript-eslint/no-explicit-any,@typescript-eslint/no-unsafe-argument,@typescript-eslint/no-unsafe-assignment,@typescript-eslint/no-unsafe-member-access,@typescript-eslint/no-unsafe-call,@typescript-eslint/no-unsafe-return */\nconst unknownFieldsSymbol = Symbol(\"@bufbuild/protobuf/unknown-fields\");\n// Default options for parsing binary data.\nconst readDefaults = {\n    readUnknownFields: true,\n    readerFactory: (bytes) => new _binary_encoding_js__WEBPACK_IMPORTED_MODULE_0__.BinaryReader(bytes),\n};\n// Default options for serializing binary data.\nconst writeDefaults = {\n    writeUnknownFields: true,\n    writerFactory: () => new _binary_encoding_js__WEBPACK_IMPORTED_MODULE_0__.BinaryWriter(),\n};\nfunction makeReadOptions(options) {\n    return options ? Object.assign(Object.assign({}, readDefaults), options) : readDefaults;\n}\nfunction makeWriteOptions(options) {\n    return options ? Object.assign(Object.assign({}, writeDefaults), options) : writeDefaults;\n}\nfunction makeBinaryFormat() {\n    return {\n        makeReadOptions,\n        makeWriteOptions,\n        listUnknownFields(message) {\n            var _a;\n            return (_a = message[unknownFieldsSymbol]) !== null && _a !== void 0 ? _a : [];\n        },\n        discardUnknownFields(message) {\n            delete message[unknownFieldsSymbol];\n        },\n        writeUnknownFields(message, writer) {\n            const m = message;\n            const c = m[unknownFieldsSymbol];\n            if (c) {\n                for (const f of c) {\n                    writer.tag(f.no, f.wireType).raw(f.data);\n                }\n            }\n        },\n        onUnknownField(message, no, wireType, data) {\n            const m = message;\n            if (!Array.isArray(m[unknownFieldsSymbol])) {\n                m[unknownFieldsSymbol] = [];\n            }\n            m[unknownFieldsSymbol].push({ no, wireType, data });\n        },\n        readMessage(message, reader, lengthOrEndTagFieldNo, options, delimitedMessageEncoding) {\n            const type = message.getType();\n            // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions\n            const end = delimitedMessageEncoding\n                ? reader.len\n                : reader.pos + lengthOrEndTagFieldNo;\n            let fieldNo, wireType;\n            while (reader.pos < end) {\n                [fieldNo, wireType] = reader.tag();\n                if (delimitedMessageEncoding === true &&\n                    wireType == _binary_encoding_js__WEBPACK_IMPORTED_MODULE_0__.WireType.EndGroup) {\n                    break;\n                }\n                const field = type.fields.find(fieldNo);\n                if (!field) {\n                    const data = reader.skip(wireType, fieldNo);\n                    if (options.readUnknownFields) {\n                        this.onUnknownField(message, fieldNo, wireType, data);\n                    }\n                    continue;\n                }\n                readField(message, reader, field, wireType, options);\n            }\n            if (delimitedMessageEncoding && // eslint-disable-line @typescript-eslint/strict-boolean-expressions\n                (wireType != _binary_encoding_js__WEBPACK_IMPORTED_MODULE_0__.WireType.EndGroup || fieldNo !== lengthOrEndTagFieldNo)) {\n                throw new Error(`invalid end group tag`);\n            }\n        },\n        readField,\n        writeMessage(message, writer, options) {\n            const type = message.getType();\n            for (const field of type.fields.byNumber()) {\n                if (!(0,_reflect_js__WEBPACK_IMPORTED_MODULE_1__.isFieldSet)(field, message)) {\n                    if (field.req) {\n                        throw new Error(`cannot encode field ${type.typeName}.${field.name} to binary: required field not set`);\n                    }\n                    continue;\n                }\n                const value = field.oneof\n                    ? message[field.oneof.localName].value\n                    : message[field.localName];\n                writeField(field, value, writer, options);\n            }\n            if (options.writeUnknownFields) {\n                this.writeUnknownFields(message, writer);\n            }\n            return writer;\n        },\n        writeField(field, value, writer, options) {\n            // The behavior of our internal function has changed, it does no longer\n            // accept `undefined` values for singular scalar and map.\n            // For backwards-compatibility, we support the old form that is part of\n            // the public API through the interface BinaryFormat.\n            if (value === undefined) {\n                return undefined;\n            }\n            writeField(field, value, writer, options);\n        },\n    };\n}\nfunction readField(target, // eslint-disable-line @typescript-eslint/no-explicit-any -- `any` is the best choice for dynamic access\nreader, field, wireType, options) {\n    let { repeated, localName } = field;\n    if (field.oneof) {\n        target = target[field.oneof.localName];\n        if (target.case != localName) {\n            delete target.value;\n        }\n        target.case = localName;\n        localName = \"value\";\n    }\n    switch (field.kind) {\n        case \"scalar\":\n        case \"enum\":\n            const scalarType = field.kind == \"enum\" ? _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.INT32 : field.T;\n            let read = readScalar;\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison -- acceptable since it's covered by tests\n            if (field.kind == \"scalar\" && field.L > 0) {\n                read = readScalarLTString;\n            }\n            if (repeated) {\n                let arr = target[localName]; // safe to assume presence of array, oneof cannot contain repeated values\n                const isPacked = wireType == _binary_encoding_js__WEBPACK_IMPORTED_MODULE_0__.WireType.LengthDelimited &&\n                    scalarType != _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.STRING &&\n                    scalarType != _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.BYTES;\n                if (isPacked) {\n                    let e = reader.uint32() + reader.pos;\n                    while (reader.pos < e) {\n                        arr.push(read(reader, scalarType));\n                    }\n                }\n                else {\n                    arr.push(read(reader, scalarType));\n                }\n            }\n            else {\n                target[localName] = read(reader, scalarType);\n            }\n            break;\n        case \"message\":\n            const messageType = field.T;\n            if (repeated) {\n                // safe to assume presence of array, oneof cannot contain repeated values\n                target[localName].push(readMessageField(reader, new messageType(), options, field));\n            }\n            else {\n                if ((0,_is_message_js__WEBPACK_IMPORTED_MODULE_3__.isMessage)(target[localName])) {\n                    readMessageField(reader, target[localName], options, field);\n                }\n                else {\n                    target[localName] = readMessageField(reader, new messageType(), options, field);\n                    if (messageType.fieldWrapper && !field.oneof && !field.repeated) {\n                        target[localName] = messageType.fieldWrapper.unwrapField(target[localName]);\n                    }\n                }\n            }\n            break;\n        case \"map\":\n            let [mapKey, mapVal] = readMapEntry(field, reader, options);\n            // safe to assume presence of map object, oneof cannot contain repeated values\n            target[localName][mapKey] = mapVal;\n            break;\n    }\n}\n// Read a message, avoiding MessageType.fromBinary() to re-use the\n// BinaryReadOptions and the IBinaryReader.\nfunction readMessageField(reader, message, options, field) {\n    const format = message.getType().runtime.bin;\n    const delimited = field === null || field === void 0 ? void 0 : field.delimited;\n    format.readMessage(message, reader, delimited ? field.no : reader.uint32(), // eslint-disable-line @typescript-eslint/strict-boolean-expressions\n    options, delimited);\n    return message;\n}\n// Read a map field, expecting key field = 1, value field = 2\nfunction readMapEntry(field, reader, options) {\n    const length = reader.uint32(), end = reader.pos + length;\n    let key, val;\n    while (reader.pos < end) {\n        const [fieldNo] = reader.tag();\n        switch (fieldNo) {\n            case 1:\n                key = readScalar(reader, field.K);\n                break;\n            case 2:\n                switch (field.V.kind) {\n                    case \"scalar\":\n                        val = readScalar(reader, field.V.T);\n                        break;\n                    case \"enum\":\n                        val = reader.int32();\n                        break;\n                    case \"message\":\n                        val = readMessageField(reader, new field.V.T(), options, undefined);\n                        break;\n                }\n                break;\n        }\n    }\n    if (key === undefined) {\n        key = (0,_scalars_js__WEBPACK_IMPORTED_MODULE_4__.scalarZeroValue)(field.K, _scalar_js__WEBPACK_IMPORTED_MODULE_2__.LongType.BIGINT);\n    }\n    if (typeof key != \"string\" && typeof key != \"number\") {\n        key = key.toString();\n    }\n    if (val === undefined) {\n        switch (field.V.kind) {\n            case \"scalar\":\n                val = (0,_scalars_js__WEBPACK_IMPORTED_MODULE_4__.scalarZeroValue)(field.V.T, _scalar_js__WEBPACK_IMPORTED_MODULE_2__.LongType.BIGINT);\n                break;\n            case \"enum\":\n                val = field.V.T.values[0].no;\n                break;\n            case \"message\":\n                val = new field.V.T();\n                break;\n        }\n    }\n    return [key, val];\n}\n// Read a scalar value, but return 64 bit integral types (int64, uint64,\n// sint64, fixed64, sfixed64) as string instead of bigint.\nfunction readScalarLTString(reader, type) {\n    const v = readScalar(reader, type);\n    return typeof v == \"bigint\" ? v.toString() : v;\n}\n// Does not use scalarTypeInfo() for better performance.\nfunction readScalar(reader, type) {\n    switch (type) {\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.STRING:\n            return reader.string();\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.BOOL:\n            return reader.bool();\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.DOUBLE:\n            return reader.double();\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.FLOAT:\n            return reader.float();\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.INT32:\n            return reader.int32();\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.INT64:\n            return reader.int64();\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.UINT64:\n            return reader.uint64();\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.FIXED64:\n            return reader.fixed64();\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.BYTES:\n            return reader.bytes();\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.FIXED32:\n            return reader.fixed32();\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.SFIXED32:\n            return reader.sfixed32();\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.SFIXED64:\n            return reader.sfixed64();\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.SINT64:\n            return reader.sint64();\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.UINT32:\n            return reader.uint32();\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.SINT32:\n            return reader.sint32();\n    }\n}\nfunction writeField(field, value, writer, options) {\n    (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assert)(value !== undefined);\n    const repeated = field.repeated;\n    switch (field.kind) {\n        case \"scalar\":\n        case \"enum\":\n            let scalarType = field.kind == \"enum\" ? _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.INT32 : field.T;\n            if (repeated) {\n                (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assert)(Array.isArray(value));\n                if (field.packed) {\n                    writePacked(writer, scalarType, field.no, value);\n                }\n                else {\n                    for (const item of value) {\n                        writeScalar(writer, scalarType, field.no, item);\n                    }\n                }\n            }\n            else {\n                writeScalar(writer, scalarType, field.no, value);\n            }\n            break;\n        case \"message\":\n            if (repeated) {\n                (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assert)(Array.isArray(value));\n                for (const item of value) {\n                    writeMessageField(writer, options, field, item);\n                }\n            }\n            else {\n                writeMessageField(writer, options, field, value);\n            }\n            break;\n        case \"map\":\n            (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assert)(typeof value == \"object\" && value != null);\n            for (const [key, val] of Object.entries(value)) {\n                writeMapEntry(writer, options, field, key, val);\n            }\n            break;\n    }\n}\nfunction writeMapEntry(writer, options, field, key, value) {\n    writer.tag(field.no, _binary_encoding_js__WEBPACK_IMPORTED_MODULE_0__.WireType.LengthDelimited);\n    writer.fork();\n    // javascript only allows number or string for object properties\n    // we convert from our representation to the protobuf type\n    let keyValue = key;\n    // eslint-disable-next-line @typescript-eslint/switch-exhaustiveness-check -- we deliberately handle just the special cases for map keys\n    switch (field.K) {\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.INT32:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.FIXED32:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.UINT32:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.SFIXED32:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.SINT32:\n            keyValue = Number.parseInt(key);\n            break;\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.BOOL:\n            (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assert)(key == \"true\" || key == \"false\");\n            keyValue = key == \"true\";\n            break;\n    }\n    // write key, expecting key field number = 1\n    writeScalar(writer, field.K, 1, keyValue);\n    // write value, expecting value field number = 2\n    switch (field.V.kind) {\n        case \"scalar\":\n            writeScalar(writer, field.V.T, 2, value);\n            break;\n        case \"enum\":\n            writeScalar(writer, _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.INT32, 2, value);\n            break;\n        case \"message\":\n            (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assert)(value !== undefined);\n            writer.tag(2, _binary_encoding_js__WEBPACK_IMPORTED_MODULE_0__.WireType.LengthDelimited).bytes(value.toBinary(options));\n            break;\n    }\n    writer.join();\n}\n// Value must not be undefined\nfunction writeMessageField(writer, options, field, value) {\n    const message = (0,_field_wrapper_js__WEBPACK_IMPORTED_MODULE_6__.wrapField)(field.T, value);\n    // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions\n    if (field.delimited)\n        writer\n            .tag(field.no, _binary_encoding_js__WEBPACK_IMPORTED_MODULE_0__.WireType.StartGroup)\n            .raw(message.toBinary(options))\n            .tag(field.no, _binary_encoding_js__WEBPACK_IMPORTED_MODULE_0__.WireType.EndGroup);\n    else\n        writer\n            .tag(field.no, _binary_encoding_js__WEBPACK_IMPORTED_MODULE_0__.WireType.LengthDelimited)\n            .bytes(message.toBinary(options));\n}\nfunction writeScalar(writer, type, fieldNo, value) {\n    (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assert)(value !== undefined);\n    let [wireType, method] = scalarTypeInfo(type);\n    writer.tag(fieldNo, wireType)[method](value);\n}\nfunction writePacked(writer, type, fieldNo, value) {\n    if (!value.length) {\n        return;\n    }\n    writer.tag(fieldNo, _binary_encoding_js__WEBPACK_IMPORTED_MODULE_0__.WireType.LengthDelimited).fork();\n    let [, method] = scalarTypeInfo(type);\n    for (let i = 0; i < value.length; i++) {\n        writer[method](value[i]);\n    }\n    writer.join();\n}\n/**\n * Get information for writing a scalar value.\n *\n * Returns tuple:\n * [0]: appropriate WireType\n * [1]: name of the appropriate method of IBinaryWriter\n * [2]: whether the given value is a default value for proto3 semantics\n *\n * If argument `value` is omitted, [2] is always false.\n */\n// TODO replace call-sites writeScalar() and writePacked(), then remove\nfunction scalarTypeInfo(type) {\n    let wireType = _binary_encoding_js__WEBPACK_IMPORTED_MODULE_0__.WireType.Varint;\n    // eslint-disable-next-line @typescript-eslint/switch-exhaustiveness-check -- INT32, UINT32, SINT32 are covered by the defaults\n    switch (type) {\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.BYTES:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.STRING:\n            wireType = _binary_encoding_js__WEBPACK_IMPORTED_MODULE_0__.WireType.LengthDelimited;\n            break;\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.DOUBLE:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.FIXED64:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.SFIXED64:\n            wireType = _binary_encoding_js__WEBPACK_IMPORTED_MODULE_0__.WireType.Bit64;\n            break;\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.FIXED32:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.SFIXED32:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.FLOAT:\n            wireType = _binary_encoding_js__WEBPACK_IMPORTED_MODULE_0__.WireType.Bit32;\n            break;\n    }\n    const method = _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType[type].toLowerCase();\n    return [wireType, method];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/binary-format.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/enum.js":
/*!******************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/private/enum.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getEnumType: () => (/* binding */ getEnumType),\n/* harmony export */   makeEnum: () => (/* binding */ makeEnum),\n/* harmony export */   makeEnumType: () => (/* binding */ makeEnumType),\n/* harmony export */   setEnumType: () => (/* binding */ setEnumType)\n/* harmony export */ });\n/* harmony import */ var _assert_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./assert.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/assert.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\nconst enumTypeSymbol = Symbol(\"@bufbuild/protobuf/enum-type\");\n/**\n * Get reflection information from a generated enum.\n * If this function is called on something other than a generated\n * enum, it raises an error.\n */\nfunction getEnumType(enumObject) {\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access,@typescript-eslint/no-unsafe-assignment,@typescript-eslint/no-explicit-any\n    const t = enumObject[enumTypeSymbol];\n    (0,_assert_js__WEBPACK_IMPORTED_MODULE_0__.assert)(t, \"missing enum type on enum object\");\n    return t; // eslint-disable-line @typescript-eslint/no-unsafe-return\n}\n/**\n * Sets reflection information on a generated enum.\n */\nfunction setEnumType(enumObject, typeName, values, opt) {\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-explicit-any\n    enumObject[enumTypeSymbol] = makeEnumType(typeName, values.map((v) => ({\n        no: v.no,\n        name: v.name,\n        localName: enumObject[v.no],\n    })), opt);\n}\n/**\n * Create a new EnumType with the given values.\n */\nfunction makeEnumType(typeName, values, \n// eslint-disable-next-line @typescript-eslint/no-unused-vars\n_opt) {\n    const names = Object.create(null);\n    const numbers = Object.create(null);\n    const normalValues = [];\n    for (const value of values) {\n        // We do not surface options at this time\n        // const value: EnumValueInfo = {...v, options: v.options ?? emptyReadonlyObject};\n        const n = normalizeEnumValue(value);\n        normalValues.push(n);\n        names[value.name] = n;\n        numbers[value.no] = n;\n    }\n    return {\n        typeName,\n        values: normalValues,\n        // We do not surface options at this time\n        // options: opt?.options ?? Object.create(null),\n        findName(name) {\n            return names[name];\n        },\n        findNumber(no) {\n            return numbers[no];\n        },\n    };\n}\n/**\n * Create a new enum object with the given values.\n * Sets reflection information.\n */\nfunction makeEnum(typeName, values, opt) {\n    const enumObject = {};\n    for (const value of values) {\n        const n = normalizeEnumValue(value);\n        enumObject[n.localName] = n.no;\n        enumObject[n.no] = n.localName;\n    }\n    setEnumType(enumObject, typeName, values, opt);\n    return enumObject;\n}\nfunction normalizeEnumValue(value) {\n    if (\"localName\" in value) {\n        return value;\n    }\n    return Object.assign(Object.assign({}, value), { localName: value.name });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/enum.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/extensions.js":
/*!************************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/private/extensions.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createExtensionContainer: () => (/* binding */ createExtensionContainer),\n/* harmony export */   filterUnknownFields: () => (/* binding */ filterUnknownFields),\n/* harmony export */   makeExtension: () => (/* binding */ makeExtension)\n/* harmony export */ });\n/* harmony import */ var _scalars_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./scalars.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/scalars.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n\n/**\n * Create a new extension using the given runtime.\n */\nfunction makeExtension(runtime, typeName, extendee, field) {\n    let fi;\n    return {\n        typeName,\n        extendee,\n        get field() {\n            if (!fi) {\n                const i = (typeof field == \"function\" ? field() : field);\n                i.name = typeName.split(\".\").pop();\n                i.jsonName = `[${typeName}]`;\n                fi = runtime.util.newFieldList([i]).list()[0];\n            }\n            return fi;\n        },\n        runtime,\n    };\n}\n/**\n * Create a container that allows us to read extension fields into it with the\n * same logic as regular fields.\n */\nfunction createExtensionContainer(extension) {\n    const localName = extension.field.localName;\n    const container = Object.create(null);\n    container[localName] = initExtensionField(extension);\n    return [container, () => container[localName]];\n}\nfunction initExtensionField(ext) {\n    const field = ext.field;\n    if (field.repeated) {\n        return [];\n    }\n    if (field.default !== undefined) {\n        return field.default;\n    }\n    switch (field.kind) {\n        case \"enum\":\n            return field.T.values[0].no;\n        case \"scalar\":\n            return (0,_scalars_js__WEBPACK_IMPORTED_MODULE_0__.scalarZeroValue)(field.T, field.L);\n        case \"message\":\n            // eslint-disable-next-line no-case-declarations\n            const T = field.T, value = new T();\n            return T.fieldWrapper ? T.fieldWrapper.unwrapField(value) : value;\n        case \"map\":\n            throw \"map fields are not allowed to be extensions\";\n    }\n}\n/**\n * Helper to filter unknown fields, optimized based on field type.\n */\nfunction filterUnknownFields(unknownFields, field) {\n    if (!field.repeated && (field.kind == \"enum\" || field.kind == \"scalar\")) {\n        // singular scalar fields do not merge, we pick the last\n        for (let i = unknownFields.length - 1; i >= 0; --i) {\n            if (unknownFields[i].no == field.no) {\n                return [unknownFields[i]];\n            }\n        }\n        return [];\n    }\n    return unknownFields.filter((uf) => uf.no === field.no);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/extensions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/field-list.js":
/*!************************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/private/field-list.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InternalFieldList: () => (/* binding */ InternalFieldList)\n/* harmony export */ });\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nclass InternalFieldList {\n    constructor(fields, normalizer) {\n        this._fields = fields;\n        this._normalizer = normalizer;\n    }\n    findJsonName(jsonName) {\n        if (!this.jsonNames) {\n            const t = {};\n            for (const f of this.list()) {\n                t[f.jsonName] = t[f.name] = f;\n            }\n            this.jsonNames = t;\n        }\n        return this.jsonNames[jsonName];\n    }\n    find(fieldNo) {\n        if (!this.numbers) {\n            const t = {};\n            for (const f of this.list()) {\n                t[f.no] = f;\n            }\n            this.numbers = t;\n        }\n        return this.numbers[fieldNo];\n    }\n    list() {\n        if (!this.all) {\n            this.all = this._normalizer(this._fields);\n        }\n        return this.all;\n    }\n    byNumber() {\n        if (!this.numbersAsc) {\n            this.numbersAsc = this.list()\n                .concat()\n                .sort((a, b) => a.no - b.no);\n        }\n        return this.numbersAsc;\n    }\n    byMember() {\n        if (!this.members) {\n            this.members = [];\n            const a = this.members;\n            let o;\n            for (const f of this.list()) {\n                if (f.oneof) {\n                    if (f.oneof !== o) {\n                        o = f.oneof;\n                        a.push(o);\n                    }\n                }\n                else {\n                    a.push(f);\n                }\n            }\n        }\n        return this.members;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/field-list.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/field-normalize.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/private/field-normalize.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   normalizeFieldInfos: () => (/* binding */ normalizeFieldInfos)\n/* harmony export */ });\n/* harmony import */ var _field_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./field.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/field.js\");\n/* harmony import */ var _names_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./names.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/names.js\");\n/* harmony import */ var _scalar_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../scalar.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/scalar.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n\n\n/**\n * Convert a collection of field info to an array of normalized FieldInfo.\n *\n * The argument `packedByDefault` specifies whether fields that do not specify\n * `packed` should be packed (proto3) or unpacked (proto2).\n */\nfunction normalizeFieldInfos(fieldInfos, packedByDefault) {\n    var _a, _b, _c, _d, _e, _f;\n    const r = [];\n    let o;\n    for (const field of typeof fieldInfos == \"function\"\n        ? fieldInfos()\n        : fieldInfos) {\n        const f = field;\n        f.localName = (0,_names_js__WEBPACK_IMPORTED_MODULE_0__.localFieldName)(field.name, field.oneof !== undefined);\n        f.jsonName = (_a = field.jsonName) !== null && _a !== void 0 ? _a : (0,_names_js__WEBPACK_IMPORTED_MODULE_0__.fieldJsonName)(field.name);\n        f.repeated = (_b = field.repeated) !== null && _b !== void 0 ? _b : false;\n        if (field.kind == \"scalar\") {\n            f.L = (_c = field.L) !== null && _c !== void 0 ? _c : _scalar_js__WEBPACK_IMPORTED_MODULE_1__.LongType.BIGINT;\n        }\n        f.delimited = (_d = field.delimited) !== null && _d !== void 0 ? _d : false;\n        f.req = (_e = field.req) !== null && _e !== void 0 ? _e : false;\n        f.opt = (_f = field.opt) !== null && _f !== void 0 ? _f : false;\n        if (field.packed === undefined) {\n            if (packedByDefault) {\n                f.packed =\n                    field.kind == \"enum\" ||\n                        (field.kind == \"scalar\" &&\n                            field.T != _scalar_js__WEBPACK_IMPORTED_MODULE_1__.ScalarType.BYTES &&\n                            field.T != _scalar_js__WEBPACK_IMPORTED_MODULE_1__.ScalarType.STRING);\n            }\n            else {\n                f.packed = false;\n            }\n        }\n        // We do not surface options at this time\n        // f.options = field.options ?? emptyReadonlyObject;\n        if (field.oneof !== undefined) {\n            const ooname = typeof field.oneof == \"string\" ? field.oneof : field.oneof.name;\n            if (!o || o.name != ooname) {\n                o = new _field_js__WEBPACK_IMPORTED_MODULE_2__.InternalOneofInfo(ooname);\n            }\n            f.oneof = o;\n            o.addField(f);\n        }\n        r.push(f);\n    }\n    return r;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/field-normalize.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/field-wrapper.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/private/field-wrapper.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getUnwrappedFieldType: () => (/* binding */ getUnwrappedFieldType),\n/* harmony export */   wrapField: () => (/* binding */ wrapField)\n/* harmony export */ });\n/* harmony import */ var _scalar_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../scalar.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/scalar.js\");\n/* harmony import */ var _is_message_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../is-message.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/is-message.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n\n\n/**\n * Wrap a primitive message field value in its corresponding wrapper\n * message. This function is idempotent.\n */\nfunction wrapField(type, value) {\n    if ((0,_is_message_js__WEBPACK_IMPORTED_MODULE_0__.isMessage)(value) || !type.fieldWrapper) {\n        return value;\n    }\n    return type.fieldWrapper.wrapField(value);\n}\n/**\n * If the given field uses one of the well-known wrapper types, return\n * the primitive type it wraps.\n */\nfunction getUnwrappedFieldType(field) {\n    if (field.fieldKind !== \"message\") {\n        return undefined;\n    }\n    if (field.repeated) {\n        return undefined;\n    }\n    if (field.oneof != undefined) {\n        return undefined;\n    }\n    return wktWrapperToScalarType[field.message.typeName];\n}\nconst wktWrapperToScalarType = {\n    \"google.protobuf.DoubleValue\": _scalar_js__WEBPACK_IMPORTED_MODULE_1__.ScalarType.DOUBLE,\n    \"google.protobuf.FloatValue\": _scalar_js__WEBPACK_IMPORTED_MODULE_1__.ScalarType.FLOAT,\n    \"google.protobuf.Int64Value\": _scalar_js__WEBPACK_IMPORTED_MODULE_1__.ScalarType.INT64,\n    \"google.protobuf.UInt64Value\": _scalar_js__WEBPACK_IMPORTED_MODULE_1__.ScalarType.UINT64,\n    \"google.protobuf.Int32Value\": _scalar_js__WEBPACK_IMPORTED_MODULE_1__.ScalarType.INT32,\n    \"google.protobuf.UInt32Value\": _scalar_js__WEBPACK_IMPORTED_MODULE_1__.ScalarType.UINT32,\n    \"google.protobuf.BoolValue\": _scalar_js__WEBPACK_IMPORTED_MODULE_1__.ScalarType.BOOL,\n    \"google.protobuf.StringValue\": _scalar_js__WEBPACK_IMPORTED_MODULE_1__.ScalarType.STRING,\n    \"google.protobuf.BytesValue\": _scalar_js__WEBPACK_IMPORTED_MODULE_1__.ScalarType.BYTES,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/field-wrapper.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/field.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/private/field.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InternalOneofInfo: () => (/* binding */ InternalOneofInfo)\n/* harmony export */ });\n/* harmony import */ var _names_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./names.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/names.js\");\n/* harmony import */ var _assert_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./assert.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/assert.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n\nclass InternalOneofInfo {\n    constructor(name) {\n        this.kind = \"oneof\";\n        this.repeated = false;\n        this.packed = false;\n        this.opt = false;\n        this.req = false;\n        this.default = undefined;\n        this.fields = [];\n        this.name = name;\n        this.localName = (0,_names_js__WEBPACK_IMPORTED_MODULE_0__.localOneofName)(name);\n    }\n    addField(field) {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.assert)(field.oneof === this, `field ${field.name} not one of ${this.name}`);\n        this.fields.push(field);\n    }\n    findField(localName) {\n        if (!this._lookup) {\n            this._lookup = Object.create(null);\n            for (let i = 0; i < this.fields.length; i++) {\n                this._lookup[this.fields[i].localName] = this.fields[i];\n            }\n        }\n        return this._lookup[localName];\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/field.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/json-format.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/private/json-format.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   makeJsonFormat: () => (/* binding */ makeJsonFormat)\n/* harmony export */ });\n/* harmony import */ var _assert_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./assert.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/assert.js\");\n/* harmony import */ var _proto_int64_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../proto-int64.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/proto-int64.js\");\n/* harmony import */ var _proto_base64_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../proto-base64.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/proto-base64.js\");\n/* harmony import */ var _extensions_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./extensions.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/extensions.js\");\n/* harmony import */ var _extension_accessor_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../extension-accessor.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/extension-accessor.js\");\n/* harmony import */ var _reflect_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./reflect.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/reflect.js\");\n/* harmony import */ var _field_wrapper_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./field-wrapper.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/field-wrapper.js\");\n/* harmony import */ var _scalars_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./scalars.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/scalars.js\");\n/* harmony import */ var _scalar_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../scalar.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/scalar.js\");\n/* harmony import */ var _is_message_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../is-message.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/is-message.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n\n\n\n\n\n\n\n\n\n\n\n/* eslint-disable no-case-declarations,@typescript-eslint/no-unsafe-argument,@typescript-eslint/no-unsafe-assignment,@typescript-eslint/no-unsafe-member-access,@typescript-eslint/no-unsafe-call */\n// Default options for parsing JSON.\nconst jsonReadDefaults = {\n    ignoreUnknownFields: false,\n};\n// Default options for serializing to JSON.\nconst jsonWriteDefaults = {\n    emitDefaultValues: false,\n    enumAsInteger: false,\n    useProtoFieldName: false,\n    prettySpaces: 0,\n};\nfunction makeReadOptions(options) {\n    return options ? Object.assign(Object.assign({}, jsonReadDefaults), options) : jsonReadDefaults;\n}\nfunction makeWriteOptions(options) {\n    return options ? Object.assign(Object.assign({}, jsonWriteDefaults), options) : jsonWriteDefaults;\n}\nconst tokenNull = Symbol();\nconst tokenIgnoredUnknownEnum = Symbol();\nfunction makeJsonFormat() {\n    return {\n        makeReadOptions,\n        makeWriteOptions,\n        readMessage(type, json, options, message) {\n            if (json == null || Array.isArray(json) || typeof json != \"object\") {\n                throw new Error(`cannot decode message ${type.typeName} from JSON: ${debugJsonValue(json)}`);\n            }\n            message = message !== null && message !== void 0 ? message : new type();\n            const oneofSeen = new Map();\n            const registry = options.typeRegistry;\n            for (const [jsonKey, jsonValue] of Object.entries(json)) {\n                const field = type.fields.findJsonName(jsonKey);\n                if (field) {\n                    if (field.oneof) {\n                        if (jsonValue === null && field.kind == \"scalar\") {\n                            // see conformance test Required.Proto3.JsonInput.OneofFieldNull{First,Second}\n                            continue;\n                        }\n                        const seen = oneofSeen.get(field.oneof);\n                        if (seen !== undefined) {\n                            throw new Error(`cannot decode message ${type.typeName} from JSON: multiple keys for oneof \"${field.oneof.name}\" present: \"${seen}\", \"${jsonKey}\"`);\n                        }\n                        oneofSeen.set(field.oneof, jsonKey);\n                    }\n                    readField(message, jsonValue, field, options, type);\n                }\n                else {\n                    let found = false;\n                    if ((registry === null || registry === void 0 ? void 0 : registry.findExtension) &&\n                        jsonKey.startsWith(\"[\") &&\n                        jsonKey.endsWith(\"]\")) {\n                        const ext = registry.findExtension(jsonKey.substring(1, jsonKey.length - 1));\n                        if (ext && ext.extendee.typeName == type.typeName) {\n                            found = true;\n                            const [container, get] = (0,_extensions_js__WEBPACK_IMPORTED_MODULE_0__.createExtensionContainer)(ext);\n                            readField(container, jsonValue, ext.field, options, ext);\n                            // We pass on the options as BinaryReadOptions/BinaryWriteOptions,\n                            // so that users can bring their own binary reader and writer factories\n                            // if necessary.\n                            (0,_extension_accessor_js__WEBPACK_IMPORTED_MODULE_1__.setExtension)(message, ext, get(), options);\n                        }\n                    }\n                    if (!found && !options.ignoreUnknownFields) {\n                        throw new Error(`cannot decode message ${type.typeName} from JSON: key \"${jsonKey}\" is unknown`);\n                    }\n                }\n            }\n            return message;\n        },\n        writeMessage(message, options) {\n            const type = message.getType();\n            const json = {};\n            let field;\n            try {\n                for (field of type.fields.byNumber()) {\n                    if (!(0,_reflect_js__WEBPACK_IMPORTED_MODULE_2__.isFieldSet)(field, message)) {\n                        // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions\n                        if (field.req) {\n                            throw `required field not set`;\n                        }\n                        if (!options.emitDefaultValues) {\n                            continue;\n                        }\n                        if (!canEmitFieldDefaultValue(field)) {\n                            continue;\n                        }\n                    }\n                    const value = field.oneof\n                        ? message[field.oneof.localName].value\n                        : message[field.localName];\n                    const jsonValue = writeField(field, value, options);\n                    if (jsonValue !== undefined) {\n                        json[options.useProtoFieldName ? field.name : field.jsonName] =\n                            jsonValue;\n                    }\n                }\n                const registry = options.typeRegistry;\n                if (registry === null || registry === void 0 ? void 0 : registry.findExtensionFor) {\n                    for (const uf of type.runtime.bin.listUnknownFields(message)) {\n                        const ext = registry.findExtensionFor(type.typeName, uf.no);\n                        if (ext && (0,_extension_accessor_js__WEBPACK_IMPORTED_MODULE_1__.hasExtension)(message, ext)) {\n                            // We pass on the options as BinaryReadOptions, so that users can bring their own\n                            // binary reader factory if necessary.\n                            const value = (0,_extension_accessor_js__WEBPACK_IMPORTED_MODULE_1__.getExtension)(message, ext, options);\n                            const jsonValue = writeField(ext.field, value, options);\n                            if (jsonValue !== undefined) {\n                                json[ext.field.jsonName] = jsonValue;\n                            }\n                        }\n                    }\n                }\n            }\n            catch (e) {\n                const m = field\n                    ? `cannot encode field ${type.typeName}.${field.name} to JSON`\n                    : `cannot encode message ${type.typeName} to JSON`;\n                const r = e instanceof Error ? e.message : String(e);\n                throw new Error(m + (r.length > 0 ? `: ${r}` : \"\"));\n            }\n            return json;\n        },\n        readScalar(type, json, longType) {\n            // The signature of our internal function has changed. For backwards-\n            // compatibility, we support the old form that is part of the public API\n            // through the interface JsonFormat.\n            return readScalar(type, json, longType !== null && longType !== void 0 ? longType : _scalar_js__WEBPACK_IMPORTED_MODULE_3__.LongType.BIGINT, true);\n        },\n        writeScalar(type, value, emitDefaultValues) {\n            // The signature of our internal function has changed. For backwards-\n            // compatibility, we support the old form that is part of the public API\n            // through the interface JsonFormat.\n            if (value === undefined) {\n                return undefined;\n            }\n            if (emitDefaultValues || (0,_scalars_js__WEBPACK_IMPORTED_MODULE_4__.isScalarZeroValue)(type, value)) {\n                return writeScalar(type, value);\n            }\n            return undefined;\n        },\n        debug: debugJsonValue,\n    };\n}\nfunction debugJsonValue(json) {\n    if (json === null) {\n        return \"null\";\n    }\n    switch (typeof json) {\n        case \"object\":\n            return Array.isArray(json) ? \"array\" : \"object\";\n        case \"string\":\n            return json.length > 100 ? \"string\" : `\"${json.split('\"').join('\\\\\"')}\"`;\n        default:\n            return String(json);\n    }\n}\n// Read a JSON value for a field.\n// The \"parentType\" argument is only used to provide context in errors.\nfunction readField(target, jsonValue, field, options, parentType) {\n    let localName = field.localName;\n    if (field.repeated) {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assert)(field.kind != \"map\");\n        if (jsonValue === null) {\n            return;\n        }\n        if (!Array.isArray(jsonValue)) {\n            throw new Error(`cannot decode field ${parentType.typeName}.${field.name} from JSON: ${debugJsonValue(jsonValue)}`);\n        }\n        const targetArray = target[localName];\n        for (const jsonItem of jsonValue) {\n            if (jsonItem === null) {\n                throw new Error(`cannot decode field ${parentType.typeName}.${field.name} from JSON: ${debugJsonValue(jsonItem)}`);\n            }\n            switch (field.kind) {\n                case \"message\":\n                    targetArray.push(field.T.fromJson(jsonItem, options));\n                    break;\n                case \"enum\":\n                    const enumValue = readEnum(field.T, jsonItem, options.ignoreUnknownFields, true);\n                    if (enumValue !== tokenIgnoredUnknownEnum) {\n                        targetArray.push(enumValue);\n                    }\n                    break;\n                case \"scalar\":\n                    try {\n                        targetArray.push(readScalar(field.T, jsonItem, field.L, true));\n                    }\n                    catch (e) {\n                        let m = `cannot decode field ${parentType.typeName}.${field.name} from JSON: ${debugJsonValue(jsonItem)}`;\n                        if (e instanceof Error && e.message.length > 0) {\n                            m += `: ${e.message}`;\n                        }\n                        throw new Error(m);\n                    }\n                    break;\n            }\n        }\n    }\n    else if (field.kind == \"map\") {\n        if (jsonValue === null) {\n            return;\n        }\n        if (typeof jsonValue != \"object\" || Array.isArray(jsonValue)) {\n            throw new Error(`cannot decode field ${parentType.typeName}.${field.name} from JSON: ${debugJsonValue(jsonValue)}`);\n        }\n        const targetMap = target[localName];\n        for (const [jsonMapKey, jsonMapValue] of Object.entries(jsonValue)) {\n            if (jsonMapValue === null) {\n                throw new Error(`cannot decode field ${parentType.typeName}.${field.name} from JSON: map value null`);\n            }\n            let key;\n            try {\n                key = readMapKey(field.K, jsonMapKey);\n            }\n            catch (e) {\n                let m = `cannot decode map key for field ${parentType.typeName}.${field.name} from JSON: ${debugJsonValue(jsonValue)}`;\n                if (e instanceof Error && e.message.length > 0) {\n                    m += `: ${e.message}`;\n                }\n                throw new Error(m);\n            }\n            switch (field.V.kind) {\n                case \"message\":\n                    targetMap[key] = field.V.T.fromJson(jsonMapValue, options);\n                    break;\n                case \"enum\":\n                    const enumValue = readEnum(field.V.T, jsonMapValue, options.ignoreUnknownFields, true);\n                    if (enumValue !== tokenIgnoredUnknownEnum) {\n                        targetMap[key] = enumValue;\n                    }\n                    break;\n                case \"scalar\":\n                    try {\n                        targetMap[key] = readScalar(field.V.T, jsonMapValue, _scalar_js__WEBPACK_IMPORTED_MODULE_3__.LongType.BIGINT, true);\n                    }\n                    catch (e) {\n                        let m = `cannot decode map value for field ${parentType.typeName}.${field.name} from JSON: ${debugJsonValue(jsonValue)}`;\n                        if (e instanceof Error && e.message.length > 0) {\n                            m += `: ${e.message}`;\n                        }\n                        throw new Error(m);\n                    }\n                    break;\n            }\n        }\n    }\n    else {\n        if (field.oneof) {\n            target = target[field.oneof.localName] = { case: localName };\n            localName = \"value\";\n        }\n        switch (field.kind) {\n            case \"message\":\n                const messageType = field.T;\n                if (jsonValue === null &&\n                    messageType.typeName != \"google.protobuf.Value\") {\n                    return;\n                }\n                let currentValue = target[localName];\n                if ((0,_is_message_js__WEBPACK_IMPORTED_MODULE_6__.isMessage)(currentValue)) {\n                    currentValue.fromJson(jsonValue, options);\n                }\n                else {\n                    target[localName] = currentValue = messageType.fromJson(jsonValue, options);\n                    if (messageType.fieldWrapper && !field.oneof) {\n                        target[localName] =\n                            messageType.fieldWrapper.unwrapField(currentValue);\n                    }\n                }\n                break;\n            case \"enum\":\n                const enumValue = readEnum(field.T, jsonValue, options.ignoreUnknownFields, false);\n                switch (enumValue) {\n                    case tokenNull:\n                        (0,_reflect_js__WEBPACK_IMPORTED_MODULE_2__.clearField)(field, target);\n                        break;\n                    case tokenIgnoredUnknownEnum:\n                        break;\n                    default:\n                        target[localName] = enumValue;\n                        break;\n                }\n                break;\n            case \"scalar\":\n                try {\n                    const scalarValue = readScalar(field.T, jsonValue, field.L, false);\n                    switch (scalarValue) {\n                        case tokenNull:\n                            (0,_reflect_js__WEBPACK_IMPORTED_MODULE_2__.clearField)(field, target);\n                            break;\n                        default:\n                            target[localName] = scalarValue;\n                            break;\n                    }\n                }\n                catch (e) {\n                    let m = `cannot decode field ${parentType.typeName}.${field.name} from JSON: ${debugJsonValue(jsonValue)}`;\n                    if (e instanceof Error && e.message.length > 0) {\n                        m += `: ${e.message}`;\n                    }\n                    throw new Error(m);\n                }\n                break;\n        }\n    }\n}\nfunction readMapKey(type, json) {\n    if (type === _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.BOOL) {\n        // eslint-disable-next-line @typescript-eslint/switch-exhaustiveness-check\n        switch (json) {\n            case \"true\":\n                json = true;\n                break;\n            case \"false\":\n                json = false;\n                break;\n        }\n    }\n    return readScalar(type, json, _scalar_js__WEBPACK_IMPORTED_MODULE_3__.LongType.BIGINT, true).toString();\n}\nfunction readScalar(type, json, longType, nullAsZeroValue) {\n    if (json === null) {\n        if (nullAsZeroValue) {\n            return (0,_scalars_js__WEBPACK_IMPORTED_MODULE_4__.scalarZeroValue)(type, longType);\n        }\n        return tokenNull;\n    }\n    // every valid case in the switch below returns, and every fall\n    // through is regarded as a failure.\n    switch (type) {\n        // float, double: JSON value will be a number or one of the special string values \"NaN\", \"Infinity\", and \"-Infinity\".\n        // Either numbers or strings are accepted. Exponent notation is also accepted.\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.DOUBLE:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.FLOAT:\n            if (json === \"NaN\")\n                return Number.NaN;\n            if (json === \"Infinity\")\n                return Number.POSITIVE_INFINITY;\n            if (json === \"-Infinity\")\n                return Number.NEGATIVE_INFINITY;\n            if (json === \"\") {\n                // empty string is not a number\n                break;\n            }\n            if (typeof json == \"string\" && json.trim().length !== json.length) {\n                // extra whitespace\n                break;\n            }\n            if (typeof json != \"string\" && typeof json != \"number\") {\n                break;\n            }\n            const float = Number(json);\n            if (Number.isNaN(float)) {\n                // not a number\n                break;\n            }\n            if (!Number.isFinite(float)) {\n                // infinity and -infinity are handled by string representation above, so this is an error\n                break;\n            }\n            if (type == _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.FLOAT)\n                (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assertFloat32)(float);\n            return float;\n        // int32, fixed32, uint32: JSON value will be a decimal number. Either numbers or strings are accepted.\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.INT32:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.FIXED32:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.SFIXED32:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.SINT32:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.UINT32:\n            let int32;\n            if (typeof json == \"number\")\n                int32 = json;\n            else if (typeof json == \"string\" && json.length > 0) {\n                if (json.trim().length === json.length)\n                    int32 = Number(json);\n            }\n            if (int32 === undefined)\n                break;\n            if (type == _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.UINT32 || type == _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.FIXED32)\n                (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assertUInt32)(int32);\n            else\n                (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assertInt32)(int32);\n            return int32;\n        // int64, fixed64, uint64: JSON value will be a decimal string. Either numbers or strings are accepted.\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.INT64:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.SFIXED64:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.SINT64:\n            if (typeof json != \"number\" && typeof json != \"string\")\n                break;\n            const long = _proto_int64_js__WEBPACK_IMPORTED_MODULE_7__.protoInt64.parse(json);\n            // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions\n            return longType ? long.toString() : long;\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.FIXED64:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.UINT64:\n            if (typeof json != \"number\" && typeof json != \"string\")\n                break;\n            const uLong = _proto_int64_js__WEBPACK_IMPORTED_MODULE_7__.protoInt64.uParse(json);\n            // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions\n            return longType ? uLong.toString() : uLong;\n        // bool:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.BOOL:\n            if (typeof json !== \"boolean\")\n                break;\n            return json;\n        // string:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.STRING:\n            if (typeof json !== \"string\") {\n                break;\n            }\n            // A string must always contain UTF-8 encoded or 7-bit ASCII.\n            // We validate with encodeURIComponent, which appears to be the fastest widely available option.\n            try {\n                encodeURIComponent(json);\n            }\n            catch (e) {\n                throw new Error(\"invalid UTF8\");\n            }\n            return json;\n        // bytes: JSON value will be the data encoded as a string using standard base64 encoding with paddings.\n        // Either standard or URL-safe base64 encoding with/without paddings are accepted.\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.BYTES:\n            if (json === \"\")\n                return new Uint8Array(0);\n            if (typeof json !== \"string\")\n                break;\n            return _proto_base64_js__WEBPACK_IMPORTED_MODULE_8__.protoBase64.dec(json);\n    }\n    throw new Error();\n}\nfunction readEnum(type, json, ignoreUnknownFields, nullAsZeroValue) {\n    if (json === null) {\n        if (type.typeName == \"google.protobuf.NullValue\") {\n            return 0; // google.protobuf.NullValue.NULL_VALUE = 0\n        }\n        return nullAsZeroValue ? type.values[0].no : tokenNull;\n    }\n    // eslint-disable-next-line @typescript-eslint/switch-exhaustiveness-check\n    switch (typeof json) {\n        case \"number\":\n            if (Number.isInteger(json)) {\n                return json;\n            }\n            break;\n        case \"string\":\n            const value = type.findName(json);\n            if (value !== undefined) {\n                return value.no;\n            }\n            if (ignoreUnknownFields) {\n                return tokenIgnoredUnknownEnum;\n            }\n            break;\n    }\n    throw new Error(`cannot decode enum ${type.typeName} from JSON: ${debugJsonValue(json)}`);\n}\n// Decide whether an unset field should be emitted with JSON write option `emitDefaultValues`\nfunction canEmitFieldDefaultValue(field) {\n    if (field.repeated || field.kind == \"map\") {\n        // maps are {}, repeated fields are []\n        return true;\n    }\n    if (field.oneof) {\n        // oneof fields are never emitted\n        return false;\n    }\n    if (field.kind == \"message\") {\n        // singular message field are allowed to emit JSON null, but we do not\n        return false;\n    }\n    // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions\n    if (field.opt || field.req) {\n        // the field uses explicit presence, so we cannot emit a zero value\n        return false;\n    }\n    return true;\n}\nfunction writeField(field, value, options) {\n    if (field.kind == \"map\") {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assert)(typeof value == \"object\" && value != null);\n        const jsonObj = {};\n        const entries = Object.entries(value);\n        switch (field.V.kind) {\n            case \"scalar\":\n                for (const [entryKey, entryValue] of entries) {\n                    jsonObj[entryKey.toString()] = writeScalar(field.V.T, entryValue); // JSON standard allows only (double quoted) string as property key\n                }\n                break;\n            case \"message\":\n                for (const [entryKey, entryValue] of entries) {\n                    // JSON standard allows only (double quoted) string as property key\n                    jsonObj[entryKey.toString()] = entryValue.toJson(options);\n                }\n                break;\n            case \"enum\":\n                const enumType = field.V.T;\n                for (const [entryKey, entryValue] of entries) {\n                    // JSON standard allows only (double quoted) string as property key\n                    jsonObj[entryKey.toString()] = writeEnum(enumType, entryValue, options.enumAsInteger);\n                }\n                break;\n        }\n        return options.emitDefaultValues || entries.length > 0\n            ? jsonObj\n            : undefined;\n    }\n    if (field.repeated) {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assert)(Array.isArray(value));\n        const jsonArr = [];\n        switch (field.kind) {\n            case \"scalar\":\n                for (let i = 0; i < value.length; i++) {\n                    jsonArr.push(writeScalar(field.T, value[i]));\n                }\n                break;\n            case \"enum\":\n                for (let i = 0; i < value.length; i++) {\n                    jsonArr.push(writeEnum(field.T, value[i], options.enumAsInteger));\n                }\n                break;\n            case \"message\":\n                for (let i = 0; i < value.length; i++) {\n                    jsonArr.push(value[i].toJson(options));\n                }\n                break;\n        }\n        return options.emitDefaultValues || jsonArr.length > 0\n            ? jsonArr\n            : undefined;\n    }\n    switch (field.kind) {\n        case \"scalar\":\n            return writeScalar(field.T, value);\n        case \"enum\":\n            return writeEnum(field.T, value, options.enumAsInteger);\n        case \"message\":\n            return (0,_field_wrapper_js__WEBPACK_IMPORTED_MODULE_9__.wrapField)(field.T, value).toJson(options);\n    }\n}\nfunction writeEnum(type, value, enumAsInteger) {\n    var _a;\n    (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assert)(typeof value == \"number\");\n    if (type.typeName == \"google.protobuf.NullValue\") {\n        return null;\n    }\n    if (enumAsInteger) {\n        return value;\n    }\n    const val = type.findNumber(value);\n    return (_a = val === null || val === void 0 ? void 0 : val.name) !== null && _a !== void 0 ? _a : value; // if we don't know the enum value, just return the number\n}\nfunction writeScalar(type, value) {\n    switch (type) {\n        // int32, fixed32, uint32: JSON value will be a decimal number. Either numbers or strings are accepted.\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.INT32:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.SFIXED32:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.SINT32:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.FIXED32:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.UINT32:\n            (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assert)(typeof value == \"number\");\n            return value;\n        // float, double: JSON value will be a number or one of the special string values \"NaN\", \"Infinity\", and \"-Infinity\".\n        // Either numbers or strings are accepted. Exponent notation is also accepted.\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.FLOAT:\n        // assertFloat32(value);\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.DOUBLE: // eslint-disable-line no-fallthrough\n            (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assert)(typeof value == \"number\");\n            if (Number.isNaN(value))\n                return \"NaN\";\n            if (value === Number.POSITIVE_INFINITY)\n                return \"Infinity\";\n            if (value === Number.NEGATIVE_INFINITY)\n                return \"-Infinity\";\n            return value;\n        // string:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.STRING:\n            (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assert)(typeof value == \"string\");\n            return value;\n        // bool:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.BOOL:\n            (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assert)(typeof value == \"boolean\");\n            return value;\n        // JSON value will be a decimal string. Either numbers or strings are accepted.\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.UINT64:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.FIXED64:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.INT64:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.SFIXED64:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.SINT64:\n            (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assert)(typeof value == \"bigint\" ||\n                typeof value == \"string\" ||\n                typeof value == \"number\");\n            return value.toString();\n        // bytes: JSON value will be the data encoded as a string using standard base64 encoding with paddings.\n        // Either standard or URL-safe base64 encoding with/without paddings are accepted.\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.BYTES:\n            (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assert)(value instanceof Uint8Array);\n            return _proto_base64_js__WEBPACK_IMPORTED_MODULE_8__.protoBase64.enc(value);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/json-format.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/message-type.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/private/message-type.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   makeMessageType: () => (/* binding */ makeMessageType)\n/* harmony export */ });\n/* harmony import */ var _message_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../message.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/message.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n/**\n * Create a new message type using the given runtime.\n */\nfunction makeMessageType(runtime, typeName, fields, opt) {\n    var _a;\n    const localName = (_a = opt === null || opt === void 0 ? void 0 : opt.localName) !== null && _a !== void 0 ? _a : typeName.substring(typeName.lastIndexOf(\".\") + 1);\n    const type = {\n        [localName]: function (data) {\n            runtime.util.initFields(this);\n            runtime.util.initPartial(data, this);\n        },\n    }[localName];\n    Object.setPrototypeOf(type.prototype, new _message_js__WEBPACK_IMPORTED_MODULE_0__.Message());\n    Object.assign(type, {\n        runtime,\n        typeName,\n        fields: runtime.util.newFieldList(fields),\n        fromBinary(bytes, options) {\n            return new type().fromBinary(bytes, options);\n        },\n        fromJson(jsonValue, options) {\n            return new type().fromJson(jsonValue, options);\n        },\n        fromJsonString(jsonString, options) {\n            return new type().fromJsonString(jsonString, options);\n        },\n        equals(a, b) {\n            return runtime.util.equals(type, a, b);\n        },\n    });\n    return type;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/message-type.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/names.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/private/names.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fieldJsonName: () => (/* binding */ fieldJsonName),\n/* harmony export */   findEnumSharedPrefix: () => (/* binding */ findEnumSharedPrefix),\n/* harmony export */   localFieldName: () => (/* binding */ localFieldName),\n/* harmony export */   localName: () => (/* binding */ localName),\n/* harmony export */   localOneofName: () => (/* binding */ localOneofName),\n/* harmony export */   safeIdentifier: () => (/* binding */ safeIdentifier),\n/* harmony export */   safeObjectProperty: () => (/* binding */ safeObjectProperty)\n/* harmony export */ });\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n/**\n * Returns the name of a protobuf element in generated code.\n *\n * Field names - including oneofs - are converted to lowerCamelCase. For\n * messages, enumerations and services, the package name is stripped from\n * the type name. For nested messages and enumerations, the names are joined\n * with an underscore. For methods, the first character is made lowercase.\n */\nfunction localName(desc) {\n    switch (desc.kind) {\n        case \"field\":\n            return localFieldName(desc.name, desc.oneof !== undefined);\n        case \"oneof\":\n            return localOneofName(desc.name);\n        case \"enum\":\n        case \"message\":\n        case \"service\":\n        case \"extension\": {\n            const pkg = desc.file.proto.package;\n            const offset = pkg === undefined ? 0 : pkg.length + 1;\n            const name = desc.typeName.substring(offset).replace(/\\./g, \"_\");\n            // For services, we only care about safe identifiers, not safe object properties,\n            // but we have shipped v1 with a bug that respected object properties, and we\n            // do not want to introduce a breaking change, so we continue to escape for\n            // safe object properties.\n            // See https://github.com/bufbuild/protobuf-es/pull/391\n            return safeObjectProperty(safeIdentifier(name));\n        }\n        case \"enum_value\": {\n            let name = desc.name;\n            const sharedPrefix = desc.parent.sharedPrefix;\n            if (sharedPrefix !== undefined) {\n                name = name.substring(sharedPrefix.length);\n            }\n            return safeObjectProperty(name);\n        }\n        case \"rpc\": {\n            let name = desc.name;\n            if (name.length == 0) {\n                return name;\n            }\n            name = name[0].toLowerCase() + name.substring(1);\n            return safeObjectProperty(name);\n        }\n    }\n}\n/**\n * Returns the name of a field in generated code.\n */\nfunction localFieldName(protoName, inOneof) {\n    const name = protoCamelCase(protoName);\n    if (inOneof) {\n        // oneof member names are not properties, but values of the `case` property.\n        return name;\n    }\n    return safeObjectProperty(safeMessageProperty(name));\n}\n/**\n * Returns the name of a oneof group in generated code.\n */\nfunction localOneofName(protoName) {\n    return localFieldName(protoName, false);\n}\n/**\n * Returns the JSON name for a protobuf field, exactly like protoc does.\n */\nconst fieldJsonName = protoCamelCase;\n/**\n * Finds a prefix shared by enum values, for example `MY_ENUM_` for\n * `enum MyEnum {MY_ENUM_A=0; MY_ENUM_B=1;}`.\n */\nfunction findEnumSharedPrefix(enumName, valueNames) {\n    const prefix = camelToSnakeCase(enumName) + \"_\";\n    for (const name of valueNames) {\n        if (!name.toLowerCase().startsWith(prefix)) {\n            return undefined;\n        }\n        const shortName = name.substring(prefix.length);\n        if (shortName.length == 0) {\n            return undefined;\n        }\n        if (/^\\d/.test(shortName)) {\n            // identifiers must not start with numbers\n            return undefined;\n        }\n    }\n    return prefix;\n}\n/**\n * Converts lowerCamelCase or UpperCamelCase into lower_snake_case.\n * This is used to find shared prefixes in an enum.\n */\nfunction camelToSnakeCase(camel) {\n    return (camel.substring(0, 1) + camel.substring(1).replace(/[A-Z]/g, (c) => \"_\" + c)).toLowerCase();\n}\n/**\n * Converts snake_case to protoCamelCase according to the convention\n * used by protoc to convert a field name to a JSON name.\n */\nfunction protoCamelCase(snakeCase) {\n    let capNext = false;\n    const b = [];\n    for (let i = 0; i < snakeCase.length; i++) {\n        let c = snakeCase.charAt(i);\n        switch (c) {\n            case \"_\":\n                capNext = true;\n                break;\n            case \"0\":\n            case \"1\":\n            case \"2\":\n            case \"3\":\n            case \"4\":\n            case \"5\":\n            case \"6\":\n            case \"7\":\n            case \"8\":\n            case \"9\":\n                b.push(c);\n                capNext = false;\n                break;\n            default:\n                if (capNext) {\n                    capNext = false;\n                    c = c.toUpperCase();\n                }\n                b.push(c);\n                break;\n        }\n    }\n    return b.join(\"\");\n}\n/**\n * Names that cannot be used for identifiers, such as class names,\n * but _can_ be used for object properties.\n */\nconst reservedIdentifiers = new Set([\n    // ECMAScript 2015 keywords\n    \"break\",\n    \"case\",\n    \"catch\",\n    \"class\",\n    \"const\",\n    \"continue\",\n    \"debugger\",\n    \"default\",\n    \"delete\",\n    \"do\",\n    \"else\",\n    \"export\",\n    \"extends\",\n    \"false\",\n    \"finally\",\n    \"for\",\n    \"function\",\n    \"if\",\n    \"import\",\n    \"in\",\n    \"instanceof\",\n    \"new\",\n    \"null\",\n    \"return\",\n    \"super\",\n    \"switch\",\n    \"this\",\n    \"throw\",\n    \"true\",\n    \"try\",\n    \"typeof\",\n    \"var\",\n    \"void\",\n    \"while\",\n    \"with\",\n    \"yield\",\n    // ECMAScript 2015 future reserved keywords\n    \"enum\",\n    \"implements\",\n    \"interface\",\n    \"let\",\n    \"package\",\n    \"private\",\n    \"protected\",\n    \"public\",\n    \"static\",\n    // Class name cannot be 'Object' when targeting ES5 with module CommonJS\n    \"Object\",\n    // TypeScript keywords that cannot be used for types (as opposed to variables)\n    \"bigint\",\n    \"number\",\n    \"boolean\",\n    \"string\",\n    \"object\",\n    // Identifiers reserved for the runtime, so we can generate legible code\n    \"globalThis\",\n    \"Uint8Array\",\n    \"Partial\",\n]);\n/**\n * Names that cannot be used for object properties because they are reserved\n * by built-in JavaScript properties.\n */\nconst reservedObjectProperties = new Set([\n    // names reserved by JavaScript\n    \"constructor\",\n    \"toString\",\n    \"toJSON\",\n    \"valueOf\",\n]);\n/**\n * Names that cannot be used for object properties because they are reserved\n * by the runtime.\n */\nconst reservedMessageProperties = new Set([\n    // names reserved by the runtime\n    \"getType\",\n    \"clone\",\n    \"equals\",\n    \"fromBinary\",\n    \"fromJson\",\n    \"fromJsonString\",\n    \"toBinary\",\n    \"toJson\",\n    \"toJsonString\",\n    // names reserved by the runtime for the future\n    \"toObject\",\n]);\nconst fallback = (name) => `${name}$`;\n/**\n * Will wrap names that are Object prototype properties or names reserved\n * for `Message`s.\n */\nconst safeMessageProperty = (name) => {\n    if (reservedMessageProperties.has(name)) {\n        return fallback(name);\n    }\n    return name;\n};\n/**\n * Names that cannot be used for object properties because they are reserved\n * by built-in JavaScript properties.\n */\nconst safeObjectProperty = (name) => {\n    if (reservedObjectProperties.has(name)) {\n        return fallback(name);\n    }\n    return name;\n};\n/**\n * Names that can be used for identifiers or class properties\n */\nconst safeIdentifier = (name) => {\n    if (reservedIdentifiers.has(name)) {\n        return fallback(name);\n    }\n    return name;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/names.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/proto-runtime.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/private/proto-runtime.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   makeProtoRuntime: () => (/* binding */ makeProtoRuntime)\n/* harmony export */ });\n/* harmony import */ var _enum_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./enum.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/enum.js\");\n/* harmony import */ var _message_type_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./message-type.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/message-type.js\");\n/* harmony import */ var _extensions_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./extensions.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/extensions.js\");\n/* harmony import */ var _json_format_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./json-format.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/json-format.js\");\n/* harmony import */ var _binary_format_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./binary-format.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/binary-format.js\");\n/* harmony import */ var _util_common_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util-common.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/util-common.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n\n\n\n\n\nfunction makeProtoRuntime(syntax, newFieldList, initFields) {\n    return {\n        syntax,\n        json: (0,_json_format_js__WEBPACK_IMPORTED_MODULE_0__.makeJsonFormat)(),\n        bin: (0,_binary_format_js__WEBPACK_IMPORTED_MODULE_1__.makeBinaryFormat)(),\n        util: Object.assign(Object.assign({}, (0,_util_common_js__WEBPACK_IMPORTED_MODULE_2__.makeUtilCommon)()), { newFieldList,\n            initFields }),\n        makeMessageType(typeName, fields, opt) {\n            return (0,_message_type_js__WEBPACK_IMPORTED_MODULE_3__.makeMessageType)(this, typeName, fields, opt);\n        },\n        makeEnum: _enum_js__WEBPACK_IMPORTED_MODULE_4__.makeEnum,\n        makeEnumType: _enum_js__WEBPACK_IMPORTED_MODULE_4__.makeEnumType,\n        getEnumType: _enum_js__WEBPACK_IMPORTED_MODULE_4__.getEnumType,\n        makeExtension(typeName, extendee, field) {\n            return (0,_extensions_js__WEBPACK_IMPORTED_MODULE_5__.makeExtension)(this, typeName, extendee, field);\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/proto-runtime.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/reflect.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/private/reflect.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearField: () => (/* binding */ clearField),\n/* harmony export */   isFieldSet: () => (/* binding */ isFieldSet)\n/* harmony export */ });\n/* harmony import */ var _scalars_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./scalars.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/scalars.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n/**\n * Returns true if the field is set.\n */\nfunction isFieldSet(field, target) {\n    const localName = field.localName;\n    if (field.repeated) {\n        return target[localName].length > 0;\n    }\n    if (field.oneof) {\n        return target[field.oneof.localName].case === localName; // eslint-disable-line @typescript-eslint/no-unsafe-member-access\n    }\n    switch (field.kind) {\n        case \"enum\":\n        case \"scalar\":\n            if (field.opt || field.req) {\n                // explicit presence\n                return target[localName] !== undefined;\n            }\n            // implicit presence\n            if (field.kind == \"enum\") {\n                return target[localName] !== field.T.values[0].no;\n            }\n            return !(0,_scalars_js__WEBPACK_IMPORTED_MODULE_0__.isScalarZeroValue)(field.T, target[localName]);\n        case \"message\":\n            return target[localName] !== undefined;\n        case \"map\":\n            return Object.keys(target[localName]).length > 0; // eslint-disable-line @typescript-eslint/no-unsafe-argument\n    }\n}\n/**\n * Resets the field, so that isFieldSet() will return false.\n */\nfunction clearField(field, target) {\n    const localName = field.localName;\n    const implicitPresence = !field.opt && !field.req;\n    if (field.repeated) {\n        target[localName] = [];\n    }\n    else if (field.oneof) {\n        target[field.oneof.localName] = { case: undefined };\n    }\n    else {\n        switch (field.kind) {\n            case \"map\":\n                target[localName] = {};\n                break;\n            case \"enum\":\n                target[localName] = implicitPresence ? field.T.values[0].no : undefined;\n                break;\n            case \"scalar\":\n                target[localName] = implicitPresence\n                    ? (0,_scalars_js__WEBPACK_IMPORTED_MODULE_0__.scalarZeroValue)(field.T, field.L)\n                    : undefined;\n                break;\n            case \"message\":\n                target[localName] = undefined;\n                break;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/reflect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/scalars.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/private/scalars.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isScalarZeroValue: () => (/* binding */ isScalarZeroValue),\n/* harmony export */   scalarEquals: () => (/* binding */ scalarEquals),\n/* harmony export */   scalarZeroValue: () => (/* binding */ scalarZeroValue)\n/* harmony export */ });\n/* harmony import */ var _proto_int64_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../proto-int64.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/proto-int64.js\");\n/* harmony import */ var _scalar_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../scalar.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/scalar.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n\n/**\n * Returns true if both scalar values are equal.\n */\nfunction scalarEquals(type, a, b) {\n    if (a === b) {\n        // This correctly matches equal values except BYTES and (possibly) 64-bit integers.\n        return true;\n    }\n    // Special case BYTES - we need to compare each byte individually\n    if (type == _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.BYTES) {\n        if (!(a instanceof Uint8Array) || !(b instanceof Uint8Array)) {\n            return false;\n        }\n        if (a.length !== b.length) {\n            return false;\n        }\n        for (let i = 0; i < a.length; i++) {\n            if (a[i] !== b[i]) {\n                return false;\n            }\n        }\n        return true;\n    }\n    // Special case 64-bit integers - we support number, string and bigint representation.\n    // eslint-disable-next-line @typescript-eslint/switch-exhaustiveness-check\n    switch (type) {\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.UINT64:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.FIXED64:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.INT64:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.SFIXED64:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.SINT64:\n            // Loose comparison will match between 0n, 0 and \"0\".\n            return a == b;\n    }\n    // Anything that hasn't been caught by strict comparison or special cased\n    // BYTES and 64-bit integers is not equal.\n    return false;\n}\n/**\n * Returns the zero value for the given scalar type.\n */\nfunction scalarZeroValue(type, longType) {\n    switch (type) {\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.BOOL:\n            return false;\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.UINT64:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.FIXED64:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.INT64:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.SFIXED64:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.SINT64:\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison -- acceptable since it's covered by tests\n            return (longType == 0 ? _proto_int64_js__WEBPACK_IMPORTED_MODULE_1__.protoInt64.zero : \"0\");\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.DOUBLE:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.FLOAT:\n            return 0.0;\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.BYTES:\n            return new Uint8Array(0);\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.STRING:\n            return \"\";\n        default:\n            // Handles INT32, UINT32, SINT32, FIXED32, SFIXED32.\n            // We do not use individual cases to save a few bytes code size.\n            return 0;\n    }\n}\n/**\n * Returns true for a zero-value. For example, an integer has the zero-value `0`,\n * a boolean is `false`, a string is `\"\"`, and bytes is an empty Uint8Array.\n *\n * In proto3, zero-values are not written to the wire, unless the field is\n * optional or repeated.\n */\nfunction isScalarZeroValue(type, value) {\n    switch (type) {\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.BOOL:\n            return value === false;\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.STRING:\n            return value === \"\";\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.BYTES:\n            return value instanceof Uint8Array && !value.byteLength;\n        default:\n            return value == 0; // Loose comparison matches 0n, 0 and \"0\"\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/scalars.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/util-common.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/private/util-common.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   makeUtilCommon: () => (/* binding */ makeUtilCommon)\n/* harmony export */ });\n/* harmony import */ var _enum_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./enum.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/enum.js\");\n/* harmony import */ var _scalars_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./scalars.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/scalars.js\");\n/* harmony import */ var _scalar_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../scalar.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/scalar.js\");\n/* harmony import */ var _is_message_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../is-message.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/is-message.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n\n\n\n\n/* eslint-disable @typescript-eslint/no-explicit-any,@typescript-eslint/no-unsafe-assignment,@typescript-eslint/no-unsafe-member-access,@typescript-eslint/no-unsafe-return,@typescript-eslint/no-unsafe-argument,no-case-declarations */\nfunction makeUtilCommon() {\n    return {\n        setEnumType: _enum_js__WEBPACK_IMPORTED_MODULE_0__.setEnumType,\n        initPartial(source, target) {\n            if (source === undefined) {\n                return;\n            }\n            const type = target.getType();\n            for (const member of type.fields.byMember()) {\n                const localName = member.localName, t = target, s = source;\n                if (s[localName] == null) {\n                    // TODO if source is a Message instance, we should use isFieldSet() here to support future field presence\n                    continue;\n                }\n                switch (member.kind) {\n                    case \"oneof\":\n                        const sk = s[localName].case;\n                        if (sk === undefined) {\n                            continue;\n                        }\n                        const sourceField = member.findField(sk);\n                        let val = s[localName].value;\n                        if (sourceField &&\n                            sourceField.kind == \"message\" &&\n                            !(0,_is_message_js__WEBPACK_IMPORTED_MODULE_1__.isMessage)(val, sourceField.T)) {\n                            val = new sourceField.T(val);\n                        }\n                        else if (sourceField &&\n                            sourceField.kind === \"scalar\" &&\n                            sourceField.T === _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.BYTES) {\n                            val = toU8Arr(val);\n                        }\n                        t[localName] = { case: sk, value: val };\n                        break;\n                    case \"scalar\":\n                    case \"enum\":\n                        let copy = s[localName];\n                        if (member.T === _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.BYTES) {\n                            copy = member.repeated\n                                ? copy.map(toU8Arr)\n                                : toU8Arr(copy);\n                        }\n                        t[localName] = copy;\n                        break;\n                    case \"map\":\n                        switch (member.V.kind) {\n                            case \"scalar\":\n                            case \"enum\":\n                                if (member.V.T === _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.BYTES) {\n                                    for (const [k, v] of Object.entries(s[localName])) {\n                                        t[localName][k] = toU8Arr(v);\n                                    }\n                                }\n                                else {\n                                    Object.assign(t[localName], s[localName]);\n                                }\n                                break;\n                            case \"message\":\n                                const messageType = member.V.T;\n                                for (const k of Object.keys(s[localName])) {\n                                    let val = s[localName][k];\n                                    if (!messageType.fieldWrapper) {\n                                        // We only take partial input for messages that are not a wrapper type.\n                                        // For those messages, we recursively normalize the partial input.\n                                        val = new messageType(val);\n                                    }\n                                    t[localName][k] = val;\n                                }\n                                break;\n                        }\n                        break;\n                    case \"message\":\n                        const mt = member.T;\n                        if (member.repeated) {\n                            t[localName] = s[localName].map((val) => (0,_is_message_js__WEBPACK_IMPORTED_MODULE_1__.isMessage)(val, mt) ? val : new mt(val));\n                        }\n                        else {\n                            const val = s[localName];\n                            if (mt.fieldWrapper) {\n                                if (\n                                // We can't use BytesValue.typeName as that will create a circular import\n                                mt.typeName === \"google.protobuf.BytesValue\") {\n                                    t[localName] = toU8Arr(val);\n                                }\n                                else {\n                                    t[localName] = val;\n                                }\n                            }\n                            else {\n                                t[localName] = (0,_is_message_js__WEBPACK_IMPORTED_MODULE_1__.isMessage)(val, mt) ? val : new mt(val);\n                            }\n                        }\n                        break;\n                }\n            }\n        },\n        // TODO use isFieldSet() here to support future field presence\n        equals(type, a, b) {\n            if (a === b) {\n                return true;\n            }\n            if (!a || !b) {\n                return false;\n            }\n            return type.fields.byMember().every((m) => {\n                const va = a[m.localName];\n                const vb = b[m.localName];\n                if (m.repeated) {\n                    if (va.length !== vb.length) {\n                        return false;\n                    }\n                    // eslint-disable-next-line @typescript-eslint/switch-exhaustiveness-check -- repeated fields are never \"map\"\n                    switch (m.kind) {\n                        case \"message\":\n                            return va.every((a, i) => m.T.equals(a, vb[i]));\n                        case \"scalar\":\n                            return va.every((a, i) => (0,_scalars_js__WEBPACK_IMPORTED_MODULE_3__.scalarEquals)(m.T, a, vb[i]));\n                        case \"enum\":\n                            return va.every((a, i) => (0,_scalars_js__WEBPACK_IMPORTED_MODULE_3__.scalarEquals)(_scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.INT32, a, vb[i]));\n                    }\n                    throw new Error(`repeated cannot contain ${m.kind}`);\n                }\n                switch (m.kind) {\n                    case \"message\":\n                        let a = va;\n                        let b = vb;\n                        if (m.T.fieldWrapper) {\n                            if (a !== undefined && !(0,_is_message_js__WEBPACK_IMPORTED_MODULE_1__.isMessage)(a)) {\n                                a = m.T.fieldWrapper.wrapField(a);\n                            }\n                            if (b !== undefined && !(0,_is_message_js__WEBPACK_IMPORTED_MODULE_1__.isMessage)(b)) {\n                                b = m.T.fieldWrapper.wrapField(b);\n                            }\n                        }\n                        return m.T.equals(a, b);\n                    case \"enum\":\n                        return (0,_scalars_js__WEBPACK_IMPORTED_MODULE_3__.scalarEquals)(_scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.INT32, va, vb);\n                    case \"scalar\":\n                        return (0,_scalars_js__WEBPACK_IMPORTED_MODULE_3__.scalarEquals)(m.T, va, vb);\n                    case \"oneof\":\n                        if (va.case !== vb.case) {\n                            return false;\n                        }\n                        const s = m.findField(va.case);\n                        if (s === undefined) {\n                            return true;\n                        }\n                        // eslint-disable-next-line @typescript-eslint/switch-exhaustiveness-check -- oneof fields are never \"map\"\n                        switch (s.kind) {\n                            case \"message\":\n                                return s.T.equals(va.value, vb.value);\n                            case \"enum\":\n                                return (0,_scalars_js__WEBPACK_IMPORTED_MODULE_3__.scalarEquals)(_scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.INT32, va.value, vb.value);\n                            case \"scalar\":\n                                return (0,_scalars_js__WEBPACK_IMPORTED_MODULE_3__.scalarEquals)(s.T, va.value, vb.value);\n                        }\n                        throw new Error(`oneof cannot contain ${s.kind}`);\n                    case \"map\":\n                        const keys = Object.keys(va).concat(Object.keys(vb));\n                        switch (m.V.kind) {\n                            case \"message\":\n                                const messageType = m.V.T;\n                                return keys.every((k) => messageType.equals(va[k], vb[k]));\n                            case \"enum\":\n                                return keys.every((k) => (0,_scalars_js__WEBPACK_IMPORTED_MODULE_3__.scalarEquals)(_scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.INT32, va[k], vb[k]));\n                            case \"scalar\":\n                                const scalarType = m.V.T;\n                                return keys.every((k) => (0,_scalars_js__WEBPACK_IMPORTED_MODULE_3__.scalarEquals)(scalarType, va[k], vb[k]));\n                        }\n                        break;\n                }\n            });\n        },\n        // TODO use isFieldSet() here to support future field presence\n        clone(message) {\n            const type = message.getType(), target = new type(), any = target;\n            for (const member of type.fields.byMember()) {\n                const source = message[member.localName];\n                let copy;\n                if (member.repeated) {\n                    copy = source.map(cloneSingularField);\n                }\n                else if (member.kind == \"map\") {\n                    copy = any[member.localName];\n                    for (const [key, v] of Object.entries(source)) {\n                        copy[key] = cloneSingularField(v);\n                    }\n                }\n                else if (member.kind == \"oneof\") {\n                    const f = member.findField(source.case);\n                    copy = f\n                        ? { case: source.case, value: cloneSingularField(source.value) }\n                        : { case: undefined };\n                }\n                else {\n                    copy = cloneSingularField(source);\n                }\n                any[member.localName] = copy;\n            }\n            for (const uf of type.runtime.bin.listUnknownFields(message)) {\n                type.runtime.bin.onUnknownField(any, uf.no, uf.wireType, uf.data);\n            }\n            return target;\n        },\n    };\n}\n// clone a single field value - i.e. the element type of repeated fields, the value type of maps\nfunction cloneSingularField(value) {\n    if (value === undefined) {\n        return value;\n    }\n    if ((0,_is_message_js__WEBPACK_IMPORTED_MODULE_1__.isMessage)(value)) {\n        return value.clone();\n    }\n    if (value instanceof Uint8Array) {\n        const c = new Uint8Array(value.byteLength);\n        c.set(value);\n        return c;\n    }\n    return value;\n}\n// converts any ArrayLike<number> to Uint8Array if necessary.\nfunction toU8Arr(input) {\n    return input instanceof Uint8Array ? input : new Uint8Array(input);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/util-common.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/proto-base64.js":
/*!******************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/proto-base64.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   protoBase64: () => (/* binding */ protoBase64)\n/* harmony export */ });\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n/* eslint-disable @typescript-eslint/ban-ts-comment, @typescript-eslint/no-unnecessary-condition, prefer-const */\n// lookup table from base64 character to byte\nlet encTable = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\".split(\"\");\n// lookup table from base64 character *code* to byte because lookup by number is fast\nlet decTable = [];\nfor (let i = 0; i < encTable.length; i++)\n    decTable[encTable[i].charCodeAt(0)] = i;\n// support base64url variants\ndecTable[\"-\".charCodeAt(0)] = encTable.indexOf(\"+\");\ndecTable[\"_\".charCodeAt(0)] = encTable.indexOf(\"/\");\nconst protoBase64 = {\n    /**\n     * Decodes a base64 string to a byte array.\n     *\n     * - ignores white-space, including line breaks and tabs\n     * - allows inner padding (can decode concatenated base64 strings)\n     * - does not require padding\n     * - understands base64url encoding:\n     *   \"-\" instead of \"+\",\n     *   \"_\" instead of \"/\",\n     *   no padding\n     */\n    dec(base64Str) {\n        // estimate byte size, not accounting for inner padding and whitespace\n        let es = (base64Str.length * 3) / 4;\n        if (base64Str[base64Str.length - 2] == \"=\")\n            es -= 2;\n        else if (base64Str[base64Str.length - 1] == \"=\")\n            es -= 1;\n        let bytes = new Uint8Array(es), bytePos = 0, // position in byte array\n        groupPos = 0, // position in base64 group\n        b, // current byte\n        p = 0; // previous byte\n        for (let i = 0; i < base64Str.length; i++) {\n            b = decTable[base64Str.charCodeAt(i)];\n            if (b === undefined) {\n                switch (base64Str[i]) {\n                    // @ts-ignore TS7029: Fallthrough case in switch\n                    case \"=\":\n                        groupPos = 0; // reset state when padding found\n                    // @ts-ignore TS7029: Fallthrough case in switch\n                    case \"\\n\":\n                    case \"\\r\":\n                    case \"\\t\":\n                    case \" \":\n                        continue; // skip white-space, and padding\n                    default:\n                        throw Error(\"invalid base64 string.\");\n                }\n            }\n            switch (groupPos) {\n                case 0:\n                    p = b;\n                    groupPos = 1;\n                    break;\n                case 1:\n                    bytes[bytePos++] = (p << 2) | ((b & 48) >> 4);\n                    p = b;\n                    groupPos = 2;\n                    break;\n                case 2:\n                    bytes[bytePos++] = ((p & 15) << 4) | ((b & 60) >> 2);\n                    p = b;\n                    groupPos = 3;\n                    break;\n                case 3:\n                    bytes[bytePos++] = ((p & 3) << 6) | b;\n                    groupPos = 0;\n                    break;\n            }\n        }\n        if (groupPos == 1)\n            throw Error(\"invalid base64 string.\");\n        return bytes.subarray(0, bytePos);\n    },\n    /**\n     * Encode a byte array to a base64 string.\n     */\n    enc(bytes) {\n        let base64 = \"\", groupPos = 0, // position in base64 group\n        b, // current byte\n        p = 0; // carry over from previous byte\n        for (let i = 0; i < bytes.length; i++) {\n            b = bytes[i];\n            switch (groupPos) {\n                case 0:\n                    base64 += encTable[b >> 2];\n                    p = (b & 3) << 4;\n                    groupPos = 1;\n                    break;\n                case 1:\n                    base64 += encTable[p | (b >> 4)];\n                    p = (b & 15) << 2;\n                    groupPos = 2;\n                    break;\n                case 2:\n                    base64 += encTable[p | (b >> 6)];\n                    base64 += encTable[b & 63];\n                    groupPos = 0;\n                    break;\n            }\n        }\n        // add output padding\n        if (groupPos) {\n            base64 += encTable[p];\n            base64 += \"=\";\n            if (groupPos == 1)\n                base64 += \"=\";\n        }\n        return base64;\n    },\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/proto-base64.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/proto-int64.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/proto-int64.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   protoInt64: () => (/* binding */ protoInt64)\n/* harmony export */ });\n/* harmony import */ var _private_assert_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./private/assert.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/assert.js\");\n/* harmony import */ var _google_varint_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./google/varint.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/google/varint.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n\nfunction makeInt64Support() {\n    const dv = new DataView(new ArrayBuffer(8));\n    // note that Safari 14 implements BigInt, but not the DataView methods\n    const ok = typeof BigInt === \"function\" &&\n        typeof dv.getBigInt64 === \"function\" &&\n        typeof dv.getBigUint64 === \"function\" &&\n        typeof dv.setBigInt64 === \"function\" &&\n        typeof dv.setBigUint64 === \"function\" &&\n        (typeof process != \"object\" ||\n            typeof process.env != \"object\" ||\n            process.env.BUF_BIGINT_DISABLE !== \"1\");\n    if (ok) {\n        const MIN = BigInt(\"-9223372036854775808\"), MAX = BigInt(\"9223372036854775807\"), UMIN = BigInt(\"0\"), UMAX = BigInt(\"18446744073709551615\");\n        return {\n            zero: BigInt(0),\n            supported: true,\n            parse(value) {\n                const bi = typeof value == \"bigint\" ? value : BigInt(value);\n                if (bi > MAX || bi < MIN) {\n                    throw new Error(`int64 invalid: ${value}`);\n                }\n                return bi;\n            },\n            uParse(value) {\n                const bi = typeof value == \"bigint\" ? value : BigInt(value);\n                if (bi > UMAX || bi < UMIN) {\n                    throw new Error(`uint64 invalid: ${value}`);\n                }\n                return bi;\n            },\n            enc(value) {\n                dv.setBigInt64(0, this.parse(value), true);\n                return {\n                    lo: dv.getInt32(0, true),\n                    hi: dv.getInt32(4, true),\n                };\n            },\n            uEnc(value) {\n                dv.setBigInt64(0, this.uParse(value), true);\n                return {\n                    lo: dv.getInt32(0, true),\n                    hi: dv.getInt32(4, true),\n                };\n            },\n            dec(lo, hi) {\n                dv.setInt32(0, lo, true);\n                dv.setInt32(4, hi, true);\n                return dv.getBigInt64(0, true);\n            },\n            uDec(lo, hi) {\n                dv.setInt32(0, lo, true);\n                dv.setInt32(4, hi, true);\n                return dv.getBigUint64(0, true);\n            },\n        };\n    }\n    const assertInt64String = (value) => (0,_private_assert_js__WEBPACK_IMPORTED_MODULE_0__.assert)(/^-?[0-9]+$/.test(value), `int64 invalid: ${value}`);\n    const assertUInt64String = (value) => (0,_private_assert_js__WEBPACK_IMPORTED_MODULE_0__.assert)(/^[0-9]+$/.test(value), `uint64 invalid: ${value}`);\n    return {\n        zero: \"0\",\n        supported: false,\n        parse(value) {\n            if (typeof value != \"string\") {\n                value = value.toString();\n            }\n            assertInt64String(value);\n            return value;\n        },\n        uParse(value) {\n            if (typeof value != \"string\") {\n                value = value.toString();\n            }\n            assertUInt64String(value);\n            return value;\n        },\n        enc(value) {\n            if (typeof value != \"string\") {\n                value = value.toString();\n            }\n            assertInt64String(value);\n            return (0,_google_varint_js__WEBPACK_IMPORTED_MODULE_1__.int64FromString)(value);\n        },\n        uEnc(value) {\n            if (typeof value != \"string\") {\n                value = value.toString();\n            }\n            assertUInt64String(value);\n            return (0,_google_varint_js__WEBPACK_IMPORTED_MODULE_1__.int64FromString)(value);\n        },\n        dec(lo, hi) {\n            return (0,_google_varint_js__WEBPACK_IMPORTED_MODULE_1__.int64ToString)(lo, hi);\n        },\n        uDec(lo, hi) {\n            return (0,_google_varint_js__WEBPACK_IMPORTED_MODULE_1__.uInt64ToString)(lo, hi);\n        },\n    };\n}\nconst protoInt64 = makeInt64Support();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGJ1ZmJ1aWxkL3Byb3RvYnVmL2Rpc3QvZXNtL3Byb3RvLWludDY0LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQzZDO0FBQ3dDO0FBQ3JGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0RBQXNELE1BQU07QUFDNUQ7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSx1REFBdUQsTUFBTTtBQUM3RDtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBLHlDQUF5QywwREFBTSw2Q0FBNkMsTUFBTTtBQUNsRywwQ0FBMEMsMERBQU0sNENBQTRDLE1BQU07QUFDbEc7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLGtFQUFlO0FBQ2xDLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLGtFQUFlO0FBQ2xDLFNBQVM7QUFDVDtBQUNBLG1CQUFtQixnRUFBYTtBQUNoQyxTQUFTO0FBQ1Q7QUFDQSxtQkFBbUIsaUVBQWM7QUFDakMsU0FBUztBQUNUO0FBQ0E7QUFDTyIsInNvdXJjZXMiOlsiRDpcXGhhY2tvdGhvblxcQmxvY2tjaGFpblxcVmludHJla1xcbm9kZV9tb2R1bGVzXFxAYnVmYnVpbGRcXHByb3RvYnVmXFxkaXN0XFxlc21cXHByb3RvLWludDY0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcHlyaWdodCAyMDIxLTIwMjQgQnVmIFRlY2hub2xvZ2llcywgSW5jLlxuLy9cbi8vIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4vLyB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4vLyBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbi8vXG4vLyAgICAgIGh0dHA6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuLy9cbi8vIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbi8vIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbi8vIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuLy8gU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuLy8gbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG5pbXBvcnQgeyBhc3NlcnQgfSBmcm9tIFwiLi9wcml2YXRlL2Fzc2VydC5qc1wiO1xuaW1wb3J0IHsgaW50NjRGcm9tU3RyaW5nLCBpbnQ2NFRvU3RyaW5nLCB1SW50NjRUb1N0cmluZywgfSBmcm9tIFwiLi9nb29nbGUvdmFyaW50LmpzXCI7XG5mdW5jdGlvbiBtYWtlSW50NjRTdXBwb3J0KCkge1xuICAgIGNvbnN0IGR2ID0gbmV3IERhdGFWaWV3KG5ldyBBcnJheUJ1ZmZlcig4KSk7XG4gICAgLy8gbm90ZSB0aGF0IFNhZmFyaSAxNCBpbXBsZW1lbnRzIEJpZ0ludCwgYnV0IG5vdCB0aGUgRGF0YVZpZXcgbWV0aG9kc1xuICAgIGNvbnN0IG9rID0gdHlwZW9mIEJpZ0ludCA9PT0gXCJmdW5jdGlvblwiICYmXG4gICAgICAgIHR5cGVvZiBkdi5nZXRCaWdJbnQ2NCA9PT0gXCJmdW5jdGlvblwiICYmXG4gICAgICAgIHR5cGVvZiBkdi5nZXRCaWdVaW50NjQgPT09IFwiZnVuY3Rpb25cIiAmJlxuICAgICAgICB0eXBlb2YgZHYuc2V0QmlnSW50NjQgPT09IFwiZnVuY3Rpb25cIiAmJlxuICAgICAgICB0eXBlb2YgZHYuc2V0QmlnVWludDY0ID09PSBcImZ1bmN0aW9uXCIgJiZcbiAgICAgICAgKHR5cGVvZiBwcm9jZXNzICE9IFwib2JqZWN0XCIgfHxcbiAgICAgICAgICAgIHR5cGVvZiBwcm9jZXNzLmVudiAhPSBcIm9iamVjdFwiIHx8XG4gICAgICAgICAgICBwcm9jZXNzLmVudi5CVUZfQklHSU5UX0RJU0FCTEUgIT09IFwiMVwiKTtcbiAgICBpZiAob2spIHtcbiAgICAgICAgY29uc3QgTUlOID0gQmlnSW50KFwiLTkyMjMzNzIwMzY4NTQ3NzU4MDhcIiksIE1BWCA9IEJpZ0ludChcIjkyMjMzNzIwMzY4NTQ3NzU4MDdcIiksIFVNSU4gPSBCaWdJbnQoXCIwXCIpLCBVTUFYID0gQmlnSW50KFwiMTg0NDY3NDQwNzM3MDk1NTE2MTVcIik7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICB6ZXJvOiBCaWdJbnQoMCksXG4gICAgICAgICAgICBzdXBwb3J0ZWQ6IHRydWUsXG4gICAgICAgICAgICBwYXJzZSh2YWx1ZSkge1xuICAgICAgICAgICAgICAgIGNvbnN0IGJpID0gdHlwZW9mIHZhbHVlID09IFwiYmlnaW50XCIgPyB2YWx1ZSA6IEJpZ0ludCh2YWx1ZSk7XG4gICAgICAgICAgICAgICAgaWYgKGJpID4gTUFYIHx8IGJpIDwgTUlOKSB7XG4gICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgaW50NjQgaW52YWxpZDogJHt2YWx1ZX1gKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgcmV0dXJuIGJpO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIHVQYXJzZSh2YWx1ZSkge1xuICAgICAgICAgICAgICAgIGNvbnN0IGJpID0gdHlwZW9mIHZhbHVlID09IFwiYmlnaW50XCIgPyB2YWx1ZSA6IEJpZ0ludCh2YWx1ZSk7XG4gICAgICAgICAgICAgICAgaWYgKGJpID4gVU1BWCB8fCBiaSA8IFVNSU4pIHtcbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGB1aW50NjQgaW52YWxpZDogJHt2YWx1ZX1gKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgcmV0dXJuIGJpO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIGVuYyh2YWx1ZSkge1xuICAgICAgICAgICAgICAgIGR2LnNldEJpZ0ludDY0KDAsIHRoaXMucGFyc2UodmFsdWUpLCB0cnVlKTtcbiAgICAgICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgICAgICBsbzogZHYuZ2V0SW50MzIoMCwgdHJ1ZSksXG4gICAgICAgICAgICAgICAgICAgIGhpOiBkdi5nZXRJbnQzMig0LCB0cnVlKSxcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIHVFbmModmFsdWUpIHtcbiAgICAgICAgICAgICAgICBkdi5zZXRCaWdJbnQ2NCgwLCB0aGlzLnVQYXJzZSh2YWx1ZSksIHRydWUpO1xuICAgICAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgICAgIGxvOiBkdi5nZXRJbnQzMigwLCB0cnVlKSxcbiAgICAgICAgICAgICAgICAgICAgaGk6IGR2LmdldEludDMyKDQsIHRydWUpLFxuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgZGVjKGxvLCBoaSkge1xuICAgICAgICAgICAgICAgIGR2LnNldEludDMyKDAsIGxvLCB0cnVlKTtcbiAgICAgICAgICAgICAgICBkdi5zZXRJbnQzMig0LCBoaSwgdHJ1ZSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGR2LmdldEJpZ0ludDY0KDAsIHRydWUpO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIHVEZWMobG8sIGhpKSB7XG4gICAgICAgICAgICAgICAgZHYuc2V0SW50MzIoMCwgbG8sIHRydWUpO1xuICAgICAgICAgICAgICAgIGR2LnNldEludDMyKDQsIGhpLCB0cnVlKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gZHYuZ2V0QmlnVWludDY0KDAsIHRydWUpO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgfTtcbiAgICB9XG4gICAgY29uc3QgYXNzZXJ0SW50NjRTdHJpbmcgPSAodmFsdWUpID0+IGFzc2VydCgvXi0/WzAtOV0rJC8udGVzdCh2YWx1ZSksIGBpbnQ2NCBpbnZhbGlkOiAke3ZhbHVlfWApO1xuICAgIGNvbnN0IGFzc2VydFVJbnQ2NFN0cmluZyA9ICh2YWx1ZSkgPT4gYXNzZXJ0KC9eWzAtOV0rJC8udGVzdCh2YWx1ZSksIGB1aW50NjQgaW52YWxpZDogJHt2YWx1ZX1gKTtcbiAgICByZXR1cm4ge1xuICAgICAgICB6ZXJvOiBcIjBcIixcbiAgICAgICAgc3VwcG9ydGVkOiBmYWxzZSxcbiAgICAgICAgcGFyc2UodmFsdWUpIHtcbiAgICAgICAgICAgIGlmICh0eXBlb2YgdmFsdWUgIT0gXCJzdHJpbmdcIikge1xuICAgICAgICAgICAgICAgIHZhbHVlID0gdmFsdWUudG9TdHJpbmcoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGFzc2VydEludDY0U3RyaW5nKHZhbHVlKTtcbiAgICAgICAgICAgIHJldHVybiB2YWx1ZTtcbiAgICAgICAgfSxcbiAgICAgICAgdVBhcnNlKHZhbHVlKSB7XG4gICAgICAgICAgICBpZiAodHlwZW9mIHZhbHVlICE9IFwic3RyaW5nXCIpIHtcbiAgICAgICAgICAgICAgICB2YWx1ZSA9IHZhbHVlLnRvU3RyaW5nKCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBhc3NlcnRVSW50NjRTdHJpbmcodmFsdWUpO1xuICAgICAgICAgICAgcmV0dXJuIHZhbHVlO1xuICAgICAgICB9LFxuICAgICAgICBlbmModmFsdWUpIHtcbiAgICAgICAgICAgIGlmICh0eXBlb2YgdmFsdWUgIT0gXCJzdHJpbmdcIikge1xuICAgICAgICAgICAgICAgIHZhbHVlID0gdmFsdWUudG9TdHJpbmcoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGFzc2VydEludDY0U3RyaW5nKHZhbHVlKTtcbiAgICAgICAgICAgIHJldHVybiBpbnQ2NEZyb21TdHJpbmcodmFsdWUpO1xuICAgICAgICB9LFxuICAgICAgICB1RW5jKHZhbHVlKSB7XG4gICAgICAgICAgICBpZiAodHlwZW9mIHZhbHVlICE9IFwic3RyaW5nXCIpIHtcbiAgICAgICAgICAgICAgICB2YWx1ZSA9IHZhbHVlLnRvU3RyaW5nKCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBhc3NlcnRVSW50NjRTdHJpbmcodmFsdWUpO1xuICAgICAgICAgICAgcmV0dXJuIGludDY0RnJvbVN0cmluZyh2YWx1ZSk7XG4gICAgICAgIH0sXG4gICAgICAgIGRlYyhsbywgaGkpIHtcbiAgICAgICAgICAgIHJldHVybiBpbnQ2NFRvU3RyaW5nKGxvLCBoaSk7XG4gICAgICAgIH0sXG4gICAgICAgIHVEZWMobG8sIGhpKSB7XG4gICAgICAgICAgICByZXR1cm4gdUludDY0VG9TdHJpbmcobG8sIGhpKTtcbiAgICAgICAgfSxcbiAgICB9O1xufVxuZXhwb3J0IGNvbnN0IHByb3RvSW50NjQgPSBtYWtlSW50NjRTdXBwb3J0KCk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/proto-int64.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/proto3.js":
/*!************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/proto3.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   proto3: () => (/* binding */ proto3)\n/* harmony export */ });\n/* harmony import */ var _private_proto_runtime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./private/proto-runtime.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/proto-runtime.js\");\n/* harmony import */ var _private_field_list_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./private/field-list.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/field-list.js\");\n/* harmony import */ var _private_scalars_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./private/scalars.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/scalars.js\");\n/* harmony import */ var _private_field_normalize_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./private/field-normalize.js */ \"(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/private/field-normalize.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n\n\n\n/**\n * Provides functionality for messages defined with the proto3 syntax.\n */\nconst proto3 = (0,_private_proto_runtime_js__WEBPACK_IMPORTED_MODULE_0__.makeProtoRuntime)(\"proto3\", (fields) => {\n    return new _private_field_list_js__WEBPACK_IMPORTED_MODULE_1__.InternalFieldList(fields, (source) => (0,_private_field_normalize_js__WEBPACK_IMPORTED_MODULE_2__.normalizeFieldInfos)(source, true));\n}, \n// TODO merge with proto2 and initExtensionField, also see initPartial, equals, clone\n(target) => {\n    for (const member of target.getType().fields.byMember()) {\n        if (member.opt) {\n            continue;\n        }\n        const name = member.localName, t = target;\n        if (member.repeated) {\n            t[name] = [];\n            continue;\n        }\n        switch (member.kind) {\n            case \"oneof\":\n                t[name] = { case: undefined };\n                break;\n            case \"enum\":\n                t[name] = 0;\n                break;\n            case \"map\":\n                t[name] = {};\n                break;\n            case \"scalar\":\n                t[name] = (0,_private_scalars_js__WEBPACK_IMPORTED_MODULE_3__.scalarZeroValue)(member.T, member.L);\n                break;\n            case \"message\":\n                // message fields are always optional in proto3\n                break;\n        }\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/proto3.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/scalar.js":
/*!************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/scalar.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LongType: () => (/* binding */ LongType),\n/* harmony export */   ScalarType: () => (/* binding */ ScalarType)\n/* harmony export */ });\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n/**\n * Scalar value types. This is a subset of field types declared by protobuf\n * enum google.protobuf.FieldDescriptorProto.Type The types GROUP and MESSAGE\n * are omitted, but the numerical values are identical.\n */\nvar ScalarType;\n(function (ScalarType) {\n    // 0 is reserved for errors.\n    // Order is weird for historical reasons.\n    ScalarType[ScalarType[\"DOUBLE\"] = 1] = \"DOUBLE\";\n    ScalarType[ScalarType[\"FLOAT\"] = 2] = \"FLOAT\";\n    // Not ZigZag encoded.  Negative numbers take 10 bytes.  Use TYPE_SINT64 if\n    // negative values are likely.\n    ScalarType[ScalarType[\"INT64\"] = 3] = \"INT64\";\n    ScalarType[ScalarType[\"UINT64\"] = 4] = \"UINT64\";\n    // Not ZigZag encoded.  Negative numbers take 10 bytes.  Use TYPE_SINT32 if\n    // negative values are likely.\n    ScalarType[ScalarType[\"INT32\"] = 5] = \"INT32\";\n    ScalarType[ScalarType[\"FIXED64\"] = 6] = \"FIXED64\";\n    ScalarType[ScalarType[\"FIXED32\"] = 7] = \"FIXED32\";\n    ScalarType[ScalarType[\"BOOL\"] = 8] = \"BOOL\";\n    ScalarType[ScalarType[\"STRING\"] = 9] = \"STRING\";\n    // Tag-delimited aggregate.\n    // Group type is deprecated and not supported in proto3. However, Proto3\n    // implementations should still be able to parse the group wire format and\n    // treat group fields as unknown fields.\n    // TYPE_GROUP = 10,\n    // TYPE_MESSAGE = 11,  // Length-delimited aggregate.\n    // New in version 2.\n    ScalarType[ScalarType[\"BYTES\"] = 12] = \"BYTES\";\n    ScalarType[ScalarType[\"UINT32\"] = 13] = \"UINT32\";\n    // TYPE_ENUM = 14,\n    ScalarType[ScalarType[\"SFIXED32\"] = 15] = \"SFIXED32\";\n    ScalarType[ScalarType[\"SFIXED64\"] = 16] = \"SFIXED64\";\n    ScalarType[ScalarType[\"SINT32\"] = 17] = \"SINT32\";\n    ScalarType[ScalarType[\"SINT64\"] = 18] = \"SINT64\";\n})(ScalarType || (ScalarType = {}));\n/**\n * JavaScript representation of fields with 64 bit integral types (int64, uint64,\n * sint64, fixed64, sfixed64).\n *\n * This is a subset of google.protobuf.FieldOptions.JSType, which defines JS_NORMAL,\n * JS_STRING, and JS_NUMBER. Protobuf-ES uses BigInt by default, but will use\n * String if `[jstype = JS_STRING]` is specified.\n *\n * ```protobuf\n * uint64 field_a = 1; // BigInt\n * uint64 field_b = 2 [jstype = JS_NORMAL]; // BigInt\n * uint64 field_b = 2 [jstype = JS_NUMBER]; // BigInt\n * uint64 field_b = 2 [jstype = JS_STRING]; // String\n * ```\n */\nvar LongType;\n(function (LongType) {\n    /**\n     * Use JavaScript BigInt.\n     */\n    LongType[LongType[\"BIGINT\"] = 0] = \"BIGINT\";\n    /**\n     * Use JavaScript String.\n     *\n     * Field option `[jstype = JS_STRING]`.\n     */\n    LongType[LongType[\"STRING\"] = 1] = \"STRING\";\n})(LongType || (LongType = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/scalar.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/service-type.js":
/*!******************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/service-type.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MethodIdempotency: () => (/* binding */ MethodIdempotency),\n/* harmony export */   MethodKind: () => (/* binding */ MethodKind)\n/* harmony export */ });\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n/**\n * MethodKind represents the four method types that can be declared in\n * protobuf with the `stream` keyword:\n *\n * 1. Unary:           rpc (Input) returns (Output)\n * 2. ServerStreaming: rpc (Input) returns (stream Output)\n * 3. ClientStreaming: rpc (stream Input) returns (Output)\n * 4. BiDiStreaming:   rpc (stream Input) returns (stream Output)\n */\nvar MethodKind;\n(function (MethodKind) {\n    MethodKind[MethodKind[\"Unary\"] = 0] = \"Unary\";\n    MethodKind[MethodKind[\"ServerStreaming\"] = 1] = \"ServerStreaming\";\n    MethodKind[MethodKind[\"ClientStreaming\"] = 2] = \"ClientStreaming\";\n    MethodKind[MethodKind[\"BiDiStreaming\"] = 3] = \"BiDiStreaming\";\n})(MethodKind || (MethodKind = {}));\n/**\n * Is this method side-effect-free (or safe in HTTP parlance), or just\n * idempotent, or neither? HTTP based RPC implementation may choose GET verb\n * for safe methods, and PUT verb for idempotent methods instead of the\n * default POST.\n *\n * This enum matches the protobuf enum google.protobuf.MethodOptions.IdempotencyLevel,\n * defined in the well-known type google/protobuf/descriptor.proto, but\n * drops UNKNOWN.\n */\nvar MethodIdempotency;\n(function (MethodIdempotency) {\n    /**\n     * Idempotent, no side effects.\n     */\n    MethodIdempotency[MethodIdempotency[\"NoSideEffects\"] = 1] = \"NoSideEffects\";\n    /**\n     * Idempotent, but may have side effects.\n     */\n    MethodIdempotency[MethodIdempotency[\"Idempotent\"] = 2] = \"Idempotent\";\n})(MethodIdempotency || (MethodIdempotency = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@bufbuild/protobuf/dist/esm/service-type.js\n");

/***/ })

};
;