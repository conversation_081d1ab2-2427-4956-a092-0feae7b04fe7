{"name": "@biglup/is-cid", "version": "1.0.3", "description": "Util to validate CIDs", "engines": {"node": ">=16.0.0", "npm": ">=7.0.0"}, "main": "dist/cjs/index.js", "exports": {".": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}}, "repository": {"type": "git", "url": "git+https://github.com/Biglup/is-cid.git"}, "publishConfig": {"access": "public"}, "contributors": ["<PERSON> <<EMAIL>>"], "license": "Apache-2.0", "scripts": {"build": "tsup --config ./tsup.config.ts && mkdir -p dist/esm && mkdir -p dist/cjs && cp ./build/esm-package.json dist/esm/package.json && mv ./dist/index.mjs ./dist/esm/index.js && mv ./dist/index.mjs.map ./dist/esm/index.js.map && cp ./dist/index.d.ts ./dist/esm/index.d.ts && cp ./dist/index.d.mts ./dist/esm/index.d.mts && mv ./dist/index.js ./dist/cjs/index.js && mv ./dist/index.js.map ./dist/cjs/index.js.map && mv ./dist/index.d.ts ./dist/cjs/index.d.ts && mv ./dist/index.d.mts ./dist/cjs/index.d.mts && cp ./build/cjs-package.json dist/cjs/package.json", "cleanup:dist": "rm -rf dist", "cleanup:nm": "rm -rf node_modules", "cleanup": "run-s cleanup:dist cleanup:nm", "lint": "eslint -c ./complete.eslintrc.js \"src/**/*.ts\" \"test/**/*.ts\"", "lint:fix": "yarn lint --fix", "prepack": "yarn build", "test": "jest -c ./jest.config.js"}, "devDependencies": {"@atixlabs/eslint-config": "^1.2.3", "@types/jest": "^26.0.24", "@types/node": "^18.11.8", "@typescript-eslint/eslint-plugin": "^5.27.1", "@typescript-eslint/parser": "^5.27.1", "eslint": "^7.32.0", "eslint-import-resolver-typescript": "^2.7.0", "eslint-plugin-filenames": "^1.3.2", "eslint-plugin-import": "^2.24.2", "eslint-plugin-jest": "^24.4.0", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-sonarjs": "^0.9.1", "eslint-plugin-sort-imports-es6-autofix": "^0.6.0", "eslint-plugin-sort-keys-fix": "^1.1.2", "eslint-plugin-unicorn": "^35.0.0", "eslint-watch": "^7.0.0", "prettier": "^2.3.2", "fs-extra": "^10.0.0", "husky": "^7.0.1", "jest": "^28.1.3", "npm-run-all": "^4.1.5", "shx": "^0.3.3", "ts-jest": "^28.0.7", "ts-node": "^10.0.0", "ts-node-dev": "^1.1.8", "typedoc": "^0.23.24", "typedoc-plugin-missing-exports": "^1.0.0", "typescript": "^4.7.4", "tsup": "^8.2.2"}, "dependencies": {"@multiformats/mafmt": "^12.1.6", "@multiformats/multiaddr": "^12.1.14", "iso-url": "^1.1.3", "multiformats": "^13.0.1", "uint8arrays": "^5.0.1"}, "files": ["dist/*", "!dist/tsconfig.tsbuildinfo", "LICENSE"], "bugs": {"url": "https://github.com/Biglup/is-cid/issues"}, "homepage": "https://github.com/Biglup/is-cid#readme", "author": "<PERSON> <<EMAIL>>"}