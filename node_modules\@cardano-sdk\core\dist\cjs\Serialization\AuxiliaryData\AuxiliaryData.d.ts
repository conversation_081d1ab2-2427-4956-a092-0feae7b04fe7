import * as Cardano from '../../Cardano';
import { GeneralTransactionMetadata } from './TransactionMetadata/GeneralTransactionMetadata';
import { HexBlob } from '@cardano-sdk/util';
import { NativeScript, PlutusV1Script, PlutusV2Script, PlutusV3Script } from '../Scripts';
export declare const SHELLEY_ERA_FIELDS_COUNT = 2;
export declare const ALONZO_AUX_TAG = 259;
export declare class AuxiliaryData {
    #private;
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): AuxiliaryData;
    toCore(): Cardano.AuxiliaryData;
    static fromCore(auxData: Cardano.AuxiliaryData): AuxiliaryData;
    metadata(): GeneralTransactionMetadata | undefined;
    setMetadata(metadata: GeneralTransactionMetadata): void;
    nativeScripts(): Array<NativeScript> | undefined;
    setNativeScripts(nativeScripts: Array<NativeScript>): void;
    plutusV1Scripts(): Array<PlutusV1Script> | undefined;
    setPlutusV1Scripts(plutusV1Scripts: Array<PlutusV1Script>): void;
    plutusV2Scripts(): Array<PlutusV2Script> | undefined;
    setPlutusV2Scripts(plutusV2Scripts: Array<PlutusV2Script>): void;
    plutusV3Scripts(): Array<PlutusV3Script> | undefined;
    setPlutusV3Scripts(plutusV3Scripts: Array<PlutusV3Script>): void;
}
//# sourceMappingURL=AuxiliaryData.d.ts.map