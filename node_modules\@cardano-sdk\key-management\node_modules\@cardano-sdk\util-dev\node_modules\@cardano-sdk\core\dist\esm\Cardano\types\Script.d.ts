import * as Crypto from '@cardano-sdk/crypto';
import { HexBlob } from '@cardano-sdk/util';
import { Slot } from './Block.js';
export declare enum ScriptType {
    Native = "native",
    Plutus = "plutus"
}
export declare enum NativeScriptKind {
    RequireSignature = 0,
    RequireAllOf = 1,
    RequireAnyOf = 2,
    RequireNOf = 3,
    RequireTimeAfter = 4,
    RequireTimeBefore = 5
}
export interface RequireSignatureScript {
    __type: ScriptType.Native;
    keyHash: Crypto.Ed25519KeyHashHex;
    kind: NativeScriptKind.RequireSignature;
}
export interface RequireAllOfScript {
    __type: ScriptType.Native;
    scripts: NativeScript[];
    kind: NativeScriptKind.RequireAllOf;
}
export interface RequireAnyOfScript {
    __type: ScriptType.Native;
    scripts: NativeScript[];
    kind: NativeScriptKind.RequireAnyOf;
}
export interface RequireAtLeastScript {
    __type: ScriptType.Native;
    required: number;
    scripts: NativeScript[];
    kind: NativeScriptKind.RequireNOf;
}
export interface RequireTimeBeforeScript {
    __type: ScriptType.Native;
    slot: Slot;
    kind: NativeScriptKind.RequireTimeBefore;
}
export interface RequireTimeAfterScript {
    __type: ScriptType.Native;
    slot: Slot;
    kind: NativeScriptKind.RequireTimeAfter;
}
export declare type NativeScript = RequireAllOfScript | RequireSignatureScript | RequireAnyOfScript | RequireAtLeastScript | RequireTimeBeforeScript | RequireTimeAfterScript;
export declare enum PlutusLanguageVersion {
    V1 = 0,
    V2 = 1,
    V3 = 2
}
export interface PlutusScript {
    __type: ScriptType.Plutus;
    bytes: HexBlob;
    version: PlutusLanguageVersion;
}
export declare type Script = NativeScript | PlutusScript;
export declare const isNativeScript: (script: Script) => script is NativeScript;
export declare const isPlutusScript: (script: Script) => script is PlutusScript;
//# sourceMappingURL=Script.d.ts.map