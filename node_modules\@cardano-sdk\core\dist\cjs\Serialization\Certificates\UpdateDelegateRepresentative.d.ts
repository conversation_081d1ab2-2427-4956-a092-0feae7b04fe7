import * as Cardano from '../../Cardano';
import { Anchor } from '../Common';
import { HexBlob } from '@cardano-sdk/util';
export declare class UpdateDelegateRepresentative {
    #private;
    constructor(drepCredential: Cardano.Credential, anchor?: Anchor);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): UpdateDelegateRepresentative;
    toCore(): Cardano.UpdateDelegateRepresentativeCertificate;
    static fromCore(cert: Cardano.UpdateDelegateRepresentativeCertificate): UpdateDelegateRepresentative;
    credential(): Cardano.Credential;
    anchor(): Anchor | undefined;
}
//# sourceMappingURL=UpdateDelegateRepresentative.d.ts.map