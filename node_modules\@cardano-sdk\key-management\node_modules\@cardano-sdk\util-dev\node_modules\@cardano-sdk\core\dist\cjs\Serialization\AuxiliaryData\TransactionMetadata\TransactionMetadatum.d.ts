import { HexBlob } from '@cardano-sdk/util';
import { MetadatumList } from './MetadatumList';
import { MetadatumMap } from './MetadatumMap';
import { TransactionMetadatumKind } from './TransactionMetadatumKind';
import type * as Cardano from '../../../Cardano';
export declare class TransactionMetadatum {
    #private;
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): TransactionMetadatum;
    toCore(): Cardano.Metadatum;
    static fromCore(metadatum: Cardano.Metadatum): TransactionMetadatum;
    static newMap(map: MetadatumMap): TransactionMetadatum;
    static newList(list: MetadatumList): TransactionMetadatum;
    static newInteger(integer: bigint): TransactionMetadatum;
    static newBytes(bytes: Uint8Array): TransactionMetadatum;
    static newText(text: string): TransactionMetadatum;
    getKind(): TransactionMetadatumKind;
    asMap(): MetadatumMap | undefined;
    asList(): MetadatumList | undefined;
    asInteger(): bigint | undefined;
    asBytes(): Uint8Array | undefined;
    asText(): string | undefined;
    equals(other: TransactionMetadatum): boolean;
    private static mapToCoreMetadatumList;
    private static bufferToBigint;
}
//# sourceMappingURL=TransactionMetadatum.d.ts.map