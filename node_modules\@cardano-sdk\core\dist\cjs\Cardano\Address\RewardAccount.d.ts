import { Credential } from './Address';
import { Ed25519KeyHashHex } from '@cardano-sdk/crypto';
import { NetworkId } from '../ChainId';
import { OpaqueString } from '@cardano-sdk/util';
export declare type RewardAccount = OpaqueString<'RewardAccount'>;
export declare const RewardAccount: {
    (value: string): RewardAccount;
    toHash(rewardAccount: RewardAccount): Ed25519KeyHashHex;
    fromCredential(credential: Credential, networkId: NetworkId): RewardAccount;
    toNetworkId(rewardAccount: RewardAccount): NetworkId;
};
export declare const createRewardAccount: (stakeKeyHash: Ed25519KeyHashHex, networkId: NetworkId) => RewardAccount;
//# sourceMappingURL=RewardAccount.d.ts.map