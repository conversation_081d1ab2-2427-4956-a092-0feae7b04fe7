import { Cardano, Provider } from '../../../index.js';
import { DeepPartial } from '@cardano-sdk/util';
import { Paginated, PaginationArgs } from '../../types/Pagination.js';
import { SortFields } from '../util.js';
export declare type FilterCondition = 'and' | 'or';
export declare type SortOrder = 'asc' | 'desc';
export declare type SortField = typeof SortFields[number];
export interface StakePoolSortOptions {
    order: SortOrder;
    field: SortField;
}
export declare type FilterIdentifiers = Partial<Pick<Cardano.PoolParameters, 'id'> & Pick<Cardano.StakePoolMetadata, 'name' | 'ticker'>>;
export interface MultipleChoiceSearchFilter<T> {
    _condition?: FilterCondition;
    values: T[];
}
export interface FuzzyOptions {
    distance: number;
    fieldNormWeight: number;
    ignoreFieldNorm: boolean;
    ignoreLocation: boolean;
    location: number;
    minMatchCharLength: number;
    threshold: number;
    useExtendedSearch: boolean;
    weights: {
        description: number;
        homepage: number;
        name: number;
        poolId: number;
        ticker: number;
    };
}
export interface QueryStakePoolsArgs {
    sort?: StakePoolSortOptions;
    filters?: {
        _condition?: FilterCondition;
        identifier?: MultipleChoiceSearchFilter<FilterIdentifiers>;
        pledgeMet?: boolean;
        status?: Cardano.StakePoolStatus[];
        text?: string;
    };
    fuzzyOptions?: DeepPartial<FuzzyOptions>;
    apyEpochsBackLimit?: number;
    epochRewards?: boolean;
    epochsLength?: number;
    pagination: PaginationArgs;
}
export interface StakePoolStats {
    qty: {
        activating: number;
        active: number;
        retired: number;
        retiring: number;
    };
}
export interface StakePoolProvider extends Provider {
    queryStakePools: (args: QueryStakePoolsArgs) => Promise<Paginated<Cardano.StakePool>>;
    stakePoolStats: () => Promise<StakePoolStats>;
}
//# sourceMappingURL=StakePoolProvider.d.ts.map