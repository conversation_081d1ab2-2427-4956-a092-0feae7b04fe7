import * as Cardano from '../../../Cardano';
import * as Crypto from '@cardano-sdk/crypto';
import { HexBlob } from '@cardano-sdk/util';
export declare class PoolMetadata {
    #private;
    constructor(url: string, poolMetadataHash: Crypto.Hash32ByteBase16);
    toCbor(): HexBlob;
    static fromCbor(cbor: HexBlob): PoolMetadata;
    toCore(): Cardano.PoolMetadataJson;
    static fromCore(metadata: Cardano.PoolMetadataJson): PoolMetadata;
    url(): string;
    setUrl(url: string): void;
    poolMetadataHash(): Crypto.Hash32ByteBase16;
    setPoolMetadataHash(poolMetadataHash: Crypto.Hash32ByteBase16): void;
}
//# sourceMappingURL=PoolMetadata.d.ts.map