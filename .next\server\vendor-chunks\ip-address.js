"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/ip-address";
exports.ids = ["vendor-chunks/ip-address"];
exports.modules = {

/***/ "(ssr)/./node_modules/ip-address/dist/address-error.js":
/*!*******************************************************!*\
  !*** ./node_modules/ip-address/dist/address-error.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AddressError = void 0;\nclass AddressError extends Error {\n    constructor(message, parseMessage) {\n        super(message);\n        this.name = 'AddressError';\n        if (parseMessage !== null) {\n            this.parseMessage = parseMessage;\n        }\n    }\n}\nexports.AddressError = AddressError;\n//# sourceMappingURL=address-error.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaXAtYWRkcmVzcy9kaXN0L2FkZHJlc3MtZXJyb3IuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsb0JBQW9CO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQjtBQUNwQiIsInNvdXJjZXMiOlsiRDpcXGhhY2tvdGhvblxcQmxvY2tjaGFpblxcVmludHJla1xcbm9kZV9tb2R1bGVzXFxpcC1hZGRyZXNzXFxkaXN0XFxhZGRyZXNzLWVycm9yLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5BZGRyZXNzRXJyb3IgPSB2b2lkIDA7XG5jbGFzcyBBZGRyZXNzRXJyb3IgZXh0ZW5kcyBFcnJvciB7XG4gICAgY29uc3RydWN0b3IobWVzc2FnZSwgcGFyc2VNZXNzYWdlKSB7XG4gICAgICAgIHN1cGVyKG1lc3NhZ2UpO1xuICAgICAgICB0aGlzLm5hbWUgPSAnQWRkcmVzc0Vycm9yJztcbiAgICAgICAgaWYgKHBhcnNlTWVzc2FnZSAhPT0gbnVsbCkge1xuICAgICAgICAgICAgdGhpcy5wYXJzZU1lc3NhZ2UgPSBwYXJzZU1lc3NhZ2U7XG4gICAgICAgIH1cbiAgICB9XG59XG5leHBvcnRzLkFkZHJlc3NFcnJvciA9IEFkZHJlc3NFcnJvcjtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFkZHJlc3MtZXJyb3IuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ip-address/dist/address-error.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ip-address/dist/common.js":
/*!************************************************!*\
  !*** ./node_modules/ip-address/dist/common.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isCorrect = exports.isInSubnet = void 0;\nfunction isInSubnet(address) {\n    if (this.subnetMask < address.subnetMask) {\n        return false;\n    }\n    if (this.mask(address.subnetMask) === address.mask()) {\n        return true;\n    }\n    return false;\n}\nexports.isInSubnet = isInSubnet;\nfunction isCorrect(defaultBits) {\n    return function () {\n        if (this.addressMinusSuffix !== this.correctForm()) {\n            return false;\n        }\n        if (this.subnetMask === defaultBits && !this.parsedSubnet) {\n            return true;\n        }\n        return this.parsedSubnet === String(this.subnetMask);\n    };\n}\nexports.isCorrect = isCorrect;\n//# sourceMappingURL=common.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaXAtYWRkcmVzcy9kaXN0L2NvbW1vbi5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxpQkFBaUIsR0FBRyxrQkFBa0I7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakIiLCJzb3VyY2VzIjpbIkQ6XFxoYWNrb3Rob25cXEJsb2NrY2hhaW5cXFZpbnRyZWtcXG5vZGVfbW9kdWxlc1xcaXAtYWRkcmVzc1xcZGlzdFxcY29tbW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5pc0NvcnJlY3QgPSBleHBvcnRzLmlzSW5TdWJuZXQgPSB2b2lkIDA7XG5mdW5jdGlvbiBpc0luU3VibmV0KGFkZHJlc3MpIHtcbiAgICBpZiAodGhpcy5zdWJuZXRNYXNrIDwgYWRkcmVzcy5zdWJuZXRNYXNrKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgaWYgKHRoaXMubWFzayhhZGRyZXNzLnN1Ym5ldE1hc2spID09PSBhZGRyZXNzLm1hc2soKSkge1xuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG4gICAgcmV0dXJuIGZhbHNlO1xufVxuZXhwb3J0cy5pc0luU3VibmV0ID0gaXNJblN1Ym5ldDtcbmZ1bmN0aW9uIGlzQ29ycmVjdChkZWZhdWx0Qml0cykge1xuICAgIHJldHVybiBmdW5jdGlvbiAoKSB7XG4gICAgICAgIGlmICh0aGlzLmFkZHJlc3NNaW51c1N1ZmZpeCAhPT0gdGhpcy5jb3JyZWN0Rm9ybSgpKSB7XG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHRoaXMuc3VibmV0TWFzayA9PT0gZGVmYXVsdEJpdHMgJiYgIXRoaXMucGFyc2VkU3VibmV0KSB7XG4gICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdGhpcy5wYXJzZWRTdWJuZXQgPT09IFN0cmluZyh0aGlzLnN1Ym5ldE1hc2spO1xuICAgIH07XG59XG5leHBvcnRzLmlzQ29ycmVjdCA9IGlzQ29ycmVjdDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNvbW1vbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ip-address/dist/common.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ip-address/dist/ip-address.js":
/*!****************************************************!*\
  !*** ./node_modules/ip-address/dist/ip-address.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.v6 = exports.AddressError = exports.Address6 = exports.Address4 = void 0;\nconst ipv4_1 = __webpack_require__(/*! ./ipv4 */ \"(ssr)/./node_modules/ip-address/dist/ipv4.js\");\nObject.defineProperty(exports, \"Address4\", ({ enumerable: true, get: function () { return ipv4_1.Address4; } }));\nconst ipv6_1 = __webpack_require__(/*! ./ipv6 */ \"(ssr)/./node_modules/ip-address/dist/ipv6.js\");\nObject.defineProperty(exports, \"Address6\", ({ enumerable: true, get: function () { return ipv6_1.Address6; } }));\nconst address_error_1 = __webpack_require__(/*! ./address-error */ \"(ssr)/./node_modules/ip-address/dist/address-error.js\");\nObject.defineProperty(exports, \"AddressError\", ({ enumerable: true, get: function () { return address_error_1.AddressError; } }));\nconst helpers = __importStar(__webpack_require__(/*! ./v6/helpers */ \"(ssr)/./node_modules/ip-address/dist/v6/helpers.js\"));\nexports.v6 = { helpers };\n//# sourceMappingURL=ip-address.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ip-address/dist/ip-address.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ip-address/dist/ipv4.js":
/*!**********************************************!*\
  !*** ./node_modules/ip-address/dist/ipv4.js ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/* eslint-disable no-param-reassign */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Address4 = void 0;\nconst common = __importStar(__webpack_require__(/*! ./common */ \"(ssr)/./node_modules/ip-address/dist/common.js\"));\nconst constants = __importStar(__webpack_require__(/*! ./v4/constants */ \"(ssr)/./node_modules/ip-address/dist/v4/constants.js\"));\nconst address_error_1 = __webpack_require__(/*! ./address-error */ \"(ssr)/./node_modules/ip-address/dist/address-error.js\");\nconst jsbn_1 = __webpack_require__(/*! jsbn */ \"(ssr)/./node_modules/jsbn/index.js\");\nconst sprintf_js_1 = __webpack_require__(/*! sprintf-js */ \"(ssr)/./node_modules/sprintf-js/src/sprintf.js\");\n/**\n * Represents an IPv4 address\n * @class Address4\n * @param {string} address - An IPv4 address string\n */\nclass Address4 {\n    constructor(address) {\n        this.groups = constants.GROUPS;\n        this.parsedAddress = [];\n        this.parsedSubnet = '';\n        this.subnet = '/32';\n        this.subnetMask = 32;\n        this.v4 = true;\n        /**\n         * Returns true if the address is correct, false otherwise\n         * @memberof Address4\n         * @instance\n         * @returns {Boolean}\n         */\n        this.isCorrect = common.isCorrect(constants.BITS);\n        /**\n         * Returns true if the given address is in the subnet of the current address\n         * @memberof Address4\n         * @instance\n         * @returns {boolean}\n         */\n        this.isInSubnet = common.isInSubnet;\n        this.address = address;\n        const subnet = constants.RE_SUBNET_STRING.exec(address);\n        if (subnet) {\n            this.parsedSubnet = subnet[0].replace('/', '');\n            this.subnetMask = parseInt(this.parsedSubnet, 10);\n            this.subnet = `/${this.subnetMask}`;\n            if (this.subnetMask < 0 || this.subnetMask > constants.BITS) {\n                throw new address_error_1.AddressError('Invalid subnet mask.');\n            }\n            address = address.replace(constants.RE_SUBNET_STRING, '');\n        }\n        this.addressMinusSuffix = address;\n        this.parsedAddress = this.parse(address);\n    }\n    static isValid(address) {\n        try {\n            // eslint-disable-next-line no-new\n            new Address4(address);\n            return true;\n        }\n        catch (e) {\n            return false;\n        }\n    }\n    /*\n     * Parses a v4 address\n     */\n    parse(address) {\n        const groups = address.split('.');\n        if (!address.match(constants.RE_ADDRESS)) {\n            throw new address_error_1.AddressError('Invalid IPv4 address.');\n        }\n        return groups;\n    }\n    /**\n     * Returns the correct form of an address\n     * @memberof Address4\n     * @instance\n     * @returns {String}\n     */\n    correctForm() {\n        return this.parsedAddress.map((part) => parseInt(part, 10)).join('.');\n    }\n    /**\n     * Converts a hex string to an IPv4 address object\n     * @memberof Address4\n     * @static\n     * @param {string} hex - a hex string to convert\n     * @returns {Address4}\n     */\n    static fromHex(hex) {\n        const padded = hex.replace(/:/g, '').padStart(8, '0');\n        const groups = [];\n        let i;\n        for (i = 0; i < 8; i += 2) {\n            const h = padded.slice(i, i + 2);\n            groups.push(parseInt(h, 16));\n        }\n        return new Address4(groups.join('.'));\n    }\n    /**\n     * Converts an integer into a IPv4 address object\n     * @memberof Address4\n     * @static\n     * @param {integer} integer - a number to convert\n     * @returns {Address4}\n     */\n    static fromInteger(integer) {\n        return Address4.fromHex(integer.toString(16));\n    }\n    /**\n     * Return an address from in-addr.arpa form\n     * @memberof Address4\n     * @static\n     * @param {string} arpaFormAddress - an 'in-addr.arpa' form ipv4 address\n     * @returns {Adress4}\n     * @example\n     * var address = Address4.fromArpa(**********.in-addr.arpa.)\n     * address.correctForm(); // '**********'\n     */\n    static fromArpa(arpaFormAddress) {\n        // remove ending \".in-addr.arpa.\" or just \".\"\n        const leader = arpaFormAddress.replace(/(\\.in-addr\\.arpa)?\\.$/, '');\n        const address = leader.split('.').reverse().join('.');\n        return new Address4(address);\n    }\n    /**\n     * Converts an IPv4 address object to a hex string\n     * @memberof Address4\n     * @instance\n     * @returns {String}\n     */\n    toHex() {\n        return this.parsedAddress.map((part) => (0, sprintf_js_1.sprintf)('%02x', parseInt(part, 10))).join(':');\n    }\n    /**\n     * Converts an IPv4 address object to an array of bytes\n     * @memberof Address4\n     * @instance\n     * @returns {Array}\n     */\n    toArray() {\n        return this.parsedAddress.map((part) => parseInt(part, 10));\n    }\n    /**\n     * Converts an IPv4 address object to an IPv6 address group\n     * @memberof Address4\n     * @instance\n     * @returns {String}\n     */\n    toGroup6() {\n        const output = [];\n        let i;\n        for (i = 0; i < constants.GROUPS; i += 2) {\n            const hex = (0, sprintf_js_1.sprintf)('%02x%02x', parseInt(this.parsedAddress[i], 10), parseInt(this.parsedAddress[i + 1], 10));\n            output.push((0, sprintf_js_1.sprintf)('%x', parseInt(hex, 16)));\n        }\n        return output.join(':');\n    }\n    /**\n     * Returns the address as a BigInteger\n     * @memberof Address4\n     * @instance\n     * @returns {BigInteger}\n     */\n    bigInteger() {\n        return new jsbn_1.BigInteger(this.parsedAddress.map((n) => (0, sprintf_js_1.sprintf)('%02x', parseInt(n, 10))).join(''), 16);\n    }\n    /**\n     * Helper function getting start address.\n     * @memberof Address4\n     * @instance\n     * @returns {BigInteger}\n     */\n    _startAddress() {\n        return new jsbn_1.BigInteger(this.mask() + '0'.repeat(constants.BITS - this.subnetMask), 2);\n    }\n    /**\n     * The first address in the range given by this address' subnet.\n     * Often referred to as the Network Address.\n     * @memberof Address4\n     * @instance\n     * @returns {Address4}\n     */\n    startAddress() {\n        return Address4.fromBigInteger(this._startAddress());\n    }\n    /**\n     * The first host address in the range given by this address's subnet ie\n     * the first address after the Network Address\n     * @memberof Address4\n     * @instance\n     * @returns {Address4}\n     */\n    startAddressExclusive() {\n        const adjust = new jsbn_1.BigInteger('1');\n        return Address4.fromBigInteger(this._startAddress().add(adjust));\n    }\n    /**\n     * Helper function getting end address.\n     * @memberof Address4\n     * @instance\n     * @returns {BigInteger}\n     */\n    _endAddress() {\n        return new jsbn_1.BigInteger(this.mask() + '1'.repeat(constants.BITS - this.subnetMask), 2);\n    }\n    /**\n     * The last address in the range given by this address' subnet\n     * Often referred to as the Broadcast\n     * @memberof Address4\n     * @instance\n     * @returns {Address4}\n     */\n    endAddress() {\n        return Address4.fromBigInteger(this._endAddress());\n    }\n    /**\n     * The last host address in the range given by this address's subnet ie\n     * the last address prior to the Broadcast Address\n     * @memberof Address4\n     * @instance\n     * @returns {Address4}\n     */\n    endAddressExclusive() {\n        const adjust = new jsbn_1.BigInteger('1');\n        return Address4.fromBigInteger(this._endAddress().subtract(adjust));\n    }\n    /**\n     * Converts a BigInteger to a v4 address object\n     * @memberof Address4\n     * @static\n     * @param {BigInteger} bigInteger - a BigInteger to convert\n     * @returns {Address4}\n     */\n    static fromBigInteger(bigInteger) {\n        return Address4.fromInteger(parseInt(bigInteger.toString(), 10));\n    }\n    /**\n     * Returns the first n bits of the address, defaulting to the\n     * subnet mask\n     * @memberof Address4\n     * @instance\n     * @returns {String}\n     */\n    mask(mask) {\n        if (mask === undefined) {\n            mask = this.subnetMask;\n        }\n        return this.getBitsBase2(0, mask);\n    }\n    /**\n     * Returns the bits in the given range as a base-2 string\n     * @memberof Address4\n     * @instance\n     * @returns {string}\n     */\n    getBitsBase2(start, end) {\n        return this.binaryZeroPad().slice(start, end);\n    }\n    /**\n     * Return the reversed ip6.arpa form of the address\n     * @memberof Address4\n     * @param {Object} options\n     * @param {boolean} options.omitSuffix - omit the \"in-addr.arpa\" suffix\n     * @instance\n     * @returns {String}\n     */\n    reverseForm(options) {\n        if (!options) {\n            options = {};\n        }\n        const reversed = this.correctForm().split('.').reverse().join('.');\n        if (options.omitSuffix) {\n            return reversed;\n        }\n        return (0, sprintf_js_1.sprintf)('%s.in-addr.arpa.', reversed);\n    }\n    /**\n     * Returns true if the given address is a multicast address\n     * @memberof Address4\n     * @instance\n     * @returns {boolean}\n     */\n    isMulticast() {\n        return this.isInSubnet(new Address4('*********/4'));\n    }\n    /**\n     * Returns a zero-padded base-2 string representation of the address\n     * @memberof Address4\n     * @instance\n     * @returns {string}\n     */\n    binaryZeroPad() {\n        return this.bigInteger().toString(2).padStart(constants.BITS, '0');\n    }\n    /**\n     * Groups an IPv4 address for inclusion at the end of an IPv6 address\n     * @returns {String}\n     */\n    groupForV6() {\n        const segments = this.parsedAddress;\n        return this.address.replace(constants.RE_ADDRESS, (0, sprintf_js_1.sprintf)('<span class=\"hover-group group-v4 group-6\">%s</span>.<span class=\"hover-group group-v4 group-7\">%s</span>', segments.slice(0, 2).join('.'), segments.slice(2, 4).join('.')));\n    }\n}\nexports.Address4 = Address4;\n//# sourceMappingURL=ipv4.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ip-address/dist/ipv4.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ip-address/dist/ipv6.js":
/*!**********************************************!*\
  !*** ./node_modules/ip-address/dist/ipv6.js ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/* eslint-disable prefer-destructuring */\n/* eslint-disable no-param-reassign */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Address6 = void 0;\nconst common = __importStar(__webpack_require__(/*! ./common */ \"(ssr)/./node_modules/ip-address/dist/common.js\"));\nconst constants4 = __importStar(__webpack_require__(/*! ./v4/constants */ \"(ssr)/./node_modules/ip-address/dist/v4/constants.js\"));\nconst constants6 = __importStar(__webpack_require__(/*! ./v6/constants */ \"(ssr)/./node_modules/ip-address/dist/v6/constants.js\"));\nconst helpers = __importStar(__webpack_require__(/*! ./v6/helpers */ \"(ssr)/./node_modules/ip-address/dist/v6/helpers.js\"));\nconst ipv4_1 = __webpack_require__(/*! ./ipv4 */ \"(ssr)/./node_modules/ip-address/dist/ipv4.js\");\nconst regular_expressions_1 = __webpack_require__(/*! ./v6/regular-expressions */ \"(ssr)/./node_modules/ip-address/dist/v6/regular-expressions.js\");\nconst address_error_1 = __webpack_require__(/*! ./address-error */ \"(ssr)/./node_modules/ip-address/dist/address-error.js\");\nconst jsbn_1 = __webpack_require__(/*! jsbn */ \"(ssr)/./node_modules/jsbn/index.js\");\nconst sprintf_js_1 = __webpack_require__(/*! sprintf-js */ \"(ssr)/./node_modules/sprintf-js/src/sprintf.js\");\nfunction assert(condition) {\n    if (!condition) {\n        throw new Error('Assertion failed.');\n    }\n}\nfunction addCommas(number) {\n    const r = /(\\d+)(\\d{3})/;\n    while (r.test(number)) {\n        number = number.replace(r, '$1,$2');\n    }\n    return number;\n}\nfunction spanLeadingZeroes4(n) {\n    n = n.replace(/^(0{1,})([1-9]+)$/, '<span class=\"parse-error\">$1</span>$2');\n    n = n.replace(/^(0{1,})(0)$/, '<span class=\"parse-error\">$1</span>$2');\n    return n;\n}\n/*\n * A helper function to compact an array\n */\nfunction compact(address, slice) {\n    const s1 = [];\n    const s2 = [];\n    let i;\n    for (i = 0; i < address.length; i++) {\n        if (i < slice[0]) {\n            s1.push(address[i]);\n        }\n        else if (i > slice[1]) {\n            s2.push(address[i]);\n        }\n    }\n    return s1.concat(['compact']).concat(s2);\n}\nfunction paddedHex(octet) {\n    return (0, sprintf_js_1.sprintf)('%04x', parseInt(octet, 16));\n}\nfunction unsignByte(b) {\n    // eslint-disable-next-line no-bitwise\n    return b & 0xff;\n}\n/**\n * Represents an IPv6 address\n * @class Address6\n * @param {string} address - An IPv6 address string\n * @param {number} [groups=8] - How many octets to parse\n * @example\n * var address = new Address6('2001::/32');\n */\nclass Address6 {\n    constructor(address, optionalGroups) {\n        this.addressMinusSuffix = '';\n        this.parsedSubnet = '';\n        this.subnet = '/128';\n        this.subnetMask = 128;\n        this.v4 = false;\n        this.zone = '';\n        // #region Attributes\n        /**\n         * Returns true if the given address is in the subnet of the current address\n         * @memberof Address6\n         * @instance\n         * @returns {boolean}\n         */\n        this.isInSubnet = common.isInSubnet;\n        /**\n         * Returns true if the address is correct, false otherwise\n         * @memberof Address6\n         * @instance\n         * @returns {boolean}\n         */\n        this.isCorrect = common.isCorrect(constants6.BITS);\n        if (optionalGroups === undefined) {\n            this.groups = constants6.GROUPS;\n        }\n        else {\n            this.groups = optionalGroups;\n        }\n        this.address = address;\n        const subnet = constants6.RE_SUBNET_STRING.exec(address);\n        if (subnet) {\n            this.parsedSubnet = subnet[0].replace('/', '');\n            this.subnetMask = parseInt(this.parsedSubnet, 10);\n            this.subnet = `/${this.subnetMask}`;\n            if (Number.isNaN(this.subnetMask) ||\n                this.subnetMask < 0 ||\n                this.subnetMask > constants6.BITS) {\n                throw new address_error_1.AddressError('Invalid subnet mask.');\n            }\n            address = address.replace(constants6.RE_SUBNET_STRING, '');\n        }\n        else if (/\\//.test(address)) {\n            throw new address_error_1.AddressError('Invalid subnet mask.');\n        }\n        const zone = constants6.RE_ZONE_STRING.exec(address);\n        if (zone) {\n            this.zone = zone[0];\n            address = address.replace(constants6.RE_ZONE_STRING, '');\n        }\n        this.addressMinusSuffix = address;\n        this.parsedAddress = this.parse(this.addressMinusSuffix);\n    }\n    static isValid(address) {\n        try {\n            // eslint-disable-next-line no-new\n            new Address6(address);\n            return true;\n        }\n        catch (e) {\n            return false;\n        }\n    }\n    /**\n     * Convert a BigInteger to a v6 address object\n     * @memberof Address6\n     * @static\n     * @param {BigInteger} bigInteger - a BigInteger to convert\n     * @returns {Address6}\n     * @example\n     * var bigInteger = new BigInteger('1000000000000');\n     * var address = Address6.fromBigInteger(bigInteger);\n     * address.correctForm(); // '::e8:d4a5:1000'\n     */\n    static fromBigInteger(bigInteger) {\n        const hex = bigInteger.toString(16).padStart(32, '0');\n        const groups = [];\n        let i;\n        for (i = 0; i < constants6.GROUPS; i++) {\n            groups.push(hex.slice(i * 4, (i + 1) * 4));\n        }\n        return new Address6(groups.join(':'));\n    }\n    /**\n     * Convert a URL (with optional port number) to an address object\n     * @memberof Address6\n     * @static\n     * @param {string} url - a URL with optional port number\n     * @example\n     * var addressAndPort = Address6.fromURL('http://[ffff::]:8080/foo/');\n     * addressAndPort.address.correctForm(); // 'ffff::'\n     * addressAndPort.port; // 8080\n     */\n    static fromURL(url) {\n        let host;\n        let port = null;\n        let result;\n        // If we have brackets parse them and find a port\n        if (url.indexOf('[') !== -1 && url.indexOf(']:') !== -1) {\n            result = constants6.RE_URL_WITH_PORT.exec(url);\n            if (result === null) {\n                return {\n                    error: 'failed to parse address with port',\n                    address: null,\n                    port: null,\n                };\n            }\n            host = result[1];\n            port = result[2];\n            // If there's a URL extract the address\n        }\n        else if (url.indexOf('/') !== -1) {\n            // Remove the protocol prefix\n            url = url.replace(/^[a-z0-9]+:\\/\\//, '');\n            // Parse the address\n            result = constants6.RE_URL.exec(url);\n            if (result === null) {\n                return {\n                    error: 'failed to parse address from URL',\n                    address: null,\n                    port: null,\n                };\n            }\n            host = result[1];\n            // Otherwise just assign the URL to the host and let the library parse it\n        }\n        else {\n            host = url;\n        }\n        // If there's a port convert it to an integer\n        if (port) {\n            port = parseInt(port, 10);\n            // squelch out of range ports\n            if (port < 0 || port > 65536) {\n                port = null;\n            }\n        }\n        else {\n            // Standardize `undefined` to `null`\n            port = null;\n        }\n        return {\n            address: new Address6(host),\n            port,\n        };\n    }\n    /**\n     * Create an IPv6-mapped address given an IPv4 address\n     * @memberof Address6\n     * @static\n     * @param {string} address - An IPv4 address string\n     * @returns {Address6}\n     * @example\n     * var address = Address6.fromAddress4('***********');\n     * address.correctForm(); // '::ffff:c0a8:1'\n     * address.to4in6(); // '::ffff:***********'\n     */\n    static fromAddress4(address) {\n        const address4 = new ipv4_1.Address4(address);\n        const mask6 = constants6.BITS - (constants4.BITS - address4.subnetMask);\n        return new Address6(`::ffff:${address4.correctForm()}/${mask6}`);\n    }\n    /**\n     * Return an address from ip6.arpa form\n     * @memberof Address6\n     * @static\n     * @param {string} arpaFormAddress - an 'ip6.arpa' form address\n     * @returns {Adress6}\n     * @example\n     * var address = Address6.fromArpa(e.f.f.f.3.c.2.6.f.f.f.e.6.6.8.e.*******.9.4.e.c.0.0.0.0.*******.ip6.arpa.)\n     * address.correctForm(); // '2001:0:ce49:7601:e866:efff:62c3:fffe'\n     */\n    static fromArpa(arpaFormAddress) {\n        // remove ending \".ip6.arpa.\" or just \".\"\n        let address = arpaFormAddress.replace(/(\\.ip6\\.arpa)?\\.$/, '');\n        const semicolonAmount = 7;\n        // correct ip6.arpa form with ending removed will be 63 characters\n        if (address.length !== 63) {\n            throw new address_error_1.AddressError(\"Invalid 'ip6.arpa' form.\");\n        }\n        const parts = address.split('.').reverse();\n        for (let i = semicolonAmount; i > 0; i--) {\n            const insertIndex = i * 4;\n            parts.splice(insertIndex, 0, ':');\n        }\n        address = parts.join('');\n        return new Address6(address);\n    }\n    /**\n     * Return the Microsoft UNC transcription of the address\n     * @memberof Address6\n     * @instance\n     * @returns {String} the Microsoft UNC transcription of the address\n     */\n    microsoftTranscription() {\n        return (0, sprintf_js_1.sprintf)('%s.ipv6-literal.net', this.correctForm().replace(/:/g, '-'));\n    }\n    /**\n     * Return the first n bits of the address, defaulting to the subnet mask\n     * @memberof Address6\n     * @instance\n     * @param {number} [mask=subnet] - the number of bits to mask\n     * @returns {String} the first n bits of the address as a string\n     */\n    mask(mask = this.subnetMask) {\n        return this.getBitsBase2(0, mask);\n    }\n    /**\n     * Return the number of possible subnets of a given size in the address\n     * @memberof Address6\n     * @instance\n     * @param {number} [size=128] - the subnet size\n     * @returns {String}\n     */\n    // TODO: probably useful to have a numeric version of this too\n    possibleSubnets(subnetSize = 128) {\n        const availableBits = constants6.BITS - this.subnetMask;\n        const subnetBits = Math.abs(subnetSize - constants6.BITS);\n        const subnetPowers = availableBits - subnetBits;\n        if (subnetPowers < 0) {\n            return '0';\n        }\n        return addCommas(new jsbn_1.BigInteger('2', 10).pow(subnetPowers).toString(10));\n    }\n    /**\n     * Helper function getting start address.\n     * @memberof Address6\n     * @instance\n     * @returns {BigInteger}\n     */\n    _startAddress() {\n        return new jsbn_1.BigInteger(this.mask() + '0'.repeat(constants6.BITS - this.subnetMask), 2);\n    }\n    /**\n     * The first address in the range given by this address' subnet\n     * Often referred to as the Network Address.\n     * @memberof Address6\n     * @instance\n     * @returns {Address6}\n     */\n    startAddress() {\n        return Address6.fromBigInteger(this._startAddress());\n    }\n    /**\n     * The first host address in the range given by this address's subnet ie\n     * the first address after the Network Address\n     * @memberof Address6\n     * @instance\n     * @returns {Address6}\n     */\n    startAddressExclusive() {\n        const adjust = new jsbn_1.BigInteger('1');\n        return Address6.fromBigInteger(this._startAddress().add(adjust));\n    }\n    /**\n     * Helper function getting end address.\n     * @memberof Address6\n     * @instance\n     * @returns {BigInteger}\n     */\n    _endAddress() {\n        return new jsbn_1.BigInteger(this.mask() + '1'.repeat(constants6.BITS - this.subnetMask), 2);\n    }\n    /**\n     * The last address in the range given by this address' subnet\n     * Often referred to as the Broadcast\n     * @memberof Address6\n     * @instance\n     * @returns {Address6}\n     */\n    endAddress() {\n        return Address6.fromBigInteger(this._endAddress());\n    }\n    /**\n     * The last host address in the range given by this address's subnet ie\n     * the last address prior to the Broadcast Address\n     * @memberof Address6\n     * @instance\n     * @returns {Address6}\n     */\n    endAddressExclusive() {\n        const adjust = new jsbn_1.BigInteger('1');\n        return Address6.fromBigInteger(this._endAddress().subtract(adjust));\n    }\n    /**\n     * Return the scope of the address\n     * @memberof Address6\n     * @instance\n     * @returns {String}\n     */\n    getScope() {\n        let scope = constants6.SCOPES[this.getBits(12, 16).intValue()];\n        if (this.getType() === 'Global unicast' && scope !== 'Link local') {\n            scope = 'Global';\n        }\n        return scope || 'Unknown';\n    }\n    /**\n     * Return the type of the address\n     * @memberof Address6\n     * @instance\n     * @returns {String}\n     */\n    getType() {\n        for (const subnet of Object.keys(constants6.TYPES)) {\n            if (this.isInSubnet(new Address6(subnet))) {\n                return constants6.TYPES[subnet];\n            }\n        }\n        return 'Global unicast';\n    }\n    /**\n     * Return the bits in the given range as a BigInteger\n     * @memberof Address6\n     * @instance\n     * @returns {BigInteger}\n     */\n    getBits(start, end) {\n        return new jsbn_1.BigInteger(this.getBitsBase2(start, end), 2);\n    }\n    /**\n     * Return the bits in the given range as a base-2 string\n     * @memberof Address6\n     * @instance\n     * @returns {String}\n     */\n    getBitsBase2(start, end) {\n        return this.binaryZeroPad().slice(start, end);\n    }\n    /**\n     * Return the bits in the given range as a base-16 string\n     * @memberof Address6\n     * @instance\n     * @returns {String}\n     */\n    getBitsBase16(start, end) {\n        const length = end - start;\n        if (length % 4 !== 0) {\n            throw new Error('Length of bits to retrieve must be divisible by four');\n        }\n        return this.getBits(start, end)\n            .toString(16)\n            .padStart(length / 4, '0');\n    }\n    /**\n     * Return the bits that are set past the subnet mask length\n     * @memberof Address6\n     * @instance\n     * @returns {String}\n     */\n    getBitsPastSubnet() {\n        return this.getBitsBase2(this.subnetMask, constants6.BITS);\n    }\n    /**\n     * Return the reversed ip6.arpa form of the address\n     * @memberof Address6\n     * @param {Object} options\n     * @param {boolean} options.omitSuffix - omit the \"ip6.arpa\" suffix\n     * @instance\n     * @returns {String}\n     */\n    reverseForm(options) {\n        if (!options) {\n            options = {};\n        }\n        const characters = Math.floor(this.subnetMask / 4);\n        const reversed = this.canonicalForm()\n            .replace(/:/g, '')\n            .split('')\n            .slice(0, characters)\n            .reverse()\n            .join('.');\n        if (characters > 0) {\n            if (options.omitSuffix) {\n                return reversed;\n            }\n            return (0, sprintf_js_1.sprintf)('%s.ip6.arpa.', reversed);\n        }\n        if (options.omitSuffix) {\n            return '';\n        }\n        return 'ip6.arpa.';\n    }\n    /**\n     * Return the correct form of the address\n     * @memberof Address6\n     * @instance\n     * @returns {String}\n     */\n    correctForm() {\n        let i;\n        let groups = [];\n        let zeroCounter = 0;\n        const zeroes = [];\n        for (i = 0; i < this.parsedAddress.length; i++) {\n            const value = parseInt(this.parsedAddress[i], 16);\n            if (value === 0) {\n                zeroCounter++;\n            }\n            if (value !== 0 && zeroCounter > 0) {\n                if (zeroCounter > 1) {\n                    zeroes.push([i - zeroCounter, i - 1]);\n                }\n                zeroCounter = 0;\n            }\n        }\n        // Do we end with a string of zeroes?\n        if (zeroCounter > 1) {\n            zeroes.push([this.parsedAddress.length - zeroCounter, this.parsedAddress.length - 1]);\n        }\n        const zeroLengths = zeroes.map((n) => n[1] - n[0] + 1);\n        if (zeroes.length > 0) {\n            const index = zeroLengths.indexOf(Math.max(...zeroLengths));\n            groups = compact(this.parsedAddress, zeroes[index]);\n        }\n        else {\n            groups = this.parsedAddress;\n        }\n        for (i = 0; i < groups.length; i++) {\n            if (groups[i] !== 'compact') {\n                groups[i] = parseInt(groups[i], 16).toString(16);\n            }\n        }\n        let correct = groups.join(':');\n        correct = correct.replace(/^compact$/, '::');\n        correct = correct.replace(/^compact|compact$/, ':');\n        correct = correct.replace(/compact/, '');\n        return correct;\n    }\n    /**\n     * Return a zero-padded base-2 string representation of the address\n     * @memberof Address6\n     * @instance\n     * @returns {String}\n     * @example\n     * var address = new Address6('2001:4860:4001:803::1011');\n     * address.binaryZeroPad();\n     * // '0010000000000001010010000110000001000000000000010000100000000011\n     * //  0000000000000000000000000000000000000000000000000001000000010001'\n     */\n    binaryZeroPad() {\n        return this.bigInteger().toString(2).padStart(constants6.BITS, '0');\n    }\n    // TODO: Improve the semantics of this helper function\n    parse4in6(address) {\n        const groups = address.split(':');\n        const lastGroup = groups.slice(-1)[0];\n        const address4 = lastGroup.match(constants4.RE_ADDRESS);\n        if (address4) {\n            this.parsedAddress4 = address4[0];\n            this.address4 = new ipv4_1.Address4(this.parsedAddress4);\n            for (let i = 0; i < this.address4.groups; i++) {\n                if (/^0[0-9]+/.test(this.address4.parsedAddress[i])) {\n                    throw new address_error_1.AddressError(\"IPv4 addresses can't have leading zeroes.\", address.replace(constants4.RE_ADDRESS, this.address4.parsedAddress.map(spanLeadingZeroes4).join('.')));\n                }\n            }\n            this.v4 = true;\n            groups[groups.length - 1] = this.address4.toGroup6();\n            address = groups.join(':');\n        }\n        return address;\n    }\n    // TODO: Make private?\n    parse(address) {\n        address = this.parse4in6(address);\n        const badCharacters = address.match(constants6.RE_BAD_CHARACTERS);\n        if (badCharacters) {\n            throw new address_error_1.AddressError((0, sprintf_js_1.sprintf)('Bad character%s detected in address: %s', badCharacters.length > 1 ? 's' : '', badCharacters.join('')), address.replace(constants6.RE_BAD_CHARACTERS, '<span class=\"parse-error\">$1</span>'));\n        }\n        const badAddress = address.match(constants6.RE_BAD_ADDRESS);\n        if (badAddress) {\n            throw new address_error_1.AddressError((0, sprintf_js_1.sprintf)('Address failed regex: %s', badAddress.join('')), address.replace(constants6.RE_BAD_ADDRESS, '<span class=\"parse-error\">$1</span>'));\n        }\n        let groups = [];\n        const halves = address.split('::');\n        if (halves.length === 2) {\n            let first = halves[0].split(':');\n            let last = halves[1].split(':');\n            if (first.length === 1 && first[0] === '') {\n                first = [];\n            }\n            if (last.length === 1 && last[0] === '') {\n                last = [];\n            }\n            const remaining = this.groups - (first.length + last.length);\n            if (!remaining) {\n                throw new address_error_1.AddressError('Error parsing groups');\n            }\n            this.elidedGroups = remaining;\n            this.elisionBegin = first.length;\n            this.elisionEnd = first.length + this.elidedGroups;\n            groups = groups.concat(first);\n            for (let i = 0; i < remaining; i++) {\n                groups.push('0');\n            }\n            groups = groups.concat(last);\n        }\n        else if (halves.length === 1) {\n            groups = address.split(':');\n            this.elidedGroups = 0;\n        }\n        else {\n            throw new address_error_1.AddressError('Too many :: groups found');\n        }\n        groups = groups.map((group) => (0, sprintf_js_1.sprintf)('%x', parseInt(group, 16)));\n        if (groups.length !== this.groups) {\n            throw new address_error_1.AddressError('Incorrect number of groups found');\n        }\n        return groups;\n    }\n    /**\n     * Return the canonical form of the address\n     * @memberof Address6\n     * @instance\n     * @returns {String}\n     */\n    canonicalForm() {\n        return this.parsedAddress.map(paddedHex).join(':');\n    }\n    /**\n     * Return the decimal form of the address\n     * @memberof Address6\n     * @instance\n     * @returns {String}\n     */\n    decimal() {\n        return this.parsedAddress.map((n) => (0, sprintf_js_1.sprintf)('%05d', parseInt(n, 16))).join(':');\n    }\n    /**\n     * Return the address as a BigInteger\n     * @memberof Address6\n     * @instance\n     * @returns {BigInteger}\n     */\n    bigInteger() {\n        return new jsbn_1.BigInteger(this.parsedAddress.map(paddedHex).join(''), 16);\n    }\n    /**\n     * Return the last two groups of this address as an IPv4 address string\n     * @memberof Address6\n     * @instance\n     * @returns {Address4}\n     * @example\n     * var address = new Address6('2001:4860:4001::1825:bf11');\n     * address.to4().correctForm(); // '************'\n     */\n    to4() {\n        const binary = this.binaryZeroPad().split('');\n        return ipv4_1.Address4.fromHex(new jsbn_1.BigInteger(binary.slice(96, 128).join(''), 2).toString(16));\n    }\n    /**\n     * Return the v4-in-v6 form of the address\n     * @memberof Address6\n     * @instance\n     * @returns {String}\n     */\n    to4in6() {\n        const address4 = this.to4();\n        const address6 = new Address6(this.parsedAddress.slice(0, 6).join(':'), 6);\n        const correct = address6.correctForm();\n        let infix = '';\n        if (!/:$/.test(correct)) {\n            infix = ':';\n        }\n        return correct + infix + address4.address;\n    }\n    /**\n     * Return an object containing the Teredo properties of the address\n     * @memberof Address6\n     * @instance\n     * @returns {Object}\n     */\n    inspectTeredo() {\n        /*\n        - Bits 0 to 31 are set to the Teredo prefix (normally 2001:0000::/32).\n        - Bits 32 to 63 embed the primary IPv4 address of the Teredo server that\n          is used.\n        - Bits 64 to 79 can be used to define some flags. Currently only the\n          higher order bit is used; it is set to 1 if the Teredo client is\n          located behind a cone NAT, 0 otherwise. For Microsoft's Windows Vista\n          and Windows Server 2008 implementations, more bits are used. In those\n          implementations, the format for these 16 bits is \"CRAAAAUG AAAAAAAA\",\n          where \"C\" remains the \"Cone\" flag. The \"R\" bit is reserved for future\n          use. The \"U\" bit is for the Universal/Local flag (set to 0). The \"G\" bit\n          is Individual/Group flag (set to 0). The A bits are set to a 12-bit\n          randomly generated number chosen by the Teredo client to introduce\n          additional protection for the Teredo node against IPv6-based scanning\n          attacks.\n        - Bits 80 to 95 contains the obfuscated UDP port number. This is the\n          port number that is mapped by the NAT to the Teredo client with all\n          bits inverted.\n        - Bits 96 to 127 contains the obfuscated IPv4 address. This is the\n          public IPv4 address of the NAT with all bits inverted.\n        */\n        const prefix = this.getBitsBase16(0, 32);\n        const udpPort = this.getBits(80, 96).xor(new jsbn_1.BigInteger('ffff', 16)).toString();\n        const server4 = ipv4_1.Address4.fromHex(this.getBitsBase16(32, 64));\n        const client4 = ipv4_1.Address4.fromHex(this.getBits(96, 128).xor(new jsbn_1.BigInteger('ffffffff', 16)).toString(16));\n        const flags = this.getBits(64, 80);\n        const flagsBase2 = this.getBitsBase2(64, 80);\n        const coneNat = flags.testBit(15);\n        const reserved = flags.testBit(14);\n        const groupIndividual = flags.testBit(8);\n        const universalLocal = flags.testBit(9);\n        const nonce = new jsbn_1.BigInteger(flagsBase2.slice(2, 6) + flagsBase2.slice(8, 16), 2).toString(10);\n        return {\n            prefix: (0, sprintf_js_1.sprintf)('%s:%s', prefix.slice(0, 4), prefix.slice(4, 8)),\n            server4: server4.address,\n            client4: client4.address,\n            flags: flagsBase2,\n            coneNat,\n            microsoft: {\n                reserved,\n                universalLocal,\n                groupIndividual,\n                nonce,\n            },\n            udpPort,\n        };\n    }\n    /**\n     * Return an object containing the 6to4 properties of the address\n     * @memberof Address6\n     * @instance\n     * @returns {Object}\n     */\n    inspect6to4() {\n        /*\n        - Bits 0 to 15 are set to the 6to4 prefix (2002::/16).\n        - Bits 16 to 48 embed the IPv4 address of the 6to4 gateway that is used.\n        */\n        const prefix = this.getBitsBase16(0, 16);\n        const gateway = ipv4_1.Address4.fromHex(this.getBitsBase16(16, 48));\n        return {\n            prefix: (0, sprintf_js_1.sprintf)('%s', prefix.slice(0, 4)),\n            gateway: gateway.address,\n        };\n    }\n    /**\n     * Return a v6 6to4 address from a v6 v4inv6 address\n     * @memberof Address6\n     * @instance\n     * @returns {Address6}\n     */\n    to6to4() {\n        if (!this.is4()) {\n            return null;\n        }\n        const addr6to4 = [\n            '2002',\n            this.getBitsBase16(96, 112),\n            this.getBitsBase16(112, 128),\n            '',\n            '/16',\n        ].join(':');\n        return new Address6(addr6to4);\n    }\n    /**\n     * Return a byte array\n     * @memberof Address6\n     * @instance\n     * @returns {Array}\n     */\n    toByteArray() {\n        const byteArray = this.bigInteger().toByteArray();\n        // work around issue where `toByteArray` returns a leading 0 element\n        if (byteArray.length === 17 && byteArray[0] === 0) {\n            return byteArray.slice(1);\n        }\n        return byteArray;\n    }\n    /**\n     * Return an unsigned byte array\n     * @memberof Address6\n     * @instance\n     * @returns {Array}\n     */\n    toUnsignedByteArray() {\n        return this.toByteArray().map(unsignByte);\n    }\n    /**\n     * Convert a byte array to an Address6 object\n     * @memberof Address6\n     * @static\n     * @returns {Address6}\n     */\n    static fromByteArray(bytes) {\n        return this.fromUnsignedByteArray(bytes.map(unsignByte));\n    }\n    /**\n     * Convert an unsigned byte array to an Address6 object\n     * @memberof Address6\n     * @static\n     * @returns {Address6}\n     */\n    static fromUnsignedByteArray(bytes) {\n        const BYTE_MAX = new jsbn_1.BigInteger('256', 10);\n        let result = new jsbn_1.BigInteger('0', 10);\n        let multiplier = new jsbn_1.BigInteger('1', 10);\n        for (let i = bytes.length - 1; i >= 0; i--) {\n            result = result.add(multiplier.multiply(new jsbn_1.BigInteger(bytes[i].toString(10), 10)));\n            multiplier = multiplier.multiply(BYTE_MAX);\n        }\n        return Address6.fromBigInteger(result);\n    }\n    /**\n     * Returns true if the address is in the canonical form, false otherwise\n     * @memberof Address6\n     * @instance\n     * @returns {boolean}\n     */\n    isCanonical() {\n        return this.addressMinusSuffix === this.canonicalForm();\n    }\n    /**\n     * Returns true if the address is a link local address, false otherwise\n     * @memberof Address6\n     * @instance\n     * @returns {boolean}\n     */\n    isLinkLocal() {\n        // Zeroes are required, i.e. we can't check isInSubnet with 'fe80::/10'\n        if (this.getBitsBase2(0, 64) ===\n            '1111111010000000000000000000000000000000000000000000000000000000') {\n            return true;\n        }\n        return false;\n    }\n    /**\n     * Returns true if the address is a multicast address, false otherwise\n     * @memberof Address6\n     * @instance\n     * @returns {boolean}\n     */\n    isMulticast() {\n        return this.getType() === 'Multicast';\n    }\n    /**\n     * Returns true if the address is a v4-in-v6 address, false otherwise\n     * @memberof Address6\n     * @instance\n     * @returns {boolean}\n     */\n    is4() {\n        return this.v4;\n    }\n    /**\n     * Returns true if the address is a Teredo address, false otherwise\n     * @memberof Address6\n     * @instance\n     * @returns {boolean}\n     */\n    isTeredo() {\n        return this.isInSubnet(new Address6('2001::/32'));\n    }\n    /**\n     * Returns true if the address is a 6to4 address, false otherwise\n     * @memberof Address6\n     * @instance\n     * @returns {boolean}\n     */\n    is6to4() {\n        return this.isInSubnet(new Address6('2002::/16'));\n    }\n    /**\n     * Returns true if the address is a loopback address, false otherwise\n     * @memberof Address6\n     * @instance\n     * @returns {boolean}\n     */\n    isLoopback() {\n        return this.getType() === 'Loopback';\n    }\n    // #endregion\n    // #region HTML\n    /**\n     * @returns {String} the address in link form with a default port of 80\n     */\n    href(optionalPort) {\n        if (optionalPort === undefined) {\n            optionalPort = '';\n        }\n        else {\n            optionalPort = (0, sprintf_js_1.sprintf)(':%s', optionalPort);\n        }\n        return (0, sprintf_js_1.sprintf)('http://[%s]%s/', this.correctForm(), optionalPort);\n    }\n    /**\n     * @returns {String} a link suitable for conveying the address via a URL hash\n     */\n    link(options) {\n        if (!options) {\n            options = {};\n        }\n        if (options.className === undefined) {\n            options.className = '';\n        }\n        if (options.prefix === undefined) {\n            options.prefix = '/#address=';\n        }\n        if (options.v4 === undefined) {\n            options.v4 = false;\n        }\n        let formFunction = this.correctForm;\n        if (options.v4) {\n            formFunction = this.to4in6;\n        }\n        if (options.className) {\n            return (0, sprintf_js_1.sprintf)('<a href=\"%1$s%2$s\" class=\"%3$s\">%2$s</a>', options.prefix, formFunction.call(this), options.className);\n        }\n        return (0, sprintf_js_1.sprintf)('<a href=\"%1$s%2$s\">%2$s</a>', options.prefix, formFunction.call(this));\n    }\n    /**\n     * Groups an address\n     * @returns {String}\n     */\n    group() {\n        if (this.elidedGroups === 0) {\n            // The simple case\n            return helpers.simpleGroup(this.address).join(':');\n        }\n        assert(typeof this.elidedGroups === 'number');\n        assert(typeof this.elisionBegin === 'number');\n        // The elided case\n        const output = [];\n        const [left, right] = this.address.split('::');\n        if (left.length) {\n            output.push(...helpers.simpleGroup(left));\n        }\n        else {\n            output.push('');\n        }\n        const classes = ['hover-group'];\n        for (let i = this.elisionBegin; i < this.elisionBegin + this.elidedGroups; i++) {\n            classes.push((0, sprintf_js_1.sprintf)('group-%d', i));\n        }\n        output.push((0, sprintf_js_1.sprintf)('<span class=\"%s\"></span>', classes.join(' ')));\n        if (right.length) {\n            output.push(...helpers.simpleGroup(right, this.elisionEnd));\n        }\n        else {\n            output.push('');\n        }\n        if (this.is4()) {\n            assert(this.address4 instanceof ipv4_1.Address4);\n            output.pop();\n            output.push(this.address4.groupForV6());\n        }\n        return output.join(':');\n    }\n    // #endregion\n    // #region Regular expressions\n    /**\n     * Generate a regular expression string that can be used to find or validate\n     * all variations of this address\n     * @memberof Address6\n     * @instance\n     * @param {boolean} substringSearch\n     * @returns {string}\n     */\n    regularExpressionString(substringSearch = false) {\n        let output = [];\n        // TODO: revisit why this is necessary\n        const address6 = new Address6(this.correctForm());\n        if (address6.elidedGroups === 0) {\n            // The simple case\n            output.push((0, regular_expressions_1.simpleRegularExpression)(address6.parsedAddress));\n        }\n        else if (address6.elidedGroups === constants6.GROUPS) {\n            // A completely elided address\n            output.push((0, regular_expressions_1.possibleElisions)(constants6.GROUPS));\n        }\n        else {\n            // A partially elided address\n            const halves = address6.address.split('::');\n            if (halves[0].length) {\n                output.push((0, regular_expressions_1.simpleRegularExpression)(halves[0].split(':')));\n            }\n            assert(typeof address6.elidedGroups === 'number');\n            output.push((0, regular_expressions_1.possibleElisions)(address6.elidedGroups, halves[0].length !== 0, halves[1].length !== 0));\n            if (halves[1].length) {\n                output.push((0, regular_expressions_1.simpleRegularExpression)(halves[1].split(':')));\n            }\n            output = [output.join(':')];\n        }\n        if (!substringSearch) {\n            output = [\n                '(?=^|',\n                regular_expressions_1.ADDRESS_BOUNDARY,\n                '|[^\\\\w\\\\:])(',\n                ...output,\n                ')(?=[^\\\\w\\\\:]|',\n                regular_expressions_1.ADDRESS_BOUNDARY,\n                '|$)',\n            ];\n        }\n        return output.join('');\n    }\n    /**\n     * Generate a regular expression that can be used to find or validate all\n     * variations of this address.\n     * @memberof Address6\n     * @instance\n     * @param {boolean} substringSearch\n     * @returns {RegExp}\n     */\n    regularExpression(substringSearch = false) {\n        return new RegExp(this.regularExpressionString(substringSearch), 'i');\n    }\n}\nexports.Address6 = Address6;\n//# sourceMappingURL=ipv6.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ip-address/dist/ipv6.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ip-address/dist/v4/constants.js":
/*!******************************************************!*\
  !*** ./node_modules/ip-address/dist/v4/constants.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.RE_SUBNET_STRING = exports.RE_ADDRESS = exports.GROUPS = exports.BITS = void 0;\nexports.BITS = 32;\nexports.GROUPS = 4;\nexports.RE_ADDRESS = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/g;\nexports.RE_SUBNET_STRING = /\\/\\d{1,2}$/;\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaXAtYWRkcmVzcy9kaXN0L3Y0L2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCx3QkFBd0IsR0FBRyxrQkFBa0IsR0FBRyxjQUFjLEdBQUcsWUFBWTtBQUM3RSxZQUFZO0FBQ1osY0FBYztBQUNkLGtCQUFrQjtBQUNsQix3QkFBd0IsU0FBUyxJQUFJO0FBQ3JDIiwic291cmNlcyI6WyJEOlxcaGFja290aG9uXFxCbG9ja2NoYWluXFxWaW50cmVrXFxub2RlX21vZHVsZXNcXGlwLWFkZHJlc3NcXGRpc3RcXHY0XFxjb25zdGFudHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLlJFX1NVQk5FVF9TVFJJTkcgPSBleHBvcnRzLlJFX0FERFJFU1MgPSBleHBvcnRzLkdST1VQUyA9IGV4cG9ydHMuQklUUyA9IHZvaWQgMDtcbmV4cG9ydHMuQklUUyA9IDMyO1xuZXhwb3J0cy5HUk9VUFMgPSA0O1xuZXhwb3J0cy5SRV9BRERSRVNTID0gL14oMjVbMC01XXwyWzAtNF1bMC05XXxbMDFdP1swLTldWzAtOV0/KVxcLigyNVswLTVdfDJbMC00XVswLTldfFswMV0/WzAtOV1bMC05XT8pXFwuKDI1WzAtNV18MlswLTRdWzAtOV18WzAxXT9bMC05XVswLTldPylcXC4oMjVbMC01XXwyWzAtNF1bMC05XXxbMDFdP1swLTldWzAtOV0/KSQvZztcbmV4cG9ydHMuUkVfU1VCTkVUX1NUUklORyA9IC9cXC9cXGR7MSwyfSQvO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y29uc3RhbnRzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ip-address/dist/v4/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ip-address/dist/v6/constants.js":
/*!******************************************************!*\
  !*** ./node_modules/ip-address/dist/v6/constants.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.RE_URL_WITH_PORT = exports.RE_URL = exports.RE_ZONE_STRING = exports.RE_SUBNET_STRING = exports.RE_BAD_ADDRESS = exports.RE_BAD_CHARACTERS = exports.TYPES = exports.SCOPES = exports.GROUPS = exports.BITS = void 0;\nexports.BITS = 128;\nexports.GROUPS = 8;\n/**\n * Represents IPv6 address scopes\n * @memberof Address6\n * @static\n */\nexports.SCOPES = {\n    0: 'Reserved',\n    1: 'Interface local',\n    2: 'Link local',\n    4: 'Admin local',\n    5: 'Site local',\n    8: 'Organization local',\n    14: 'Global',\n    15: 'Reserved',\n};\n/**\n * Represents IPv6 address types\n * @memberof Address6\n * @static\n */\nexports.TYPES = {\n    'ff01::1/128': 'Multicast (All nodes on this interface)',\n    'ff01::2/128': 'Multicast (All routers on this interface)',\n    'ff02::1/128': 'Multicast (All nodes on this link)',\n    'ff02::2/128': 'Multicast (All routers on this link)',\n    'ff05::2/128': 'Multicast (All routers in this site)',\n    'ff02::5/128': 'Multicast (OSPFv3 AllSPF routers)',\n    'ff02::6/128': 'Multicast (OSPFv3 AllDR routers)',\n    'ff02::9/128': 'Multicast (RIP routers)',\n    'ff02::a/128': 'Multicast (EIGRP routers)',\n    'ff02::d/128': 'Multicast (PIM routers)',\n    'ff02::16/128': 'Multicast (MLDv2 reports)',\n    'ff01::fb/128': 'Multicast (mDNSv6)',\n    'ff02::fb/128': 'Multicast (mDNSv6)',\n    'ff05::fb/128': 'Multicast (mDNSv6)',\n    'ff02::1:2/128': 'Multicast (All DHCP servers and relay agents on this link)',\n    'ff05::1:2/128': 'Multicast (All DHCP servers and relay agents in this site)',\n    'ff02::1:3/128': 'Multicast (All DHCP servers on this link)',\n    'ff05::1:3/128': 'Multicast (All DHCP servers in this site)',\n    '::/128': 'Unspecified',\n    '::1/128': 'Loopback',\n    'ff00::/8': 'Multicast',\n    'fe80::/10': 'Link-local unicast',\n};\n/**\n * A regular expression that matches bad characters in an IPv6 address\n * @memberof Address6\n * @static\n */\nexports.RE_BAD_CHARACTERS = /([^0-9a-f:/%])/gi;\n/**\n * A regular expression that matches an incorrect IPv6 address\n * @memberof Address6\n * @static\n */\nexports.RE_BAD_ADDRESS = /([0-9a-f]{5,}|:{3,}|[^:]:$|^:[^:]|\\/$)/gi;\n/**\n * A regular expression that matches an IPv6 subnet\n * @memberof Address6\n * @static\n */\nexports.RE_SUBNET_STRING = /\\/\\d{1,3}(?=%|$)/;\n/**\n * A regular expression that matches an IPv6 zone\n * @memberof Address6\n * @static\n */\nexports.RE_ZONE_STRING = /%.*$/;\nexports.RE_URL = new RegExp(/^\\[{0,1}([0-9a-f:]+)\\]{0,1}/);\nexports.RE_URL_WITH_PORT = new RegExp(/\\[([0-9a-f:]+)\\]:([0-9]{1,5})/);\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ip-address/dist/v6/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ip-address/dist/v6/helpers.js":
/*!****************************************************!*\
  !*** ./node_modules/ip-address/dist/v6/helpers.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.simpleGroup = exports.spanLeadingZeroes = exports.spanAll = exports.spanAllZeroes = void 0;\nconst sprintf_js_1 = __webpack_require__(/*! sprintf-js */ \"(ssr)/./node_modules/sprintf-js/src/sprintf.js\");\n/**\n * @returns {String} the string with all zeroes contained in a <span>\n */\nfunction spanAllZeroes(s) {\n    return s.replace(/(0+)/g, '<span class=\"zero\">$1</span>');\n}\nexports.spanAllZeroes = spanAllZeroes;\n/**\n * @returns {String} the string with each character contained in a <span>\n */\nfunction spanAll(s, offset = 0) {\n    const letters = s.split('');\n    return letters\n        .map((n, i) => (0, sprintf_js_1.sprintf)('<span class=\"digit value-%s position-%d\">%s</span>', n, i + offset, spanAllZeroes(n)) // XXX Use #base-2 .value-0 instead?\n    )\n        .join('');\n}\nexports.spanAll = spanAll;\nfunction spanLeadingZeroesSimple(group) {\n    return group.replace(/^(0+)/, '<span class=\"zero\">$1</span>');\n}\n/**\n * @returns {String} the string with leading zeroes contained in a <span>\n */\nfunction spanLeadingZeroes(address) {\n    const groups = address.split(':');\n    return groups.map((g) => spanLeadingZeroesSimple(g)).join(':');\n}\nexports.spanLeadingZeroes = spanLeadingZeroes;\n/**\n * Groups an address\n * @returns {String} a grouped address\n */\nfunction simpleGroup(addressString, offset = 0) {\n    const groups = addressString.split(':');\n    return groups.map((g, i) => {\n        if (/group-v4/.test(g)) {\n            return g;\n        }\n        return (0, sprintf_js_1.sprintf)('<span class=\"hover-group group-%d\">%s</span>', i + offset, spanLeadingZeroesSimple(g));\n    });\n}\nexports.simpleGroup = simpleGroup;\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ip-address/dist/v6/helpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/ip-address/dist/v6/regular-expressions.js":
/*!****************************************************************!*\
  !*** ./node_modules/ip-address/dist/v6/regular-expressions.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.possibleElisions = exports.simpleRegularExpression = exports.ADDRESS_BOUNDARY = exports.padGroup = exports.groupPossibilities = void 0;\nconst v6 = __importStar(__webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/ip-address/dist/v6/constants.js\"));\nconst sprintf_js_1 = __webpack_require__(/*! sprintf-js */ \"(ssr)/./node_modules/sprintf-js/src/sprintf.js\");\nfunction groupPossibilities(possibilities) {\n    return (0, sprintf_js_1.sprintf)('(%s)', possibilities.join('|'));\n}\nexports.groupPossibilities = groupPossibilities;\nfunction padGroup(group) {\n    if (group.length < 4) {\n        return (0, sprintf_js_1.sprintf)('0{0,%d}%s', 4 - group.length, group);\n    }\n    return group;\n}\nexports.padGroup = padGroup;\nexports.ADDRESS_BOUNDARY = '[^A-Fa-f0-9:]';\nfunction simpleRegularExpression(groups) {\n    const zeroIndexes = [];\n    groups.forEach((group, i) => {\n        const groupInteger = parseInt(group, 16);\n        if (groupInteger === 0) {\n            zeroIndexes.push(i);\n        }\n    });\n    // You can technically elide a single 0, this creates the regular expressions\n    // to match that eventuality\n    const possibilities = zeroIndexes.map((zeroIndex) => groups\n        .map((group, i) => {\n        if (i === zeroIndex) {\n            const elision = i === 0 || i === v6.GROUPS - 1 ? ':' : '';\n            return groupPossibilities([padGroup(group), elision]);\n        }\n        return padGroup(group);\n    })\n        .join(':'));\n    // The simplest case\n    possibilities.push(groups.map(padGroup).join(':'));\n    return groupPossibilities(possibilities);\n}\nexports.simpleRegularExpression = simpleRegularExpression;\nfunction possibleElisions(elidedGroups, moreLeft, moreRight) {\n    const left = moreLeft ? '' : ':';\n    const right = moreRight ? '' : ':';\n    const possibilities = [];\n    // 1. elision of everything (::)\n    if (!moreLeft && !moreRight) {\n        possibilities.push('::');\n    }\n    // 2. complete elision of the middle\n    if (moreLeft && moreRight) {\n        possibilities.push('');\n    }\n    if ((moreRight && !moreLeft) || (!moreRight && moreLeft)) {\n        // 3. complete elision of one side\n        possibilities.push(':');\n    }\n    // 4. elision from the left side\n    possibilities.push((0, sprintf_js_1.sprintf)('%s(:0{1,4}){1,%d}', left, elidedGroups - 1));\n    // 5. elision from the right side\n    possibilities.push((0, sprintf_js_1.sprintf)('(0{1,4}:){1,%d}%s', elidedGroups - 1, right));\n    // 6. no elision\n    possibilities.push((0, sprintf_js_1.sprintf)('(0{1,4}:){%d}0{1,4}', elidedGroups - 1));\n    // 7. elision (including sloppy elision) from the middle\n    for (let groups = 1; groups < elidedGroups - 1; groups++) {\n        for (let position = 1; position < elidedGroups - groups; position++) {\n            possibilities.push((0, sprintf_js_1.sprintf)('(0{1,4}:){%d}:(0{1,4}:){%d}0{1,4}', position, elidedGroups - position - groups - 1));\n        }\n    }\n    return groupPossibilities(possibilities);\n}\nexports.possibleElisions = possibleElisions;\n//# sourceMappingURL=regular-expressions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/ip-address/dist/v6/regular-expressions.js\n");

/***/ })

};
;